export namespace values {
    let ht: "\t";
    let lf: "\n";
    let cr: "\r";
    let space: " ";
    let exclamationMark: "!";
    let quotationMark: "\"";
    let numberSign: "#";
    let dollarSign: "$";
    let percentSign: "%";
    let ampersand: "&";
    let apostrophe: "'";
    let leftParenthesis: "(";
    let rightParenthesis: ")";
    let asterisk: "*";
    let plusSign: "+";
    let comma: ",";
    let dash: "-";
    let dot: ".";
    let slash: "/";
    let digit0: "0";
    let digit1: "1";
    let digit2: "2";
    let digit3: "3";
    let digit4: "4";
    let digit5: "5";
    let digit6: "6";
    let digit7: "7";
    let digit8: "8";
    let digit9: "9";
    let colon: ":";
    let semicolon: ";";
    let lessThan: "<";
    let equalsTo: "=";
    let greaterThan: ">";
    let questionMark: "?";
    let atSign: "@";
    let uppercaseA: "A";
    let uppercaseB: "B";
    let uppercaseC: "C";
    let uppercaseD: "D";
    let uppercaseE: "E";
    let uppercaseF: "F";
    let uppercaseG: "G";
    let uppercaseH: "H";
    let uppercaseI: "I";
    let uppercaseJ: "J";
    let uppercaseK: "K";
    let uppercaseL: "L";
    let uppercaseM: "M";
    let uppercaseN: "N";
    let uppercaseO: "O";
    let uppercaseP: "P";
    let uppercaseQ: "Q";
    let uppercaseR: "R";
    let uppercaseS: "S";
    let uppercaseT: "T";
    let uppercaseU: "U";
    let uppercaseV: "V";
    let uppercaseW: "W";
    let uppercaseX: "X";
    let uppercaseY: "Y";
    let uppercaseZ: "Z";
    let leftSquareBracket: "[";
    let backslash: "\\";
    let rightSquareBracket: "]";
    let caret: "^";
    let underscore: "_";
    let graveAccent: "`";
    let lowercaseA: "a";
    let lowercaseB: "b";
    let lowercaseC: "c";
    let lowercaseD: "d";
    let lowercaseE: "e";
    let lowercaseF: "f";
    let lowercaseG: "g";
    let lowercaseH: "h";
    let lowercaseI: "i";
    let lowercaseJ: "j";
    let lowercaseK: "k";
    let lowercaseL: "l";
    let lowercaseM: "m";
    let lowercaseN: "n";
    let lowercaseO: "o";
    let lowercaseP: "p";
    let lowercaseQ: "q";
    let lowercaseR: "r";
    let lowercaseS: "s";
    let lowercaseT: "t";
    let lowercaseU: "u";
    let lowercaseV: "v";
    let lowercaseW: "w";
    let lowercaseX: "x";
    let lowercaseY: "y";
    let lowercaseZ: "z";
    let leftCurlyBrace: "{";
    let verticalBar: "|";
    let rightCurlyBrace: "}";
    let tilde: "~";
    let replacementCharacter: "�";
}
//# sourceMappingURL=values.d.ts.map
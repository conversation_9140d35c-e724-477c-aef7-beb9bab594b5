export namespace types {
    let data: "data";
    let whitespace: "whitespace";
    let lineEnding: "lineEnding";
    let lineEndingBlank: "lineEndingBlank";
    let linePrefix: "linePrefix";
    let lineSuffix: "lineSuffix";
    let atxHeading: "atxHeading";
    let atxHeadingSequence: "atxHeadingSequence";
    let atxHeadingText: "atxHeadingText";
    let autolink: "autolink";
    let autolinkEmail: "autolinkEmail";
    let autolinkMarker: "autolinkMarker";
    let autolinkProtocol: "autolinkProtocol";
    let characterEscape: "characterEscape";
    let characterEscapeValue: "characterEscapeValue";
    let characterReference: "characterReference";
    let characterReferenceMarker: "characterReferenceMarker";
    let characterReferenceMarkerNumeric: "characterReferenceMarkerNumeric";
    let characterReferenceMarkerHexadecimal: "characterReferenceMarkerHexadecimal";
    let characterReferenceValue: "characterReferenceValue";
    let codeFenced: "codeFenced";
    let codeFencedFence: "codeFencedFence";
    let codeFencedFenceSequence: "codeFencedFenceSequence";
    let codeFencedFenceInfo: "codeFencedFenceInfo";
    let codeFencedFenceMeta: "codeFencedFenceMeta";
    let codeFlowValue: "codeFlowValue";
    let codeIndented: "codeIndented";
    let codeText: "codeText";
    let codeTextData: "codeTextData";
    let codeTextPadding: "codeTextPadding";
    let codeTextSequence: "codeTextSequence";
    let content: "content";
    let definition: "definition";
    let definitionDestination: "definitionDestination";
    let definitionDestinationLiteral: "definitionDestinationLiteral";
    let definitionDestinationLiteralMarker: "definitionDestinationLiteralMarker";
    let definitionDestinationRaw: "definitionDestinationRaw";
    let definitionDestinationString: "definitionDestinationString";
    let definitionLabel: "definitionLabel";
    let definitionLabelMarker: "definitionLabelMarker";
    let definitionLabelString: "definitionLabelString";
    let definitionMarker: "definitionMarker";
    let definitionTitle: "definitionTitle";
    let definitionTitleMarker: "definitionTitleMarker";
    let definitionTitleString: "definitionTitleString";
    let emphasis: "emphasis";
    let emphasisSequence: "emphasisSequence";
    let emphasisText: "emphasisText";
    let escapeMarker: "escapeMarker";
    let hardBreakEscape: "hardBreakEscape";
    let hardBreakTrailing: "hardBreakTrailing";
    let htmlFlow: "htmlFlow";
    let htmlFlowData: "htmlFlowData";
    let htmlText: "htmlText";
    let htmlTextData: "htmlTextData";
    let image: "image";
    let label: "label";
    let labelText: "labelText";
    let labelLink: "labelLink";
    let labelImage: "labelImage";
    let labelMarker: "labelMarker";
    let labelImageMarker: "labelImageMarker";
    let labelEnd: "labelEnd";
    let link: "link";
    let paragraph: "paragraph";
    let reference: "reference";
    let referenceMarker: "referenceMarker";
    let referenceString: "referenceString";
    let resource: "resource";
    let resourceDestination: "resourceDestination";
    let resourceDestinationLiteral: "resourceDestinationLiteral";
    let resourceDestinationLiteralMarker: "resourceDestinationLiteralMarker";
    let resourceDestinationRaw: "resourceDestinationRaw";
    let resourceDestinationString: "resourceDestinationString";
    let resourceMarker: "resourceMarker";
    let resourceTitle: "resourceTitle";
    let resourceTitleMarker: "resourceTitleMarker";
    let resourceTitleString: "resourceTitleString";
    let setextHeading: "setextHeading";
    let setextHeadingText: "setextHeadingText";
    let setextHeadingLine: "setextHeadingLine";
    let setextHeadingLineSequence: "setextHeadingLineSequence";
    let strong: "strong";
    let strongSequence: "strongSequence";
    let strongText: "strongText";
    let thematicBreak: "thematicBreak";
    let thematicBreakSequence: "thematicBreakSequence";
    let blockQuote: "blockQuote";
    let blockQuotePrefix: "blockQuotePrefix";
    let blockQuoteMarker: "blockQuoteMarker";
    let blockQuotePrefixWhitespace: "blockQuotePrefixWhitespace";
    let listOrdered: "listOrdered";
    let listUnordered: "listUnordered";
    let listItemIndent: "listItemIndent";
    let listItemMarker: "listItemMarker";
    let listItemPrefix: "listItemPrefix";
    let listItemPrefixWhitespace: "listItemPrefixWhitespace";
    let listItemValue: "listItemValue";
    let chunkDocument: "chunkDocument";
    let chunkContent: "chunkContent";
    let chunkFlow: "chunkFlow";
    let chunkText: "chunkText";
    let chunkString: "chunkString";
}
//# sourceMappingURL=types.d.ts.map
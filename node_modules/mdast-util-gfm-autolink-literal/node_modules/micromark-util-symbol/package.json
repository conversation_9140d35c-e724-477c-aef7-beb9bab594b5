{"name": "micromark-util-symbol", "version": "2.0.1", "description": "micromark utility with symbols", "license": "MIT", "keywords": ["micromark", "util", "utility", "symbol"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-symbol", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["lib/"], "exports": "./lib/default.js", "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"unicorn/prefer-code-point": "off"}}}
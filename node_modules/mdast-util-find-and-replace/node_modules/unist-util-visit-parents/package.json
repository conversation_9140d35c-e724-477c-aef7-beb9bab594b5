{"name": "unist-util-visit-parents", "version": "6.0.1", "description": "unist utility to recursively walk over nodes, with ancestral information", "license": "MIT", "keywords": ["unist", "unist-util", "util", "utility", "tree", "ast", "visit", "traverse", "walk", "check", "parent", "parents"], "repository": "syntax-tree/unist-util-visit-parents", "bugs": "https://github.com/syntax-tree/unist-util-visit-parents/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": {".": "./index.js", "./do-not-use-color": {"node": "./lib/color.node.js", "default": "./lib/color.js"}}, "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0", "unist-util-is": "^6.0.0"}, "devDependencies": {"@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "@types/node": "^20.0.0", "@types/xast": "^2.0.0", "c8": "^8.0.0", "mdast-util-from-markdown": "^1.0.0", "mdast-util-gfm": "^2.0.0", "micromark-extension-gfm": "^2.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "strip-ansi": "^7.0.0", "tsd": "^0.28.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.54.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "#": "needed `any`s", "ignoreFiles": ["lib/index.d.ts"], "ignoreCatch": true, "strict": true}, "xo": {"prettier": true}}
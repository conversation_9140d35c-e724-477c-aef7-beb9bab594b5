{"name": "@uiw/codemirror-themes", "version": "4.23.13", "description": "Themes for CodeMirror.", "homepage": "https://uiwjs.github.io/react-codemirror/#/theme/doc", "funding": "https://jaywcjlove.github.io/#/sponsor", "author": "kenny wong <<EMAIL>>", "license": "MIT", "main": "./cjs/index.js", "module": "./esm/index.js", "exports": {".": {"require": "./cjs/index.js", "import": "./esm/index.js"}, "./*": "./*"}, "scripts": {"watch": "tsbb watch src/*.tsx --use-babel", "build": "tsbb build src/*.tsx --use-babel"}, "repository": {"type": "git", "url": "https://github.com/uiwjs/react-codemirror.git"}, "files": ["src", "esm", "cjs"], "peerDependencies": {"@codemirror/language": ">=6.0.0", "@codemirror/state": ">=6.0.0", "@codemirror/view": ">=6.0.0"}, "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0"}, "keywords": ["codemirror", "codemirror6", "theme", "syntax", "ide", "code"], "jest": {"coverageReporters": ["lcov", "json-summary"]}}
{"version": 3, "file": "IncompatiblePlugin.js", "sourceRoot": "", "sources": ["../src/IncompatiblePlugin.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAAkD;AAClD,iDAA6C;AAC7C,iDAA6C;AAE7C,SAAS,IAAI;IACX,mBAAmB;AACrB,CAAC;AAeD,SAAgB,mBAAmB,CAAC,KAA+B;IAC1D,IAAA,OAAO,GAAI,KAAK,QAAT,CAAS;IAEvB,IAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAArB,CAAqB,CAAC,CAAA;IACrE,OAAO,CACL,uCACE,KAAK,EAAE;YACL,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,YAAY,EAAE,QAAQ;YACtB,UAAU,EAAE,OAAO;YACnB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;SACf;QAED,uCAAK,KAAK,EAAE,EAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAC;YAChF,sCAAI,KAAK,EAAE,EAAC,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAC;;gBACpC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAC9C;YACL;;gBAEG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa;4DAEjD;YAEN,uCAAK,KAAK,EAAE,EAAC,SAAS,EAAE,MAAM,EAAC,IAC5B,OAAO,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAClB,uCAAK,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAC,YAAY,EAAE,MAAM,EAAC;gBAC7C,uCAAK,KAAK,EAAE,EAAC,WAAW,EAAE,MAAM,EAAC;oBAC/B;wBACE,wCAAM,KAAK,EAAE,EAAC,KAAK,EAAE,SAAS,EAAC,IAAG,CAAC,CAAC,IAAI,CAAQ,CACzC,CACL;gBACN;;oBACW,wCAAM,KAAK,EAAE,EAAC,KAAK,EAAE,SAAS,EAAC,IAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAQ,CAC5D,CACF,CACP,EAXmB,CAWnB,CAAC,CACE;YAEN;gBACG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO;;gBAAY,GAAG;gBAC1D,qCAAG,IAAI,EAAC,iCAAiC,uBAAqB;oBAC1D;YAEN;gBACE,sCAAI,KAAK,EAAE,EAAC,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAC,wBAEpE;gBAEL,8BAAC,mBAAmB,IAAC,OAAO,EAAE,OAAO,GAAI;gBACzC,wCAAM,KAAK,EAAE,EAAC,MAAM,EAAE,EAAE,EAAC;oBACvB,8BAAC,aAAa,IAAC,OAAO,EAAE,OAAO,GAAI,CAC9B;gBAEP,8EAA2C;gBAE3C;oBACE,sCAAI,KAAK,EAAE,EAAC,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAC,uBAEpE;oBACJ,gBAAgB,CAAC,MAAM,GAAG,CAAC,IAAI,CAC9B,2CACG,gBAAgB,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAC3B,uCAAK,GAAG,EAAE,CAAC,CAAC,IAAI;wBACb,CAAC,CAAC,IAAI;;wBAAK,qCAAG,IAAI,EAAE,CAAC,CAAC,iBAAiB,sBAAqB,CACzD,CACP,EAJ4B,CAI5B,CAAC,CACE,CACP;oBAED,uCAAK,KAAK,EAAE,EAAC,SAAS,EAAE,EAAE,EAAC;wBACzB,qCAAG,IAAI,EAAC,sDAAsD,mCAE1D,CACA,CACF,CACF,CACF,CACF,CACP,CAAA;AACH,CAAC;AAjFD,kDAiFC;AAED,SAAS,mBAAmB,CAAC,KAA+B;IAC1D,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAf,CAAe,CAAC,CAAA;IAE5D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACnB,OAAO,IAAI,CAAA;KACZ;IAED,IAAM,WAAW,GAAG,mBAAY,OAAO,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,UAAG,CAAC,CAAC,IAAI,cAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAE,EAA5B,CAA4B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAE,CAAA;IAE5F,OAAO,CACL;QACE,uCAAK,KAAK,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC;;YACP,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gEAE9C;QACN,8BAAC,OAAO,IAAC,OAAO,EAAE,WAAW,GAAI,CAChC,CACJ,CAAA;AACH,CAAC;AAED,SAAS,aAAa,CAAC,KAA+B;IACpD,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAd,CAAc,CAAC,CAAA;IAE3D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACnB,OAAO,IAAI,CAAA;KACZ;IAED,IAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,2BAAoB,CAAC,CAAC,IAAI,CAAE,EAA5B,CAA4B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAEtF,OAAO,CACL;QACE,uCAAK,KAAK,EAAE,EAAC,YAAY,EAAE,EAAE,EAAC;;YACP,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gEAE9C;QACN,8BAAC,OAAO,IAAC,OAAO,EAAE,gBAAgB,GAAI,CACrC,CACJ,CAAA;AACH,CAAC;AAED,SAAS,OAAO,CAAC,EAA4B;QAA3B,OAAO,aAAA;IACjB,IAAA,KAAA,OAAuB,OAAO,CAAC,OAAO,CAAC,IAAA,EAAtC,MAAM,QAAA,EAAE,UAAU,QAAoB,CAAA;IAE7C,OAAO,CACL,uCACE,KAAK,EAAE;YACL,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,qBAAqB;YAC7B,OAAO,EAAE,MAAM;YACf,cAAc,EAAE,eAAe;SAChC;QAED;YACE,uCACE,KAAK,EAAE;oBACL,KAAK,EAAE,WAAW;oBAClB,SAAS,EAAE,MAAM;oBACjB,MAAM,EAAE,OAAO;oBACf,UAAU,EAAE,WAAW;oBACvB,OAAO,EAAE,MAAM;oBACf,UAAU,EAAE,QAAQ;iBACrB,IAEA,OAAO,CACJ,CACF;QACN;YACE,0CACE,IAAI,EAAC,QAAQ,EACb,KAAK,EAAE,EAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAC,EAC7E,KAAK,EAAC,mBAAmB,EACzB,OAAO,EAAE,UAAU,IAElB,MAAM,CAAC,CAAC,CAAC,CACR,8BAAC,6BAAa,IAAC,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,GAAI,CACzC,CAAC,CAAC,CAAC,CACF,8BAAC,6BAAa,IAAC,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,GAAI,CACzC,CACM,CACL,CACF,CACP,CAAA;AACH,CAAC;AAED,SAAS,OAAO,CAAC,OAAe;IACxB,IAAA,KAAA,OAAsB,IAAA,gBAAQ,EAAC,KAAK,CAAC,IAAA,EAApC,MAAM,QAAA,EAAE,SAAS,QAAmB,CAAA;IAE3C,IAAM,UAAU,GAAG,IAAA,mBAAW,EAAC;QAC7B,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,WAAW,EAAE;YAC9C,OAAO,IAAI,CAAA;SACZ;QAED,SAAS,CAAC,SAAS;aAChB,SAAS,CAAC,OAAO,CAAC;aAClB,IAAI,CAAC,cAAM,OAAA,SAAS,CAAC,IAAI,CAAC,EAAf,CAAe,CAAC;aAC3B,KAAK,CAAC,IAAI,CAAC,CAAA;QAEd,SAAS,CAAC,IAAI,CAAC,CAAA;QACf,IAAM,OAAO,GAAG,UAAU,CAAC,cAAM,OAAA,SAAS,CAAC,KAAK,CAAC,EAAhB,CAAgB,EAAE,IAAI,CAAC,CAAA;QACxD,OAAO,cAAM,OAAA,YAAY,CAAC,OAAO,CAAC,EAArB,CAAqB,CAAA;IACpC,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAA;IAExB,OAAO,CAAC,MAAM,EAAE,UAAU,CAAuC,CAAA;AACnE,CAAC"}
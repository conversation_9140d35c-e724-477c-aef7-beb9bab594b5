{"name": "@sanity/incompatible-plugin", "version": "1.0.5", "description": "Display an error dialog in Sanity Studio v2 when a v3 plugin has been installed.", "keywords": ["sanity", "sanity-plugin"], "homepage": "https://github.com/sanity-io/incompatible-plugin#readme", "bugs": {"url": "https://github.com/sanity-io/incompatible-plugin/issues"}, "repository": {"type": "git", "url": "**************:sanity-io/incompatible-plugin.git"}, "license": "MIT", "author": "Sanity.io <<EMAIL>>", "main": "./lib/index.js", "source": "./src/index.tsx", "types": "./lib/index.d.ts", "files": ["src", "lib"], "scripts": {"prebuild": "npm run clean", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> lib", "compile": "tsc --noEmit", "format": "prettier --ignore-path .giti<PERSON>re -w .", "lint": "eslint .", "prepare": "husky install", "prepublishOnly": "npm run build", "watch": "tsc -w"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@sanity/semantic-release-preset": "^5.0.0", "@types/react": "^18.0.10", "@types/react-dom": "^18.0.5", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-config-sanity": "^6.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-packagejson": "^2.3.0", "react": "^17.0.2", "react-dom": "^17.0.2", "rimraf": "^3.0.2", "semantic-release": "^24.2.0", "typescript": "5.0.4", "yalc": "^1.0.0-pre.53"}, "peerDependencies": {"react": "^16.9 || ^17 || ^18 || ^19", "react-dom": "^16.9 || ^17 || ^18 || ^19"}, "overrides": {"conventional-changelog-conventionalcommits": ">= 8.0.0"}}
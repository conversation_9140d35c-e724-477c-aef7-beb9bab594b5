import React, {forwardRef} from 'react'

/**
 * @public
 */
export const CheckmarkIcon = forwardRef(function CheckmarkIcon(
  props: React.SVGProps<SVGSVGElement>,
  ref: React.Ref<SVGSVGElement>
) {
  return (
    <svg
      data-sanity-icon="checkmark"
      width="1em"
      height="1em"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      ref={ref}
      {...props}
    >
      <path
        d="M5.5 11.5L10.5 16.5L19.5 7.60001"
        stroke="currentColor"
        strokeWidth={1.2}
        strokeLinejoin="round"
      />
    </svg>
  )
})
CheckmarkIcon.displayName = 'ForwardRef(CheckmarkIcon)'

{"version": 3, "file": "CodeMirrorProxy.js", "sources": ["../../src/codemirror/defaultCodeModes.ts", "../../src/codemirror/extensions/backwardsCompatibleTone.ts", "../../src/codemirror/extensions/highlightLineExtension.ts", "../../src/codemirror/extensions/theme.ts", "../../src/codemirror/extensions/useCodeMirrorTheme.ts", "../../src/codemirror/extensions/useFontSize.ts", "../../src/codemirror/CodeMirrorProxy.tsx"], "sourcesContent": ["import {StreamLanguage} from '@codemirror/language'\nimport type {Extension} from '@codemirror/state'\n\nexport interface CodeMode {\n  name: string\n  loader: ModeLoader\n}\nexport type ModeLoader = () => Promise<Extension | undefined> | Extension | undefined\n\nexport const defaultCodeModes: CodeMode[] = [\n  {\n    name: 'groq',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascriptLanguage}) => javascriptLanguage),\n  },\n  {\n    name: 'javascript',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) => javascript({jsx: false})),\n  },\n  {\n    name: 'jsx',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) => javascript({jsx: true})),\n  },\n  {\n    name: 'typescript',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) =>\n        javascript({jsx: false, typescript: true}),\n      ),\n  },\n  {\n    name: 'tsx',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) =>\n        javascript({jsx: true, typescript: true}),\n      ),\n  },\n  {name: 'php', loader: () => import('@codemirror/lang-php').then(({php}) => php())},\n  {name: 'sql', loader: () => import('@codemirror/lang-sql').then(({sql}) => sql())},\n  {\n    name: 'mysql',\n    loader: () => import('@codemirror/lang-sql').then(({sql, MySQL}) => sql({dialect: MySQL})),\n  },\n  {name: 'json', loader: () => import('@codemirror/lang-json').then(({json}) => json())},\n  {\n    name: 'markdown',\n    loader: () => import('@codemirror/lang-markdown').then(({markdown}) => markdown()),\n  },\n  {name: 'java', loader: () => import('@codemirror/lang-java').then(({java}) => java())},\n  {name: 'html', loader: () => import('@codemirror/lang-html').then(({html}) => html())},\n  {\n    name: 'csharp',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/clike').then(({csharp}) =>\n        StreamLanguage.define(csharp),\n      ),\n  },\n  {\n    name: 'sh',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/shell').then(({shell}) => StreamLanguage.define(shell)),\n  },\n  {\n    name: 'css',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/css').then(({css}) => StreamLanguage.define(css)),\n  },\n  {\n    name: 'scss',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/css').then(({css}) => StreamLanguage.define(css)),\n  },\n  {\n    name: 'sass',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/sass').then(({sass}) => StreamLanguage.define(sass)),\n  },\n  {\n    name: 'ruby',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/ruby').then(({ruby}) => StreamLanguage.define(ruby)),\n  },\n  {\n    name: 'python',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/python').then(({python}) =>\n        StreamLanguage.define(python),\n      ),\n  },\n  {\n    name: 'xml',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/xml').then(({xml}) => StreamLanguage.define(xml)),\n  },\n  {\n    name: 'yaml',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/yaml').then(({yaml}) => StreamLanguage.define(yaml)),\n  },\n  {\n    name: 'golang',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/go').then(({go}) => StreamLanguage.define(go)),\n  },\n  {name: 'text', loader: () => undefined},\n  {name: 'batch', loader: () => undefined},\n]\n", "import type {ThemeContextValue} from '@sanity/ui'\n\n/**\n * `@sanity/ui@v2.9` introduced two new tones; \"neutral\" and \"suggest\",\n * which maps to \"default\" and \"primary\" respectively in the old theme.\n * This function returns the \"backwards compatible\" tone value.\n *\n * @returns The tone value that is backwards compatible with the old theme.\n * @internal\n */\nexport function getBackwardsCompatibleTone(\n  themeCtx: ThemeContextValue,\n): Exclude<ThemeContextValue['tone'], 'neutral' | 'suggest'> {\n  if (themeCtx.tone !== 'neutral' && themeCtx.tone !== 'suggest') {\n    return themeCtx.tone\n  }\n\n  return themeCtx.tone === 'neutral' ? 'default' : 'primary'\n}\n", "/* eslint-disable no-param-reassign */\n\nimport {type Extension, StateEffect, StateField} from '@codemirror/state'\nimport {Decoration, type DecorationSet, EditorView, lineNumbers} from '@codemirror/view'\nimport type {ThemeContextValue} from '@sanity/ui'\nimport {rgba} from '@sanity/ui/theme'\n\nimport {getBackwardsCompatibleTone} from './backwardsCompatibleTone'\n\nconst highlightLineClass = 'cm-highlight-line'\n\nexport const addLineHighlight = StateEffect.define<number>()\nexport const removeLineHighlight = StateEffect.define<number>()\n\nexport const lineHighlightField = StateField.define({\n  create() {\n    return Decoration.none\n  },\n  update(lines, tr) {\n    lines = lines.map(tr.changes)\n    for (const e of tr.effects) {\n      if (e.is(addLineHighlight)) {\n        lines = lines.update({add: [lineHighlightMark.range(e.value)]})\n      }\n      if (e.is(removeLineHighlight)) {\n        lines = lines.update({\n          filter: (from) => {\n            // removeLineHighlight value is lineStart for the highlight, so keep other effects\n            return from !== e.value\n          },\n        })\n      }\n    }\n    return lines\n  },\n  toJSON(value, state) {\n    const highlightLines: number[] = []\n    const iter = value.iter()\n    while (iter.value) {\n      const lineNumber = state.doc.lineAt(iter.from).number\n      if (!highlightLines.includes(lineNumber)) {\n        highlightLines.push(lineNumber)\n      }\n      iter.next()\n    }\n    return highlightLines\n  },\n  fromJSON(value: number[], state) {\n    const lines = state.doc.lines\n    const highlights = value\n      .filter((line) => line <= lines) // one-indexed\n      .map((line) => lineHighlightMark.range(state.doc.line(line).from))\n    highlights.sort((a, b) => a.from - b.from)\n    try {\n      return Decoration.none.update({\n        add: highlights,\n      })\n    } catch (e) {\n      console.error(e)\n      return Decoration.none\n    }\n  },\n  provide: (f) => EditorView.decorations.from(f),\n})\n\nconst lineHighlightMark = Decoration.line({\n  class: highlightLineClass,\n})\n\nexport const highlightState: {\n  [prop: string]: StateField<DecorationSet>\n} = {\n  highlight: lineHighlightField,\n}\n\nexport interface HighlightLineConfig {\n  onHighlightChange?: (lines: number[]) => void\n  readOnly?: boolean\n  theme: ThemeContextValue\n}\n\nfunction createCodeMirrorTheme(options: {themeCtx: ThemeContextValue}) {\n  const {themeCtx} = options\n\n  const fallbackTone = getBackwardsCompatibleTone(themeCtx)\n\n  const dark = {color: themeCtx.theme.color.dark[fallbackTone]}\n  const light = {color: themeCtx.theme.color.light[fallbackTone]}\n\n  return EditorView.baseTheme({\n    '.cm-lineNumbers': {\n      cursor: 'default',\n    },\n    '.cm-line.cm-line': {\n      position: 'relative',\n    },\n\n    // need set background with pseudoelement so it does not render over selection color\n    [`.${highlightLineClass}::before`]: {\n      position: 'absolute',\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0,\n      zIndex: -3,\n      content: \"''\",\n      boxSizing: 'border-box',\n    },\n    [`&dark .${highlightLineClass}::before`]: {\n      background: rgba(dark.color.muted.caution.pressed.bg, 0.5),\n    },\n    [`&light .${highlightLineClass}::before`]: {\n      background: rgba(light.color.muted.caution.pressed.bg, 0.75),\n    },\n  })\n}\n\nexport const highlightLine = (config: HighlightLineConfig): Extension => {\n  const highlightTheme = createCodeMirrorTheme({themeCtx: config.theme})\n\n  return [\n    lineHighlightField,\n    config.readOnly\n      ? []\n      : lineNumbers({\n          domEventHandlers: {\n            mousedown: (editorView, lineInfo) => {\n              // Determine if the line for the clicked gutter line number has highlighted state or not\n              const line = editorView.state.doc.lineAt(lineInfo.from)\n              let isHighlighted = false\n              editorView.state\n                .field(lineHighlightField)\n                .between(line.from, line.to, (from, to, value) => {\n                  if (value) {\n                    isHighlighted = true\n                    return false // stop iteration\n                  }\n                  return undefined\n                })\n\n              if (isHighlighted) {\n                editorView.dispatch({effects: removeLineHighlight.of(line.from)})\n              } else {\n                editorView.dispatch({effects: addLineHighlight.of(line.from)})\n              }\n              if (config?.onHighlightChange) {\n                config.onHighlightChange(editorView.state.toJSON(highlightState).highlight)\n              }\n              return true\n            },\n          },\n        }),\n    highlightTheme,\n  ]\n}\n\n/**\n * Adds and removes highlights to the provided view using highlightLines\n * @param view\n * @param highlightLines\n */\nexport function setHighlightedLines(view: EditorView, highlightLines: number[]): void {\n  const doc = view.state.doc\n  const lines = doc.lines\n  //1-based line numbers\n  const allLineNumbers = Array.from({length: lines}, (x, i) => i + 1)\n  view.dispatch({\n    effects: allLineNumbers.map((lineNumber) => {\n      const line = doc.line(lineNumber)\n      if (highlightLines?.includes(lineNumber)) {\n        return addLineHighlight.of(line.from)\n      }\n      return removeLineHighlight.of(line.from)\n    }),\n  })\n}\n", "import type {Extension} from '@codemirror/state'\nimport {EditorView} from '@codemirror/view'\nimport {useRootTheme} from '@sanity/ui'\nimport {rgba} from '@sanity/ui/theme'\nimport {useMemo} from 'react'\n\nimport {getBackwardsCompatibleTone} from './backwardsCompatibleTone'\n\nexport function useThemeExtension(): Extension {\n  const themeCtx = useRootTheme()\n\n  return useMemo(() => {\n    const fallbackTone = getBackwardsCompatibleTone(themeCtx)\n    const dark = {color: themeCtx.theme.color.dark[fallbackTone]}\n    const light = {color: themeCtx.theme.color.light[fallbackTone]}\n\n    return EditorView.baseTheme({\n      '&.cm-editor': {\n        height: '100%',\n      },\n      '&.cm-editor.cm-focused': {\n        outline: 'none',\n      },\n\n      // Matching brackets\n      '&.cm-editor.cm-focused .cm-matchingBracket': {\n        backgroundColor: 'transparent',\n      },\n      '&.cm-editor.cm-focused .cm-nonmatchingBracket': {\n        backgroundColor: 'transparent',\n      },\n      '&dark.cm-editor.cm-focused .cm-matchingBracket': {\n        outline: `1px solid ${dark.color.base.border}`,\n      },\n      '&dark.cm-editor.cm-focused .cm-nonmatchingBracket': {\n        outline: `1px solid ${dark.color.base.border}`,\n      },\n      '&light.cm-editor.cm-focused .cm-matchingBracket': {\n        outline: `1px solid ${light.color.base.border}`,\n      },\n      '&light.cm-editor.cm-focused .cm-nonmatchingBracket': {\n        outline: `1px solid ${light.color.base.border}`,\n      },\n\n      // Size and padding of gutter\n      '& .cm-lineNumbers .cm-gutterElement': {\n        minWidth: `32px !important`,\n        padding: `0 8px !important`,\n      },\n      '& .cm-gutter.cm-foldGutter': {\n        width: `0px !important`,\n      },\n\n      // Color of gutter\n      '&dark .cm-gutters': {\n        color: `${rgba(dark.color.card.enabled.code.fg, 0.5)} !important`,\n        borderRight: `1px solid ${rgba(dark.color.base.border, 0.5)}`,\n      },\n      '&light .cm-gutters': {\n        color: `${rgba(light.color.card.enabled.code.fg, 0.5)} !important`,\n        borderRight: `1px solid ${rgba(light.color.base.border, 0.5)}`,\n      },\n    })\n  }, [themeCtx])\n}\n", "import type {Extension} from '@codemirror/state'\nimport {tags as t} from '@lezer/highlight'\nimport {useTheme} from '@sanity/ui'\nimport {rgba} from '@sanity/ui/theme'\nimport {createTheme} from '@uiw/codemirror-themes'\nimport {useMemo} from 'react'\n\nexport function useCodeMirrorTheme(): Extension {\n  const theme = useTheme()\n\n  return useMemo(() => {\n    const {code: codeFont} = theme.sanity.fonts\n    const {base, card, dark, syntax} = theme.sanity.color\n\n    return createTheme({\n      theme: dark ? 'dark' : 'light',\n      settings: {\n        background: card.enabled.bg,\n        foreground: card.enabled.code.fg,\n        lineHighlight: card.enabled.bg,\n        fontFamily: codeFont.family,\n        caret: base.focusRing,\n        selection: rgba(base.focusRing, 0.2),\n        selectionMatch: rgba(base.focusRing, 0.4),\n        gutterBackground: card.disabled.bg,\n        gutterForeground: card.disabled.code.fg,\n        gutterActiveForeground: card.enabled.fg,\n      },\n      styles: [\n        {\n          tag: [t.heading, t.heading2, t.heading3, t.heading4, t.heading5, t.heading6],\n          color: card.enabled.fg,\n        },\n        {tag: t.angleBracket, color: card.enabled.code.fg},\n        {tag: t.atom, color: syntax.keyword},\n        {tag: t.attributeName, color: syntax.attrName},\n        {tag: t.bool, color: syntax.boolean},\n        {tag: t.bracket, color: card.enabled.code.fg},\n        {tag: t.className, color: syntax.className},\n        {tag: t.comment, color: syntax.comment},\n        {tag: t.definition(t.typeName), color: syntax.function},\n        {\n          tag: [\n            t.definition(t.variableName),\n            t.function(t.variableName),\n            t.className,\n            t.attributeName,\n          ],\n          color: syntax.function,\n        },\n        {tag: [t.function(t.propertyName), t.propertyName], color: syntax.function},\n        {tag: t.keyword, color: syntax.keyword},\n        {tag: t.null, color: syntax.number},\n        {tag: t.number, color: syntax.number},\n        {tag: t.meta, color: card.enabled.code.fg},\n        {tag: t.operator, color: syntax.operator},\n        {tag: t.propertyName, color: syntax.property},\n        {tag: [t.string, t.special(t.brace)], color: syntax.string},\n        {tag: t.tagName, color: syntax.className},\n        {tag: t.typeName, color: syntax.keyword},\n      ],\n    })\n  }, [theme])\n}\n", "import type {Extension} from '@codemirror/state'\nimport {EditorView} from '@codemirror/view'\nimport {rem, useTheme} from '@sanity/ui'\nimport {useMemo} from 'react'\n\nexport function useFontSizeExtension(props: {fontSize: number}): Extension {\n  const {fontSize: fontSizeProp} = props\n  const theme = useTheme()\n\n  return useMemo(() => {\n    const {code: codeFont} = theme.sanity.fonts\n    const {fontSize, lineHeight} = codeFont.sizes[fontSizeProp] || codeFont.sizes[2]\n\n    return EditorView.baseTheme({\n      '&': {\n        fontSize: rem(fontSize),\n      },\n\n      '& .cm-scroller': {\n        lineHeight: `${lineHeight / fontSize} !important`,\n      },\n    })\n  }, [fontSizeProp, theme])\n}\n", "import type {Extension} from '@codemirror/state'\nimport {EditorView} from '@codemirror/view'\nimport {useRootTheme} from '@sanity/ui'\nimport CodeMirror, {type ReactCodeMirrorProps, type ReactCodeMirrorRef} from '@uiw/react-codemirror'\nimport {forwardRef, useCallback, useContext, useEffect, useMemo, useState} from 'react'\n\nimport {CodeInputConfigContext} from './CodeModeContext'\nimport {defaultCodeModes} from './defaultCodeModes'\nimport {\n  highlightLine,\n  highlightState,\n  setHighlightedLines,\n} from './extensions/highlightLineExtension'\nimport {useThemeExtension} from './extensions/theme'\nimport {useCodeMirrorTheme} from './extensions/useCodeMirrorTheme'\nimport {useFontSizeExtension} from './extensions/useFontSize'\n\nexport interface CodeMirrorProps extends ReactCodeMirrorProps {\n  highlightLines?: number[]\n  languageMode?: string\n  onHighlightChange?: (lines: number[]) => void\n}\n\n/**\n * CodeMirrorProxy is a wrapper component around CodeMirror that we lazy load to reduce initial bundle size.\n *\n * It is also responsible for integrating any CodeMirror extensions.\n */\nconst CodeMirrorProxy = forwardRef<ReactCodeMirrorRef, CodeMirrorProps>(\n  function CodeMirrorProxy(props, ref) {\n    const {\n      basicSetup: basicSetupProp,\n      highlightLines,\n      languageMode,\n      onHighlightChange,\n      readOnly,\n      value,\n      ...codeMirrorProps\n    } = props\n\n    const themeCtx = useRootTheme()\n    const codeMirrorTheme = useCodeMirrorTheme()\n    const [editorView, setEditorView] = useState<EditorView | undefined>(undefined)\n\n    // Resolve extensions\n    const themeExtension = useThemeExtension()\n    const fontSizeExtension = useFontSizeExtension({fontSize: 1})\n    const languageExtension = useLanguageExtension(languageMode)\n    const highlightLineExtension = useMemo(\n      () =>\n        highlightLine({\n          onHighlightChange,\n          readOnly,\n          theme: themeCtx,\n        }),\n      [onHighlightChange, readOnly, themeCtx],\n    )\n\n    const extensions = useMemo(() => {\n      const baseExtensions = [\n        themeExtension,\n        fontSizeExtension,\n        highlightLineExtension,\n        EditorView.lineWrapping,\n      ]\n      if (languageExtension) {\n        return [...baseExtensions, languageExtension]\n      }\n      return baseExtensions\n    }, [fontSizeExtension, highlightLineExtension, languageExtension, themeExtension])\n\n    useEffect(() => {\n      if (editorView) {\n        setHighlightedLines(editorView, highlightLines ?? [])\n      }\n    }, [editorView, highlightLines, value])\n\n    const initialState = useMemo(() => {\n      return {\n        json: {\n          doc: value ?? '',\n          selection: {\n            main: 0,\n            ranges: [{anchor: 0, head: 0}],\n          },\n          highlight: highlightLines ?? [],\n        },\n        fields: highlightState,\n      }\n      // only need to calculate this on initial render\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    const handleCreateEditor = useCallback((view: EditorView) => {\n      setEditorView(view)\n    }, [])\n\n    const basicSetup = useMemo(\n      () =>\n        basicSetupProp ?? {\n          highlightActiveLine: false,\n        },\n      [basicSetupProp],\n    )\n\n    return (\n      <CodeMirror\n        {...codeMirrorProps}\n        value={value}\n        ref={ref}\n        extensions={extensions}\n        theme={codeMirrorTheme}\n        onCreateEditor={handleCreateEditor}\n        initialState={initialState}\n        basicSetup={basicSetup}\n      />\n    )\n  },\n)\n\nfunction useLanguageExtension(mode?: string) {\n  const codeConfig = useContext(CodeInputConfigContext)\n\n  const [languageExtension, setLanguageExtension] = useState<Extension | undefined>()\n\n  useEffect(() => {\n    const customModes = codeConfig?.codeModes ?? []\n    const modes = [...customModes, ...defaultCodeModes]\n\n    const codeMode = modes.find((m) => m.name === mode)\n    if (!codeMode?.loader) {\n      console.warn(\n        `Found no codeMode for language mode ${mode}, syntax highlighting will be disabled.`,\n      )\n    }\n    let active = true\n    Promise.resolve(codeMode?.loader())\n      .then((extension) => {\n        if (active) {\n          setLanguageExtension(extension)\n        }\n      })\n      .catch((e) => {\n        console.error(`Failed to load language mode ${mode}`, e)\n        if (active) {\n          setLanguageExtension(undefined)\n        }\n      })\n    return () => {\n      active = false\n    }\n  }, [mode, codeConfig])\n\n  return languageExtension\n}\n\nexport default CodeMirrorProxy\n"], "names": ["t"], "mappings": ";;;;;;;;;;;AASO,MAAM,mBAA+B;AAAA,EAC1C;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,6BAA6B,EAAE,KAAK,CAAC,EAAC,mBAAkB,MAAM,kBAAkB;AAAA,EAC3F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,6BAA6B,EAAE,KAAK,CAAC,EAAC,iBAAgB,WAAW,EAAC,KAAK,GAAA,CAAM,CAAC;AAAA,EACzF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,6BAA6B,EAAE,KAAK,CAAC,EAAC,iBAAgB,WAAW,EAAC,KAAK,GAAA,CAAK,CAAC;AAAA,EACxF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,6BAA6B,EAAE;AAAA,MAAK,CAAC,EAAC,WAC3C,MAAA,WAAW,EAAC,KAAK,IAAO,YAAY,GAAK,CAAA;AAAA,IAAA;AAAA,EAE/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,6BAA6B,EAAE;AAAA,MAAK,CAAC,EAAC,WAC3C,MAAA,WAAW,EAAC,KAAK,IAAM,YAAY,GAAK,CAAA;AAAA,IAAA;AAAA,EAE9C;AAAA,EACA,EAAC,MAAM,OAAO,QAAQ,MAAM,OAAO,sBAAsB,EAAE,KAAK,CAAC,EAAC,UAAS,IAAK,CAAA,EAAC;AAAA,EACjF,EAAC,MAAM,OAAO,QAAQ,MAAM,OAAO,sBAAsB,EAAE,KAAK,CAAC,EAAC,UAAS,IAAK,CAAA,EAAC;AAAA,EACjF;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MAAM,OAAO,sBAAsB,EAAE,KAAK,CAAC,EAAC,KAAK,YAAW,IAAI,EAAC,SAAS,MAAA,CAAM,CAAC;AAAA,EAC3F;AAAA,EACA,EAAC,MAAM,QAAQ,QAAQ,MAAM,OAAO,uBAAuB,EAAE,KAAK,CAAC,EAAC,WAAU,KAAM,CAAA,EAAC;AAAA,EACrF;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MAAM,OAAO,2BAA2B,EAAE,KAAK,CAAC,EAAC,SAAc,MAAA,SAAU,CAAA;AAAA,EACnF;AAAA,EACA,EAAC,MAAM,QAAQ,QAAQ,MAAM,OAAO,uBAAuB,EAAE,KAAK,CAAC,EAAC,WAAU,KAAM,CAAA,EAAC;AAAA,EACrF,EAAC,MAAM,QAAQ,QAAQ,MAAM,OAAO,uBAAuB,EAAE,KAAK,CAAC,EAAC,WAAU,KAAM,CAAA,EAAC;AAAA,EACrF;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,qCAAqC,EAAE;AAAA,MAAK,CAAC,EAAC,OAAA,MACnD,eAAe,OAAO,MAAM;AAAA,IAAA;AAAA,EAElC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,qCAAqC,EAAE,KAAK,CAAC,EAAC,YAAW,eAAe,OAAO,KAAK,CAAC;AAAA,EAChG;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,mCAAmC,EAAE,KAAK,CAAC,EAAC,UAAS,eAAe,OAAO,GAAG,CAAC;AAAA,EAC1F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,mCAAmC,EAAE,KAAK,CAAC,EAAC,UAAS,eAAe,OAAO,GAAG,CAAC;AAAA,EAC1F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,oCAAoC,EAAE,KAAK,CAAC,EAAC,WAAU,eAAe,OAAO,IAAI,CAAC;AAAA,EAC7F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,oCAAoC,EAAE,KAAK,CAAC,EAAC,WAAU,eAAe,OAAO,IAAI,CAAC;AAAA,EAC7F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,sCAAsC,EAAE;AAAA,MAAK,CAAC,EAAC,OAAA,MACpD,eAAe,OAAO,MAAM;AAAA,IAAA;AAAA,EAElC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,mCAAmC,EAAE,KAAK,CAAC,EAAC,UAAS,eAAe,OAAO,GAAG,CAAC;AAAA,EAC1F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,oCAAoC,EAAE,KAAK,CAAC,EAAC,WAAU,eAAe,OAAO,IAAI,CAAC;AAAA,EAC7F;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ,MACN,OAAO,kCAAkC,EAAE,KAAK,CAAC,EAAC,SAAQ,eAAe,OAAO,EAAE,CAAC;AAAA,EACvF;AAAA,EACA,EAAC,MAAM,QAAQ,QAAQ,MAAG;AAAA,EAAA,EAAY;AAAA,EACtC,EAAC,MAAM,SAAS,QAAQ,MAAG;AAAA,EAAY,EAAA;AACzC;AClGO,SAAS,2BACd,UAC2D;AACvD,SAAA,SAAS,SAAS,aAAa,SAAS,SAAS,YAC5C,SAAS,OAGX,SAAS,SAAS,YAAY,YAAY;AACnD;ACTA,MAAM,qBAAqB,qBAEd,mBAAmB,YAAY,OAAA,GAC/B,sBAAsB,YAAY,UAElC,qBAAqB,WAAW,OAAO;AAAA,EAClD,SAAS;AACP,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,IAAI;AACR,YAAA,MAAM,IAAI,GAAG,OAAO;AAC5B,eAAW,KAAK,GAAG;AACb,QAAE,GAAG,gBAAgB,MACvB,QAAQ,MAAM,OAAO,EAAC,KAAK,CAAC,kBAAkB,MAAM,EAAE,KAAK,CAAC,GAAE,IAE5D,EAAE,GAAG,mBAAmB,MAC1B,QAAQ,MAAM,OAAO;AAAA,QACnB,QAAQ,CAAC,SAEA,SAAS,EAAE;AAAA,MAAA,CAErB;AAGE,WAAA;AAAA,EACT;AAAA,EACA,OAAO,OAAO,OAAO;AACnB,UAAM,iBAA2B,CAC3B,GAAA,OAAO,MAAM,KAAK;AACxB,WAAO,KAAK,SAAO;AACjB,YAAM,aAAa,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE;AAC1C,qBAAe,SAAS,UAAU,KACrC,eAAe,KAAK,UAAU,GAEhC,KAAK,KAAK;AAAA,IAAA;AAEL,WAAA;AAAA,EACT;AAAA,EACA,SAAS,OAAiB,OAAO;AACzB,UAAA,QAAQ,MAAM,IAAI,OAClB,aAAa,MAChB,OAAO,CAAC,SAAS,QAAQ,KAAK,EAC9B,IAAI,CAAC,SAAS,kBAAkB,MAAM,MAAM,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC;AACnE,eAAW,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AACrC,QAAA;AACK,aAAA,WAAW,KAAK,OAAO;AAAA,QAC5B,KAAK;AAAA,MAAA,CACN;AAAA,aACM,GAAG;AACF,aAAA,QAAA,MAAM,CAAC,GACR,WAAW;AAAA,IAAA;AAAA,EAEtB;AAAA,EACA,SAAS,CAAC,MAAM,WAAW,YAAY,KAAK,CAAC;AAC/C,CAAC,GAEK,oBAAoB,WAAW,KAAK;AAAA,EACxC,OAAO;AACT,CAAC,GAEY,iBAET;AAAA,EACF,WAAW;AACb;AAQA,SAAS,sBAAsB,SAAwC;AAC/D,QAAA,EAAC,SAAQ,IAAI,SAEb,eAAe,2BAA2B,QAAQ,GAElD,OAAO,EAAC,OAAO,SAAS,MAAM,MAAM,KAAK,YAAY,KACrD,QAAQ,EAAC,OAAO,SAAS,MAAM,MAAM,MAAM,YAAY,EAAC;AAE9D,SAAO,WAAW,UAAU;AAAA,IAC1B,mBAAmB;AAAA,MACjB,QAAQ;AAAA,IACV;AAAA,IACA,oBAAoB;AAAA,MAClB,UAAU;AAAA,IACZ;AAAA;AAAA,IAGA,CAAC,IAAI,kBAAkB,UAAU,GAAG;AAAA,MAClC,UAAU;AAAA,MACV,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAAA,IACA,CAAC,UAAU,kBAAkB,UAAU,GAAG;AAAA,MACxC,YAAY,KAAK,KAAK,MAAM,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAAA,IAC3D;AAAA,IACA,CAAC,WAAW,kBAAkB,UAAU,GAAG;AAAA,MACzC,YAAY,KAAK,MAAM,MAAM,MAAM,QAAQ,QAAQ,IAAI,IAAI;AAAA,IAAA;AAAA,EAC7D,CACD;AACH;AAEa,MAAA,gBAAgB,CAAC,WAA2C;AACvE,QAAM,iBAAiB,sBAAsB,EAAC,UAAU,OAAO,OAAM;AAE9D,SAAA;AAAA,IACL;AAAA,IACA,OAAO,WACH,CAAC,IACD,YAAY;AAAA,MACV,kBAAkB;AAAA,QAChB,WAAW,CAAC,YAAY,aAAa;AAEnC,gBAAM,OAAO,WAAW,MAAM,IAAI,OAAO,SAAS,IAAI;AACtD,cAAI,gBAAgB;AACpB,iBAAA,WAAW,MACR,MAAM,kBAAkB,EACxB,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,UAAU;AAC5C,gBAAA;AACF,qBAAA,gBAAgB,IACT;AAAA,UAAA,CAGV,GAEC,gBACF,WAAW,SAAS,EAAC,SAAS,oBAAoB,GAAG,KAAK,IAAI,EAAE,CAAA,IAEhE,WAAW,SAAS,EAAC,SAAS,iBAAiB,GAAG,KAAK,IAAI,EAAC,CAAC,GAE3D,UAAQ,QAAA,OAAA,qBACV,OAAO,kBAAkB,WAAW,MAAM,OAAO,cAAc,EAAE,SAAS,GAErE;AAAA,QAAA;AAAA,MACT;AAAA,IACF,CACD;AAAA,IACL;AAAA,EACF;AACF;AAOgB,SAAA,oBAAoB,MAAkB,gBAAgC;AACpF,QAAM,MAAM,KAAK,MAAM,KACjB,QAAQ,IAAI,OAEZ,iBAAiB,MAAM,KAAK,EAAC,QAAQ,MAAK,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC;AAClE,OAAK,SAAS;AAAA,IACZ,SAAS,eAAe,IAAI,CAAC,eAAe;AACpC,YAAA,OAAO,IAAI,KAAK,UAAU;AAC5B,aAAA,kBAAA,QAAA,eAAgB,SAAS,UAAA,IACpB,iBAAiB,GAAG,KAAK,IAAI,IAE/B,oBAAoB,GAAG,KAAK,IAAI;AAAA,IACxC,CAAA;AAAA,EAAA,CACF;AACH;ACvKO,SAAS,oBAA+B;AAC7C,QAAM,WAAW,aAAa;AAE9B,SAAO,QAAQ,MAAM;AACb,UAAA,eAAe,2BAA2B,QAAQ,GAClD,OAAO,EAAC,OAAO,SAAS,MAAM,MAAM,KAAK,YAAY,EAAC,GACtD,QAAQ,EAAC,OAAO,SAAS,MAAM,MAAM,MAAM,YAAY,EAAC;AAE9D,WAAO,WAAW,UAAU;AAAA,MAC1B,eAAe;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,MACA,0BAA0B;AAAA,QACxB,SAAS;AAAA,MACX;AAAA;AAAA,MAGA,8CAA8C;AAAA,QAC5C,iBAAiB;AAAA,MACnB;AAAA,MACA,iDAAiD;AAAA,QAC/C,iBAAiB;AAAA,MACnB;AAAA,MACA,kDAAkD;AAAA,QAChD,SAAS,aAAa,KAAK,MAAM,KAAK,MAAM;AAAA,MAC9C;AAAA,MACA,qDAAqD;AAAA,QACnD,SAAS,aAAa,KAAK,MAAM,KAAK,MAAM;AAAA,MAC9C;AAAA,MACA,mDAAmD;AAAA,QACjD,SAAS,aAAa,MAAM,MAAM,KAAK,MAAM;AAAA,MAC/C;AAAA,MACA,sDAAsD;AAAA,QACpD,SAAS,aAAa,MAAM,MAAM,KAAK,MAAM;AAAA,MAC/C;AAAA;AAAA,MAGA,uCAAuC;AAAA,QACrC,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,8BAA8B;AAAA,QAC5B,OAAO;AAAA,MACT;AAAA;AAAA,MAGA,qBAAqB;AAAA,QACnB,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,CAAC;AAAA,QACpD,aAAa,aAAa,KAAK,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,MAC7D;AAAA,MACA,sBAAsB;AAAA,QACpB,OAAO,GAAG,KAAK,MAAM,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,CAAC;AAAA,QACrD,aAAa,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,MAAA;AAAA,IAC9D,CACD;AAAA,EAAA,GACA,CAAC,QAAQ,CAAC;AACf;ACzDO,SAAS,qBAAgC;AAC9C,QAAM,QAAQ,SAAS;AAEvB,SAAO,QAAQ,MAAM;AACnB,UAAM,EAAC,MAAM,SAAQ,IAAI,MAAM,OAAO,OAChC,EAAC,MAAM,MAAM,MAAM,OAAM,IAAI,MAAM,OAAO;AAEhD,WAAO,YAAY;AAAA,MACjB,OAAO,OAAO,SAAS;AAAA,MACvB,UAAU;AAAA,QACR,YAAY,KAAK,QAAQ;AAAA,QACzB,YAAY,KAAK,QAAQ,KAAK;AAAA,QAC9B,eAAe,KAAK,QAAQ;AAAA,QAC5B,YAAY,SAAS;AAAA,QACrB,OAAO,KAAK;AAAA,QACZ,WAAW,KAAK,KAAK,WAAW,GAAG;AAAA,QACnC,gBAAgB,KAAK,KAAK,WAAW,GAAG;AAAA,QACxC,kBAAkB,KAAK,SAAS;AAAA,QAChC,kBAAkB,KAAK,SAAS,KAAK;AAAA,QACrC,wBAAwB,KAAK,QAAQ;AAAA,MACvC;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,UACE,KAAK,CAACA,KAAE,SAASA,KAAE,UAAUA,KAAE,UAAUA,KAAE,UAAUA,KAAE,UAAUA,KAAE,QAAQ;AAAA,UAC3E,OAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QACA,EAAC,KAAKA,KAAE,cAAc,OAAO,KAAK,QAAQ,KAAK,GAAE;AAAA,QACjD,EAAC,KAAKA,KAAE,MAAM,OAAO,OAAO,QAAO;AAAA,QACnC,EAAC,KAAKA,KAAE,eAAe,OAAO,OAAO,SAAQ;AAAA,QAC7C,EAAC,KAAKA,KAAE,MAAM,OAAO,OAAO,QAAO;AAAA,QACnC,EAAC,KAAKA,KAAE,SAAS,OAAO,KAAK,QAAQ,KAAK,GAAE;AAAA,QAC5C,EAAC,KAAKA,KAAE,WAAW,OAAO,OAAO,UAAS;AAAA,QAC1C,EAAC,KAAKA,KAAE,SAAS,OAAO,OAAO,QAAO;AAAA,QACtC,EAAC,KAAKA,KAAE,WAAWA,KAAE,QAAQ,GAAG,OAAO,OAAO,SAAQ;AAAA,QACtD;AAAA,UACE,KAAK;AAAA,YACHA,KAAE,WAAWA,KAAE,YAAY;AAAA,YAC3BA,KAAE,SAASA,KAAE,YAAY;AAAA,YACzBA,KAAE;AAAA,YACFA,KAAE;AAAA,UACJ;AAAA,UACA,OAAO,OAAO;AAAA,QAChB;AAAA,QACA,EAAC,KAAK,CAACA,KAAE,SAASA,KAAE,YAAY,GAAGA,KAAE,YAAY,GAAG,OAAO,OAAO,SAAQ;AAAA,QAC1E,EAAC,KAAKA,KAAE,SAAS,OAAO,OAAO,QAAO;AAAA,QACtC,EAAC,KAAKA,KAAE,MAAM,OAAO,OAAO,OAAM;AAAA,QAClC,EAAC,KAAKA,KAAE,QAAQ,OAAO,OAAO,OAAM;AAAA,QACpC,EAAC,KAAKA,KAAE,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAE;AAAA,QACzC,EAAC,KAAKA,KAAE,UAAU,OAAO,OAAO,SAAQ;AAAA,QACxC,EAAC,KAAKA,KAAE,cAAc,OAAO,OAAO,SAAQ;AAAA,QAC5C,EAAC,KAAK,CAACA,KAAE,QAAQA,KAAE,QAAQA,KAAE,KAAK,CAAC,GAAG,OAAO,OAAO,OAAM;AAAA,QAC1D,EAAC,KAAKA,KAAE,SAAS,OAAO,OAAO,UAAS;AAAA,QACxC,EAAC,KAAKA,KAAE,UAAU,OAAO,OAAO,QAAO;AAAA,MAAA;AAAA,IACzC,CACD;AAAA,EAAA,GACA,CAAC,KAAK,CAAC;AACZ;AC1DO,SAAS,qBAAqB,OAAsC;AACzE,QAAM,EAAC,UAAU,aAAA,IAAgB,OAC3B,QAAQ,SAAS;AAEvB,SAAO,QAAQ,MAAM;AACnB,UAAM,EAAC,MAAM,SAAA,IAAY,MAAM,OAAO,OAChC,EAAC,UAAU,eAAc,SAAS,MAAM,YAAY,KAAK,SAAS,MAAM,CAAC;AAE/E,WAAO,WAAW,UAAU;AAAA,MAC1B,KAAK;AAAA,QACH,UAAU,IAAI,QAAQ;AAAA,MACxB;AAAA,MAEA,kBAAkB;AAAA,QAChB,YAAY,GAAG,aAAa,QAAQ;AAAA,MAAA;AAAA,IACtC,CACD;AAAA,EAAA,GACA,CAAC,cAAc,KAAK,CAAC;AAC1B;;;;;;;;;;;;;;;;;ACKA,MAAM,kBAAkB;AAAA,EACtB,SAAyB,OAAO,KAAK;AACnC,UAQI,KAPF,OAAA;AAAA,MAAY,YAAA;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IApCN,IAsCQ,IADC,kBAAA,UACD,IADC;AAAA,MANH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAII,CAAA,GAAA,WAAW,aAAa,GACxB,kBAAkB,mBAAA,GAClB,CAAC,YAAY,aAAa,IAAI,SAAiC,MAAS,GAGxE,iBAAiB,qBACjB,oBAAoB,qBAAqB,EAAC,UAAU,GAAE,GACtD,oBAAoB,qBAAqB,YAAY,GACrD,yBAAyB;AAAA,MAC7B,MACE,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA,OAAO;AAAA,MAAA,CACR;AAAA,MACH,CAAC,mBAAmB,UAAU,QAAQ;AAAA,IAAA,GAGlC,aAAa,QAAQ,MAAM;AAC/B,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AACA,aAAI,oBACK,CAAC,GAAG,gBAAgB,iBAAiB,IAEvC;AAAA,OACN,CAAC,mBAAmB,wBAAwB,mBAAmB,cAAc,CAAC;AAEjF,cAAU,MAAM;AACV,oBACF,oBAAoB,YAAY,kBAAA,OAAA,iBAAkB,EAAE;AAAA,IAErD,GAAA,CAAC,YAAY,gBAAgB,KAAK,CAAC;AAEhC,UAAA,eAAe,QAAQ,OACpB;AAAA,MACL,MAAM;AAAA,QACJ,KAAK,SAAS,OAAA,QAAA;AAAA,QACd,WAAW;AAAA,UACT,MAAM;AAAA,UACN,QAAQ,CAAC,EAAC,QAAQ,GAAG,MAAM,EAAE,CAAA;AAAA,QAC/B;AAAA,QACA,WAAW,0CAAkB,CAAA;AAAA,MAC/B;AAAA,MACA,QAAQ;AAAA,QAIT,CAAA,CAAE,GAEC,qBAAqB,YAAY,CAAC,SAAqB;AAC3D,oBAAc,IAAI;AAAA,IAAA,GACjB,CAAE,CAAA,GAEC,aAAa;AAAA,MACjB,MACE,kBAAkB,OAAA,iBAAA;AAAA,QAChB,qBAAqB;AAAA,MACvB;AAAA,MACF,CAAC,cAAc;AAAA,IACjB;AAGE,WAAA;AAAA,MAAC;AAAA,MAAA,cAAA,eAAA,IACK,eADL,GAAA;AAAA,QAEC;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,MAAA,CAAA;AAAA,IACF;AAAA,EAAA;AAGN;AAEA,SAAS,qBAAqB,MAAe;AACrC,QAAA,aAAa,WAAW,sBAAsB,GAE9C,CAAC,mBAAmB,oBAAoB,IAAI,SAAgC;AAElF,SAAA,UAAU,MAAM;AA7HlB,QAAA;AAiII,UAAM,WAFQ,CAAC,IADK,KAAY,cAAA,OAAA,SAAA,WAAA,cAAZ,YAAyB,CAAA,GACd,GAAG,gBAAgB,EAE3B,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI;AAC7C,gBAAA,QAAA,SAAU,UACb,QAAQ;AAAA,MACN,uCAAuC,IAAI;AAAA,IAC7C;AAEF,QAAI,SAAS;AACb,WAAA,QAAQ,QAAQ,YAAU,OAAA,SAAA,SAAA,OAAQ,CAAA,EAC/B,KAAK,CAAC,cAAc;AACf,gBACF,qBAAqB,SAAS;AAAA,IAAA,CAEjC,EACA,MAAM,CAAC,MAAM;AACJ,cAAA,MAAM,gCAAgC,IAAI,IAAI,CAAC,GACnD,UACF,qBAAqB,MAAS;AAAA,IAEjC,CAAA,GACI,MAAM;AACF,eAAA;AAAA,IACX;AAAA,EACC,GAAA,CAAC,MAAM,UAAU,CAAC,GAEd;AACT;"}
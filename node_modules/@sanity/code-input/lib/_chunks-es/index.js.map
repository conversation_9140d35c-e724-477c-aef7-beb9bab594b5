{"version": 3, "file": "index.js", "sources": ["../../src/codemirror/useCodeMirror.tsx", "../../src/config.ts", "../../src/codemirror/useLanguageMode.tsx", "../../src/LanguageInput.tsx", "../../src/LanguageField.tsx", "../../src/ui/focusRingStyle.ts", "../../src/useFieldMember.ts", "../../src/CodeInput.tsx", "../../src/getMedia.tsx", "../../src/PreviewCode.tsx", "../../src/schema.tsx", "../../src/codemirror/CodeModeContext.tsx", "../../src/plugin.tsx"], "sourcesContent": ["import {lazy, useEffect, useState} from 'react'\n\nexport const CodeMirrorProxy = lazy(() => import('./CodeMirrorProxy'))\n\nexport function useCodeMirror() {\n  const [mounted, setMounted] = useState(false)\n  useEffect(() => {\n    requestAnimationFrame(() => setMounted(true))\n  }, [])\n\n  return mounted ? CodeMirrorProxy : null\n}\n", "import {CodeInputLanguage} from './types'\n\n// NOTE: MAKE SURE THESE ALIGN WITH CODE MODES IN ./codemirror/defaultCodeModes.ts\nexport const SUPPORTED_LANGUAGES: CodeInputLanguage[] = [\n  {title: 'Batch file', value: 'batchfile'},\n  {title: 'C#', value: 'csharp'},\n  {title: 'CSS', value: 'css'},\n  {title: 'Go', value: 'golang'},\n  {title: 'GROQ', value: 'groq'},\n  {title: 'HTML', value: 'html'},\n  {title: 'Java', value: 'java'},\n  {title: 'JavaScript', value: 'javascript'},\n  {title: 'JSON', value: 'json'},\n  {title: 'JSX', value: 'jsx'},\n  {title: 'Markdown', value: 'markdown'},\n  {title: 'MySQL', value: 'mysql'},\n  {title: 'PHP', value: 'php'},\n  {title: 'Plain text', value: 'text'},\n  {title: 'Python', value: 'python'},\n  {title: 'Ruby', value: 'ruby'},\n  {title: 'SASS', value: 'sass'},\n  {title: 'SCSS', value: 'scss'},\n  {title: 'sh', value: 'sh'},\n  {title: 'TSX', value: 'tsx'},\n  {title: 'TypeScript', value: 'typescript'},\n  {title: 'XML', value: 'xml'},\n  {title: 'YAML', value: 'yaml'},\n]\n\nexport const LANGUAGE_ALIASES: Record<string, string | undefined> = {js: 'javascript'}\n\nexport const PATH_LANGUAGE = ['language']\nexport const PATH_CODE = ['code']\nexport const PATH_FILENAME = ['filename']\n", "import {useMemo} from 'react'\n\nimport {LANGUAGE_ALIASES, SUPPORTED_LANGUAGES} from '../config'\nimport type {CodeInputLanguage, CodeInputValue, CodeSchemaType} from '../types'\n\nexport const defaultLanguageMode = 'text'\n\nexport function useLanguageMode(\n  schemaType: CodeSchemaType,\n  value?: CodeInputValue,\n): {\n  language: string\n  languageMode: string\n  languages: CodeInputLanguage[]\n} {\n  const languages = useLanguageAlternatives(schemaType)\n  const fixedLanguage = schemaType.options?.language\n  const language = value?.language ?? fixedLanguage ?? defaultLanguageMode\n\n  // the language config from the schema\n  const configured = languages.find((entry) => entry.value === language)\n  const languageMode = configured?.mode ?? resolveAliasedLanguage(language) ?? defaultLanguageMode\n\n  return {language, languageMode, languages}\n}\n\nfunction resolveAliasedLanguage(lang?: string) {\n  return (lang && LANGUAGE_ALIASES[lang]) ?? lang\n}\n\nfunction useLanguageAlternatives(type: CodeSchemaType) {\n  return useMemo((): CodeInputLanguage[] => {\n    const languageAlternatives = type.options?.languageAlternatives\n    if (!languageAlternatives) {\n      return SUPPORTED_LANGUAGES\n    }\n\n    if (!Array.isArray(languageAlternatives)) {\n      throw new Error(\n        `'options.languageAlternatives' should be an array, got ${typeof languageAlternatives}`,\n      )\n    }\n\n    return languageAlternatives.reduce((acc: CodeInputLanguage[], {title, value: val, mode}) => {\n      const alias = LANGUAGE_ALIASES[val]\n      if (alias) {\n        // eslint-disable-next-line no-console\n        console.warn(\n          `'options.languageAlternatives' lists a language with value \"%s\", which is an alias of \"%s\" - please replace the value to read \"%s\"`,\n          val,\n          alias,\n          alias,\n        )\n\n        return acc.concat({title, value: alias, mode: mode})\n      }\n      return acc.concat({title, value: val, mode})\n    }, [])\n  }, [type])\n}\n", "import {Select} from '@sanity/ui'\nimport {type ChangeEvent, useCallback} from 'react'\nimport {set, type StringInputProps, unset} from 'sanity'\n\nimport {CodeInputLanguage} from './types'\n\nexport interface LanguageInputProps {\n  language: string\n  languages: CodeInputLanguage[]\n  onChange: StringInputProps['onChange']\n  elementProps: StringInputProps['elementProps']\n}\n\n/** @internal */\nexport function LanguageInput(props: LanguageInputProps) {\n  const {language, languages, onChange, elementProps} = props\n\n  const handleChange = useCallback(\n    (e: ChangeEvent<HTMLSelectElement>) => {\n      const newValue = e.currentTarget.value\n      onChange(newValue ? set(newValue) : unset())\n    },\n    [onChange],\n  )\n\n  return (\n    <Select {...elementProps} value={language} onChange={handleChange}>\n      {languages.map((lang: {title: string; value: string}) => (\n        <option key={lang.value} value={lang.value}>\n          {lang.title}\n        </option>\n      ))}\n    </Select>\n  )\n}\n", "import {useCallback} from 'react'\nimport {\n  FieldMember,\n  type InputProps,\n  MemberField,\n  type MemberFieldProps,\n  type PrimitiveInputElementProps,\n} from 'sanity'\n\nimport {LanguageInput} from './LanguageInput'\nimport type {CodeInputLanguage} from './types'\n\nexport function LanguageField(\n  props: MemberFieldProps & {member: FieldMember; language: string; languages: CodeInputLanguage[]},\n) {\n  const {member, languages, language, renderItem, renderField, renderPreview} = props\n\n  const renderInput = useCallback(\n    ({elementProps, onChange}: Omit<InputProps, 'renderDefault'>) => {\n      return (\n        <LanguageInput\n          onChange={onChange}\n          elementProps={elementProps as PrimitiveInputElementProps}\n          language={language}\n          languages={languages}\n        />\n      )\n    },\n    [languages, language],\n  )\n\n  return (\n    <MemberField\n      member={member}\n      renderItem={renderItem}\n      renderField={renderField}\n      renderInput={renderInput}\n      renderPreview={renderPreview}\n    />\n  )\n}\n", "/** @internal */\n// todo: import from @sanity/ui instead\nexport function focusRingBorderStyle(border: {color: string; width: number}): string {\n  return `inset 0 0 0 ${border.width}px ${border.color}`\n}\n\n/** @internal */\n// todo: import from @sanity/ui instead\nexport function focusRingStyle(opts: {\n  base?: {bg: string}\n  border?: {color: string; width: number}\n  focusRing: {offset: number; width: number}\n}): string {\n  const {base, border, focusRing} = opts\n  const focusRingOutsetWidth = focusRing.offset + focusRing.width\n  const focusRingInsetWidth = 0 - focusRing.offset\n  const bgColor = base ? base.bg : 'var(--card-bg-color)'\n\n  return [\n    focusRingInsetWidth > 0 && `inset 0 0 0 ${focusRingInsetWidth}px var(--card-focus-ring-color)`,\n    border && focusRingBorderStyle(border),\n    focusRingInsetWidth < 0 && `0 0 0 ${0 - focusRingInsetWidth}px ${bgColor}`,\n    focusRingOutsetWidth > 0 && `0 0 0 ${focusRingOutsetWidth}px var(--card-focus-ring-color)`,\n  ]\n    .filter(Boolean)\n    .join(',')\n}\n", "import {useMemo} from 'react'\nimport {FieldMember, ObjectMember} from 'sanity'\n\n/** @internal */\nexport function useFieldMember(\n  members: ObjectMember[],\n  fieldName: string,\n): FieldMember | undefined {\n  return useMemo(\n    () =>\n      members.find(\n        (member): member is FieldMember => member.kind === 'field' && member.name === fieldName,\n      ),\n    [members, fieldName],\n  )\n}\n", "import {Box, Card, Stack, Text} from '@sanity/ui'\nimport {Suspense, useCallback} from 'react'\nimport {MemberField, ObjectInputProps, RenderInputCallback, set, setIfMissing, unset} from 'sanity'\nimport styled, {css} from 'styled-components'\n\nimport {useCodeMirror} from './codemirror/useCodeMirror'\nimport {useLanguageMode} from './codemirror/useLanguageMode'\nimport {PATH_CODE} from './config'\nimport {LanguageField} from './LanguageField'\nimport {CodeInputValue, CodeSchemaType} from './types'\nimport {focusRingBorderStyle, focusRingStyle} from './ui/focusRingStyle'\nimport {useFieldMember} from './useFieldMember'\n\nexport type {CodeInputLanguage, CodeInputValue} from './types'\n\n/**\n * @public\n */\nexport interface CodeInputProps extends ObjectInputProps<CodeInputValue, CodeSchemaType> {}\n\nconst EditorContainer = styled(Card)(({theme}) => {\n  const {focusRing, input} = theme.sanity\n  const base = theme.sanity.color.base\n  const color = theme.sanity.color.input\n  const border = {\n    color: color.default.enabled.border,\n    width: input.border.width,\n  }\n\n  return css`\n    --input-box-shadow: ${focusRingBorderStyle(border)};\n\n    box-shadow: var(--input-box-shadow);\n    height: 250px;\n    min-height: 80px;\n    overflow-y: auto;\n    position: relative;\n    resize: vertical;\n    z-index: 0;\n\n    & > .cm-theme {\n      height: 100%;\n    }\n\n    &:focus-within {\n      --input-box-shadow: ${focusRingStyle({\n        base,\n        border,\n        focusRing,\n      })};\n    }\n  `\n})\n\n/** @public */\nexport function CodeInput(props: CodeInputProps) {\n  const {\n    members,\n    elementProps,\n    onChange,\n    readOnly,\n    renderField,\n    renderInput,\n    renderItem,\n    renderPreview,\n    schemaType: type,\n    value,\n    onPathFocus,\n  } = props\n\n  const languageFieldMember = useFieldMember(members, 'language')\n  const filenameMember = useFieldMember(members, 'filename')\n  const codeFieldMember = useFieldMember(members, 'code')\n\n  const handleCodeFocus = useCallback(() => {\n    onPathFocus(PATH_CODE)\n  }, [onPathFocus])\n\n  const onHighlightChange = useCallback(\n    (lines: number[]) => onChange(set(lines, ['highlightedLines'])),\n    [onChange],\n  )\n\n  const handleCodeChange = useCallback(\n    (code: string) => {\n      const path = PATH_CODE\n      const fixedLanguage = type.options?.language\n\n      onChange([\n        setIfMissing({_type: type.name, language: fixedLanguage}),\n        code ? set(code, path) : unset(path),\n      ])\n    },\n    [onChange, type],\n  )\n  const {languages, language, languageMode} = useLanguageMode(props.schemaType, props.value)\n\n  const CodeMirror = useCodeMirror()\n\n  const renderCodeInput: RenderInputCallback = useCallback(\n    (inputProps) => {\n      return (\n        <EditorContainer border overflow=\"hidden\" radius={1} sizing=\"border\" readOnly={readOnly}>\n          {CodeMirror && (\n            <Suspense\n              fallback={\n                <Box padding={3}>\n                  <Text>Loading code editor...</Text>\n                </Box>\n              }\n            >\n              <CodeMirror\n                languageMode={languageMode}\n                onChange={handleCodeChange}\n                value={inputProps.value as string}\n                highlightLines={value?.highlightedLines}\n                onHighlightChange={onHighlightChange}\n                readOnly={readOnly}\n                onFocus={handleCodeFocus}\n                onBlur={elementProps.onBlur}\n              />\n            </Suspense>\n          )}\n        </EditorContainer>\n      )\n    },\n    [\n      CodeMirror,\n      handleCodeChange,\n      handleCodeFocus,\n      onHighlightChange,\n      languageMode,\n      elementProps.onBlur,\n      readOnly,\n      value,\n    ],\n  )\n\n  return (\n    <Stack space={4}>\n      {languageFieldMember && (\n        <LanguageField\n          member={languageFieldMember}\n          language={language}\n          languages={languages}\n          renderField={renderField}\n          renderItem={renderItem}\n          renderInput={renderInput}\n          renderPreview={renderPreview}\n        />\n      )}\n\n      {type.options?.withFilename && filenameMember && (\n        <MemberField\n          member={filenameMember}\n          renderItem={renderItem}\n          renderField={renderField}\n          renderInput={renderInput}\n          renderPreview={renderPreview}\n        />\n      )}\n\n      {codeFieldMember && (\n        <MemberField\n          member={codeFieldMember}\n          renderInput={renderCodeInput}\n          renderItem={renderItem}\n          renderField={renderField}\n          renderPreview={renderPreview}\n        />\n      )}\n    </Stack>\n  )\n}\n", "export function getMedia(language?: string) {\n  if (language === 'jsx') {\n    return (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\">\n        <g fill=\"#61DAFB\">\n          <circle cx=\"64\" cy=\"64\" r=\"11.4\" />\n          <path d=\"M107.3 45.2c-2.2-.8-4.5-1.6-6.9-2.3.6-2.4 1.1-4.8 1.5-7.1 2.1-13.2-.2-22.5-6.6-26.1-1.9-1.1-4-1.6-6.4-1.6-7 0-15.9 5.2-24.9 13.9-9-8.7-17.9-13.9-24.9-13.9-2.4 0-4.5.5-6.4 1.6-6.4 3.7-8.7 13-6.6 26.1.4 2.3.9 4.7 1.5 7.1-2.4.7-4.7 1.4-6.9 2.3C8.2 50 1.4 56.6 1.4 64s6.9 14 19.3 18.8c2.2.8 4.5 1.6 6.9 2.3-.6 2.4-1.1 4.8-1.5 7.1-2.1 13.2.2 22.5 6.6 26.1 1.9 1.1 4 1.6 6.4 1.6 7.1 0 16-5.2 24.9-13.9 9 8.7 17.9 13.9 24.9 13.9 2.4 0 4.5-.5 6.4-1.6 6.4-3.7 8.7-13 6.6-26.1-.4-2.3-.9-4.7-1.5-7.1 2.4-.7 4.7-1.4 6.9-2.3 12.5-4.8 19.3-11.4 19.3-18.8s-6.8-14-19.3-18.8zM92.5 14.7c4.1 2.4 5.5 9.8 3.8 20.3-.3 2.1-.8 4.3-1.4 6.6-5.2-1.2-10.7-2-16.5-2.5-3.4-4.8-6.9-9.1-10.4-13 7.4-7.3 14.9-12.3 21-12.3 1.3 0 2.5.3 3.5.9zM81.3 74c-1.8 3.2-3.9 6.4-6.1 9.6-3.7.3-7.4.4-11.2.4-3.9 0-7.6-.1-11.2-.4-2.2-3.2-4.2-6.4-6-9.6-1.9-3.3-3.7-6.7-5.3-10 1.6-3.3 3.4-6.7 5.3-10 1.8-3.2 3.9-6.4 6.1-9.6 3.7-.3 7.4-.4 11.2-.4 3.9 0 7.6.1 11.2.4 2.2 3.2 4.2 6.4 6 9.6 1.9 3.3 3.7 6.7 5.3 10-1.7 3.3-3.4 6.6-5.3 10zm8.3-3.3c1.5 3.5 2.7 6.9 3.8 10.3-3.4.8-7 1.4-10.8 1.9 1.2-1.9 2.5-3.9 3.6-6 1.2-2.1 2.3-4.2 3.4-6.2zM64 97.8c-2.4-2.6-4.7-5.4-6.9-8.3 2.3.1 4.6.2 6.9.2 2.3 0 4.6-.1 6.9-.2-2.2 2.9-4.5 5.7-6.9 8.3zm-18.6-15c-3.8-.5-7.4-1.1-10.8-1.9 1.1-3.3 2.3-6.8 3.8-10.3 1.1 2 2.2 4.1 3.4 6.1 1.2 2.2 2.4 4.1 3.6 6.1zm-7-25.5c-1.5-3.5-2.7-6.9-3.8-10.3 3.4-.8 7-1.4 10.8-1.9-1.2 1.9-2.5 3.9-3.6 6-1.2 2.1-2.3 4.2-3.4 6.2zM64 30.2c2.4 2.6 4.7 5.4 6.9 8.3-2.3-.1-4.6-.2-6.9-.2-2.3 0-4.6.1-6.9.2 2.2-2.9 4.5-5.7 6.9-8.3zm22.2 21l-3.6-6c3.8.5 7.4 1.1 10.8 1.9-1.1 3.3-2.3 6.8-3.8 10.3-1.1-2.1-2.2-4.2-3.4-6.2zM31.7 35c-1.7-10.5-.3-17.9 3.8-20.3 1-.6 2.2-.9 3.5-.9 6 0 13.5 4.9 21 12.3-3.5 3.8-7 8.2-10.4 13-5.8.5-11.3 1.4-16.5 2.5-.6-2.3-1-4.5-1.4-6.6zM7 64c0-4.7 5.7-9.7 15.7-13.4 2-.8 4.2-1.5 6.4-2.1 1.6 5 3.6 10.3 6 15.6-2.4 5.3-4.5 10.5-6 15.5C15.3 75.6 7 69.6 7 64zm28.5 49.3c-4.1-2.4-5.5-9.8-3.8-20.3.3-2.1.8-4.3 1.4-6.6 5.2 1.2 10.7 2 16.5 2.5 3.4 4.8 6.9 9.1 10.4 13-7.4 7.3-14.9 12.3-21 12.3-1.3 0-2.5-.3-3.5-.9zM96.3 93c1.7 10.5.3 17.9-3.8 20.3-1 .6-2.2.9-3.5.9-6 0-13.5-4.9-21-12.3 3.5-3.8 7-8.2 10.4-13 5.8-.5 11.3-1.4 16.5-2.5.6 2.3 1 4.5 1.4 6.6zm9-15.6c-2 .8-4.2 1.5-6.4 2.1-1.6-5-3.6-10.3-6-15.6 2.4-5.3 4.5-10.5 6-15.5 13.8 4 22.1 10 22.1 15.6 0 4.7-5.8 9.7-15.7 13.4z\" />\n        </g>\n      </svg>\n    )\n  }\n\n  if (language === 'javascript') {\n    return (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 128 128\">\n        <path fill=\"#F0DB4F\" d=\"M1.408 1.408h125.184v125.185H1.408z\" />\n        <path\n          fill=\"#323330\"\n          d=\"M116.347 96.736c-.917-5.711-4.641-10.508-15.672-14.981-3.832-1.761-8.104-3.022-9.377-5.926-.452-1.69-.512-2.642-.226-3.665.821-3.32 4.784-4.355 7.925-3.403 2.023.678 3.938 2.237 5.093 4.724 5.402-3.498 5.391-3.475 9.163-5.879-1.381-2.141-2.118-3.129-3.022-4.045-3.249-3.629-7.676-5.498-14.756-5.355l-3.688.477c-3.534.893-6.902 2.748-8.877 5.235-5.926 6.724-4.236 18.492 2.975 23.335 7.104 5.332 17.54 6.545 18.873 11.531 1.297 6.104-4.486 8.08-10.234 7.378-4.236-.881-6.592-3.034-9.139-6.949-4.688 2.713-4.688 2.713-9.508 5.485 1.143 2.499 2.344 3.63 4.26 5.795 9.068 9.198 31.76 8.746 35.83-5.176.165-.478 1.261-3.666.38-8.581zM69.462 58.943H57.753l-.048 30.272c0 6.438.333 12.34-.714 14.149-1.713 3.558-6.152 3.117-8.175 2.427-2.059-1.012-3.106-2.451-4.319-4.485-.333-.584-.583-1.036-.667-1.071l-9.52 5.83c1.583 3.249 3.915 6.069 6.902 7.901 4.462 2.678 10.459 3.499 16.731 2.059 4.082-1.189 7.604-3.652 9.448-7.401 2.666-4.915 2.094-10.864 2.07-17.444.06-10.735.001-21.468.001-32.237z\"\n        />\n      </svg>\n    )\n  }\n\n  if (language === 'php') {\n    return (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 800 400\">\n        <g transform=\"translate(-44.632 -141.55)\">\n          <g transform=\"matrix(8.3528 0 0 8.3119 -727.13 -3759.5)\">\n            <path d=\"m99.974 479.48h14.204c4.1693 0.0354 7.1903 1.2367 9.063 3.604 1.8726 2.3674 2.491 5.6004 1.855 9.699-0.24737 1.8727-0.79504 3.71-1.643 5.512-0.8127 1.802-1.9434 3.4273-3.392 4.876-1.7667 1.8373-3.657 3.0033-5.671 3.498-2.014 0.49467-4.0987 0.742-6.254 0.742h-6.36l-2.014 10.07h-7.367l7.579-38.001m6.201 6.042-3.18 15.9c0.21198 0.0353 0.42398 0.053 0.636 0.053h0.742c3.392 0.0353 6.2186-0.30033 8.48-1.007 2.2613-0.74199 3.7806-3.3213 4.558-7.738 0.63597-3.71-0.00003-5.8476-1.908-6.413-1.8727-0.56531-4.2224-0.83031-7.049-0.795-0.42402 0.0353-0.83035 0.053-1.219 0.053-0.35335 0.00002-0.72435 0.00002-1.113 0l0.053-0.053\" />\n            <path d=\"m133.49 469.36h7.314l-2.067 10.123h6.572c3.604 0.0707 6.2893 0.81269 8.056 2.226 1.802 1.4134 2.332 4.0987 1.59 8.056l-3.551 17.649h-7.42l3.392-16.854c0.35328-1.7666 0.2473-3.021-0.318-3.763-0.56536-0.74198-1.7844-1.113-3.657-1.113l-5.883-0.053-4.346 21.783h-7.314l7.632-38.054\" />\n            <path d=\"m162.81 479.48h14.204c4.1693 0.0354 7.1903 1.2367 9.063 3.604 1.8726 2.3674 2.491 5.6004 1.855 9.699-0.24737 1.8727-0.79503 3.71-1.643 5.512-0.8127 1.802-1.9434 3.4273-3.392 4.876-1.7667 1.8373-3.657 3.0033-5.671 3.498-2.014 0.49467-4.0987 0.742-6.254 0.742h-6.36l-2.014 10.07h-7.367l7.579-38.001m6.201 6.042-3.18 15.9c0.21199 0.0353 0.42399 0.053 0.636 0.053h0.742c3.392 0.0353 6.2186-0.30033 8.48-1.007 2.2613-0.74199 3.7806-3.3213 4.558-7.738 0.63597-3.71-0.00003-5.8476-1.908-6.413-1.8727-0.56531-4.2224-0.83031-7.049-0.795-0.42402 0.0353-0.83035 0.053-1.219 0.053-0.35335 0.00002-0.72435 0.00002-1.113 0l0.053-0.053\" />\n          </g>\n        </g>\n      </svg>\n    )\n  }\n\n  if (language === 'json') {\n    return (\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n        viewBox=\"0 0 160 160\"\n      >\n        <defs>\n          <linearGradient id=\"a\">\n            <stop offset=\"0\" />\n            <stop offset=\"1\" stopColor=\"#fff\" />\n          </linearGradient>\n          <linearGradient\n            x1=\"-553.27\"\n            y1=\"525.908\"\n            x2=\"-666.116\"\n            y2=\"413.045\"\n            id=\"c\"\n            xlinkHref=\"#a\"\n            gradientUnits=\"userSpaceOnUse\"\n            gradientTransform=\"matrix(.99884 0 0 .9987 689.008 -388.844)\"\n          />\n          <linearGradient\n            x1=\"-666.117\"\n            y1=\"413.045\"\n            x2=\"-553.27\"\n            y2=\"525.908\"\n            id=\"b\"\n            xlinkHref=\"#a\"\n            gradientUnits=\"userSpaceOnUse\"\n            gradientTransform=\"matrix(.99884 0 0 .9987 689.008 -388.844)\"\n          />\n        </defs>\n        <path\n          d=\"M79.865 119.1c35.397 48.255 70.04-13.469 69.988-50.587-.06-43.886-44.54-68.414-70.017-68.414C38.943.1 0 33.895 0 80.135 0 131.531 44.64 160 79.836 160c-7.965-1.147-34.507-6.834-34.863-67.967-.24-41.346 13.487-57.865 34.805-50.599.477.177 23.514 9.265 23.514 38.95 0 29.56-23.427 38.716-23.427 38.716z\"\n          style={{marker: 'none'}}\n          color=\"#000\"\n          fill=\"url(#b)\"\n          fillRule=\"evenodd\"\n          overflow=\"visible\"\n        />\n        <path\n          d=\"M79.823 41.4C56.433 33.34 27.78 52.618 27.78 91.23c0 63.048 46.72 68.77 52.384 68.77C121.057 160 160 126.204 160 79.964 160 28.568 115.36.1 80.164.1c9.749-1.35 52.541 10.55 52.541 69.037 0 38.141-31.953 58.905-52.735 50.033-.478-.177-23.514-9.264-23.514-38.95 0-29.56 23.367-38.818 23.367-38.818z\"\n          style={{marker: 'none'}}\n          color=\"#000\"\n          fill=\"url(#c)\"\n          fillRule=\"evenodd\"\n          overflow=\"visible\"\n        />\n      </svg>\n    )\n  }\n\n  return undefined\n}\n", "import {Box, Card, Flex, Label, Text} from '@sanity/ui'\nimport {Suspense} from 'react'\nimport {PreviewProps} from 'sanity'\nimport styled from 'styled-components'\n\nimport {useCodeMirror} from './codemirror/useCodeMirror'\nimport {useLanguageMode} from './codemirror/useLanguageMode'\nimport {CodeInputValue, CodeSchemaType} from './types'\n\nconst PreviewContainer = styled(Box)`\n  position: relative;\n`\n\n/**\n * @public\n */\nexport interface PreviewCodeProps extends PreviewProps {\n  selection?: CodeInputValue\n}\n\n/**\n * @public\n */\nexport function PreviewCode(props: PreviewCodeProps) {\n  const {selection, schemaType: type} = props\n  const {languageMode} = useLanguageMode(type as CodeSchemaType, props.selection)\n\n  const CodeMirror = useCodeMirror()\n  return (\n    <PreviewContainer>\n      <Card padding={4}>\n        {selection?.filename || selection?.language ? (\n          <Card\n            paddingBottom={4}\n            marginBottom={selection.code ? 4 : 0}\n            borderBottom={!!selection.code}\n          >\n            <Flex align=\"center\" justify=\"flex-end\">\n              {selection?.filename ? (\n                <Box flex={1}>\n                  <Text>\n                    <code>{selection.filename}</code>\n                  </Text>\n                </Box>\n              ) : null}\n              {selection?.language ? <Label muted>{selection.language}</Label> : null}\n            </Flex>\n          </Card>\n        ) : null}\n        {CodeMirror && (\n          <Suspense fallback={<Card padding={2}>Loading code preview...</Card>}>\n            <CodeMirror\n              readOnly\n              editable={false}\n              value={selection?.code || ''}\n              highlightLines={selection?.highlightedLines || []}\n              basicSetup={{\n                lineNumbers: false,\n                foldGutter: false,\n                highlightSelectionMatches: false,\n                highlightActiveLineGutter: false,\n                highlightActiveLine: false,\n              }}\n              languageMode={languageMode}\n            />\n          </Suspense>\n        )}\n      </Card>\n    </PreviewContainer>\n  )\n}\n", "import {CodeBlockIcon} from '@sanity/icons'\nimport {defineType, ObjectDefinition} from 'sanity'\n\nimport {CodeInput} from './CodeInput'\nimport {getMedia} from './getMedia'\nimport {PreviewCode} from './PreviewCode'\nimport {CodeOptions} from './types'\n\n/**\n * @public\n */\nexport const codeTypeName = 'code' as const\n\n/**\n * @public\n */\nexport interface CodeDefinition extends Omit<ObjectDefinition, 'type' | 'fields' | 'options'> {\n  type: typeof codeTypeName\n  options?: CodeOptions\n}\n\ndeclare module '@sanity/types' {\n  // makes type: 'code' narrow correctly when using defineType/defineField/defineArrayMember\n  export interface IntrinsicDefinitions {\n    code: CodeDefinition\n  }\n}\n\n/**\n * @public\n */\nexport const codeSchema = defineType({\n  name: 'code',\n  type: 'object',\n  title: 'Code',\n  components: {input: CodeInput, preview: PreviewCode},\n  icon: CodeBlockIcon,\n  fields: [\n    {\n      name: 'language',\n      title: 'Language',\n      type: 'string',\n    },\n    {\n      name: 'filename',\n      title: 'Filename',\n      type: 'string',\n    },\n    {\n      title: 'Code',\n      name: 'code',\n      type: 'text',\n    },\n    {\n      title: 'Highlighted lines',\n      name: 'highlightedLines',\n      type: 'array',\n      of: [\n        {\n          type: 'number',\n          title: 'Highlighted line',\n        },\n      ],\n    },\n  ],\n  preview: {\n    select: {\n      language: 'language',\n      code: 'code',\n      filename: 'filename',\n      highlightedLines: 'highlightedLines',\n    },\n    prepare: (value: {\n      language?: string\n      code?: string\n      filename?: string\n      highlightedLines?: number[]\n    }) => {\n      return {\n        title: value.filename || (value.language || 'unknown').toUpperCase(),\n        media: getMedia(value?.language),\n        selection: value,\n      }\n    },\n  },\n})\n", "import {createContext} from 'react'\n\nimport type {CodeInputConfig} from '../plugin'\n\nexport const CodeInputConfigContext = createContext<CodeInputConfig | undefined>(undefined)\n", "import {definePlugin} from 'sanity'\n\nimport {CodeInputConfigContext} from './codemirror/CodeModeContext'\nimport {CodeMode} from './codemirror/defaultCodeModes'\nimport {codeSchema} from './schema'\n\nexport interface CodeInputConfig {\n  codeModes?: CodeMode[]\n}\n\n/**\n * @public\n */\nexport const codeInput = definePlugin<CodeInputConfig | void>((config) => {\n  const codeModes = config && config.codeModes\n  const basePlugin = {\n    name: '@sanity/code-input',\n    schema: {types: [codeSchema]},\n  }\n  if (!codeModes) {\n    return basePlugin\n  }\n  return {\n    ...basePlugin,\n    form: {\n      components: {\n        input: (props) => {\n          if (props.id !== 'root') {\n            return props.renderDefault(props)\n          }\n          return (\n            <CodeInputConfigContext.Provider value={config}>\n              {props.renderDefault(props)}\n            </CodeInputConfigContext.Provider>\n          )\n        },\n      },\n    },\n  }\n})\n"], "names": ["_a"], "mappings": ";;;;;;AAEO,MAAM,kBAAkB,KAAK,MAAM,OAAO,sBAAmB,CAAC;AAE9D,SAAS,gBAAgB;AAC9B,QAAM,CAAC,SAAS,UAAU,IAAI,SAAS,EAAK;AAC5C,SAAA,UAAU,MAAM;AACQ,0BAAA,MAAM,WAAW,EAAI,CAAC;AAAA,EAC3C,GAAA,EAAE,GAEE,UAAU,kBAAkB;AACrC;ACRO,MAAM,sBAA2C;AAAA,EACtD,EAAC,OAAO,cAAc,OAAO,YAAW;AAAA,EACxC,EAAC,OAAO,MAAM,OAAO,SAAQ;AAAA,EAC7B,EAAC,OAAO,OAAO,OAAO,MAAK;AAAA,EAC3B,EAAC,OAAO,MAAM,OAAO,SAAQ;AAAA,EAC7B,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,cAAc,OAAO,aAAY;AAAA,EACzC,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,OAAO,OAAO,MAAK;AAAA,EAC3B,EAAC,OAAO,YAAY,OAAO,WAAU;AAAA,EACrC,EAAC,OAAO,SAAS,OAAO,QAAO;AAAA,EAC/B,EAAC,OAAO,OAAO,OAAO,MAAK;AAAA,EAC3B,EAAC,OAAO,cAAc,OAAO,OAAM;AAAA,EACnC,EAAC,OAAO,UAAU,OAAO,SAAQ;AAAA,EACjC,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,QAAQ,OAAO,OAAM;AAAA,EAC7B,EAAC,OAAO,MAAM,OAAO,KAAI;AAAA,EACzB,EAAC,OAAO,OAAO,OAAO,MAAK;AAAA,EAC3B,EAAC,OAAO,cAAc,OAAO,aAAY;AAAA,EACzC,EAAC,OAAO,OAAO,OAAO,MAAK;AAAA,EAC3B,EAAC,OAAO,QAAQ,OAAO,OAAM;AAC/B,GAEa,mBAAuD,EAAC,IAAI,aAAA,GAG5D,YAAY,CAAC,MAAM,GC3BnB,sBAAsB;AAEnB,SAAA,gBACd,YACA,OAKA;AAdF,MAAA,IAAA,IAAA,IAAA,IAAA;AAeE,QAAM,YAAY,wBAAwB,UAAU,GAC9C,iBAAgB,KAAW,WAAA,YAAX,OAAoB,SAAA,GAAA,UACpC,YAAW,MAAA,KAAA,SAAA,OAAA,SAAA,MAAO,aAAP,OAAA,KAAmB,kBAAnB,OAAoC,KAAA,qBAG/C,aAAa,UAAU,KAAK,CAAC,UAAU,MAAM,UAAU,QAAQ,GAC/D,gBAAe,MAAA,KAAA,cAAA,OAAA,SAAA,WAAY,SAAZ,OAAA,KAAoB,uBAAuB,QAAQ,MAAnD,OAAwD,KAAA;AAEtE,SAAA,EAAC,UAAU,cAAc,UAAS;AAC3C;AAEA,SAAS,uBAAuB,MAAe;AA1B/C,MAAA;AA2BE,UAAQ,KAAQ,QAAA,iBAAiB,IAAI,MAA7B,OAAmC,KAAA;AAC7C;AAEA,SAAS,wBAAwB,MAAsB;AACrD,SAAO,QAAQ,MAA2B;AA/B5C,QAAA;AAgCU,UAAA,wBAAuB,KAAK,KAAA,YAAL,OAAc,SAAA,GAAA;AAC3C,QAAI,CAAC;AACI,aAAA;AAGL,QAAA,CAAC,MAAM,QAAQ,oBAAoB;AACrC,YAAM,IAAI;AAAA,QACR,0DAA0D,OAAO,oBAAoB;AAAA,MACvF;AAGK,WAAA,qBAAqB,OAAO,CAAC,KAA0B,EAAC,OAAO,OAAO,KAAK,WAAU;AACpF,YAAA,QAAQ,iBAAiB,GAAG;AAClC,aAAI,SAEF,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,GAGK,IAAI,OAAO,EAAC,OAAO,OAAO,OAAO,KAAA,CAAW,KAE9C,IAAI,OAAO,EAAC,OAAO,OAAO,KAAK,MAAK;AAAA,IAC7C,GAAG,EAAE;AAAA,EAAA,GACJ,CAAC,IAAI,CAAC;AACX;;;;;;;;;AC7CO,SAAS,cAAc,OAA2B;AACvD,QAAM,EAAC,UAAU,WAAW,UAAU,iBAAgB,OAEhD,eAAe;AAAA,IACnB,CAAC,MAAsC;AAC/B,YAAA,WAAW,EAAE,cAAc;AACjC,eAAS,WAAW,IAAI,QAAQ,IAAI,OAAO;AAAA,IAC7C;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAGE,SAAA,oBAAC,6CAAW,YAAX,GAAA,EAAyB,OAAO,UAAU,UAAU,cAClD,UAAA,UAAU,IAAI,CAAC,SACb,oBAAA,UAAA,EAAwB,OAAO,KAAK,OAClC,eAAK,MADK,GAAA,KAAK,KAElB,CACD,EACH,CAAA,CAAA;AAEJ;ACtBO,SAAS,cACd,OACA;AACM,QAAA,EAAC,QAAQ,WAAW,UAAU,YAAY,aAAa,cAAiB,IAAA,OAExE,cAAc;AAAA,IAClB,CAAC,EAAC,cAAc,SAAA,MAEZ;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,IAGJ,CAAC,WAAW,QAAQ;AAAA,EACtB;AAGE,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EACF;AAEJ;ACtCO,SAAS,qBAAqB,QAAgD;AACnF,SAAO,eAAe,OAAO,KAAK,MAAM,OAAO,KAAK;AACtD;AAIO,SAAS,eAAe,MAIpB;AACT,QAAM,EAAC,MAAM,QAAQ,cAAa,MAC5B,uBAAuB,UAAU,SAAS,UAAU,OACpD,sBAAsB,IAAI,UAAU,QACpC,UAAU,OAAO,KAAK,KAAK;AAE1B,SAAA;AAAA,IACL,sBAAsB,KAAK,eAAe,mBAAmB;AAAA,IAC7D,UAAU,qBAAqB,MAAM;AAAA,IACrC,sBAAsB,KAAK,SAAS,IAAI,mBAAmB,MAAM,OAAO;AAAA,IACxE,uBAAuB,KAAK,SAAS,oBAAoB;AAAA,EAExD,EAAA,OAAO,OAAO,EACd,KAAK,GAAG;AACb;ACtBgB,SAAA,eACd,SACA,WACyB;AAClB,SAAA;AAAA,IACL,MACE,QAAQ;AAAA,MACN,CAAC,WAAkC,OAAO,SAAS,WAAW,OAAO,SAAS;AAAA,IAChF;AAAA,IACF,CAAC,SAAS,SAAS;AAAA,EACrB;AACF;ACKA,MAAM,kBAAkB,OAAO,IAAI,EAAE,CAAC,EAAC,YAAW;AAC1C,QAAA,EAAC,WAAW,MAAS,IAAA,MAAM,QAC3B,OAAO,MAAM,OAAO,MAAM,MAE1B,SAAS;AAAA,IACb,OAFY,MAAM,OAAO,MAAM,MAElB,QAAQ,QAAQ;AAAA,IAC7B,OAAO,MAAM,OAAO;AAAA,EACtB;AAEO,SAAA;AAAA,0BACiB,qBAAqB,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAe1B,eAAe;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD,CAAC;AAAA;AAAA;AAGR,CAAC;AAGM,SAAS,UAAU,OAAuB;AAvDjD,MAAA;AAwDQ,QAAA;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EAAA,IACE,OAEE,sBAAsB,eAAe,SAAS,UAAU,GACxD,iBAAiB,eAAe,SAAS,UAAU,GACnD,kBAAkB,eAAe,SAAS,MAAM,GAEhD,kBAAkB,YAAY,MAAM;AACxC,gBAAY,SAAS;AAAA,EACpB,GAAA,CAAC,WAAW,CAAC,GAEV,oBAAoB;AAAA,IACxB,CAAC,UAAoB,SAAS,IAAI,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAAA,IAC9D,CAAC,QAAQ;AAAA,KAGL,mBAAmB;AAAA,IACvB,CAAC,SAAiB;AApFtBA,UAAAA;AAqFM,YAAM,OAAO,WACP,iBAAgBA,MAAA,KAAK,YAAL,gBAAAA,IAAc;AAE3B,eAAA;AAAA,QACP,aAAa,EAAC,OAAO,KAAK,MAAM,UAAU,eAAc;AAAA,QACxD,OAAO,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI;AAAA,MAAA,CACpC;AAAA,IACH;AAAA,IACA,CAAC,UAAU,IAAI;AAAA,EAEX,GAAA,EAAC,WAAW,UAAU,aAAgB,IAAA,gBAAgB,MAAM,YAAY,MAAM,KAAK,GAEnF,aAAa,iBAEb,kBAAuC;AAAA,IAC3C,CAAC,eAEI,oBAAA,iBAAA,EAAgB,QAAM,IAAC,UAAS,UAAS,QAAQ,GAAG,QAAO,UAAS,UAClE,UACC,cAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,8BACG,KAAI,EAAA,SAAS,GACZ,UAAC,oBAAA,MAAA,EAAK,oCAAsB,EAC9B,CAAA;AAAA,QAGF,UAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC;AAAA,YACA,UAAU;AAAA,YACV,OAAO,WAAW;AAAA,YAClB,gBAAgB,SAAO,OAAA,SAAA,MAAA;AAAA,YACvB;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,QAAQ,aAAa;AAAA,UAAA;AAAA,QAAA;AAAA,MACvB;AAAA,IAAA,GAGN;AAAA,IAGJ;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IAAA;AAAA,EAEJ;AAGE,SAAA,qBAAC,OAAM,EAAA,OAAO,GACX,UAAA;AAAA,IACC,uBAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,MAGD,KAAK,KAAA,YAAL,OAAc,SAAA,GAAA,iBAAgB,kBAC7B;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,IAGD,mBACC;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,QAAQ;AAAA,QACR,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAAA;AAAA,EACF,GAEJ;AAEJ;AC7KO,SAAS,SAAS,UAAmB;AAC1C,MAAI,aAAa;AAEb,WAAA,oBAAC,SAAI,OAAM,8BAA6B,SAAQ,eAC9C,UAAA,qBAAC,KAAE,EAAA,MAAK,WACN,UAAA;AAAA,MAAA,oBAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,QAAO;AAAA,MACjC,oBAAC,QAAK,EAAA,GAAE,8sEAA8sE,CAAA;AAAA,IAAA,EAAA,CACxtE,EACF,CAAA;AAIJ,MAAI,aAAa;AACf,WACG,qBAAA,OAAA,EAAI,OAAM,8BAA6B,SAAQ,eAC9C,UAAA;AAAA,MAAA,oBAAC,QAAK,EAAA,MAAK,WAAU,GAAE,uCAAsC;AAAA,MAC7D;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,GAAE;AAAA,QAAA;AAAA,MAAA;AAAA,IACJ,GACF;AAIJ,MAAI,aAAa;AACf,WACG,oBAAA,OAAA,EAAI,OAAM,8BAA6B,SAAQ,eAC9C,UAAC,oBAAA,KAAA,EAAE,WAAU,8BACX,UAAC,qBAAA,KAAA,EAAE,WAAU,6CACX,UAAA;AAAA,MAAC,oBAAA,QAAA,EAAK,GAAE,+mBAA+mB,CAAA;AAAA,MACvnB,oBAAC,QAAK,EAAA,GAAE,wRAAwR,CAAA;AAAA,MAChS,oBAAC,QAAK,EAAA,GAAE,+mBAA+mB,CAAA;AAAA,IAAA,EACznB,CAAA,EACF,CAAA,GACF;AAIJ,MAAI,aAAa;AAEb,WAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OAAM;AAAA,QACN,YAAW;AAAA,QACX,SAAQ;AAAA,QAER,UAAA;AAAA,UAAA,qBAAC,QACC,EAAA,UAAA;AAAA,YAAC,qBAAA,kBAAA,EAAe,IAAG,KACjB,UAAA;AAAA,cAAC,oBAAA,QAAA,EAAK,QAAO,IAAI,CAAA;AAAA,cAChB,oBAAA,QAAA,EAAK,QAAO,KAAI,WAAU,OAAO,CAAA;AAAA,YAAA,GACpC;AAAA,YACA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,WAAU;AAAA,gBACV,eAAc;AAAA,gBACd,mBAAkB;AAAA,cAAA;AAAA,YACpB;AAAA,YACA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,IAAG;AAAA,gBACH,WAAU;AAAA,gBACV,eAAc;AAAA,gBACd,mBAAkB;AAAA,cAAA;AAAA,YAAA;AAAA,UACpB,GACF;AAAA,UACA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,GAAE;AAAA,cACF,OAAO,EAAC,QAAQ,OAAM;AAAA,cACtB,OAAM;AAAA,cACN,MAAK;AAAA,cACL,UAAS;AAAA,cACT,UAAS;AAAA,YAAA;AAAA,UACX;AAAA,UACA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,GAAE;AAAA,cACF,OAAO,EAAC,QAAQ,OAAM;AAAA,cACtB,OAAM;AAAA,cACN,MAAK;AAAA,cACL,UAAS;AAAA,cACT,UAAS;AAAA,YAAA;AAAA,UAAA;AAAA,QACX;AAAA,MAAA;AAAA,IACF;AAKN;ACnFA,MAAM,mBAAmB,OAAO,GAAG;AAAA;AAAA;AAc5B,SAAS,YAAY,OAAyB;AACnD,QAAM,EAAC,WAAW,YAAY,SAAQ,OAChC,EAAC,aAAY,IAAI,gBAAgB,MAAwB,MAAM,SAAS,GAExE,aAAa,cAAc;AACjC,SACG,oBAAA,kBAAA,EACC,UAAC,qBAAA,MAAA,EAAK,SAAS,GACZ,UAAA;AAAA,IAAW,aAAA,QAAA,UAAA,YAAY,+BAAW,WACjC;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,eAAe;AAAA,QACf,cAAc,UAAU,OAAO,IAAI;AAAA,QACnC,cAAc,CAAC,CAAC,UAAU;AAAA,QAE1B,UAAC,qBAAA,MAAA,EAAK,OAAM,UAAS,SAAQ,YAC1B,UAAA;AAAA,UAAA,aAAA,QAAA,UAAW,WACV,oBAAC,KAAI,EAAA,MAAM,GACT,UAAA,oBAAC,MACC,EAAA,UAAA,oBAAC,QAAM,EAAA,UAAA,UAAU,UAAS,EAAA,CAC5B,EACF,CAAA,IACE;AAAA,UACH,aAAA,QAAA,UAAW,WAAY,oBAAA,OAAA,EAAM,OAAK,IAAE,UAAA,UAAU,SAAS,CAAA,IAAW;AAAA,QAAA,EACrE,CAAA;AAAA,MAAA;AAAA,IAAA,IAEA;AAAA,IACH,kCACE,UAAS,EAAA,8BAAW,MAAK,EAAA,SAAS,GAAG,UAAA,0BAAA,CAAuB,GAC3D,UAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,UAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAO,uCAAW,SAAQ;AAAA,QAC1B,iBAAgB,aAAW,OAAA,SAAA,UAAA,qBAAoB,CAAC;AAAA,QAChD,YAAY;AAAA,UACV,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,2BAA2B;AAAA,UAC3B,2BAA2B;AAAA,UAC3B,qBAAqB;AAAA,QACvB;AAAA,QACA;AAAA,MAAA;AAAA,IAAA,EAEJ,CAAA;AAAA,EAAA,EAAA,CAEJ,EACF,CAAA;AAEJ;AC3Da,MAAA,eAAe,QAoBf,aAAa,WAAW;AAAA,EACnC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,YAAY,EAAC,OAAO,WAAW,SAAS,YAAW;AAAA,EACnD,MAAM;AAAA,EACN,QAAQ;AAAA,IACN;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,IAAI;AAAA,QACF;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QAAA;AAAA,MACT;AAAA,IACF;AAAA,EAEJ;AAAA,EACA,SAAS;AAAA,IACP,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,kBAAkB;AAAA,IACpB;AAAA,IACA,SAAS,CAAC,WAMD;AAAA,MACL,OAAO,MAAM,aAAa,MAAM,YAAY,WAAW,YAAY;AAAA,MACnE,OAAO,SAAS,SAAA,OAAA,SAAA,MAAO,QAAQ;AAAA,MAC/B,WAAW;AAAA,IACb;AAAA,EAAA;AAGN,CAAC,GCjFY,yBAAyB,cAA2C,MAAS;;;;;;;;;ACS7E,MAAA,YAAY,aAAqC,CAAC,WAAW;AACxE,QAAM,YAAY,UAAU,OAAO,WAC7B,aAAa;AAAA,IACjB,MAAM;AAAA,IACN,QAAQ,EAAC,OAAO,CAAC,UAAU,EAAC;AAAA,EAC9B;AACK,SAAA,YAGE,iCACF,UADE,GAAA;AAAA,IAEL,MAAM;AAAA,MACJ,YAAY;AAAA,QACV,OAAO,CAAC,UACF,MAAM,OAAO,SACR,MAAM,cAAc,KAAK,IAG/B,oBAAA,uBAAuB,UAAvB,EAAgC,OAAO,QACrC,UAAM,MAAA,cAAc,KAAK,EAC5B,CAAA;AAAA,MAAA;AAAA,IAGN;AAAA,EAhBK,CAAA,IAAA;AAmBX,CAAC;"}
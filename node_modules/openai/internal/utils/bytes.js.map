{"version": 3, "file": "bytes.js", "sourceRoot": "", "sources": ["../../src/internal/utils/bytes.ts"], "names": [], "mappings": ";;AAAA,kCAaC;AAGD,gCAMC;AAGD,gCAMC;AA/BD,SAAgB,WAAW,CAAC,OAAqB;IAC/C,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;IAC1B,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1B,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,IAAI,WAAwC,CAAC;AAC7C,SAAgB,UAAU,CAAC,GAAW;IACpC,IAAI,OAAO,CAAC;IACZ,OAAO,CACL,WAAW;QACX,CAAC,CAAC,OAAO,GAAG,IAAK,UAAkB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAClG,CAAC,GAAG,CAAC,CAAC;AACT,CAAC;AAED,IAAI,WAA0C,CAAC;AAC/C,SAAgB,UAAU,CAAC,KAAiB;IAC1C,IAAI,OAAO,CAAC;IACZ,OAAO,CACL,WAAW;QACX,CAAC,CAAC,OAAO,GAAG,IAAK,UAAkB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAClG,CAAC,KAAK,CAAC,CAAC;AACX,CAAC"}
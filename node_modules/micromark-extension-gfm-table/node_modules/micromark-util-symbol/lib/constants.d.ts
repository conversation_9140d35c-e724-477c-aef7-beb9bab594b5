export namespace constants {
    let attentionSideAfter: 2;
    let attentionSideBefore: 1;
    let atxHeadingOpeningFenceSizeMax: 6;
    let autolinkDomainSizeMax: 63;
    let autolinkSchemeSizeMax: 32;
    let cdataOpeningString: "CDATA[";
    let characterGroupPunctuation: 2;
    let characterGroupWhitespace: 1;
    let characterReferenceDecimalSizeMax: 7;
    let characterReferenceHexadecimalSizeMax: 6;
    let characterReferenceNamedSizeMax: 31;
    let codeFencedSequenceSizeMin: 3;
    let contentTypeContent: "content";
    let contentTypeDocument: "document";
    let contentTypeFlow: "flow";
    let contentTypeString: "string";
    let contentTypeText: "text";
    let hardBreakPrefixSizeMin: 2;
    let htmlBasic: 6;
    let htmlCdata: 5;
    let htmlComment: 2;
    let htmlComplete: 7;
    let htmlDeclaration: 4;
    let htmlInstruction: 3;
    let htmlRawSizeMax: 8;
    let htmlRaw: 1;
    let linkResourceDestinationBalanceMax: 32;
    let linkReferenceSizeMax: 999;
    let listItemValueSizeMax: 10;
    let numericBaseDecimal: 10;
    let numericBaseHexadecimal: 16;
    let tabSize: 4;
    let thematicBreakMarkerCountMin: 3;
    let v8MaxSafeChunkSize: 10000;
}
//# sourceMappingURL=constants.d.ts.map
const e = "react", r = "React is a JavaScript library for building user interfaces.", s = ["react"], t = "19.1.0", c = "https://react.dev/", a = "https://github.com/facebook/react/issues", n = "MIT", i = ["LICENSE", "README.md", "index.js", "cjs/", "compiler-runtime.js", "jsx-runtime.js", "jsx-runtime.react-server.js", "jsx-dev-runtime.js", "jsx-dev-runtime.react-server.js", "react.react-server.js"], o = "index.js", j = { ".": { "react-server": "./react.react-server.js", default: "./index.js" }, "./package.json": "./package.json", "./jsx-runtime": { "react-server": "./jsx-runtime.react-server.js", default: "./jsx-runtime.js" }, "./jsx-dev-runtime": { "react-server": "./jsx-dev-runtime.react-server.js", default: "./jsx-dev-runtime.js" }, "./compiler-runtime": { "react-server": "./compiler-runtime.js", default: "./compiler-runtime.js" } }, u = { type: "git", url: "https://github.com/facebook/react.git", directory: "packages/react" }, m = { node: ">=0.10.0" }, d = {
  name: e,
  description: r,
  keywords: s,
  version: t,
  homepage: c,
  bugs: a,
  license: n,
  files: i,
  main: o,
  exports: j,
  repository: u,
  engines: m
};
export {
  a as bugs,
  d as default,
  r as description,
  m as engines,
  j as exports,
  i as files,
  c as homepage,
  s as keywords,
  n as license,
  o as main,
  e as name,
  u as repository,
  t as version
};

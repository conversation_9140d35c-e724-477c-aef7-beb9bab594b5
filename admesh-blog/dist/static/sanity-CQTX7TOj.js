import{defineType as l,defineField as e,defineArray<PERSON>ember as r,defineConfig as p,renderStudio as c}from"sanity";import{structureTool as m}from"sanity/structure";import{visionTool as h}from"@sanity/vision";import{jsxs as s,jsx as o}from"react/jsx-runtime";import{forwardRef as n}from"react";const g=n(function(t,i){return s("svg",{"data-sanity-icon":"calendar",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:[o("path",{d:"M5.5 18.5H19.5V6.5H5.5V18.5Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"}),o("path",{d:"M16.5 8V4M8.5 8V4M8 12.5H10M8 15.5H10M11.5 12.5H13.5M11.5 15.5H13.5M15 12.5H17M15 15.5H17M12.5 8V4M5.5 9.5H19.5",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})]})}),a=n(function(t,i){return s("svg",{"data-sanity-icon":"document-text",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:[o("path",{d:"M11.5 4.5V9.5H6.5",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"}),o("path",{d:"M16 13H9M14 16H9M11.5 4.5H18.5V20.5H6.5V9.5L11.5 4.5Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})]})}),y=n(function(t,i){return o("svg",{"data-sanity-icon":"edit",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:o("path",{d:"M15 7L18 10M6 19L7 15L17 5L20 8L10 18L6 19Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})})}),f=n(function(t,i){return o("svg",{"data-sanity-icon":"star",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:o("path",{d:"M12.5 5L14.3956 9.89092L19.6329 10.1824L15.5672 13.4966L16.9084 18.5676L12.5 15.725L8.09161 18.5676L9.43284 13.4966L5.36708 10.1824L10.6044 9.89092L12.5 5Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})})}),v=n(function(t,i){return s("svg",{"data-sanity-icon":"tag",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:[o("path",{d:"M12.5 20L5 20L5 12.5L12.5 5L20 12.5L12.5 20Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"}),o("path",{d:"M11 15.5C11 16.3284 10.3284 17 9.5 17C8.67157 17 8 16.3284 8 15.5C8 14.6716 8.67157 14 9.5 14C10.3284 14 11 14.6716 11 15.5Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})]})}),w=n(function(t,i){return o("svg",{"data-sanity-icon":"tags",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:o("path",{d:"M12.1562 7.93179L13.9717 6.11633L20.3553 12.5L13.9717 18.8836L10.6855 18.8836M11.0283 18.8836L17.4119 12.5L11.0283 6.11633L4.64462 12.5L4.64462 18.8836L11.0283 18.8836ZM9.75153 15.0534C9.75153 15.7585 9.17992 16.3302 8.47481 16.3302C7.76969 16.3302 7.19808 15.7585 7.19808 15.0534C7.19808 14.3483 7.76969 13.7767 8.47481 13.7767C9.17992 13.7767 9.75153 14.3483 9.75153 15.0534Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})})}),L=n(function(t,i){return o("svg",{"data-sanity-icon":"user",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:o("path",{d:"M8 14.5C7 15 5.5 16 5.5 19.5H19.5C19.5 16 18.3416 15.1708 17 14.5C16 14 14 14 14 12.5C14 11 15 10.25 15 8.25C15 6.25 14 5.25 12.5 5.25C11 5.25 10 6.25 10 8.25C10 10.25 11 11 11 12.5C11 14 9 14 8 14.5Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})})}),b=n(function(t,i){return o("svg",{"data-sanity-icon":"users",width:"1em",height:"1em",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,ref:i,children:o("path",{d:"M17.5 18.5H21.5C21.5 15 20.8416 14.1708 19.5 13.5C18.5 13 16.5 12.5 16.5 11C16.5 9.5 17.5 9 17.5 7C17.5 5 16.5 4 15 4C13.6628 4 12.723 4.79472 12.5347 6.38415M4.5 20.5C4.5 17 5.5 16 6.5 15.5C7.5 15 9.5 14.5 9.5 13C9.5 11.5 8.5 11 8.5 9C8.5 7 9.5 6 11 6C12.5 6 13.5 7 13.5 9C13.5 11 12.5 11.5 12.5 13C12.5 14.5 14.5 15 15.5 15.5C16.8416 16.1708 17.5 17 17.5 20.5H4.5Z",stroke:"currentColor",strokeWidth:1.2,strokeLinejoin:"round"})})}),C=l({name:"post",title:"Blog Post",type:"document",icon:a,fields:[e({name:"title",title:"Title",type:"string",validation:t=>t.required().max(100)}),e({name:"slug",title:"Slug",type:"slug",options:{source:"title",maxLength:96},validation:t=>t.required()}),e({name:"author",title:"Author",type:"reference",to:{type:"author"},validation:t=>t.required()}),e({name:"mainImage",title:"Featured Image",type:"image",options:{hotspot:!0},fields:[e({name:"alt",title:"Alternative Text",type:"string",description:"Important for SEO and accessibility.",validation:t=>t.required()})]}),e({name:"categories",title:"Categories",type:"array",of:[{type:"reference",to:{type:"category"}}],validation:t=>t.max(3)}),e({name:"excerpt",title:"Excerpt",type:"text",rows:3,description:"Brief description of the post for previews and SEO.",validation:t=>t.max(200)}),e({name:"publishedAt",title:"Published At",type:"datetime",initialValue:()=>new Date().toISOString(),validation:t=>t.required()}),e({name:"updatedAt",title:"Last Updated",type:"datetime",readOnly:!0}),e({name:"body",title:"Content",type:"blockContent"}),e({name:"featured",title:"Featured Post",type:"boolean",description:"Mark this post as featured to highlight it.",initialValue:!1}),e({name:"readingTime",title:"Reading Time (minutes)",type:"number",description:"Estimated reading time in minutes.",validation:t=>t.min(1).max(60)}),e({name:"seo",title:"SEO Settings",type:"object",fields:[e({name:"metaTitle",title:"Meta Title",type:"string",description:"Title for search engines (leave empty to use post title).",validation:t=>t.max(60)}),e({name:"metaDescription",title:"Meta Description",type:"text",rows:3,description:"Description for search engines (leave empty to use excerpt).",validation:t=>t.max(160)}),e({name:"keywords",title:"Keywords",type:"array",of:[{type:"string"}],options:{layout:"tags"},description:"Keywords for SEO (optional)."}),e({name:"noIndex",title:"No Index",type:"boolean",description:"Prevent search engines from indexing this post.",initialValue:!1})],options:{collapsible:!0,collapsed:!0}})],preview:{select:{title:"title",author:"author.name",media:"mainImage",publishedAt:"publishedAt"},prepare(t){const{author:i,publishedAt:d}=t,u=d?new Date(d).toLocaleDateString():"No date";return{...t,subtitle:i?`by ${i} • ${u}`:u}}},orderings:[{title:"Published Date, New",name:"publishedAtDesc",by:[{field:"publishedAt",direction:"desc"}]},{title:"Published Date, Old",name:"publishedAtAsc",by:[{field:"publishedAt",direction:"asc"}]},{title:"Title A-Z",name:"titleAsc",by:[{field:"title",direction:"asc"}]}]}),k=l({name:"author",title:"Author",type:"document",icon:L,fields:[e({name:"name",title:"Name",type:"string",validation:t=>t.required()}),e({name:"slug",title:"Slug",type:"slug",options:{source:"name",maxLength:96},validation:t=>t.required()}),e({name:"image",title:"Profile Image",type:"image",options:{hotspot:!0},fields:[e({name:"alt",title:"Alternative Text",type:"string",description:"Important for SEO and accessibility."})]}),e({name:"bio",title:"Bio",type:"array",of:[{type:"block",styles:[{title:"Normal",value:"normal"}],lists:[],marks:{decorators:[{title:"Strong",value:"strong"},{title:"Emphasis",value:"em"}],annotations:[{title:"URL",name:"link",type:"object",fields:[{title:"URL",name:"href",type:"url"}]}]}}]}),e({name:"email",title:"Email",type:"email",description:"Contact email for the author (optional)"}),e({name:"socialLinks",title:"Social Links",type:"object",fields:[e({name:"twitter",title:"Twitter",type:"url"}),e({name:"linkedin",title:"LinkedIn",type:"url"}),e({name:"github",title:"GitHub",type:"url"}),e({name:"website",title:"Website",type:"url"})]})],preview:{select:{title:"name",media:"image"}}}),x=l({name:"category",title:"Category",type:"document",icon:v,fields:[e({name:"title",title:"Title",type:"string",validation:t=>t.required()}),e({name:"slug",title:"Slug",type:"slug",options:{source:"title",maxLength:96},validation:t=>t.required()}),e({name:"description",title:"Description",type:"text",rows:3}),e({name:"color",title:"Color",type:"string",options:{list:[{title:"Blue",value:"blue"},{title:"Green",value:"green"},{title:"Purple",value:"purple"},{title:"Red",value:"red"},{title:"Orange",value:"orange"},{title:"Yellow",value:"yellow"},{title:"Pink",value:"pink"},{title:"Gray",value:"gray"}]},initialValue:"blue"})],preview:{select:{title:"title",subtitle:"description"}}}),M=l({title:"Block Content",name:"blockContent",type:"array",of:[r({title:"Block",type:"block",styles:[{title:"Normal",value:"normal"},{title:"H1",value:"h1"},{title:"H2",value:"h2"},{title:"H3",value:"h3"},{title:"H4",value:"h4"},{title:"Quote",value:"blockquote"}],lists:[{title:"Bullet",value:"bullet"},{title:"Numbered",value:"number"}],marks:{decorators:[{title:"Strong",value:"strong"},{title:"Emphasis",value:"em"},{title:"Code",value:"code"}],annotations:[{title:"URL",name:"link",type:"object",fields:[{title:"URL",name:"href",type:"url",validation:t=>t.required()},{title:"Open in new tab",name:"blank",type:"boolean",initialValue:!0}]}]}}),r({type:"image",options:{hotspot:!0},fields:[{name:"alt",type:"string",title:"Alternative Text",description:"Important for SEO and accessibility.",validation:t=>t.required()},{name:"caption",type:"string",title:"Caption"}]}),r({type:"object",name:"codeBlock",title:"Code Block",fields:[{name:"language",title:"Language",type:"string",options:{list:[{title:"JavaScript",value:"javascript"},{title:"TypeScript",value:"typescript"},{title:"Python",value:"python"},{title:"HTML",value:"html"},{title:"CSS",value:"css"},{title:"JSON",value:"json"},{title:"Bash",value:"bash"},{title:"SQL",value:"sql"}]}},{name:"code",title:"Code",type:"text",rows:10}],preview:{select:{title:"language",subtitle:"code"},prepare({title:t,subtitle:i}){return{title:`Code: ${t||"Unknown language"}`,subtitle:i?`${i.slice(0,50)}...`:"No code"}}}})]}),T=[C,k,x,M],A=t=>t.list().title("AdMesh Blog Content").items([t.listItem().title("Blog Posts").icon(a).child(t.list().title("Blog Posts").items([t.listItem().title("All Posts").icon(a).child(t.documentTypeList("post").title("All Blog Posts").filter('_type == "post"').defaultOrdering([{field:"publishedAt",direction:"desc"}])),t.listItem().title("Featured Posts").icon(f).child(t.documentTypeList("post").title("Featured Posts").filter('_type == "post" && featured == true').defaultOrdering([{field:"publishedAt",direction:"desc"}])),t.listItem().title("Draft Posts").icon(y).child(t.documentTypeList("post").title("Draft Posts").filter('_type == "post" && !defined(publishedAt)').defaultOrdering([{field:"_createdAt",direction:"desc"}])),t.listItem().title("Posts by Date").icon(g).child(t.documentTypeList("post").title("Posts by Date").filter('_type == "post" && defined(publishedAt)').defaultOrdering([{field:"publishedAt",direction:"desc"}]))])),t.listItem().title("Authors").icon(b).child(t.documentTypeList("author").title("Authors").filter('_type == "author"').defaultOrdering([{field:"name",direction:"asc"}])),t.listItem().title("Categories").icon(w).child(t.documentTypeList("category").title("Categories").filter('_type == "category"').defaultOrdering([{field:"title",direction:"asc"}])),t.divider(),...t.documentTypeListItems().filter(i=>!["post","author","category"].includes(i.getId()||""))]),I=p({name:"default",title:"AdMesh Blog",projectId:"h0kukqbi",dataset:"production",plugins:[m({structure:A}),h()],schema:{types:T}});c(document.getElementById("sanity"),I,{reactStrictMode:!1,basePath:"/"});

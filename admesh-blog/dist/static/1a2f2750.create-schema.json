[{"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}, {"flag": "max", "constraint": 100}], "level": "error"}], "name": "title", "type": "string"}, {"options": {"source": "title", "maxLength": 96}, "validation": [{"rules": [{"flag": "custom"}, {"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "slug", "type": "slug"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error"}], "to": [{"type": "author"}], "name": "author", "type": "reference"}, {"options": {"hotspot": true}, "fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error"}], "description": "Important for SEO and accessibility.", "name": "alt", "type": "string", "title": "Alternative Text"}], "name": "mainImage", "type": "image", "title": "Featured Image"}, {"validation": [{"rules": [{"flag": "max", "constraint": 3}], "level": "error"}], "of": [{"to": [{"type": "category"}], "type": "reference", "title": "Reference to category"}], "name": "categories", "type": "array"}, {"rows": 3, "validation": [{"rules": [{"flag": "max", "constraint": 200}], "level": "error"}], "description": "Brief description of the post for previews and SEO.", "name": "excerpt", "type": "text"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "publishedAt", "type": "datetime"}, {"readOnly": true, "name": "updatedAt", "type": "datetime", "title": "Last Updated"}, {"name": "body", "type": "blockContent", "title": "Content"}, {"initialValue": false, "description": "Mark this post as featured to highlight it.", "name": "featured", "type": "boolean", "title": "Featured Post"}, {"validation": [{"rules": [{"flag": "min", "constraint": 1}, {"flag": "max", "constraint": 60}], "level": "error"}], "description": "Estimated reading time in minutes.", "name": "readingTime", "type": "number", "title": "Reading Time (minutes)"}, {"options": {"collapsible": true, "collapsed": true}, "fields": [{"validation": [{"rules": [{"flag": "max", "constraint": 60}], "level": "error"}], "description": "Title for search engines (leave empty to use post title).", "name": "metaTitle", "type": "string"}, {"rows": 3, "validation": [{"rules": [{"flag": "max", "constraint": 160}], "level": "error"}], "description": "Description for search engines (leave empty to use excerpt).", "name": "metaDescription", "type": "text"}, {"options": {"layout": "tags"}, "description": "Keywords for SEO (optional).", "of": [{"type": "string"}], "name": "keywords", "type": "array"}, {"initialValue": false, "description": "Prevent search engines from indexing this post.", "name": "noIndex", "type": "boolean"}], "name": "seo", "type": "object", "title": "SEO Settings"}], "name": "post", "type": "document", "title": "Blog Post"}, {"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "name", "type": "string"}, {"options": {"source": "name", "maxLength": 96}, "validation": [{"rules": [{"flag": "custom"}, {"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "slug", "type": "slug"}, {"options": {"hotspot": true}, "fields": [{"description": "Important for SEO and accessibility.", "name": "alt", "type": "string", "title": "Alternative Text"}], "name": "image", "type": "image", "title": "Profile Image"}, {"of": [{"marks": {"annotations": [{"fields": [{"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "href", "type": "url", "title": "URL"}], "name": "link", "type": "object", "title": "URL"}], "decorators": [{"value": "strong", "title": "Strong"}, {"value": "em", "title": "Emphasis"}]}, "styles": [{"value": "normal", "title": "Normal"}], "of": [], "type": "block"}], "name": "bio", "type": "array"}, {"description": "Contact email for the author (optional)", "name": "email", "type": "email"}, {"fields": [{"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "twitter", "type": "url"}, {"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "linkedin", "type": "url", "title": "LinkedIn"}, {"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "github", "type": "url", "title": "GitHub"}, {"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "website", "type": "url"}], "name": "socialLinks", "type": "object"}], "name": "author", "type": "document"}, {"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "title", "type": "string"}, {"options": {"source": "title", "maxLength": 96}, "validation": [{"rules": [{"flag": "custom"}, {"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "slug", "type": "slug"}, {"rows": 3, "name": "description", "type": "text"}, {"options": {"list": [{"title": "Blue", "value": "blue"}, {"title": "Green", "value": "green"}, {"title": "Purple", "value": "purple"}, {"title": "Red", "value": "red"}, {"title": "Orange", "value": "orange"}, {"title": "Yellow", "value": "yellow"}, {"title": "Pink", "value": "pink"}, {"title": "<PERSON>", "value": "gray"}]}, "initialValue": "blue", "name": "color", "type": "string"}], "name": "category", "type": "document"}, {"of": [{"marks": {"annotations": [{"fields": [{"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}, {"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "href", "type": "url", "title": "URL"}, {"initialValue": true, "name": "blank", "type": "boolean", "title": "Open in new tab"}], "name": "link", "type": "object", "title": "URL"}], "decorators": [{"value": "strong", "title": "Strong"}, {"value": "em", "title": "Emphasis"}, {"value": "code", "title": "Code"}]}, "lists": [{"value": "bullet", "title": "Bullet"}, {"value": "number", "title": "Numbered"}], "styles": [{"value": "normal", "title": "Normal"}, {"value": "h1", "title": "H1"}, {"value": "h2", "title": "H2"}, {"value": "h3", "title": "H3"}, {"value": "h4", "title": "H4"}, {"value": "blockquote", "title": "Quote"}], "of": [], "type": "block"}, {"options": {"hotspot": true}, "fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error"}], "description": "Important for SEO and accessibility.", "name": "alt", "type": "string", "title": "Alternative Text"}, {"name": "caption", "type": "string"}], "type": "image"}, {"fields": [{"options": {"list": [{"title": "JavaScript", "value": "javascript"}, {"title": "TypeScript", "value": "typescript"}, {"title": "Python", "value": "python"}, {"title": "HTML", "value": "html"}, {"title": "CSS", "value": "css"}, {"title": "JSON", "value": "json"}, {"title": "<PERSON><PERSON>", "value": "bash"}, {"title": "SQL", "value": "sql"}]}, "name": "language", "type": "string"}, {"rows": 10, "name": "code", "type": "text"}], "type": "object", "name": "codeBlock"}], "name": "blockContent", "type": "array"}]
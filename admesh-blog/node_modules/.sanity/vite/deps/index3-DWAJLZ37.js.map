{"version": 3, "sources": ["../../../../../node_modules/sanity/src/structure/components/paneRouter/PaneRouterProvider.tsx", "../../../../../node_modules/sanity/src/structure/structureResolvers/PaneResolutionError.ts", "../../../../../node_modules/sanity/src/structure/structureResolvers/assignId.ts", "../../../../../node_modules/sanity/src/structure/structureResolvers/createPaneResolver.ts", "../../../../../node_modules/sanity/src/structure/structureResolvers/memoBind.ts", "../../../../../node_modules/sanity/src/structure/structureResolvers/resolveIntent.ts", "../../../../../node_modules/sanity/src/structure/structureResolvers/createResolvedPaneNodeStream.ts", "../../../../../node_modules/sanity/src/structure/structureResolvers/useResolvedPanes.ts", "../../../../../node_modules/sanity/src/structure/components/structureTool/intentResolver/utils.ts", "../../../../../node_modules/sanity/src/structure/components/structureTool/intentResolver/IntentResolver.tsx", "../../../../../node_modules/sanity/src/structure/components/structureTool/StructureError.tsx", "../../../../../node_modules/sanity/src/structure/panes/unknown/UnknownPaneType.tsx", "../../../../../node_modules/sanity/src/structure/panes/StructureToolPane.tsx", "../../../../../node_modules/sanity/src/structure/components/structureTool/NoDocumentTypesScreen.tsx", "../../../../../node_modules/sanity/src/structure/components/structureTool/StructureTitle.tsx", "../../../../../node_modules/sanity/src/structure/components/structureTool/StructureTool.tsx", "../../../../../node_modules/sanity/src/structure/components/structureTool/StructureToolBoundary.tsx"], "sourcesContent": ["import {toString as pathToString} from '@sanity/util/paths'\nimport {omit} from 'lodash'\nimport {type ReactNode, useCallback, useMemo} from 'react'\nimport {PaneRouterContext} from 'sanity/_singletons'\nimport {useRouter, useRouterState} from 'sanity/router'\n\nimport {type RouterPaneGroup, type RouterPanes, type RouterPaneSibling} from '../../types'\nimport {usePaneLayout} from '../pane/usePaneLayout'\nimport {BackLink} from './BackLink'\nimport {ChildLink} from './ChildLink'\nimport {ParameterizedLink} from './ParameterizedLink'\nimport {ReferenceChildLink} from './ReferenceChildLink'\nimport {type PaneRouterContextValue} from './types'\n\nconst emptyArray: never[] = []\n\n/**\n * @internal\n */\nexport function PaneRouterProvider(props: {\n  children: ReactNode\n  flatIndex: number\n  index: number\n  params: Record<string, string | undefined>\n  payload: unknown\n  siblingIndex: number\n}) {\n  const {children, flatIndex, index, params, payload, siblingIndex} = props\n  const {navigate, navigateIntent, resolvePathFromState} = useRouter()\n  const routerState = useRouterState()\n  const {panes, expand} = usePaneLayout()\n  const routerPaneGroups: RouterPaneGroup[] = useMemo(\n    () => (routerState?.panes || emptyArray) as RouterPanes,\n    [routerState?.panes],\n  )\n  const lastPane = useMemo(() => panes?.[panes.length - 2], [panes])\n\n  const groupIndex = index - 1\n\n  const createNextRouterState = useCallback(\n    (modifier: (siblings: RouterPaneGroup, item: RouterPaneSibling) => RouterPaneGroup) => {\n      const currentGroup = routerPaneGroups[groupIndex] || []\n      const currentItem = currentGroup[siblingIndex]\n      const nextGroup = modifier(currentGroup, currentItem)\n      const nextPanes = [\n        ...routerPaneGroups.slice(0, groupIndex),\n        nextGroup,\n        ...routerPaneGroups.slice(groupIndex + 1),\n      ]\n      const nextRouterState = {...routerState, panes: nextPanes}\n\n      return nextRouterState\n    },\n    [groupIndex, routerPaneGroups, routerState, siblingIndex],\n  )\n\n  const modifyCurrentGroup = useCallback(\n    (modifier: (siblings: RouterPaneGroup, item: RouterPaneSibling) => RouterPaneGroup) => {\n      const nextRouterState = createNextRouterState(modifier)\n      setTimeout(() => navigate(nextRouterState), 0)\n      return nextRouterState\n    },\n    [createNextRouterState, navigate],\n  )\n\n  const createPathWithParams: PaneRouterContextValue['createPathWithParams'] = useCallback(\n    (nextParams) => {\n      const nextRouterState = createNextRouterState((siblings, item) => [\n        ...siblings.slice(0, siblingIndex),\n        {...item, params: nextParams},\n        ...siblings.slice(siblingIndex + 1),\n      ])\n\n      return resolvePathFromState(nextRouterState)\n    },\n    [createNextRouterState, resolvePathFromState, siblingIndex],\n  )\n\n  const setPayload: PaneRouterContextValue['setPayload'] = useCallback(\n    (nextPayload) => {\n      modifyCurrentGroup((siblings, item) => [\n        ...siblings.slice(0, siblingIndex),\n        {...item, payload: nextPayload},\n        ...siblings.slice(siblingIndex + 1),\n      ])\n    },\n    [modifyCurrentGroup, siblingIndex],\n  )\n\n  const setParams: PaneRouterContextValue['setParams'] = useCallback(\n    (nextParams) => {\n      modifyCurrentGroup((siblings, item) => [\n        ...siblings.slice(0, siblingIndex),\n        {...item, params: nextParams},\n        ...siblings.slice(siblingIndex + 1),\n      ])\n    },\n    [modifyCurrentGroup, siblingIndex],\n  )\n\n  const handleEditReference: PaneRouterContextValue['handleEditReference'] = useCallback(\n    ({id, parentRefPath, type, template, version}) => {\n      navigate({\n        panes: [\n          ...routerPaneGroups.slice(0, groupIndex + 1),\n          [\n            {\n              id,\n              params: {\n                template: template.id,\n                parentRefPath: pathToString(parentRefPath),\n                type,\n                version,\n              },\n              payload: template.params,\n            },\n          ],\n        ],\n      })\n    },\n    [groupIndex, navigate, routerPaneGroups],\n  )\n\n  const ctx: PaneRouterContextValue = useMemo(\n    () => ({\n      // Zero-based index (position) of pane, visually\n      index: flatIndex,\n\n      // Zero-based index of pane group (within URL structure)\n      groupIndex,\n\n      // Zero-based index of pane within sibling group\n      siblingIndex,\n\n      // Payload of the current pane\n      payload,\n\n      // Params of the current pane\n      params,\n\n      // Whether or not the pane has any siblings (within the same group)\n      hasGroupSiblings: routerPaneGroups[groupIndex]\n        ? routerPaneGroups[groupIndex].length > 1\n        : false,\n\n      // The length of the current group\n      groupLength: routerPaneGroups[groupIndex] ? routerPaneGroups[groupIndex].length : 0,\n\n      // Current router state for the \"panes\" property\n      routerPanesState: routerPaneGroups,\n\n      // Curried StateLink that passes the correct state automatically\n      ChildLink,\n\n      // Curried StateLink that pops off the last pane group\n      // Only pass if this is not the first pane\n      BackLink: flatIndex ? BackLink : undefined,\n\n      // A specialized `ChildLink` that takes in the needed props to open a\n      // referenced document to the right\n      ReferenceChildLink,\n\n      // Similar to `ReferenceChildLink` expect without the wrapping component\n      handleEditReference,\n\n      // Curried StateLink that passed the correct state, but merges params/payload\n      ParameterizedLink,\n\n      // Replaces the current pane with a new one\n      replaceCurrent: (opts = {}): void => {\n        modifyCurrentGroup(() => [\n          {id: opts.id || '', payload: opts.payload, params: opts.params || {}},\n        ])\n      },\n\n      // Removes the current pane from the group\n      closeCurrent: (): void => {\n        modifyCurrentGroup((siblings, item) =>\n          siblings.length > 1 ? siblings.filter((sibling) => sibling !== item) : siblings,\n        )\n      },\n\n      // Removes all panes to the right including current\n      closeCurrentAndAfter: (expandLast = true): void => {\n        if (expandLast && lastPane) {\n          expand(lastPane.element)\n        }\n        navigate({\n          panes: routerPaneGroups.slice(0, groupIndex),\n        })\n      },\n\n      // Duplicate the current pane, with optional overrides for payload, parameters\n      duplicateCurrent: (options): void => {\n        modifyCurrentGroup((siblings, item) => {\n          const duplicatedItem = {\n            ...item,\n            payload: options?.payload || item.payload,\n            params: options?.params || item.params,\n          }\n\n          return [\n            ...siblings.slice(0, siblingIndex),\n            duplicatedItem,\n            ...siblings.slice(siblingIndex),\n          ]\n        })\n      },\n\n      // Set the view for the current pane\n      setView: (viewId) => {\n        const restParams = omit(params, 'view')\n        return setParams(viewId ? {...restParams, view: viewId} : restParams)\n      },\n\n      // Set the parameters for the current pane\n      setParams,\n\n      // Set the payload for the current pane\n      setPayload,\n\n      // A function that returns a path with the given parameters\n      createPathWithParams,\n\n      // Proxied navigation to a given intent. Consider just exposing `router` instead?\n      navigateIntent,\n    }),\n    [\n      flatIndex,\n      groupIndex,\n      siblingIndex,\n      payload,\n      params,\n      routerPaneGroups,\n      handleEditReference,\n      setParams,\n      setPayload,\n      createPathWithParams,\n      navigateIntent,\n      modifyCurrentGroup,\n      lastPane,\n      navigate,\n      expand,\n    ],\n  )\n\n  return <PaneRouterContext.Provider value={ctx}>{children}</PaneRouterContext.Provider>\n}\n", "import {type RouterPaneSiblingContext} from '../types'\n\nexport interface PaneResolutionErrorOptions {\n  message: string\n  context?: RouterPaneSiblingContext\n  helpId?: string\n  cause?: Error\n}\n\n/**\n * An error thrown during pane resolving. This error is meant to be bubbled up\n * through react and handled in an error boundary. It includes a `cause`\n * property which is the original error caught\n */\nexport class PaneResolutionError extends Error {\n  cause: Error | undefined\n  context: RouterPaneSiblingContext | undefined\n  helpId: string | undefined\n\n  constructor({message, context, helpId, cause}: PaneResolutionErrorOptions) {\n    super(message)\n    this.name = 'PaneResolutionError'\n    this.context = context\n    this.helpId = helpId\n    this.cause = cause\n  }\n}\n", "import {nanoid} from 'nanoid'\n\n// `WeakMap`s require the first type param to extend `object`\nconst randomIdCache = new WeakMap<object, string>()\n\n/**\n * given an object, this function randomly generates an ID and returns it. this\n * result is then saved in a WeakMap so subsequent requests for the same object\n * will receive the same ID\n */\nexport function assignId(obj: object): string {\n  const cachedValue = randomIdCache.get(obj)\n  if (cachedValue) return cachedValue\n\n  const id = nanoid()\n  randomIdCache.set(obj, id)\n  return id\n}\n", "import {from, isObservable, type Observable, of as observableOf} from 'rxjs'\nimport {publishReplay, refCount, switchMap} from 'rxjs/operators'\nimport {isRecord} from 'sanity'\n\nimport {type PaneNode, type RouterPaneSiblingContext, type UnresolvedPaneNode} from '../types'\nimport {PaneResolutionError} from './PaneResolutionError'\n\ninterface Serializable {\n  serialize: (...args: never[]) => unknown\n}\n\nconst isPromise = (thing: any): thing is PromiseLike<unknown> => {\n  return !!thing && typeof thing?.then === 'function'\n}\nconst isSerializable = (thing: unknown): thing is Serializable => {\n  if (!isRecord(thing)) return false\n  return typeof thing.serialize === 'function'\n}\n\n/**\n * The signature of the function used to take an `UnresolvedPaneNode` and turn\n * it into an `Observable<PaneNode>`.\n */\nexport type PaneResolver = (\n  unresolvedPane: UnresolvedPaneNode | undefined,\n  context: RouterPaneSiblingContext,\n  flatIndex: number,\n) => Observable<PaneNode>\n\nexport type PaneResolverMiddleware = (paneResolveFn: PaneResolver) => PaneResolver\n\nconst rethrowWithPaneResolutionErrors: PaneResolverMiddleware =\n  (next) => (unresolvedPane, context, flatIndex) => {\n    try {\n      return next(unresolvedPane, context, flatIndex)\n    } catch (e) {\n      // re-throw errors that are already `PaneResolutionError`s\n      if (e instanceof PaneResolutionError) {\n        throw e\n      }\n\n      // anything else, wrap with `PaneResolutionError` and set the underlying\n      // error as a the `cause`\n      throw new PaneResolutionError({\n        message: typeof e?.message === 'string' ? e.message : '',\n        context,\n        cause: e,\n      })\n    }\n  }\n\nconst wrapWithPublishReplay: PaneResolverMiddleware =\n  (next) =>\n  (...args) => {\n    return next(...args).pipe(\n      // need to add publishReplay + refCount to ensure new subscribers always\n      // get an emission. without this, memoized observables may get stuck\n      // waiting for their first emissions resulting in a loading pane\n      publishReplay(1),\n      refCount(),\n    )\n  }\n\nexport function createPaneResolver(middleware: PaneResolverMiddleware): PaneResolver {\n  // note: this API includes a middleware/wrapper function because the function\n  // is recursive. we want to call the wrapped version of the function always\n  // (even inside of nested calls) so the identifier invoked for the recursion\n  // should be the wrapped version\n  const resolvePane = rethrowWithPaneResolutionErrors(\n    wrapWithPublishReplay(\n      middleware((unresolvedPane, context, flatIndex) => {\n        if (!unresolvedPane) {\n          throw new PaneResolutionError({\n            message: 'Pane returned no child',\n            context,\n            helpId: 'structure-item-returned-no-child',\n          })\n        }\n\n        if (isPromise(unresolvedPane) || isObservable(unresolvedPane)) {\n          return from(unresolvedPane).pipe(\n            switchMap((result) => resolvePane(result, context, flatIndex)),\n          )\n        }\n\n        if (isSerializable(unresolvedPane)) {\n          return resolvePane(unresolvedPane.serialize(context), context, flatIndex)\n        }\n\n        if (typeof unresolvedPane === 'function') {\n          return resolvePane(unresolvedPane(context.id, context), context, flatIndex)\n        }\n\n        return observableOf(unresolvedPane)\n      }),\n    ),\n  )\n\n  return resolvePane\n}\n", "/* eslint-disable @typescript-eslint/no-unsafe-function-type */\n\n// `WeakMap`s require the first type param to extend `object`\nconst bindCache = new WeakMap<object, Map<string, Function>>()\n\n/**\n * An alternative to `obj.method.bind(obj)` that utilizes a weakmap to return\n * the same memory reference for sequent binds.\n */\nexport function memoBind<\n  T extends object,\n  K extends keyof {[P in keyof T]: T[P] extends Function ? T[P] : never},\n>(obj: T, methodKey: K): T[K]\nexport function memoBind(obj: Record<string, unknown>, methodKey: string): Function {\n  const boundMethods = bindCache.get(obj) || new Map<string, Function>()\n  if (boundMethods) {\n    const bound = boundMethods.get(methodKey)\n    if (bound) return bound\n  }\n\n  const method = obj[methodKey]\n\n  if (typeof method !== 'function') {\n    throw new Error(\n      `Expected property \\`${methodKey}\\` to be a function but got ${typeof method} instead.`,\n    )\n  }\n\n  const bound = method.bind(obj)\n  boundMethods.set(methodKey, bound)\n  bindCache.set(obj, boundMethods)\n\n  return bound\n}\n", "import {omit} from 'lodash'\nimport {firstValueFrom, type Observable} from 'rxjs'\n\nimport {type StructureContext} from '../structureBuilder'\nimport {\n  type PaneNode,\n  type RouterPanes,\n  type RouterPaneSiblingContext,\n  type UnresolvedPaneNode,\n} from '../types'\nimport {assignId} from './assignId'\nimport {createPaneResolver, type PaneResolverMiddleware} from './createPaneResolver'\nimport {memoBind} from './memoBind'\n\ninterface TraverseOptions {\n  unresolvedPane: UnresolvedPaneNode | undefined\n  intent: string\n  params: {type: string; id: string; [key: string]: string | undefined}\n  payload: unknown\n  parent: PaneNode | null\n  path: string[]\n  currentId: string\n  flatIndex: number\n  levelIndex: number\n  structureContext: StructureContext\n}\n\nexport interface ResolveIntentOptions {\n  rootPaneNode?: UnresolvedPaneNode\n  intent: string\n  params: {type: string; id: string; [key: string]: string | undefined}\n  payload: unknown\n  structureContext: StructureContext\n}\n\n/**\n * Resolves an intent request using breadth first search. If a match is not\n * found, the intent will resolve to the fallback editor.\n *\n * A match is found if:\n * 1. the `PaneNode` is of type `document` and the its ID matches the intent ID\n * 2. the `PaneNode` is of type `documentList` and the `schemaTypeName` matches\n * 3. the `PaneNode`'s `canHandleIntent` method returns true\n *\n * If a `PaneNode` of type `list` is found, it will be searched for a match.\n *\n * @see PaneNode\n */\nexport async function resolveIntent(options: ResolveIntentOptions): Promise<RouterPanes> {\n  const resolvedPaneCache = new Map<string, Observable<PaneNode>>()\n\n  // this is a simple version of the memoizer in `createResolvedPaneNodeStream`\n  const memoize: PaneResolverMiddleware = (nextFn) => (unresolvedPane, context, flatIndex) => {\n    const key = unresolvedPane && `${assignId(unresolvedPane)}-${context.path.join('__')}`\n    const cachedResolvedPane = key && resolvedPaneCache.get(key)\n    if (cachedResolvedPane) return cachedResolvedPane\n\n    const result = nextFn(unresolvedPane, context, flatIndex)\n    if (key) resolvedPaneCache.set(key, result)\n    return result\n  }\n\n  const resolvePane = createPaneResolver(memoize)\n\n  const fallbackEditorPanes: RouterPanes = [\n    [\n      {\n        id: `__edit__${options.params.id}`,\n        params: {...omit(options.params, ['id']), type: options.params.type},\n        payload: options.payload,\n      },\n    ],\n  ]\n\n  async function traverse({\n    currentId,\n    flatIndex,\n    intent,\n    params,\n    parent,\n    path,\n    payload,\n    unresolvedPane,\n    levelIndex,\n    structureContext,\n  }: TraverseOptions): Promise<\n    Array<{panes: RouterPanes; depthIndex: number; levelIndex: number}>\n  > {\n    if (!unresolvedPane) return []\n\n    const {id: targetId, type: schemaTypeName, ...otherParams} = params\n    const context: RouterPaneSiblingContext = {\n      id: currentId,\n      splitIndex: 0,\n      parent,\n      path,\n      index: flatIndex,\n      params: {},\n      payload: undefined,\n      structureContext,\n    }\n    const resolvedPane = await firstValueFrom(resolvePane(unresolvedPane, context, flatIndex))\n\n    // if the resolved pane is a document pane and the pane's ID matches then\n    // resolve the intent to the current path\n    if (resolvedPane.type === 'document' && resolvedPane.id === targetId) {\n      return [\n        {\n          panes: [\n            ...path.slice(0, path.length - 1).map((i) => [{id: i}]),\n            [{id: targetId, params: otherParams, payload}],\n          ],\n          depthIndex: path.length,\n          levelIndex,\n        },\n      ]\n    }\n\n    // NOTE: if you update this logic, please also update the similar handler in\n    // `getIntentState.ts`\n    if (\n      // if the resolve pane's `canHandleIntent` returns true, then resolve\n      resolvedPane.canHandleIntent?.(intent, params, {\n        pane: resolvedPane,\n        index: flatIndex,\n      }) ||\n      // if the pane's `canHandleIntent` did not return true, then match against\n      // this default case. we will resolve the intent if:\n      (resolvedPane.type === 'documentList' &&\n        // 1. the schema type matches (this required for the document to render)\n        resolvedPane.schemaTypeName === schemaTypeName &&\n        // 2. the filter is the default filter.\n        //\n        // NOTE: this case is to prevent false positive matches where the user\n        // has configured a more specific filter for a particular type. In that\n        // case, the user can implement their own `canHandleIntent` function\n        resolvedPane.options.filter === '_type == $type')\n    ) {\n      return [\n        {\n          panes: [\n            // map the current path to router panes\n            ...path.map((id) => [{id}]),\n            // then augment with the intents IDs and params\n            [{id: params.id, params: otherParams, payload}],\n          ],\n          depthIndex: path.length,\n          levelIndex,\n        },\n      ]\n    }\n\n    if (resolvedPane.type === 'list' && resolvedPane.child && resolvedPane.items) {\n      return (\n        await Promise.all(\n          resolvedPane.items.map((item, nextLevelIndex) => {\n            if (item.type === 'divider') return Promise.resolve([])\n\n            return traverse({\n              currentId: item._id || item.id,\n              flatIndex: flatIndex + 1,\n              intent,\n              params,\n              parent: resolvedPane,\n              path: [...path, item.id],\n              payload,\n              unresolvedPane:\n                typeof resolvedPane.child === 'function'\n                  ? memoBind(resolvedPane, 'child')\n                  : resolvedPane.child,\n              levelIndex: nextLevelIndex,\n              structureContext,\n            })\n          }),\n        )\n      ).flat()\n    }\n\n    return []\n  }\n\n  const matchingPanes = await traverse({\n    currentId: 'root',\n    flatIndex: 0,\n    levelIndex: 0,\n    intent: options.intent,\n    params: options.params,\n    parent: null,\n    path: [],\n    payload: options.payload,\n    unresolvedPane: options.rootPaneNode,\n    structureContext: options.structureContext,\n  })\n\n  const closestPaneToRoot = matchingPanes.sort((a, b) => {\n    // break ties with the level index\n    if (a.depthIndex === b.depthIndex) return a.levelIndex - b.levelIndex\n    return a.depthIndex - b.depthIndex\n  })[0]\n\n  if (closestPaneToRoot) {\n    return closestPaneToRoot.panes\n  }\n\n  return fallbackEditorPanes\n}\n", "import {generateHelpUrl} from '@sanity/generate-help-url'\nimport {isEqual} from 'lodash'\nimport {concat, NEVER, type Observable, of as observableOf} from 'rxjs'\nimport {distinctUntilChanged, map, pairwise, scan, startWith, switchMap} from 'rxjs/operators'\n\nimport {type StructureContext} from '../structureBuilder'\nimport {\n  type DocumentPaneNode,\n  type PaneNode,\n  type PaneNodeResolver,\n  type RouterPanes,\n  type RouterPaneSibling,\n  type RouterPaneSiblingContext,\n  type UnresolvedPaneNode,\n} from '../types'\nimport {assignId} from './assignId'\nimport {\n  createPaneResolver,\n  type PaneResolver,\n  type PaneResolverMiddleware,\n} from './createPaneResolver'\nimport {memoBind} from './memoBind'\nimport {PaneResolutionError} from './PaneResolutionError'\n\n/**\n * the fallback editor child that is implicitly inserted into the structure tree\n * if the id starts with `__edit__`\n */\nconst fallbackEditorChild: PaneNodeResolver = (nodeId, context): DocumentPaneNode => {\n  const id = nodeId.replace(/^__edit__/, '')\n  const {\n    params,\n    payload,\n    structureContext: {resolveDocumentNode},\n  } = context\n  const {type, template} = params\n\n  if (!type) {\n    throw new Error(\n      `Document type for document with ID ${id} was not provided in the router params.`,\n    )\n  }\n\n  let defaultDocumentBuilder = resolveDocumentNode({schemaType: type, documentId: id}).id('editor')\n\n  if (template) {\n    defaultDocumentBuilder = defaultDocumentBuilder.initialValueTemplate(\n      template,\n      payload as {[key: string]: unknown},\n    )\n  }\n\n  return defaultDocumentBuilder.serialize() as DocumentPaneNode\n}\n\n/**\n * takes in a `RouterPaneSiblingContext` and returns a normalized string\n * representation that can be used for comparisons\n */\nfunction hashContext(context: RouterPaneSiblingContext): string {\n  return `contextHash(${JSON.stringify({\n    id: context.id,\n    parentId: parent && assignId(parent),\n    path: context.path,\n    index: context.index,\n    splitIndex: context.splitIndex,\n    serializeOptionsIndex: context.serializeOptions?.index,\n    serializeOptionsPath: context.serializeOptions?.path,\n  })})`\n}\n\n/**\n * takes in `ResolvedPaneMeta` and returns a normalized string representation\n * that can be used for comparisons\n */\nconst hashResolvedPaneMeta = (meta: ResolvedPaneMeta): string => {\n  const normalized = {\n    type: meta.type,\n    id: meta.routerPaneSibling.id,\n    params: meta.routerPaneSibling.params || {},\n    payload: meta.routerPaneSibling.payload || null,\n    flatIndex: meta.flatIndex,\n    groupIndex: meta.groupIndex,\n    siblingIndex: meta.siblingIndex,\n    path: meta.path,\n    paneNode: meta.type === 'resolvedMeta' ? assignId(meta.paneNode) : null,\n  }\n\n  return `metaHash(${JSON.stringify(normalized)})`\n}\n\n/**\n * Represents one flattened \"router pane\", including the source group and\n * sibling indexes.\n *\n * @see RouterPanes\n */\ninterface FlattenedRouterPane {\n  routerPaneSibling: RouterPaneSibling\n  flatIndex: number\n  groupIndex: number\n  siblingIndex: number\n}\n\n/**\n * The state of the accumulator used to store and manage memo cache state\n */\ninterface CacheState {\n  /**\n   * Holds the memoization results keyed by a combination of `assignId` and a\n   * context hash.\n   */\n  resolvedPaneCache: Map<string, Observable<PaneNode>>\n  /**\n   * Acts as a dictionary that stores cache keys by their flat index. This is\n   * used to clean up the cache between different branches in the pane\n   * structure.\n   *\n   * @see createResolvedPaneNodeStream look inside the `scan` where `wrapFn` is\n   * defined\n   */\n  cacheKeysByFlatIndex: Array<Set<string>>\n  /**\n   * The resulting memoized `PaneResolver` function. This function closes over\n   * the `resolvedPaneCache`.\n   */\n  resolvePane: PaneResolver\n  flattenedRouterPanes: FlattenedRouterPane[]\n}\n\nexport interface CreateResolvedPaneNodeStreamOptions {\n  /**\n   * an input stream of `RouterPanes`\n   * @see RouterPanes\n   */\n  routerPanesStream: Observable<RouterPanes>\n  /**\n   * any `UnresolvedPaneNode` (could be an observable, promise, pane resolver etc)\n   */\n  rootPaneNode: UnresolvedPaneNode\n  /** used primarily for testing */\n  initialCacheState?: CacheState\n\n  structureContext: StructureContext\n}\n\n/**\n * The result of pane resolving\n */\nexport type ResolvedPaneMeta = {\n  groupIndex: number\n  siblingIndex: number\n  flatIndex: number\n  routerPaneSibling: RouterPaneSibling\n  path: string[]\n} & ({type: 'loading'; paneNode: null} | {type: 'resolvedMeta'; paneNode: PaneNode})\n\ninterface ResolvePaneTreeOptions {\n  resolvePane: PaneResolver\n  flattenedRouterPanes: FlattenedRouterPane[]\n  unresolvedPane: UnresolvedPaneNode | undefined\n  parent: PaneNode | null\n  path: string[]\n  structureContext: StructureContext\n}\n\n/**\n * A recursive pane resolving function. Starts at one unresolved pane node and\n * continues until there is no more flattened router panes that can be used as\n * input to the unresolved panes.\n */\nfunction resolvePaneTree({\n  unresolvedPane,\n  flattenedRouterPanes,\n  parent,\n  path,\n  resolvePane,\n  structureContext,\n}: ResolvePaneTreeOptions): Observable<ResolvedPaneMeta[]> {\n  const [current, ...rest] = flattenedRouterPanes\n  const next = rest[0] as FlattenedRouterPane | undefined\n\n  const context: RouterPaneSiblingContext = {\n    id: current.routerPaneSibling.id,\n    splitIndex: current.siblingIndex,\n    parent,\n    path: [...path, current.routerPaneSibling.id],\n    index: current.flatIndex,\n    params: current.routerPaneSibling.params || {},\n    payload: current.routerPaneSibling.payload,\n    structureContext,\n  }\n\n  try {\n    return resolvePane(unresolvedPane, context, current.flatIndex).pipe(\n      // this switch map receives a resolved pane\n      switchMap((paneNode) => {\n        // we can create a `resolvedMeta` type using it\n        const resolvedPaneMeta: ResolvedPaneMeta = {\n          type: 'resolvedMeta',\n          ...current,\n          paneNode: paneNode,\n          path: context.path,\n        }\n\n        // for the other unresolved panes, we can create \"loading panes\"\n        const loadingPanes = rest.map((i, restIndex) => {\n          const loadingPanePath = [\n            ...context.path,\n            ...rest.slice(restIndex).map((_, currentIndex) => `[${i.flatIndex + currentIndex}]`),\n          ]\n\n          const loadingPane: ResolvedPaneMeta = {\n            type: 'loading',\n            path: loadingPanePath,\n            paneNode: null,\n            ...i,\n          }\n\n          return loadingPane\n        })\n\n        if (!rest.length) {\n          return observableOf([resolvedPaneMeta])\n        }\n\n        let nextStream\n\n        if (\n          // the fallback editor case\n          next?.routerPaneSibling.id.startsWith('__edit__')\n        ) {\n          nextStream = resolvePaneTree({\n            unresolvedPane: fallbackEditorChild,\n            flattenedRouterPanes: rest,\n            parent,\n            path: context.path,\n            resolvePane,\n            structureContext,\n          })\n        } else if (current.groupIndex === next?.groupIndex) {\n          // if the next flattened router pane has the same group index as the\n          // current flattened router pane, then the next flattened router pane\n          // belongs to the same group (i.e. it is a split pane)\n          nextStream = resolvePaneTree({\n            unresolvedPane,\n            flattenedRouterPanes: rest,\n            parent,\n            path,\n            resolvePane,\n            structureContext,\n          })\n        } else {\n          // normal children resolving\n          nextStream = resolvePaneTree({\n            unresolvedPane:\n              typeof paneNode.child === 'function'\n                ? (memoBind(paneNode, 'child') as PaneNodeResolver)\n                : paneNode.child,\n            flattenedRouterPanes: rest,\n            parent: paneNode,\n            path: context.path,\n            resolvePane,\n            structureContext,\n          })\n        }\n\n        return concat(\n          // we emit the loading panes first in a concat (this emits immediately)\n          observableOf([resolvedPaneMeta, ...loadingPanes]),\n          // then whenever the next stream is done, the results will be combined.\n          nextStream.pipe(map((nextResolvedPanes) => [resolvedPaneMeta, ...nextResolvedPanes])),\n        )\n      }),\n    )\n  } catch (e) {\n    if (e instanceof PaneResolutionError) {\n      if (e.context) {\n        console.warn(\n          `Pane resolution error at index ${e.context.index}${\n            e.context.splitIndex > 0 ? ` for split pane index ${e.context.splitIndex}` : ''\n          }: ${e.message}${e.helpId ? ` - see ${generateHelpUrl(e.helpId)}` : ''}`,\n          e,\n        )\n      }\n\n      if (e.helpId === 'structure-item-returned-no-child') {\n        // returning an observable of an empty array will remove loading panes\n        // note: this one intentionally does not throw\n        return observableOf([])\n      }\n    }\n\n    throw e\n  }\n}\n\n/**\n * Takes in a stream of `RouterPanes` and an unresolved root pane and returns\n * a stream of `ResolvedPaneMeta`\n */\nexport function createResolvedPaneNodeStream({\n  routerPanesStream,\n  rootPaneNode,\n  initialCacheState = {\n    cacheKeysByFlatIndex: [],\n    flattenedRouterPanes: [],\n    resolvedPaneCache: new Map(),\n    resolvePane: () => NEVER,\n  },\n  structureContext,\n}: CreateResolvedPaneNodeStreamOptions): Observable<ResolvedPaneMeta[]> {\n  const resolvedPanes$ = routerPanesStream.pipe(\n    // add in implicit \"root\" router pane\n    map((rawRouterPanes) => [[{id: 'root'}], ...rawRouterPanes]),\n    // create flattened router panes\n    map((routerPanes) => {\n      const flattenedRouterPanes: FlattenedRouterPane[] = routerPanes\n        .flatMap((routerPaneGroup, groupIndex) =>\n          routerPaneGroup.map((routerPaneSibling, siblingIndex) => ({\n            routerPaneSibling,\n            groupIndex,\n            siblingIndex,\n          })),\n        )\n        // add in the flat index\n        .map((i, index) => ({...i, flatIndex: index}))\n\n      return flattenedRouterPanes\n    }),\n    // calculate a \"diffIndex\" used for clearing the memo cache\n    startWith([] as FlattenedRouterPane[]),\n    pairwise(),\n    map(([prev, curr]) => {\n      for (let i = 0; i < curr.length; i++) {\n        const prevValue = prev[i]\n        const currValue = curr[i]\n\n        if (!isEqual(prevValue, currValue)) {\n          return {\n            flattenedRouterPanes: curr,\n            diffIndex: i,\n          }\n        }\n      }\n\n      return {\n        flattenedRouterPanes: curr,\n        diffIndex: curr.length,\n      }\n    }),\n    // create the memoized `resolvePane` function and manage the memo cache\n    scan((acc, next) => {\n      const {cacheKeysByFlatIndex, resolvedPaneCache} = acc\n      const {flattenedRouterPanes, diffIndex} = next\n\n      // use the `cacheKeysByFlatIndex` like a dictionary to find cache keys to\n      // and cache keys to delete\n      const beforeDiffIndex = cacheKeysByFlatIndex.slice(0, diffIndex + 1)\n      const afterDiffIndex = cacheKeysByFlatIndex.slice(diffIndex + 1)\n\n      const keysToKeep = new Set(beforeDiffIndex.flatMap((keySet) => Array.from(keySet)))\n      const keysToDelete = afterDiffIndex\n        .flatMap((keySet) => Array.from(keySet))\n        .filter((key) => !keysToKeep.has(key))\n\n      for (const key of keysToDelete) {\n        resolvedPaneCache.delete(key)\n      }\n\n      // create a memoizing pane resolver middleware that utilizes the cache\n      // maintained above. this keeps the cache from growing indefinitely\n      const memoize: PaneResolverMiddleware = (nextFn) => (unresolvedPane, context, flatIndex) => {\n        const key = unresolvedPane && `${assignId(unresolvedPane)}-${hashContext(context)}`\n        const cachedResolvedPane = key && resolvedPaneCache.get(key)\n        if (cachedResolvedPane) return cachedResolvedPane\n\n        const result = nextFn(unresolvedPane, context, flatIndex)\n        if (!key) return result\n\n        const cacheKeySet = cacheKeysByFlatIndex[flatIndex] || new Set()\n        cacheKeySet.add(key)\n        cacheKeysByFlatIndex[flatIndex] = cacheKeySet\n        resolvedPaneCache.set(key, result)\n        return result\n      }\n\n      return {\n        flattenedRouterPanes,\n        cacheKeysByFlatIndex,\n        resolvedPaneCache,\n        resolvePane: createPaneResolver(memoize),\n      }\n    }, initialCacheState),\n    // run the memoized, recursive resolving\n    switchMap(({flattenedRouterPanes, resolvePane}) =>\n      resolvePaneTree({\n        unresolvedPane: rootPaneNode,\n        flattenedRouterPanes,\n        parent: null,\n        path: [],\n        resolvePane,\n        structureContext,\n      }),\n    ),\n  )\n\n  // after we've created a stream of `ResolvedPaneMeta[]`, we need to clean up\n  // the results to remove unwanted loading panes and prevent unnecessary\n  // emissions\n  return resolvedPanes$.pipe(\n    // this diffs the previous emission with the current one. if there is a new\n    // loading pane at the same position where a previous pane already had a\n    // resolved value (looking at the IDs to compare), then return the previous\n    // pane instead of the loading pane\n    scan(\n      (prev, next) =>\n        next.map((nextPane, index) => {\n          const prevPane = prev[index] as ResolvedPaneMeta | undefined\n          if (!prevPane) return nextPane\n          if (nextPane.type !== 'loading') return nextPane\n\n          if (prevPane.routerPaneSibling.id === nextPane.routerPaneSibling.id) {\n            return prevPane\n          }\n          return nextPane\n        }),\n      [] as ResolvedPaneMeta[],\n    ),\n    // this prevents duplicate emissions\n    distinctUntilChanged((prev, next) => {\n      if (prev.length !== next.length) return false\n\n      for (let i = 0; i < next.length; i++) {\n        const prevValue = prev[i]\n        const nextValue = next[i]\n        if (hashResolvedPaneMeta(prevValue) !== hashResolvedPaneMeta(nextValue)) {\n          return false\n        }\n      }\n\n      return true\n    }),\n  )\n}\n", "import {useEffect, useMemo, useState} from 'react'\nimport {ReplaySubject} from 'rxjs'\nimport {map} from 'rxjs/operators'\nimport {type RouterState, useRouter} from 'sanity/router'\n\nimport {LOADING_PANE} from '../constants'\nimport {type PaneNode, type RouterPaneGroup, type RouterPanes} from '../types'\nimport {useStructureTool} from '../useStructureTool'\nimport {createResolvedPaneNodeStream} from './createResolvedPaneNodeStream'\n\ninterface PaneData {\n  active: boolean\n  childItemId: string | null\n  groupIndex: number\n  index: number\n  itemId: string\n  key: string\n  pane: PaneNode | typeof LOADING_PANE\n  params: Record<string, string | undefined> & {perspective?: string}\n  path: string\n  payload: unknown\n  selected: boolean\n  siblingIndex: number\n}\n\nexport interface Panes {\n  paneDataItems: PaneData[]\n  routerPanes: RouterPanes\n  resolvedPanes: (PaneNode | typeof LOADING_PANE)[]\n}\n\nfunction useRouterPanesStream() {\n  const [routerStateSubject] = useState(() => new ReplaySubject<RouterState>(1))\n  const routerPanes$ = useMemo(\n    () =>\n      routerStateSubject\n        .asObservable()\n        .pipe(map((_routerState) => (_routerState?.panes || []) as RouterPanes)),\n    [routerStateSubject],\n  )\n  const {state: routerState} = useRouter()\n  useEffect(() => {\n    routerStateSubject.next(routerState)\n  }, [routerState, routerStateSubject])\n\n  return routerPanes$\n}\n\nexport function useResolvedPanes(): Panes {\n  // used to propagate errors from async effect. throwing inside of the render\n  // will bubble the error to react where it can be picked up by standard error\n  // boundaries\n  const [error, setError] = useState<unknown>()\n  if (error) throw error\n\n  const {structureContext, rootPaneNode} = useStructureTool()\n\n  const [data, setData] = useState<Panes>({\n    paneDataItems: [],\n    resolvedPanes: [],\n    routerPanes: [],\n  })\n\n  const routerPanesStream = useRouterPanesStream()\n\n  useEffect(() => {\n    const resolvedPanes$ = createResolvedPaneNodeStream({\n      rootPaneNode,\n      routerPanesStream,\n      structureContext,\n    }).pipe(\n      map((resolvedPanes) => {\n        const routerPanes = resolvedPanes.reduce<RouterPanes>((acc, next) => {\n          const currentGroup = acc[next.groupIndex] || []\n          currentGroup[next.siblingIndex] = next.routerPaneSibling\n          acc[next.groupIndex] = currentGroup\n          return acc\n        }, [])\n\n        const groupsLen = routerPanes.length\n\n        const paneDataItems = resolvedPanes.map((pane) => {\n          const {groupIndex, flatIndex, siblingIndex, routerPaneSibling, path} = pane\n          const itemId = routerPaneSibling.id\n          const nextGroup = routerPanes[groupIndex + 1] as RouterPaneGroup | undefined\n\n          const paneDataItem: PaneData = {\n            active: groupIndex === groupsLen - 2,\n            childItemId: nextGroup?.[0].id ?? null,\n            index: flatIndex,\n            itemId: routerPaneSibling.id,\n            groupIndex,\n            key: `${\n              pane.type === 'loading' ? 'unknown' : pane.paneNode.id\n            }-${itemId}-${siblingIndex}`,\n            pane: pane.type === 'loading' ? LOADING_PANE : pane.paneNode,\n            params: routerPaneSibling.params || {},\n            path: path.join(';'),\n            payload: routerPaneSibling.payload,\n            selected: flatIndex === resolvedPanes.length - 1,\n            siblingIndex,\n          }\n\n          return paneDataItem\n        })\n\n        return {\n          paneDataItems,\n          routerPanes,\n          resolvedPanes: paneDataItems.map((pane) => pane.pane),\n        }\n      }),\n    )\n\n    const subscription = resolvedPanes$.subscribe({\n      next: (result) => setData(result),\n      error: (e) => setError(e),\n    })\n\n    return () => subscription.unsubscribe()\n  }, [rootPaneNode, routerPanesStream, structureContext])\n\n  return data\n}\n", "import {uuid} from '@sanity/uuid'\nimport {firstValueFrom, type Observable} from 'rxjs'\nimport {type DocumentStore, getPublishedId} from 'sanity'\n\nimport {PaneResolutionError} from '../../../structureResolvers'\n\nexport function removeDraftPrefix(documentId: string): string {\n  const publishedId = getPublishedId(documentId)\n\n  if (publishedId !== documentId) {\n    console.warn(\n      'Removed unexpected draft id in document link: All links to documents should have the ' +\n        '`drafts.`-prefix removed and something appears to have made an intent link to `%s`',\n      documentId,\n    )\n  }\n\n  return publishedId\n}\n\nexport async function ensureDocumentIdAndType(\n  documentStore: DocumentStore,\n  id: string | undefined,\n  type: string | undefined,\n): Promise<{id: string; type: string}> {\n  if (id && type) return {id, type}\n  if (!id && type) return {id: uuid(), type}\n  if (id && !type) {\n    const resolvedType = await firstValueFrom(\n      documentStore.resolveTypeForDocument(id) as Observable<string>,\n    )\n\n    return {id, type: resolvedType}\n  }\n\n  throw new PaneResolutionError({\n    message: 'Neither document `id` or `type` was provided when trying to resolve intent.',\n  })\n}\n", "import {memo, useCallback, useEffect, useState} from 'react'\nimport {isRecord, useDocumentStore} from 'sanity'\nimport {useRouter, useRouterState} from 'sanity/router'\n\nimport {resolveIntent} from '../../../structureResolvers'\nimport {useStructureTool} from '../../../useStructureTool'\nimport {ensureDocumentIdAndType} from './utils'\n\nconst EMPTY_RECORD: Record<string, unknown> = {}\n\n/**\n * A component that receives an intent from props and redirects to the resolved\n * intent location (while showing a loading spinner during the process)\n */\nexport const IntentResolver = memo(function IntentResolver() {\n  const {navigate} = useRouter()\n  const maybeIntent = useRouterState(\n    useCallback((routerState) => {\n      const intentName = typeof routerState.intent === 'string' ? routerState.intent : undefined\n      return intentName\n        ? {\n            intent: intentName,\n            params: isRecord(routerState.params) ? routerState.params : EMPTY_RECORD,\n            payload: routerState.payload,\n          }\n        : undefined\n    }, []),\n  )\n  const {rootPaneNode, structureContext} = useStructureTool()\n  const documentStore = useDocumentStore()\n  const [error, setError] = useState<unknown>(null)\n\n  // this re-throws errors so that parent ErrorBoundary's can handle them properly\n  if (error) throw error\n\n  // eslint-disable-next-line consistent-return\n  useEffect(() => {\n    if (maybeIntent) {\n      const {intent, params, payload} = maybeIntent\n\n      let cancelled = false\n      // eslint-disable-next-line no-inner-declarations\n      async function effect() {\n        const {id, type} = await ensureDocumentIdAndType(\n          documentStore,\n          typeof params.id === 'string' ? params.id : undefined,\n          typeof params.type === 'string' ? params.type : undefined,\n        )\n\n        if (cancelled) return\n\n        const panes = await resolveIntent({\n          intent,\n          params: {...params, id, type},\n          payload,\n          rootPaneNode,\n          structureContext,\n        })\n\n        if (cancelled) return\n\n        navigate({panes}, {replace: true})\n      }\n\n      effect().catch(setError)\n\n      return () => {\n        cancelled = true\n      }\n    }\n  }, [documentStore, maybeIntent, navigate, rootPaneNode, structureContext])\n\n  return null\n})\n", "import {generateHelpUrl} from '@sanity/generate-help-url'\nimport {SyncIcon} from '@sanity/icons'\nimport {Box, Card, Code, Container, Heading, Stack, Text} from '@sanity/ui'\nimport {useCallback} from 'react'\nimport {useTranslation} from 'sanity'\nimport {styled} from 'styled-components'\n\nimport {Button} from '../../../ui-components'\nimport {structureLocaleNamespace} from '../../i18n'\nimport {SerializeError} from '../../structureBuilder'\nimport {PaneResolutionError} from '../../structureResolvers'\n\nconst PathSegment = styled.span`\n  &:not(:last-child)::after {\n    content: ' ➝ ';\n    opacity: 0.5;\n  }\n`\n\nfunction formatStack(stack: string) {\n  return (\n    stack\n      // Prettify builder functions\n      .replace(/\\(\\.\\.\\.\\)\\./g, '(...)\\n  .')\n      // Remove webpack cruft from function names\n      .replace(/__WEBPACK_IMPORTED_MODULE_\\d+_+/g, '')\n      // Remove default export postfix from function names\n      .replace(/___default\\./g, '.')\n      // Replace full host path, leave only path to JS-file\n      .replace(new RegExp(` \\\\(https?:\\\\/\\\\/${window.location.host}`, 'g'), ' (')\n  )\n}\n\ninterface StructureErrorProps {\n  error: unknown\n}\n\nexport function StructureError({error}: StructureErrorProps) {\n  if (!(error instanceof PaneResolutionError)) {\n    throw error\n  }\n  const {cause} = error\n  const {t} = useTranslation(structureLocaleNamespace)\n\n  // Serialize errors are well-formatted and should be readable, in these cases a stack trace is\n  // usually not helpful. Build errors in dev (with HMR) usually also contains a bunch of garbage\n  // instead of an actual error message, so make sure we show the message in these cases as well\n  const stack = cause?.stack || error.stack\n  const showStack =\n    stack && !(cause instanceof SerializeError) && !error.message.includes('Module build failed:')\n\n  const path = cause instanceof SerializeError ? cause.path : []\n  const helpId = (cause instanceof SerializeError && cause.helpId) || error.helpId\n\n  const handleReload = useCallback(() => {\n    window.location.reload()\n  }, [])\n\n  return (\n    <Card height=\"fill\" overflow=\"auto\" padding={4} sizing=\"border\" tone=\"critical\">\n      <Container>\n        <Heading as=\"h2\">{t('structure-error.header.text')}</Heading>\n\n        <Card marginTop={4} padding={4} radius={2} overflow=\"auto\" shadow={1} tone=\"inherit\">\n          {path.length > 0 && (\n            <Stack space={2}>\n              <Text size={1} weight=\"medium\">\n                {t('structure-error.structure-path.label')}\n              </Text>\n              <Code>\n                {/* TODO: it seems like the path is off by one and includes */}\n                {/* `root` twice  */}\n                {path.slice(1).map((segment, i) => (\n                  // eslint-disable-next-line react/no-array-index-key\n                  <PathSegment key={`${segment}-${i}`}>{segment}</PathSegment>\n                ))}\n              </Code>\n            </Stack>\n          )}\n\n          <Stack marginTop={4} space={2}>\n            <Text size={1} weight=\"medium\">\n              {t('structure-error.error.label')}\n            </Text>\n            <Code>{showStack ? formatStack(stack) : error.message}</Code>\n          </Stack>\n\n          {helpId && (\n            <Box marginTop={4}>\n              <Text>\n                <a href={generateHelpUrl(helpId)} rel=\"noopener noreferrer\" target=\"_blank\">\n                  {t('structure-error.docs-link.text')}\n                </a>\n              </Text>\n            </Box>\n          )}\n\n          <Box marginTop={4}>\n            <Button\n              text={t('structure-error.reload-button.text')}\n              icon={SyncIcon}\n              tone=\"primary\"\n              onClick={handleReload}\n            />\n          </Box>\n        </Card>\n      </Container>\n    </Card>\n  )\n}\n", "import {Box, Text} from '@sanity/ui'\nimport {isRecord, Translate, useTranslation} from 'sanity'\n\nimport {Pane, PaneContent, PaneHeader} from '../../components/pane'\nimport {structureLocaleNamespace} from '../../i18n'\n\ninterface UnknownPaneProps {\n  isSelected: boolean\n  pane: unknown\n  paneKey: string\n}\n\n/**\n * @internal\n */\nexport function UnknownPane(props: UnknownPaneProps) {\n  const {isSelected, pane, paneKey} = props\n  const type = (isRecord(pane) && pane.type) || null\n  const {t} = useTranslation(structureLocaleNamespace)\n  return (\n    <Pane id={paneKey} selected={isSelected}>\n      <PaneHeader title={t('panes.unknown-pane-type.title')} />\n      <PaneContent>\n        <Box padding={4}>\n          {typeof type === 'string' ? (\n            <Text as=\"p\" muted>\n              <Translate\n                t={t}\n                i18nKey=\"panes.unknown-pane-type.unknown-type.text\"\n                values={{type}}\n              />\n            </Text>\n          ) : (\n            <Text as=\"p\" muted>\n              <Translate t={t} i18nKey=\"panes.unknown-pane-type.missing-type.text\" />\n            </Text>\n          )}\n        </Box>\n      </PaneContent>\n    </Pane>\n  )\n}\n", "import {isEqual} from 'lodash'\nimport {lazy, memo, Suspense} from 'react'\n\nimport {PaneRouterProvider} from '../components/paneRouter'\nimport {type PaneNode} from '../types'\nimport {LoadingPane} from './loading'\nimport {UnknownPane} from './unknown'\n\ninterface StructureToolPaneProps {\n  active: boolean\n  childItemId: string | null\n  groupIndex: number\n  index: number\n  itemId: string\n  pane: PaneNode\n  paneKey: string\n  params: Record<string, string | undefined> & {perspective?: string}\n  payload: unknown\n  path: string\n  selected: boolean\n  siblingIndex: number\n}\n\n// TODO: audit this creates separate chunks\nconst paneMap = {\n  component: lazy(() => import('./userComponent')),\n  document: lazy(() => import('./document/pane')),\n  documentList: lazy(() => import('./documentList/pane')),\n  list: lazy(() => import('./list')),\n}\n\n/**\n * NOTE: The same pane might appear multiple times (split pane), so use index as tiebreaker\n *\n * @internal\n */\nexport const StructureToolPane = memo(\n  function StructureToolPane(props: StructureToolPaneProps) {\n    const {\n      active,\n      childItemId,\n      groupIndex,\n      index,\n      itemId,\n      pane,\n      paneKey,\n      params,\n      payload,\n      path,\n      selected,\n      siblingIndex,\n    } = props\n\n    const PaneComponent = paneMap[pane.type] || UnknownPane\n\n    return (\n      <PaneRouterProvider\n        flatIndex={index}\n        index={groupIndex}\n        params={params}\n        payload={payload}\n        siblingIndex={siblingIndex}\n      >\n        <Suspense fallback={<LoadingPane paneKey={paneKey} path={path} selected={selected} />}>\n          <PaneComponent\n            childItemId={childItemId || ''}\n            index={index}\n            itemId={itemId}\n            isActive={active}\n            isSelected={selected}\n            paneKey={paneKey}\n            // @ts-expect-error TS doesn't know how to handle this intersection\n            pane={pane}\n          />\n        </Suspense>\n      </PaneRouterProvider>\n    )\n  },\n  (\n    {params: prevParams = {}, payload: prevPayload = null, ...prev},\n    {params: nextParams = {}, payload: nextPayload = null, ...next},\n  ) => {\n    // deeply compare these objects (it's okay, they're small)\n    if (!isEqual(prevParams, nextParams)) return false\n    if (!isEqual(prevPayload, nextPayload)) return false\n\n    const keys = new Set([...Object.keys(prev), ...Object.keys(next)]) as Set<\n      keyof typeof next | keyof typeof prev\n    >\n\n    // then shallow equal the rest\n    for (const key of keys) {\n      if (prev[key] !== next[key]) return false\n    }\n\n    return true\n  },\n)\n", "import {WarningOutlineIcon} from '@sanity/icons'\nimport {<PERSON>, Card, Container, Flex, Stack, Text} from '@sanity/ui'\nimport {useTranslation} from 'sanity'\n\nimport {structureLocaleNamespace} from '../../i18n'\n\nexport function NoDocumentTypesScreen() {\n  const {t} = useTranslation(structureLocaleNamespace)\n\n  return (\n    <Card height=\"fill\">\n      <Flex align=\"center\" height=\"fill\" justify=\"center\" padding={4} sizing=\"border\">\n        <Container width={0}>\n          <Card padding={4} radius={2} shadow={1} tone=\"caution\">\n            <Flex>\n              <Box>\n                <Text size={1}>\n                  <WarningOutlineIcon />\n                </Text>\n              </Box>\n              <Stack flex={1} marginLeft={3} space={3}>\n                <Text as=\"h1\" size={1} weight=\"medium\">\n                  {t('no-document-types-screen.title')}\n                </Text>\n                <Text as=\"p\" muted size={1}>\n                  {t('no-document-types-screen.subtitle')}\n                </Text>\n                <Text as=\"p\" muted size={1}>\n                  <a\n                    href=\"https://www.sanity.io/docs/create-a-schema-and-configure-sanity-studio\"\n                    target=\"_blank\"\n                    rel=\"noreferrer\"\n                  >\n                    {t('no-document-types-screen.link-text')}\n                  </a>\n                </Text>\n              </Stack>\n            </Flex>\n          </Card>\n        </Container>\n      </Flex>\n    </Card>\n  )\n}\n", "import {type ObjectSchemaType} from '@sanity/types'\nimport {useEffect} from 'react'\nimport {\n  unstable_useValuePreview as useValuePreview,\n  useEditState,\n  usePerspective,\n  useSchema,\n  useTranslation,\n} from 'sanity'\n\nimport {LOADING_PANE} from '../../constants'\nimport {structureLocaleNamespace} from '../../i18n'\nimport {type Panes} from '../../structureResolvers'\nimport {type DocumentPaneNode} from '../../types'\nimport {useStructureTool} from '../../useStructureTool'\n\ninterface StructureTitleProps {\n  resolvedPanes: Panes['resolvedPanes']\n}\n\n// TODO: Fix state jank when editing different versions inside panes.\nconst DocumentTitle = (props: {documentId: string; documentType: string}) => {\n  const {documentId, documentType} = props\n  const {selectedReleaseId} = usePerspective()\n\n  const editState = useEditState(documentId, documentType, 'default', selectedReleaseId)\n  const schema = useSchema()\n  const {t} = useTranslation(structureLocaleNamespace)\n  const isNewDocument = !editState?.published && !editState?.draft\n  const documentValue = editState?.version || editState?.draft || editState?.published\n  const schemaType = schema.get(documentType) as ObjectSchemaType | undefined\n\n  const {value, isLoading: previewValueIsLoading} = useValuePreview({\n    enabled: true,\n    schemaType,\n    value: documentValue,\n  })\n\n  const documentTitle = isNewDocument\n    ? t('browser-document-title.new-document', {\n        schemaType: schemaType?.title || schemaType?.name,\n      })\n    : value?.title || t('browser-document-title.untitled-document')\n\n  const settled = editState.ready && !previewValueIsLoading\n  const newTitle = useConstructDocumentTitle(documentTitle)\n  useEffect(() => {\n    if (!settled) return\n    // Set the title as the document title\n    document.title = newTitle\n  }, [documentTitle, settled, newTitle])\n\n  return null\n}\n\nconst PassthroughTitle = (props: {title?: string}) => {\n  const {title} = props\n  const newTitle = useConstructDocumentTitle(title)\n  useEffect(() => {\n    // Set the title as the document title\n    document.title = newTitle\n  }, [newTitle, title])\n  return null\n}\n\nexport const StructureTitle = (props: StructureTitleProps) => {\n  const {resolvedPanes} = props\n\n  if (!resolvedPanes?.length) return null\n\n  const lastPane = resolvedPanes[resolvedPanes.length - 1]\n\n  // If the last pane is loading, display the structure tool title only\n  if (isLoadingPane(lastPane)) {\n    return <PassthroughTitle />\n  }\n\n  // If the last pane is a document\n  if (isDocumentPane(lastPane)) {\n    // Passthrough the document pane's title, which may be defined in structure builder\n    if (lastPane?.title) {\n      return <PassthroughTitle title={lastPane.title} />\n    }\n\n    // Otherwise, display a `document.title` containing the resolved Sanity document title\n    return <DocumentTitle documentId={lastPane.options.id} documentType={lastPane.options.type} />\n  }\n\n  // Otherwise, display the last pane's title (if present)\n  return <PassthroughTitle title={lastPane?.title} />\n}\n\n/**\n * Construct a pipe delimited title containing `activeTitle` (if applicable) and the base structure title.\n *\n * @param activeTitle - Title of the first segment\n *\n * @returns A pipe delimited title in the format `${activeTitle} | %BASE_STRUCTURE_TITLE%`\n * or simply `%BASE_STRUCTURE_TITLE` if `activeTitle` is undefined.\n */\nfunction useConstructDocumentTitle(activeTitle?: string) {\n  const structureToolBaseTitle = useStructureTool().structureContext.title\n  return [activeTitle, structureToolBaseTitle].filter((title) => title).join(' | ')\n}\n\n// Type guards\nfunction isDocumentPane(pane: Panes['resolvedPanes'][number]): pane is DocumentPaneNode {\n  return pane !== LOADING_PANE && pane.type === 'document'\n}\n\nfunction isLoadingPane(pane: Panes['resolvedPanes'][number]): pane is typeof LOADING_PANE {\n  return pane === LOADING_PANE\n}\n", "import {Portal<PERSON>rovider, useTheme, useToast} from '@sanity/ui'\nimport {isHotkey} from 'is-hotkey-esm'\nimport {Fragment, memo, useCallback, useEffect, useState} from 'react'\nimport {_isCustomDocumentTypeDefinition, useSchema} from 'sanity'\nimport {useRouterState} from 'sanity/router'\nimport {styled} from 'styled-components'\n\nimport {LOADING_PANE} from '../../constants'\nimport {LoadingPane, StructureToolPane} from '../../panes'\nimport {useResolvedPanes} from '../../structureResolvers'\nimport {type PaneNode} from '../../types'\nimport {useStructureTool} from '../../useStructureTool'\nimport {PaneLayout} from '../pane'\nimport {NoDocumentTypesScreen} from './NoDocumentTypesScreen'\nimport {StructureTitle} from './StructureTitle'\n\ninterface StructureToolProps {\n  onPaneChange: (panes: Array<PaneNode | typeof LOADING_PANE>) => void\n}\n\nconst StyledPaneLayout = styled(PaneLayout)`\n  min-height: 100%;\n  min-width: 320px;\n`\n\nconst isSaveHotkey = isHotkey('mod+s')\n\n/**\n * @internal\n */\nexport const StructureTool = memo(function StructureTool({onPaneChange}: StructureToolProps) {\n  const {push: pushToast} = useToast()\n  const schema = useSchema()\n  const {layoutCollapsed, setLayoutCollapsed} = useStructureTool()\n  const {paneDataItems, resolvedPanes} = useResolvedPanes()\n  // Intent resolving is processed by the sibling `<IntentResolver />` but it doesn't have a UI for indicating progress.\n  // We handle that here, so if there are only 1 pane (the root structure), and there's an intent state in the router, we need to show a placeholder LoadingPane until\n  // the structure is resolved and we know what panes to load/display\n  const isResolvingIntent = useRouterState(\n    useCallback((routerState) => typeof routerState.intent === 'string', []),\n  )\n  const {\n    sanity: {media},\n  } = useTheme()\n\n  const [portalElement, setPortalElement] = useState<HTMLDivElement | null>(null)\n\n  const handleRootCollapse = useCallback(() => setLayoutCollapsed(true), [setLayoutCollapsed])\n  const handleRootExpand = useCallback(() => setLayoutCollapsed(false), [setLayoutCollapsed])\n\n  useEffect(() => {\n    // we check for length before emitting here to skip the initial empty array\n    // state from the `useResolvedPanes` hook. there should always be a root\n    // pane emitted on subsequent emissions\n    if (resolvedPanes.length) {\n      onPaneChange(resolvedPanes)\n    }\n  }, [onPaneChange, resolvedPanes])\n\n  useEffect(() => {\n    const handleGlobalKeyDown = (event: KeyboardEvent) => {\n      // Prevent `Cmd+S`\n      if (isSaveHotkey(event)) {\n        event.preventDefault()\n\n        pushToast({\n          closable: true,\n          id: 'auto-save-message',\n          status: 'info',\n          title: 'Your work is automatically saved!',\n          duration: 4000,\n        })\n      }\n    }\n\n    window.addEventListener('keydown', handleGlobalKeyDown)\n    return () => window.removeEventListener('keydown', handleGlobalKeyDown)\n  }, [pushToast])\n\n  const hasDefinedDocumentTypes = schema._original?.types.some(_isCustomDocumentTypeDefinition)\n\n  if (!hasDefinedDocumentTypes) {\n    return <NoDocumentTypesScreen />\n  }\n\n  return (\n    <PortalProvider element={portalElement || null}>\n      <StyledPaneLayout\n        flex={1}\n        height={layoutCollapsed ? undefined : 'fill'}\n        minWidth={media[1]}\n        onCollapse={handleRootCollapse}\n        onExpand={handleRootExpand}\n      >\n        {paneDataItems.map(\n          ({\n            active,\n            childItemId,\n            groupIndex,\n            itemId,\n            key: paneKey,\n            pane,\n            index: paneIndex,\n            params: paneParams,\n            path,\n            payload,\n            siblingIndex,\n            selected,\n          }) => (\n            <Fragment key={`${pane === LOADING_PANE ? 'loading' : pane.type}-${paneIndex}`}>\n              {pane === LOADING_PANE ? (\n                <LoadingPane paneKey={paneKey} path={path} selected={selected} />\n              ) : (\n                <StructureToolPane\n                  active={active}\n                  groupIndex={groupIndex}\n                  index={paneIndex}\n                  pane={pane}\n                  childItemId={childItemId}\n                  itemId={itemId}\n                  paneKey={paneKey}\n                  params={paneParams}\n                  payload={payload}\n                  path={path}\n                  selected={selected}\n                  siblingIndex={siblingIndex}\n                />\n              )}\n            </Fragment>\n          ),\n        )}\n        {/* If there's just 1 pane (the root), or less, and we're resolving an intent then it's necessary to show */}\n        {/* a loading indicator as the intent resolving is async, could take a while and can also be interrupted/redirected */}\n        {paneDataItems.length <= 1 && isResolvingIntent && (\n          <LoadingPane paneKey=\"intent-resolver\" />\n        )}\n      </StyledPaneLayout>\n      <StructureTitle resolvedPanes={resolvedPanes} />\n      <div data-portal=\"\" ref={setPortalElement} />\n    </PortalProvider>\n  )\n})\n", "import {useEffect, useState} from 'react'\nimport {SourceProvider, type Tool, useWorkspace} from 'sanity'\n\nimport {ErrorBoundary} from '../../../ui-components/errorBoundary'\nimport {setActivePanes} from '../../getIntentState'\nimport {StructureToolProvider} from '../../StructureToolProvider'\nimport {type StructureToolOptions} from '../../types'\nimport {IntentResolver} from './intentResolver'\nimport {StructureError} from './StructureError'\nimport {StructureTool} from './StructureTool'\n\ninterface StructureToolBoundaryProps {\n  tool: Tool<StructureToolOptions>\n}\n\nexport function StructureToolBoundary({tool: {options}}: StructureToolBoundaryProps) {\n  const {unstable_sources: sources} = useWorkspace()\n  const [firstSource] = sources\n  const {source, defaultDocumentNode, structure} = options || {}\n\n  // Set active panes to blank on mount and unmount\n  useEffect(() => {\n    setActivePanes([])\n    return () => setActivePanes([])\n  }, [])\n\n  const [{error}, setError] = useState<{error: unknown}>({error: null})\n\n  // this re-throws if the error it catches is not a PaneResolutionError\n  if (error) return <StructureError error={error} />\n\n  return (\n    <ErrorBoundary onCatch={setError}>\n      <SourceProvider name={source || firstSource.name}>\n        <StructureToolProvider defaultDocumentNode={defaultDocumentNode} structure={structure}>\n          <StructureTool onPaneChange={setActivePanes} />\n          <IntentResolver />\n        </StructureToolProvider>\n      </SourceProvider>\n    </ErrorBoundary>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAMA,aAAsB,CAAE;AAKvB,SAAAC,mBAAAC,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GAQL;IAAAC;IAAAC;IAAAC;IAAAC;IAAAC;IAAAC;EAAAA,IAAoER,OACpE;IAAAS;IAAAC;IAAAC;EAAyDC,IAAAA,UACzDC,GAAAA,cAAoBC,eAAAA,GACpB;IAAAC;IAAAC;EAAAA,IAAwBC,cAAc;AAGxBC,MAAAA;AAAAA,QADLL,2CAAWE,UAAAjB;AADpB,QAAAqB,mBAA4CD;AAG3CE,MAAAA;AAC8BL,OAAAA,+BAAQA,MAAKM,SAAW;AAAvDC,QAAAA,WAAiBF,IAEjBG,aAAmBlB,QAAS;AAAAmB,MAAAA;AAAAvB,IAAA,CAAA,MAAAsB,cAAAtB,EAAAkB,CAAAA,MAAAA,oBAAAlB,EAAAY,CAAAA,MAAAA,eAAAZ,EAAAA,CAAAA,MAAAO,gBAG1BgB,KAAAC,CAAA,aAAA;AACE,UAAAC,eAAqBP,iBAAiBI,UAAU,KAAO,CAAA,GACvDI,cAAoBD,aAAalB,YAAY,GAC7CoB,YAAkBH,SAASC,cAAcC,WAAW,GACpDE,YACKV,CAAAA,GAAAA,iBAAgBW,MAAAA,GAAUP,UAAU,GACvCK,WAAAA,GACGT,iBAAgBW,MAAOP,aAAAA,CAAc,CAAC;AAE3C,WAAA;MAAA,GAA4BV;MAAWE,OAASc;IAAS;EAAA,GAG1D5B,EAAAA,CAAAA,IAAAsB,YAAAtB,EAAAA,CAAAA,IAAAkB,kBAAAlB,EAAAA,CAAAA,IAAAY,aAAAZ,EAAAA,CAAAA,IAAAO,cAAAP,EAAAA,CAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,CAAA;AAbH,QAAA8B,wBAA8BP;AAe7BQ,MAAAA;AAAA/B,IAAA8B,CAAAA,MAAAA,yBAAA9B,EAAAA,CAAAA,MAAAQ,YAGCuB,KAAAC,CAAA,eAAA;AACEC,UAAAA,oBAAwBH,sBAAsBN,UAAQ;AACtDU,WAAAA,WAAAA,MAAiB1B,SAAS2B,iBAAe,GAAA,CAAI,GACtCA;EACRnC,GAAAA,EAAAA,CAAAA,IAAA8B,uBAAA9B,EAAAA,CAAAA,IAAAQ,UAAAR,EAAAA,CAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,CAAA;AALH,QAAAoC,qBAA2BL;AAO1BM,MAAAA;AAAArC,IAAA8B,CAAAA,MAAAA,yBAAA9B,EAAAA,CAAAA,MAAAU,wBAAAV,EAAA,EAAA,MAAAO,gBAGC8B,KAAAC,CAAA,eAAA;AACEC,UAAAA,oBAAwBT,sBAAqB,CAAAU,UAAAC,SAAA,CAAA,GACxCD,SAAQX,MAAA,GAAUtB,YAAY,GAAC;MAAA,GAC9BkC;MAAIpC,QAAUiC;IAAAA,GACfE,GAAAA,SAAQX,MAAOtB,eAAgB,CAAA,CAAC,CACpC;AAAC,WAEKG,qBAAqByB,iBAAe;EAAA,GAC5CnC,EAAAA,CAAAA,IAAA8B,uBAAA9B,EAAAA,CAAAA,IAAAU,sBAAAV,EAAAA,EAAAA,IAAAO,cAAAP,EAAAA,EAAAA,IAAAqC,MAAAA,KAAArC,EAAA,EAAA;AATH,QAAA0C,uBAA6EL;AAW5EM,MAAAA;AAAA3C,IAAAoC,EAAAA,MAAAA,sBAAApC,EAAAA,EAAAA,MAAAO,gBAGCoC,KAAAC,CAAA,gBAAA;AACoB,uBAAA,CAAAC,YAAAC,WAAA,CAAA,GACbN,WAAQX,MAAUtB,GAAAA,YAAY,GAAC;MAAA,GAC9BkC;MAAInC,SAAWsC;IAAAA,GAChBJ,GAAAA,WAAQX,MAAOtB,eAAgB,CAAA,CAAC,CACpC;EACFP,GAAAA,EAAAA,EAAAA,IAAAoC,oBAAApC,EAAAA,EAAAA,IAAAO,cAAAP,EAAAA,EAAAA,IAAA2C,MAAAA,KAAA3C,EAAA,EAAA;AAPH,QAAA+C,aAAyDJ;AASxDK,MAAAA;AAAAhD,IAAAoC,EAAAA,MAAAA,sBAAApC,EAAAA,EAAAA,MAAAO,gBAGCyC,KAAAC,CAAA,iBAAA;AACoB,uBAAA,CAAAC,YAAAC,WAAA,CAAA,GACbX,WAAQX,MAAUtB,GAAAA,YAAY,GAAC;MAAA,GAC9BkC;MAAIpC,QAAUiC;IAAAA,GACfE,GAAAA,WAAQX,MAAOtB,eAAgB,CAAA,CAAC,CACpC;EACFP,GAAAA,EAAAA,EAAAA,IAAAoC,oBAAApC,EAAAA,EAAAA,IAAAO,cAAAP,EAAAA,EAAAA,IAAAgD,MAAAA,KAAAhD,EAAA,EAAA;AAPH,QAAAoD,YAAuDJ;AAStDK,MAAAA;AAAArD,IAAAsB,EAAAA,MAAAA,cAAAtB,EAAAA,EAAAA,MAAAQ,YAAAR,EAAA,EAAA,MAAAkB,oBAGCmC,KAAAC,CAAAA,QAAA;AAAC,UAAA;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;IAAAA,IAAAL;AACS,aAAA;MAAAxC,OAAAA,CAAAA,GAEDI,iBAAgBW,MAAUP,GAAAA,aAAAA,CAAc,GAAC,CAAA;QAAAiC;QAAAlD,QAAA;UAAAqD,UAK5BA,SAAQH;UAAAC,eACHI,SAAaJ,aAAa;UAACC;UAAAE;QAAA;QAAArD,SAInCoD,SAAQrD;MAAAA,CAAA,CAAA;IAAA,CAIxB;EAAA,GACFL,EAAAA,EAAAA,IAAAsB,YAAAtB,EAAAA,EAAAA,IAAAQ,UAAAR,EAAAA,EAAAA,IAAAkB,kBAAAlB,EAAAA,EAAAA,IAAAqD,MAAAA,KAAArD,EAAA,EAAA;AAnBH,QAAA6D,sBAA2ER;AAqB1EC,MAAAA;AAoBqBQ,QAAAA,KAAA5C,iBAAiBI,UAAU,IACzCJ,iBAAiBI,UAAU,EAAAF,SAAA,IACtB,OAGI2C,MAAA7C,iBAAiBI,UAAU,IAAIJ,iBAAiBI,UAAU,EAAAF,SAAY,GAUzE4C,MAAA7D,YAAS8D,WAAAC;AAAuB,MAAAC,KAAAC;AAAApE,IAAAA,EAAAA,MAAAoC,sBAa1B+B,MAAAE,CAAAA,SAAA;AAAC,UAAAC,OAAAD,SAASH,SAAAA,CAATG,IAAAA;AACfjC,uBAAkB,MAAA,CAAA;MAAAmB,IACXe,KAAIf,MAAO;MAAEjD,SAAWgE,KAAIhE;MAAAD,QAAkBiE,KAAIjE,UAAA,CAAA;IAAA,CAAa,CACrE;EAAC,GAIU+D,MAAAA,MAAA;AACZhC,uBAAkBmC,OAElB;EAAA,GACDvE,EAAAA,EAAAA,IAAAoC,oBAAApC,EAAAA,EAAAA,IAAAmE,KAAAnE,EAAAA,EAAAA,IAAAoE,QAAAD,MAAAnE,EAAA,EAAA,GAAAoE,MAAApE,EAAA,EAAA;AAAAqE,MAAAA;AAAArE,IAAAe,EAAAA,MAAAA,UAAAf,EAAA,EAAA,MAAAsB,cAAAtB,EAAAqB,EAAAA,MAAAA,YAAArB,EAAA,EAAA,MAAAQ,YAAAR,EAAAA,EAAAA,MAAAkB,oBAGqBmD,MAAAG,CAAAA,SAAA;AAACA,KAAAA,SAAiBN,UAAjBM,SACHnD,YAChBN,OAAOM,SAAQoD,OAAQ,GAEzBjE,SAAQ;MAAAM,OACCI,iBAAgBW,MAAAA,GAAUP,UAAU;IAAA,CAC5C;EAAC,GACHtB,EAAAA,EAAAA,IAAAe,QAAAf,EAAAA,EAAAA,IAAAsB,YAAAtB,EAAAA,EAAAA,IAAAqB,UAAArB,EAAAA,EAAAA,IAAAQ,UAAAR,EAAAA,EAAAA,IAAAkB,kBAAAlB,EAAAA,EAAAA,IAAAqE,OAAAA,MAAArE,EAAA,EAAA;AAAAwE,MAAAA;AAAAxE,IAAAoC,EAAAA,MAAAA,sBAAApC,EAAAA,EAAAA,MAAAO,gBAGiBiE,MAAAE,CAAA,YAAA;AACE,uBAAA,CAAAC,YAAAC,WAAA;AAChB,YAAAC,iBAAA;QAAA,GACKpC;QAAInC,UACEoE,mCAAOpE,YAAamC,OAAInC;QAAQD,SACjCqE,mCAAOrE,WAAYoC,OAAIpC;MAAO;AACvC,aAAA,CAAA,GAGImC,WAAQX,MAAA,GAAUtB,YAAY,GACjCsE,gBAAc,GACXrC,WAAQX,MAAOtB,YAAY,CAAC;IAAA,CAElC;EACFP,GAAAA,EAAAA,EAAAA,IAAAoC,oBAAApC,EAAAA,EAAAA,IAAAO,cAAAP,EAAAA,EAAAA,IAAAwE,OAAAA,MAAAxE,EAAA,EAAA;AAAA8E,MAAAA;AAAA9E,IAAAK,EAAAA,MAAAA,UAAAL,EAAAA,EAAAA,MAAAoD,aAGQ0B,MAAAC,CAAA,WAAA;AACPC,UAAAA,iBAAmBC,YAAAA,SAAK5E,QAAQ,MAAM;AAAC,WAChC+C,UAAU2B,SAAM;MAAA,GAAOC;MAAUE,MAAQH;IAAAA,IAAUC,UAAU;EACrEhF,GAAAA,EAAAA,EAAAA,IAAAK,QAAAL,EAAAA,EAAAA,IAAAoD,WAAApD,EAAAA,EAAAA,IAAA8E,OAAAA,MAAA9E,EAAA,EAAA;AAAAmF,MAAAA;AAAAnF,IAAA,EAAA,MAAA0C,wBAAA1C,EAAAG,EAAAA,MAAAA,aAAAH,EAAAA,EAAAA,MAAAsB,cAAAtB,EAAA,EAAA,MAAA6D,uBAAA7D,EAAAS,EAAAA,MAAAA,kBAAAT,EAAA,EAAA,MAAAK,UAAAL,EAAAM,EAAAA,MAAAA,WAAAN,EAAAA,EAAAA,MAAAkB,oBAAAlB,EAAA,EAAA,MAAAoD,aAAApD,EAAA+C,EAAAA,MAAAA,cAAA/C,EAAAO,EAAAA,MAAAA,gBAAAP,EAAA,EAAA,MAAA+D,OAAA/D,EAAAgE,EAAAA,MAAAA,OAAAhE,EAAAA,EAAAA,MAAAmE,OAAAnE,EAAA,EAAA,MAAAoE,OAAApE,EAAAqE,EAAAA,MAAAA,OAAArE,EAAA,EAAA,MAAAwE,OAAAxE,EAAA8E,EAAAA,MAAAA,OAAA9E,EAAAA,EAAAA,MAAA8D,MAzFIqB,MAAA;IAAA/E,OAEED;IAASmB;IAAAf;IAAAD;IAAAD;IAAA+E,kBAeEtB;IAETuB,aAGItB;IAAsEuB,kBAGjEpE;IAAgBqE;IAAAtB,UAOxBD;IAAgCwB;IAAA3B;IAAA4B;IAAAC,gBAa1BvB;IAIfwB,cAGavB;IAIbwB,sBAGqBvB;IAOrBwB,kBAGiBrB;IAcjBsB,SAGQhB;IAGR1B;IAAAL;IAAAL;IAAAjC;EAAAA,GAaFT,EAAAA,EAAAA,IAAA0C,sBAAA1C,EAAAA,EAAAA,IAAAG,WAAAH,EAAAA,EAAAA,IAAAsB,YAAAtB,EAAAA,EAAAA,IAAA6D,qBAAA7D,EAAAA,EAAAA,IAAAS,gBAAAT,EAAAA,EAAAA,IAAAK,QAAAL,EAAAA,EAAAA,IAAAM,SAAAN,EAAAA,EAAAA,IAAAkB,kBAAAlB,EAAAA,EAAAA,IAAAoD,WAAApD,EAAAA,EAAAA,IAAA+C,YAAA/C,EAAAA,EAAAA,IAAAO,cAAAP,EAAAA,EAAAA,IAAA+D,KAAA/D,EAAAA,EAAAA,IAAAgE,KAAAhE,EAAAA,EAAAA,IAAAmE,KAAAnE,EAAAA,EAAAA,IAAAoE,KAAApE,EAAAA,EAAAA,IAAAqE,KAAArE,EAAAA,EAAAA,IAAAwE,KAAAxE,EAAAA,EAAAA,IAAA8E,KAAA9E,EAAAA,EAAAA,IAAA8D,IAAA9D,EAAAA,EAAAA,IAAAmF,OAAAA,MAAAnF,EAAA,EAAA,GAAAsD,KAtGM6B;AADT,QAAAY,MAAoCzC;AAyHnC0C,MAAAA;AAAA,SAAAhG,EAAAE,EAAAA,MAAAA,YAAAF,EAAAA,EAAAA,MAAA+F,OAEMC,UAAA,wBAAA,kBAAA,UAAA,EAAmCD,OAAAA,KAAM7F,SAAAA,CAAS,GAA6BF,EAAAA,EAAAA,IAAAE,UAAAF,EAAAA,EAAAA,IAAA+F,KAAA/F,EAAAA,EAAAA,IAAAgG,OAAAA,MAAAhG,EAAA,EAAA,GAA/EgG;AAA+E;AAnOjF,SAAAzB,QAAA0B,YAAAC,QAAA;AA+JG1D,SAAAA,WAAQpB,SAAW,IAAGoB,WAAQ2D,OAAAC,CAAqBA,YAAAA,YAAY3D,MAAI,IAAID;AAAQ;ACpKlF,IAAM6D,sBAAN,cAAkCC,MAAM;EAK7CC,YAAY;IAACC;IAASC;IAASC;IAAQC;EAAAA,GAAoC;AACzE,UAAMH,OAAO,GACb,KAAKI,OAAO,uBACZ,KAAKH,UAAUA,SACf,KAAKC,SAASA,QACd,KAAKC,QAAQA;EAAAA;AAEjB;ACvBA,IAAME,gBAAAA,oBAAoBC,QAAwB;AAO3C,SAASC,SAASC,KAAqB;AACtCC,QAAAA,cAAcJ,cAAcK,IAAIF,GAAG;AACzC,MAAIC,YAAoBA,QAAAA;AAExB,QAAM1D,KAAK4D,OAAO;AACJC,SAAAA,cAAAA,IAAIJ,KAAKzD,EAAE,GAClBA;AACT;ACNA,IAAM8D,YAAaC,CACV,UAAA,CAAC,CAACA,SAAS,QAAOA,+BAAOC,SAAS;AAD3C,IAGMC,iBAAkBF,CACjBG,UAAAA,WAASH,KAAK,IACZ,OAAOA,MAAMI,aAAc,aADL;AAJ/B,IAoBMC,kCACHC,CAAS,SAAA,CAACC,gBAAgBpB,SAAStG,cAAc;AAC5C,MAAA;AACKyH,WAAAA,KAAKC,gBAAgBpB,SAAStG,SAAS;EAAA,SACvC2H,GAAG;AAEV,UAAIA,aAAazB,sBACTyB,IAKF,IAAIzB,oBAAoB;MAC5BG,SAAS,QAAOsB,uBAAGtB,YAAY,WAAWsB,EAAEtB,UAAU;MACtDC;MACAE,OAAOmB;IAAAA,CACR;EAAA;AAEL;AAtCF,IAwCMC,wBACHH,CACD,SAAA,IAAII,SACKJ,KAAK,GAAGI,IAAI,EAAEC;;;;EAInBC,cAAc,CAAC;EACfC,SAAS;AACX;AAGG,SAASC,mBAAmBC,YAAkD;AAKnF,QAAMC,cAAcX,gCAClBI,sBACEM,WAAW,CAACR,gBAAgBpB,SAAStG,cAAc;AACjD,QAAI,CAAC0H;AACH,YAAM,IAAIxB,oBAAoB;QAC5BG,SAAS;QACTC;QACAC,QAAQ;MAAA,CACT;AAGH,WAAIW,UAAUQ,cAAc,KAAKU,aAAaV,cAAc,IACnDW,KAAKX,cAAc,EAAEI,KAC1BQ,UAAWC,CAAAA,WAAWJ,YAAYI,QAAQjC,SAAStG,SAAS,CAAC,CAC/D,IAGEqH,eAAeK,cAAc,IACxBS,YAAYT,eAAeH,UAAUjB,OAAO,GAAGA,SAAStG,SAAS,IAGtE,OAAO0H,kBAAmB,aACrBS,YAAYT,eAAepB,QAAQlD,IAAIkD,OAAO,GAAGA,SAAStG,SAAS,IAGrEwI,GAAad,cAAc;EACnC,CAAA,CACH,CACF;AAEOS,SAAAA;AACT;AChGA,IAAMM,YAAAA,oBAAgB9B,QAAuC;AAU7C+B,SAAAA,SAAS7B,KAA8B8B,WAA6B;AAClF,QAAMC,eAAeH,UAAU1B,IAAIF,GAAG,KAAA,oBAASgC,IAAsB;AACrE,MAAID,cAAc;AACVE,UAAAA,SAAQF,aAAa7B,IAAI4B,SAAS;AACxC,QAAIG,OAAcA,QAAAA;EAAAA;AAGdC,QAAAA,SAASlC,IAAI8B,SAAS;AAE5B,MAAI,OAAOI,UAAW;AACpB,UAAM,IAAI5C,MACR,uBAAuBwC,SAAS,+BAA+B,OAAOI,MAAM,WAC9E;AAGID,QAAAA,QAAQC,OAAOC,KAAKnC,GAAG;AAChBI,SAAAA,aAAAA,IAAI0B,WAAWG,KAAK,GACjCL,UAAUxB,IAAIJ,KAAK+B,YAAY,GAExBE;AACT;ACeA,eAAsBG,cAAc1E,SAAqD;AACjF2E,QAAAA,oBAAwBL,oBAAAA,IAaxBV,GAAAA,cAAcF,mBAVqBkB,CAAW,WAAA,CAACzB,gBAAgBpB,SAAStG,cAAc;AAC1F,UAAMoJ,MAAM1B,kBAAkB,GAAGd,SAASc,cAAc,CAAC,IAAIpB,QAAQ+C,KAAKC,KAAK,IAAI,CAAC,IAC9EC,qBAAqBH,OAAOF,kBAAkBnC,IAAIqC,GAAG;AAC3D,QAAIG,mBAA2BA,QAAAA;AAE/B,UAAMhB,SAASY,OAAOzB,gBAAgBpB,SAAStG,SAAS;AACxD,WAAIoJ,OAAKF,kBAAkBjC,IAAImC,KAAKb,MAAM,GACnCA;EAAAA,CAGqC,GAExCiB,sBAAmC,CACvC,CACE;IACEpG,IAAI,WAAWmB,QAAQrE,OAAOkD,EAAE;IAChClD,QAAQ;MAAC,OAAG4E,YAAAA,SAAKP,QAAQrE,QAAQ,CAAC,IAAI,CAAC;MAAGoD,MAAMiB,QAAQrE,OAAOoD;IAAI;IACnEnD,SAASoE,QAAQpE;EAAAA,CAClB,CACF;AAGH,iBAAesJ,SAAS;IACtBC;IACA1J;IACA2J;IACAzJ;IACA0J,QAAAA;IACAP;IACAlJ;IACAuH;IACAmC;IACAC;EAAAA,GAGA;;AACI,QAAA,CAACpC,eAAgB,QAAO,CAAE;AAExB,UAAA;MAACtE,IAAI2G;MAAUzG,MAAM0G;MAAgB,GAAGC;IAAAA,IAAe/J,QAWvDgK,eAAe,MAAMC,eAAehC,YAAYT,gBAVZ;MACxCtE,IAAIsG;MACJU,YAAY;MACZR,QAAAA;MACAP;MACApJ,OAAOD;MACPE,QAAQ,CAAC;MACTC,SAAS4D;MACT+F;IACF,GAC+E9J,SAAS,CAAC;AAIzF,WAAIkK,aAAa5G,SAAS,cAAc4G,aAAa9G,OAAO2G,WACnD,CACL;MACEpJ,OAAO,CACL,GAAG0I,KAAK3H,MAAM,GAAG2H,KAAKpI,SAAS,CAAC,EAAEoJ,IAAKC,CAAAA,MAAM,CAAC;QAAClH,IAAIkH;MAAE,CAAA,CAAC,GACtD,CAAC;QAAClH,IAAI2G;QAAU7J,QAAQ+J;QAAa9J;MAAAA,CAAQ,CAAC;MAEhDoK,YAAYlB,KAAKpI;MACjB4I;IAAAA,CACD;;QAQHK,kBAAaM,oBAAbN,sCAA+BP,QAAQzJ,QAAQ;QAC7CuK,MAAMP;QACNjK,OAAOD;MAAAA;;MAIRkK,aAAa5G,SAAS;MAErB4G,aAAaF,mBAAmBA;;;;;MAMhCE,aAAa3F,QAAQyB,WAAW,mBAE3B,CACL;QACErF,OAAO;;UAEL,GAAG0I,KAAKgB,IAAKjH,CAAAA,OAAO,CAAC;YAACA;UAAAA,CAAG,CAAC;;UAE1B,CAAC;YAACA,IAAIlD,OAAOkD;YAAIlD,QAAQ+J;YAAa9J;UAAQ,CAAA;QAAC;QAEjDoK,YAAYlB,KAAKpI;QACjB4I;MACD,CAAA,IAIDK,aAAa5G,SAAS,UAAU4G,aAAaQ,SAASR,aAAaS,SAEnE,MAAMC,QAAQC,IACZX,aAAaS,MAAMN,IAAI,CAAC/H,MAAMwI,mBACxBxI,KAAKgB,SAAS,YAAkBsH,QAAQG,QAAQ,CAAE,CAAA,IAE/CtB,SAAS;QACdC,WAAWpH,KAAK0I,OAAO1I,KAAKc;QAC5BpD,WAAWA,YAAY;QACvB2J;QACAzJ;QACA0J,QAAQM;QACRb,MAAM,CAAC,GAAGA,MAAM/G,KAAKc,EAAE;QACvBjD;QACAuH,gBACE,OAAOwC,aAAaQ,SAAU,aAC1BhC,SAASwB,cAAc,OAAO,IAC9BA,aAAaQ;QACnBb,YAAYiB;QACZhB;MAAAA,CACD,CACF,CACH,GACAmB,KAAAA,IAGG,CAAA;;EAAE;AAgBLC,QAAAA,qBAbgB,MAAMzB,SAAS;IACnCC,WAAW;IACX1J,WAAW;IACX6J,YAAY;IACZF,QAAQpF,QAAQoF;IAChBzJ,QAAQqE,QAAQrE;IAChB0J,QAAQ;IACRP,MAAM,CAAE;IACRlJ,SAASoE,QAAQpE;IACjBuH,gBAAgBnD,QAAQ4G;IACxBrB,kBAAkBvF,QAAQuF;EAAAA,CAC3B,GAEuCsB,KAAK,CAACC,GAAGC,MAE3CD,EAAEd,eAAee,EAAEf,aAAmBc,EAAExB,aAAayB,EAAEzB,aACpDwB,EAAEd,aAAae,EAAEf,UACzB,EAAE,CAAC;AAEAW,SAAAA,oBACKA,kBAAkBvK,QAGpB6I;AACT;ACjLA,IAAM+B,sBAAwCA,CAACC,QAAQlF,YAA8B;AACnF,QAAMlD,KAAKoI,OAAOC,QAAQ,aAAa,EAAE,GACnC;IACJvL;IACAC;IACA2J,kBAAkB;MAAC4B;IAAAA;EAAAA,IACjBpF,SACE;IAAChD;IAAMC;EAAAA,IAAYrD;AAEzB,MAAI,CAACoD;AACH,UAAM,IAAI6C,MACR,sCAAsC/C,EAAE,yCAC1C;AAGF,MAAIuI,yBAAyBD,oBAAoB;IAACE,YAAYtI;IAAMuI,YAAYzI;EAAAA,CAAG,EAAEA,GAAG,QAAQ;AAE5FG,SAAAA,aACFoI,yBAAyBA,uBAAuBG,qBAC9CvI,UACApD,OACF,IAGKwL,uBAAuBpE,UAAU;AAC1C;AAMA,SAASwE,YAAYzF,SAA2C;;AACvD,SAAA,eAAe0F,KAAKC,UAAU;IACnC7I,IAAIkD,QAAQlD;IACZ8I,UAAUtC,UAAUhD,SAASgD,MAAM;IACnCP,MAAM/C,QAAQ+C;IACdpJ,OAAOqG,QAAQrG;IACfmK,YAAY9D,QAAQ8D;IACpB+B,wBAAuB7F,aAAQ8F,qBAAR9F,mBAA0BrG;IACjDoM,uBAAsB/F,aAAQ8F,qBAAR9F,mBAA0B+C;EACjD,CAAA,CAAC;AACJ;AAMA,IAAMiD,uBAAwBC,CAAmC,SAAA;AAC/D,QAAMC,aAAa;IACjBlJ,MAAMiJ,KAAKjJ;IACXF,IAAImJ,KAAKE,kBAAkBrJ;IAC3BlD,QAAQqM,KAAKE,kBAAkBvM,UAAU,CAAC;IAC1CC,SAASoM,KAAKE,kBAAkBtM,WAAW;IAC3CH,WAAWuM,KAAKvM;IAChBmB,YAAYoL,KAAKpL;IACjBf,cAAcmM,KAAKnM;IACnBiJ,MAAMkD,KAAKlD;IACXqD,UAAUH,KAAKjJ,SAAS,iBAAiBsD,SAAS2F,KAAKG,QAAQ,IAAI;EACrE;AAEA,SAAO,YAAYV,KAAKC,UAAUO,UAAU,CAAC;AAC/C;AAkFA,SAASG,gBAAgB;EACvBjF;EACAkF;EACAhD,QAAAA;EACAP;EACAlB;EACA2B;AACsB,GAAmC;AACnD,QAAA,CAAC+C,SAAS,GAAGC,IAAI,IAAIF,sBACrBnF,OAAOqF,KAAK,CAAC,GAEbxG,UAAoC;IACxClD,IAAIyJ,QAAQJ,kBAAkBrJ;IAC9BgH,YAAYyC,QAAQzM;IACpBwJ,QAAAA;IACAP,MAAM,CAAC,GAAGA,MAAMwD,QAAQJ,kBAAkBrJ,EAAE;IAC5CnD,OAAO4M,QAAQ7M;IACfE,QAAQ2M,QAAQJ,kBAAkBvM,UAAU,CAAC;IAC7CC,SAAS0M,QAAQJ,kBAAkBtM;IACnC2J;EACF;AAEI,MAAA;AACF,WAAO3B,YAAYT,gBAAgBpB,SAASuG,QAAQ7M,SAAS,EAAE8H;;MAE7DQ,UAAWoE,CAAa,aAAA;AAEtB,cAAMK,mBAAqC;UACzCzJ,MAAM;UACN,GAAGuJ;UACHH;UACArD,MAAM/C,QAAQ+C;QAAAA,GAIV2D,eAAeF,KAAKzC,IAAI,CAACC,GAAG2C,eAMM;UACpC3J,MAAM;UACN+F,MAPsB,CACtB,GAAG/C,QAAQ+C,MACX,GAAGyD,KAAKpL,MAAMuL,SAAS,EAAE5C,IAAI,CAAC6C,GAAGC,iBAAiB,IAAI7C,EAAEtK,YAAYmN,YAAY,GAAG,CAAC;UAMpFT,UAAU;UACV,GAAGpC;QAAAA,EAIN;AAED,YAAI,CAACwC,KAAK7L;AACDuH,iBAAAA,GAAa,CAACuE,gBAAgB,CAAC;AAGpCK,YAAAA;AAEJ;;WAEE3F,6BAAMgF,kBAAkBrJ,GAAGiK,WAAW,eAEtCD,aAAaT,gBAAgB;YAC3BjF,gBAAgB6D;YAChBqB,sBAAsBE;YACtBlD,QAAAA;YACAP,MAAM/C,QAAQ+C;YACdlB;YACA2B;UAAAA,CACD,IACQ+C,QAAQ1L,gBAAesG,6BAAMtG,cAItCiM,aAAaT,gBAAgB;YAC3BjF;YACAkF,sBAAsBE;YACtBlD,QAAAA;YACAP;YACAlB;YACA2B;UAAAA,CACD,IAGDsD,aAAaT,gBAAgB;YAC3BjF,gBACE,OAAOgF,SAAShC,SAAU,aACrBhC,SAASgE,UAAU,OAAO,IAC3BA,SAAShC;YACfkC,sBAAsBE;YACtBlD,QAAQ8C;YACRrD,MAAM/C,QAAQ+C;YACdlB;YACA2B;UACD,CAAA,GAGIwD;;YAEL9E,GAAa,CAACuE,kBAAkB,GAAGC,YAAY,CAAC;;YAEhDI,WAAWtF,KAAKuC,IAAKkD,CAAAA,sBAAsB,CAACR,kBAAkB,GAAGQ,iBAAiB,CAAC,CAAC;UACtF;;MACD,CAAA;IACH;EAAA,SACO5F,GAAG;AACV,QAAIA,aAAazB,wBACXyB,EAAErB,WACJkH,QAAQC,KACN,kCAAkC9F,EAAErB,QAAQrG,KAAK,GAC/C0H,EAAErB,QAAQ8D,aAAa,IAAI,yBAAyBzC,EAAErB,QAAQ8D,UAAU,KAAK,EAAE,KAC5EzC,EAAEtB,OAAO,GAAGsB,EAAEpB,SAAS,UAAUmH,gBAAgB/F,EAAEpB,MAAM,CAAC,KAAK,EAAE,IACtEoB,CACF,GAGEA,EAAEpB,WAAW;AAGRiC,aAAAA,GAAa,CAAA,CAAE;AAIpBb,UAAAA;EAAAA;AAEV;AAMO,SAASgG,6BAA6B;EAC3CC;EACAzC;EACA0C,oBAAoB;IAClBC,sBAAsB,CAAE;IACxBlB,sBAAsB,CAAE;IACxB1D,mBAAAA,oBAAuBL,IAAI;IAC3BV,aAAaA,MAAM4F;EACrB;EACAjE;AACmC,GAAmC;AAmGtE,SAlGuB8D,kBAAkB9F;;IAEvCuC,IAAK2D,CAAmB,mBAAA,CAAC,CAAC;MAAC5K,IAAI;IAAA,CAAO,GAAG,GAAG4K,cAAc,CAAC;;IAE3D3D,IAAK4D,CACiDA,gBAAAA,YACjDC,QAAQ,CAACC,iBAAiBhN,eACzBgN,gBAAgB9D,IAAI,CAACoC,mBAAmBrM,kBAAkB;MACxDqM;MACAtL;MACAf;IAAAA,EACA,CACJ,EAECiK,IAAI,CAACC,GAAGrK,WAAW;MAAC,GAAGqK;MAAGtK,WAAWC;IAAAA,EAAO,CAGhD;;IAEDmO,UAAU,CAAA,CAA2B;IACrCC,SAAS;IACThE,IAAI,CAAC,CAACiE,MAAMC,IAAI,MAAM;AACpB,eAASjE,IAAI,GAAGA,IAAIiE,KAAKtN,QAAQqJ,KAAK;AACpC,cAAMkE,YAAYF,KAAKhE,CAAC,GAClBmE,YAAYF,KAAKjE,CAAC;AAEpB,YAAA,KAACoE,eAAAA,SAAQF,WAAWC,SAAS;AACxB,iBAAA;YACL7B,sBAAsB2B;YACtBI,WAAWrE;UACb;MAAA;AAIG,aAAA;QACLsC,sBAAsB2B;QACtBI,WAAWJ,KAAKtN;MAClB;IAAA,CACD;;IAED2N,KAAK,CAACC,KAAKpH,SAAS;AACZ,YAAA;QAACqG;QAAsB5E;MAAAA,IAAqB2F,KAC5C;QAACjC;QAAsB+B;MAAAA,IAAalH,MAIpCqH,kBAAkBhB,qBAAqBpM,MAAM,GAAGiN,YAAY,CAAC,GAC7DI,iBAAiBjB,qBAAqBpM,MAAMiN,YAAY,CAAC,GAEzDK,aAAa,IAAIC,IAAIH,gBAAgBZ,QAASgB,CAAAA,WAAWC,MAAM9G,KAAK6G,MAAM,CAAC,CAAC,GAC5EE,eAAeL,eAClBb,QAASgB,CAAWC,WAAAA,MAAM9G,KAAK6G,MAAM,CAAC,EACtClJ,OAAQoD,CAAAA,QAAQ,CAAC4F,WAAWK,IAAIjG,GAAG,CAAC;AAEvC,iBAAWA,OAAOgG;AAChBlG,0BAAkBoG,OAAOlG,GAAG;AAoBvB,aAAA;QACLwD;QACAkB;QACA5E;QACAf,aAAaF,mBAnB0BkB,CAAAA,WAAW,CAACzB,gBAAgBpB,SAAStG,cAAc;AAC1F,gBAAMoJ,MAAM1B,kBAAkB,GAAGd,SAASc,cAAc,CAAC,IAAIqE,YAAYzF,OAAO,CAAC,IAC3EiD,qBAAqBH,OAAOF,kBAAkBnC,IAAIqC,GAAG;AAC3D,cAAIG,mBAA2BA,QAAAA;AAE/B,gBAAMhB,SAASY,OAAOzB,gBAAgBpB,SAAStG,SAAS;AACpD,cAAA,CAACoJ,IAAYb,QAAAA;AAEjB,gBAAMgH,cAAczB,qBAAqB9N,SAAS,KAAA,oBAASiP,IAAI;AACnDO,iBAAAA,YAAAA,IAAIpG,GAAG,GACnB0E,qBAAqB9N,SAAS,IAAIuP,aAClCrG,kBAAkBjC,IAAImC,KAAKb,MAAM,GAC1BA;QAOgC,CAAA;MACzC;IAAA,GACCsF,iBAAiB;;IAEpBvF,UAAU,CAAC;MAACsE;MAAsBzE;IAAAA,MAChCwE,gBAAgB;MACdjF,gBAAgByD;MAChByB;MACAhD,QAAQ;MACRP,MAAM,CAAE;MACRlB;MACA2B;IAAAA,CACD,CACH;EAAA,EAMoBhC;;;;;IAKpB8G,KACE,CAACN,MAAM7G,SACLA,KAAK4C,IAAI,CAACoF,UAAUxP,UAAU;AACtByP,YAAAA,WAAWpB,KAAKrO,KAAK;AAE3B,aADI,CAACyP,YACDD,SAASnM,SAAS,YAAkBmM,WAEpCC,SAASjD,kBAAkBrJ,OAAOqM,SAAShD,kBAAkBrJ,KACxDsM,WAEFD;IACR,CAAA,GACH,CAAA,CACF;;IAEAE,qBAAqB,CAACrB,MAAM7G,SAAS;AACnC,UAAI6G,KAAKrN,WAAWwG,KAAKxG,OAAe,QAAA;AAExC,eAASqJ,IAAI,GAAGA,IAAI7C,KAAKxG,QAAQqJ,KAAK;AACpC,cAAMkE,YAAYF,KAAKhE,CAAC,GAClBsF,YAAYnI,KAAK6C,CAAC;AACxB,YAAIgC,qBAAqBkC,SAAS,MAAMlC,qBAAqBsD,SAAS;AAC7D,iBAAA;MAAA;AAIJ,aAAA;IACR,CAAA;EACH;AACF;AC7ZA,SAAAC,uBAAA;AAAAhQ,QAAAA,QAAAC,iCAAA,CAAA,GACE,CAAAgQ,kBAAA,QAA6BC,uBAAA3L,OAAgD;AAAC,MAAAtD,IAAAE;AAAAnB,IAAAA,CAAAA,MAAAiQ,sBAG1E9O,KAAA8O,mBAAkBE,aACF,EAAClI,KACTuC,IAAA4F,QAAgE,CAAC,GAACpQ,EAAAA,CAAAA,IAAAiQ,oBAAAjQ,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA,GAAAiB,KAF1EE;AAFJ,QAAAkP,eAAqBpP,IAOrB;IAAAqP,OAAA1P;EAAAA,IAA6BD,UAAU;AAAC,MAAAY,IAAAQ;AAAA/B,SAAAA,EAAAY,CAAAA,MAAAA,eAAAZ,EAAAA,CAAAA,MAAAiQ,sBAC9B1O,KAAAA,MAAA;AACR0O,uBAAkBrI,KAAMhH,WAAW;EAClCmB,GAAAA,KAAA,CAACnB,aAAaqP,kBAAkB,GAACjQ,EAAAA,CAAAA,IAAAY,aAAAZ,EAAAA,CAAAA,IAAAiQ,oBAAAjQ,EAAAA,CAAAA,IAAAuB,IAAAvB,EAAAA,CAAAA,IAAA+B,OAAAR,KAAAvB,EAAA,CAAA,GAAA+B,KAAA/B,EAAA,CAAA,QAFpCuQ,wBAAUhP,IAEPQ,EAAiC,GAE7BsO;AAAY;AAdrB,SAAAD,SAAAI,cAAA;AAMqCA,UAAAA,6CAAY1P,UAAa,CAAA;AAAA;AAN9D,SAAAyD,UAAA;AAAA,SAAA,IAAAkM,cAAA,CAAA;AAAA;AAiBO,SAAAC,mBAAA;AAAA1Q,QAAAA,QAAAC,iCAAA,CAAA,GAIL,CAAA0Q,OAAAC,QAAA,QAA0BV,uBAAkB;AACxCS,MAAAA;AAAaA,UAAAA;AAEjB,QAAA;IAAA1G;IAAAqB;EAAAA,IAAyCuF,iBAAiB;AAAC5P,MAAAA;AAAAjB,IAAA,CAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAEnB9P,KAAA;IAAA+P,eAAA,CAAA;IAAAC,eAAA,CAAA;IAAA7C,aAAA,CAAA;EAAA,GAIvCpO,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA;AAJD,QAAA,CAAAkR,MAAAC,OAAA,QAAwBjB,uBAAgBjP,EAIvC,GAED8M,oBAA0BiC,qBAAqB;AAAC,MAAA7O,IAAAI;AAAA,SAAAvB,EAAAsL,CAAAA,MAAAA,gBAAAtL,EAAAA,CAAAA,MAAA+N,qBAAA/N,EAAA,CAAA,MAAAiK,oBAEtC9I,KAAAA,MAAA;AAiDR,UAAAiQ,eAhDuBtD,6BAAA;MAAAxC;MAAAyC;MAAA9D;IAItB,CAAA,EAAChC,KACAuC,IAAA6G,MAwCC,CACH,EAEmCC,UAAA;MAAA1J,MAAAc,CACfyI,WAAAA,QAAQzI,MAAM;MAACiI,OAAA7I,CACnB8I,MAAAA,SAAS9I,CAAC;IAAA,CACzB;AAAC,WAAA,MAEWsJ,aAAYG,YAAa;EAAA,GACrChQ,KAAC+J,CAAAA,cAAcyC,mBAAmB9D,gBAAgB,GAACjK,EAAAA,CAAAA,IAAAsL,cAAAtL,EAAAA,CAAAA,IAAA+N,mBAAA/N,EAAAA,CAAAA,IAAAiK,kBAAAjK,EAAAA,CAAAA,IAAAmB,IAAAnB,EAAAA,CAAAA,IAAAuB,OAAAJ,KAAAnB,EAAA,CAAA,GAAAuB,KAAAvB,EAAA,CAAA,QAvDtDuQ,wBAAUpP,IAuDPI,EAAmD,GAE/C2P;AAAI;AA1EN,SAAAG,OAAAJ,eAAA;AAwBC,QAAA7C,cAAoB6C,cAAaO,OAAAC,QAAAA,CAK5B,CAAA,GAELC,YAAkBtD,YAAWhN,QAE7B4P,gBAAsBC,cAAazG,IAAAI,CAAA,SAAA;AACjC,UAAA;MAAAtJ;MAAAnB;MAAAI;MAAAqM;MAAApD;IAAAA,IAAuEoB,MACvE+G,SAAe/E,kBAAiBrJ,IAChC5B,YAAkByM,YAAY9M,aAAc,CAAA;AAE5C,WAAA;MAAAsQ,QACUtQ,eAAeoQ,YAAa;MAAAG,cACvBlQ,uCAAS4B,GAAAA,OAAgB;MAAAnD,OAC/BD;MAASwR,QACR/E,kBAAiBrJ;MAAAjC;MAAAiI,KAEpB,GACHqB,KAAInH,SAAU,YAAY,YAAYmH,KAAIiC,SAAAtJ,EAAY,IACpDoO,MAAM,IAAIpR,YAAY;MAAEqK,MACtBA,KAAInH,SAAU,YAASqO,eAAkBlH,KAAIiC;MAASxM,QACpDuM,kBAAiBvM,UAAa,CAAA;MAAAmJ,MAChCA,KAAIC,KAAM,GAAG;MAACnJ,SACXsM,kBAAiBtM;MAAAyR,UAChB5R,cAAc8Q,cAAa7P,SAAW;MAAAb;IAAA;EAAA,CAKnD;AAAC,SAAA;IAAAyQ;IAAA5C;IAAA6C,eAKeD,cAAaxG,IAAAwH,MAAwB;EAAC;AAAA;AA7DxD,SAAAA,OAAAC,QAAA;AAAA,SA6D8CrH,OAAIA;AAAA;AA7DlD,SAAA6G,OAAAzC,KAAApH,MAAA;AAyBG,QAAAnG,eAAqBuN,IAAIpH,KAAItG,UAAA,KAAkB,CAAA;AAClCsG,SAAAA,aAAAA,KAAIrH,YAAA,IAAiBqH,KAAIgF,mBACtCoC,IAAIpH,KAAItG,UAAA,IAAeG,cAChBuN;AAAG;ACxDEkD,eAAAA,wBACpBC,eACA5O,IACAE,MACqC;AACjCF,MAAAA,MAAME,KAAa,QAAA;IAACF;IAAIE;EAAI;AAC5B,MAAA,CAACF,MAAME,KAAa,QAAA;IAACF,IAAI6O,WAAK;IAAG3O;EAAI;AACrCF,MAAAA,MAAM,CAACE,MAAM;AACf,UAAM4O,eAAe,MAAM/H,eACzB6H,cAAcG,uBAAuB/O,EAAE,CACzC;AAEO,WAAA;MAACA;MAAIE,MAAM4O;IAAY;EAAA;AAGhC,QAAM,IAAIhM,oBAAoB;IAC5BG,SAAS;EAAA,CACV;AACH;AC9BA,IAAM+L,eAAwC,CAAC;AAA/C,IAMaC,qBAAiBC,mBAAK,WAAA;AAAAzS,QAAAA,QAAAC,iCAAA,CAAA,GACjC;IAAAO;EAAAA,IAAmBG,UAAU,GAC7B+R,cAAoB7R,eAAA0D,OAWpB,GACA;IAAA+G;IAAArB;EAAAA,IAAyC4G,iBAAiB,GAC1DsB,gBAAsBQ,iBACtB,GAAA,CAAAhC,OAAAC,QAAA,QAA0BV,uBAAAA,IAAsB;AAG5CS,MAAAA;AAAaA,UAAAA;AAAK,MAAA1P,IAAAE;AAAAnB,SAAAA,EAAAmS,CAAAA,MAAAA,iBAAAnS,EAAA,CAAA,MAAA0S,eAAA1S,EAAAQ,CAAAA,MAAAA,YAAAR,EAAA,CAAA,MAAAsL,gBAAAtL,EAAAA,CAAAA,MAAAiK,oBAGZhJ,KAAAA,MAAA;AAAA,QACJyR,aAAW;AACb,YAAA;QAAA5I;QAAAzJ;QAAAC;MAAAA,IAAkCoS;AAElC,UAAAE,YAAA;AAwBAC,aAAAA,iBAtBA;AACE,cAAA;UAAAtP;UAAAE;QAAAA,IAAA,MAAyByO,wBACvBC,eACA,OAAO9R,OAAMkD,MAAQ,WAAWlD,OAAMkD,KAAAW,QACtC,OAAO7D,OAAMoD,QAAU,WAAWpD,OAAMoD,OAAAS,MAC1C;AAEI0O,YAAAA;AAAS;AAEb9R,cAAAA,QAAAA,MAAoBsI,cAAA;UAAAU;UAAAzJ,QAAA;YAAA,GAENA;YAAMkD;YAAAE;UAAA;UAAAnD;UAAAgL;UAAArB;QAAAA,CAInB;AAEG2I,qBAEJpS,SAAQ;UAAAM;QAAAA,GAAA;UAAA8K,SAAA;QAAA,CAAyB;MAAA,EAAA,EAG3BkH,MAAOlC,QAAQ,GAAC,MAAA;AAGtBgC,oBAAAA;MAAS;IAAA;EAAA,GAGZzR,KAAA,CAACgR,eAAeO,aAAalS,UAAU8K,cAAcrB,gBAAgB,GAACjK,EAAAA,CAAAA,IAAAmS,eAAAnS,EAAAA,CAAAA,IAAA0S,aAAA1S,EAAAA,CAAAA,IAAAQ,UAAAR,EAAAA,CAAAA,IAAAsL,cAAAtL,EAAAA,CAAAA,IAAAiK,kBAAAjK,EAAAA,CAAAA,IAAAiB,IAAAjB,EAAAA,CAAAA,IAAAmB,OAAAF,KAAAjB,EAAA,CAAA,GAAAmB,KAAAnB,EAAA,CAAA,QAlCzEuQ,wBAAUtP,IAkCPE,EAAsE,GAAC;AAAA,CAG3E;AA3DkC,SAAAoD,QAAA3D,aAAA;AAI7B,QAAAmS,aAAmB,OAAOnS,YAAWkJ,UAAY,WAAWlJ,YAAWkJ,SAAA5F;AAAmB,SACnF6O,aAAU;IAAAjJ,QAEHiJ;IAAU1S,QACVoH,WAAS7G,YAAWP,MAAO,IAAIO,YAAWP,SAAAkS;IAAsBjS,SAC/DM,YAAWN;EAAAA,IAAA4D;AAEb;ACbnB,IAAM8O,cAAcC,GAAOC;;;;;;AAO3B,SAASC,YAAYC,OAAe;AAEhCA,SAAAA,MAEGxH,QAAQ,iBAAiB;IAAY,EAErCA,QAAQ,oCAAoC,EAAE,EAE9CA,QAAQ,iBAAiB,GAAG,EAE5BA,QAAQ,IAAIyH,OAAO,oBAAoBC,OAAOC,SAASC,IAAI,IAAI,GAAG,GAAG,IAAI;AAEhF;AAMO,SAAAC,eAAAxS,IAAA;AAAAjB,QAAAA,QAAAC,iCAAA,EAAA,GAAwB;IAAA0Q;EAAAA,IAAA1P;AAA4B,MACnD0P,EAAAA,iBAAKtK;AACHsK,UAAAA;AAER,QAAA;IAAAhK;EAAAA,IAAgBgK,OAChB;IAAA+C;EAAAA,IAAYC,eAAAC,wBAAuC,GAKnDR,SAAczM,+BAAKyM,UAAWzC,MAAKyC,OACnCS,YACET,SAAWzM,EAAAA,iBAAKmN,mBAAgCnD,CAAAA,MAAKnK,QAAAuN,SAAkB,sBAAsB;AAAC5S,MAAAA;AAAAnB,IAAAA,CAAAA,MAAA2G,SAEnFxF,KAAAwF,iBAAKmN,iBAA6BnN,MAAK6C,OAAU,CAAA,GAAAxJ,EAAAA,CAAAA,IAAA2G,OAAA3G,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA;AAA9DwJ,QAAAA,OAAarI,IACbuF,SAAgBC,iBAAKmN,kBAA8BnN,MAAKD,UAAYiK,MAAKjK,QAEzEsN,eAAAzP;AAEMhD,MAAAA;AAAAvB,IAAAA,CAAAA,MAAA0T,KAKkBnS,KAAAmS,EAAE,6BAA6B,GAAC1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,CAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,CAAA;AAAA+B,MAAAA;AAAA/B,IAAAA,CAAAA,MAAAuB,MAAlDQ,SAAC,wBAAA,SAAA,EAAW,IAAA,MAAMR,UAAAA,GAAAA,CAAiC,GAAUvB,EAAAA,CAAAA,IAAAuB,IAAAvB,EAAAA,CAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,CAAA;AAAAqC,MAAAA;AAAArC,IAAAwJ,CAAAA,MAAAA,QAAAxJ,EAAAA,CAAAA,MAAA0T,KAG1DrR,KAAAmH,KAAIpI,SAAAA,SACF,yBAAA,OAAA,EAAa,OAAA,GACZ,UAAA;QAAA,wBAAC,MAAA,EAAW,MAAA,GAAU,QAAA,UACnBsS,UAAAA,EAAE,sCAAsC,EAAA,CAC3C;QACA,wBAAC,MAAA,EAGElK,UAAI3H,KAAAA,MAAAA,CAAQ,EAAC2I,IAAA4F,QAGb,EACH,CAAA;EAAA,EACF,CAAA,GACDpQ,EAAAA,CAAAA,IAAAwJ,MAAAxJ,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,CAAAA,IAAAqC,MAAAA,KAAArC,EAAA,CAAA;AAAA2C,MAAAA;AAAA3C,IAAAA,CAAAA,MAAA0T,KAII/Q,KAAA+Q,EAAE,6BAA6B,GAAC1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,EAAAA,IAAA2C,MAAAA,KAAA3C,EAAA,EAAA;AAAAgD,MAAAA;AAAAhD,IAAAA,EAAAA,MAAA2C,MADnCK,SAAC,wBAAA,MAAA,EAAW,MAAA,GAAU,QAAA,UACnBL,UAAAA,GAAAA,CACH,GAAO3C,EAAAA,EAAAA,IAAA2C,IAAA3C,EAAAA,EAAAA,IAAAgD,MAAAA,KAAAhD,EAAA,EAAA;AAAAqD,MAAAA;AAAArD,IAAA,EAAA,MAAA2Q,MAAAnK,WAAAxG,EAAA,EAAA,MAAA6T,aAAA7T,EAAA,EAAA,MAAAoT,SACA/P,KAAAwQ,YAAYV,YAAYC,KAAK,IAAIzC,MAAKnK,SAAQxG,EAAA,EAAA,IAAA2Q,MAAAnK,SAAAxG,EAAAA,EAAAA,IAAA6T,WAAA7T,EAAAA,EAAAA,IAAAoT,OAAApT,EAAAA,EAAAA,IAAAqD,MAAAA,KAAArD,EAAA,EAAA;AAAAsD,MAAAA;AAAAtD,IAAAA,EAAAA,MAAAqD,MAArDC,SAAA,wBAAC,MAAA,EAAMD,UAA+C,GAAA,CAAA,GAAOrD,EAAAA,EAAAA,IAAAqD,IAAArD,EAAAA,EAAAA,IAAAsD,MAAAA,KAAAtD,EAAA,EAAA;AAAA8D,MAAAA;AAAA9D,IAAAgD,EAAAA,MAAAA,MAAAhD,EAAAA,EAAAA,MAAAsD,MAJ/DQ,SAAC,yBAAA,OAAA,EAAiB,WAAA,GAAU,OAAA,GAC1Bd,UAAAA;IAAAA;IAGAM;EAAAA,EACF,CAAA,GAAQtD,EAAAA,EAAAA,IAAAgD,IAAAhD,EAAAA,EAAAA,IAAAsD,IAAAtD,EAAAA,EAAAA,IAAA8D,MAAAA,KAAA9D,EAAA,EAAA;AAAA+D,MAAAA;AAAA/D,IAAA0G,EAAAA,MAAAA,UAAA1G,EAAAA,EAAAA,MAAA0T,KAEP3P,MAAA2C,cAAAA,wBACE,KAAe,EAAA,WAAC,GACf,cAAC,wBAAA,MAAA,EACC,cAAA,wBAAA,KAAS,EAAA,MAAAmH,gBAAgBnH,MAAM,GAAO,KAAA,uBAA6B,QAAA,UAChEgN,UAAAA,EAAE,gCAAgC,EAAA,CACrC,EAAA,CACF,EACF,CAAA,GACD1T,EAAAA,EAAAA,IAAA0G,QAAA1G,EAAAA,EAAAA,IAAA0T,GAAA1T,EAAAA,EAAAA,IAAA+D,OAAAA,MAAA/D,EAAA,EAAA;AAAAgE,MAAAA;AAAAhE,IAAAA,EAAAA,MAAA0T,KAIS1P,MAAA0P,EAAE,oCAAoC,GAAC1T,EAAAA,EAAAA,IAAA0T,GAAA1T,EAAAA,EAAAA,IAAAgE,OAAAA,MAAAhE,EAAA,EAAA;AAAAmE,MAAAA;AAAAnE,IAAAA,EAAAA,MAAAgE,OAFjDG,UAAC,wBAAA,KAAe,EAAA,WAAC,GACf,cAAA,wBAAC,QACO,EAAA,MAAAH,KACAiQ,MAAOA,UACR,MAAA,WACID,SAAW,aAExB,CAAA,EAAA,CAAA,GAAMhU,EAAAA,EAAAA,IAAAgE,KAAAhE,EAAAA,EAAAA,IAAAmE,OAAAA,MAAAnE,EAAA,EAAA;AAAAoE,MAAAA;AAAApE,IAAA,EAAA,MAAA+D,OAAA/D,EAAAmE,EAAAA,MAAAA,OAAAnE,EAAAqC,EAAAA,MAAAA,MAAArC,EAAAA,EAAAA,MAAA8D,MAzCRM,UAAC,yBAAA,MAAA,EAAgB,WAAA,GAAY,SAAC,GAAU,QAAA,GAAY,UAAA,QAAe,QAAA,GAAQ,MAAA,WACxE/B,UAAAA;IAAAA;IAgBDyB;IAOCC;IAUDI;EAAAA,EAAAA,CAQF,GAAOnE,EAAAA,EAAAA,IAAA+D,KAAA/D,EAAAA,EAAAA,IAAAmE,KAAAnE,EAAAA,EAAAA,IAAAqC,IAAArC,EAAAA,EAAAA,IAAA8D,IAAA9D,EAAAA,EAAAA,IAAAoE,OAAAA,MAAApE,EAAA,EAAA;AAAAqE,MAAAA;AAAArE,SAAAA,EAAAoE,EAAAA,MAAAA,OAAApE,EAAAA,EAAAA,MAAA+B,MA9CXsC,UAAC,wBAAA,MAAA,EAAY,QAAA,QAAgB,UAAA,QAAgB,SAAC,GAAS,QAAA,UAAc,MAAA,YACnE,cAAA,yBAAC,WACCtC,EAAAA,UAAAA;IAAAA;IAEAqC;EAAAA,EA2CF,CAAA,EACF,CAAA,GAAOpE,EAAAA,EAAAA,IAAAoE,KAAApE,EAAAA,EAAAA,IAAA+B,IAAA/B,EAAAA,EAAAA,IAAAqE,OAAAA,MAAArE,EAAA,EAAA,GAhDPqE;AAgDO;AAtEJ,SAAA+L,SAAA8D,SAAAzJ,GAAA;AAAA,aAAA,wBAqCY,aAAqCyJ,EAAAA,UAAAA,QAAAA,GAAAA,GAAjBA,OAAO,IAAIzJ,CAAC,EAAa;AAAc;AArCvE,SAAAlG,UAAA;AAkBH+O,SAAAC,SAAAY,OAAuB;AAAC;ACxCrB,SAAAC,YAAArU,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GACL;IAAAoU;IAAAzJ;IAAA0J;EAAAA,IAAoCvU;AAAKkB,MAAAA;AAAAjB,IAAAA,CAAAA,MAAA4K,QAC5B3J,KAACwG,WAASmD,IAAI,KAAKA,KAAInH,QAAc,MAAAzD,EAAAA,CAAAA,IAAA4K,MAAA5K,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA;AAAlD,QAAAyD,OAAaxC,IACb;IAAAyS;EAAAA,IAAYC,eAAAC,wBAAuC;AAACzS,MAAAA;AAAAnB,IAAAA,CAAAA,MAAA0T,KAG7BvS,KAAAuS,EAAE,+BAA+B,GAAC1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA;AAAAuB,MAAAA;AAAAvB,IAAAA,CAAAA,MAAAmB,MAArDI,SAAC,wBAAA,cAAA,EAAkB,OAAAJ,GAAsC,CAAA,GAAAnB,EAAAA,CAAAA,IAAAmB,IAAAnB,EAAAA,CAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,CAAA;AAAA+B,MAAAA;AAAA/B,IAAA0T,CAAAA,MAAAA,KAAA1T,EAAAA,CAAAA,MAAAyD,QACzD1B,SAAAA,wBAAC,aACC,EAAA,cAAA,wBAAC,KAAa,EAAA,SAAA,GACX,UAAA,OAAO0B,QAAS,eACd,wBAAA,MAAA,EAAQ,IAAA,KAAI,OAAA,MACX,cAAC,wBAAA,WAAA,EACIiQ,GACK,SAAA,6CACA,QAAA;IAAAjQ;EAAAA,EAEZ,CAAA,EAAA,CAAA,QAEA,wBAAC,MAAA,EAAQ,IAAA,KAAI,OAAA,MACX,cAAC,wBAAA,WAAA,EAAaiQ,GAAW,SAAA,4CAA2C,CAAA,EACtE,CAAA,EAEJ,CAAA,EACF,CAAA,GAAc1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,CAAAA,IAAAyD,MAAAzD,EAAAA,CAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,CAAA;AAAAqC,MAAAA;AAAArC,SAAAA,EAAA,CAAA,MAAAqU,cAAArU,EAAAsU,EAAAA,MAAAA,WAAAtU,EAAAuB,EAAAA,MAAAA,MAAAvB,EAAAA,EAAAA,MAAA+B,MAlBhBM,SAAAA,yBAAC,MAASiS,EAAAA,IAAM,SAAaD,UAAAA,YAC3B9S,UAAAA;IAAAA;IACAQ;EAiBF,EAAA,CAAA,GAAO/B,EAAAA,CAAAA,IAAAqU,YAAArU,EAAAA,EAAAA,IAAAsU,SAAAtU,EAAAA,EAAAA,IAAAuB,IAAAvB,EAAAA,EAAAA,IAAA+B,IAAA/B,EAAAA,EAAAA,IAAAqC,MAAAA,KAAArC,EAAA,EAAA,GAnBPqC;AAmBO;ACfX,IAAMkS,UAAU;EACdC,eAAWC,mBAAK,MAAM,OAAO,0BAAiB,CAAC;EAC/CC,cAAUD,mBAAK,MAAM,OAAO,oBAAiB,EAAA,KAAA,SAAA,GAAA;;IAAC;EAC9CE,kBAAcF,mBAAK,MAAM,OAAO,oBAAqB,EAAA,KAAA,SAAA,GAAA;;IAAC;EACtDG,UAAMH,mBAAK,MAAM,OAAO,sBAAQ,CAAC;AACnC;AALA,IAYaI,wBAAoBpC,mBAC/B,SAAA1S,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GACE;IAAA2R;IAAAC;IAAAvQ;IAAAlB;IAAAuR;IAAA/G;IAAA0J;IAAAjU;IAAAC;IAAAkJ;IAAAuI;IAAAxR;EAAAA,IAaIR,OAEJ+U,gBAAsBP,QAAQ3J,KAAInH,IAAA,KAAA2Q;AAAqBnT,MAAAA;AAAAjB,IAAAsU,CAAAA,MAAAA,WAAAtU,EAAAA,CAAAA,MAAAwJ,QAAAxJ,EAAA,CAAA,MAAA+R,YAU/B9Q,SAAC,wBAAA,aAAA,EAAqBqT,SAAe9K,MAAgBuI,SAAAA,CAAY,GAAA/R,EAAAA,CAAAA,IAAAsU,SAAAtU,EAAAA,CAAAA,IAAAwJ,MAAAxJ,EAAAA,CAAAA,IAAA+R,UAAA/R,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA;AAEpE,QAAAmB,KAAA0Q,eAAe;AAAEtQ,MAAAA;AAAAvB,IAAA,CAAA,MAAA8U,iBAAA9U,EAAA,CAAA,MAAA4R,UAAA5R,EAAA,CAAA,MAAAI,SAAAJ,EAAA2R,CAAAA,MAAAA,UAAA3R,EAAA4K,CAAAA,MAAAA,QAAA5K,EAAAsU,CAAAA,MAAAA,WAAAtU,EAAA+R,EAAAA,MAAAA,YAAA/R,EAAAA,EAAAA,MAAAmB,MADhCI,SAAAA,wBAAC,eACc,EAAA,aAAAJ,IACNf,OACCuR,QACEC,UAAAA,QACEG,YAAAA,UACHuC,SAEH1J,KAAAA,CACN,GAAA5K,EAAAA,CAAAA,IAAA8U,eAAA9U,EAAAA,CAAAA,IAAA4R,QAAA5R,EAAAA,CAAAA,IAAAI,OAAAJ,EAAAA,CAAAA,IAAA2R,QAAA3R,EAAAA,CAAAA,IAAA4K,MAAA5K,EAAAA,CAAAA,IAAAsU,SAAAtU,EAAAA,EAAAA,IAAA+R,UAAA/R,EAAAA,EAAAA,IAAAmB,IAAAnB,EAAAA,EAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,EAAA;AAAA+B,MAAAA;AAAA/B,IAAAiB,EAAAA,MAAAA,MAAAjB,EAAAA,EAAAA,MAAAuB,MAVJQ,SAAC,wBAAA,uBAAmB,EAAA,UAAAd,IAClBM,UAAAA,GAAAA,CAUF,GAAWvB,EAAAA,EAAAA,IAAAiB,IAAAjB,EAAAA,EAAAA,IAAAuB,IAAAvB,EAAAA,EAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,EAAA;AAAAqC,MAAAA;AAAA,SAAArC,EAAAA,EAAAA,MAAAsB,cAAAtB,EAAAI,EAAAA,MAAAA,SAAAJ,EAAAK,EAAAA,MAAAA,UAAAL,EAAA,EAAA,MAAAM,WAAAN,EAAAA,EAAAA,MAAAO,gBAAAP,EAAA,EAAA,MAAA+B,MAlBbM,SAAC,wBAAA,oBACYjC,EAAAA,WAAI,OACRkB,OAAAA,YACCjB,QACCC,SACKC,cAEdwB,UAAAA,GAYF,CAAA,GAAqB/B,EAAAA,EAAAA,IAAAsB,YAAAtB,EAAAA,EAAAA,IAAAI,OAAAJ,EAAAA,EAAAA,IAAAK,QAAAL,EAAAA,EAAAA,IAAAM,SAAAN,EAAAA,EAAAA,IAAAO,cAAAP,EAAAA,EAAAA,IAAA+B,IAAA/B,EAAAA,EAAAA,IAAAqC,MAAAA,KAAArC,EAAA,EAAA,GAnBrBqC;AAmBqB,GAGzB,CACE;EAAChC,QAAQ0U,aAAa,CAAC;EAAGzU,SAAS0U,cAAc;EAAM,GAAGvG;AAAI,GAC9D;EAACpO,QAAQiC,aAAa,CAAC;EAAGhC,SAASsC,cAAc;EAAM,GAAGgF;AAAI,MAC3D;AAEC,MAAA,KAACiH,eAAAA,SAAQkG,YAAYzS,UAAU,KAC/B,KAACuM,eAAAA,SAAQmG,aAAapS,WAAW,EAAU,QAAA;AAE/C,QAAMqS,OAAO,oBAAI7F,IAAI,CAAC,GAAG8F,OAAOD,KAAKxG,IAAI,GAAG,GAAGyG,OAAOD,KAAKrN,IAAI,CAAC,CAAC;AAKjE,aAAW2B,OAAO0L;AAChB,QAAIxG,KAAKlF,GAAG,MAAM3B,KAAK2B,GAAG,EAAU,QAAA;AAG/B,SAAA;AACT,CACF;AC3FO,SAAA4L,wBAAA;AAAAnV,QAAAA,QAAAC,iCAAA,EAAA,GACL;IAAAyT;EAAAA,IAAYC,eAAAC,wBAAuC;AAAC3S,MAAAA;AAAAjB,IAAA,CAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAQxC9P,SAAC,wBAAA,KAAA,EACC,cAAC,wBAAA,MAAA,EAAW,MAAA,GACV,cAAA,wBAAC,oBACH,CAAA,CAAA,EAAA,CAAA,EACF,CAAA,GAAMjB,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA;AAAAmB,MAAAA;AAAAnB,IAAAA,CAAAA,MAAA0T,KAGDvS,KAAAuS,EAAE,gCAAgC,GAAC1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA;AAAAuB,MAAAA;AAAAvB,IAAAA,CAAAA,MAAAmB,MADtCI,SAAAA,wBAAC,MAAQ,EAAA,IAAA,MAAW,MAAA,GAAU,QAAA,UAC3BJ,UACH,GAAA,CAAA,GAAOnB,EAAAA,CAAAA,IAAAmB,IAAAnB,EAAAA,CAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,CAAA;AAAA+B,MAAAA;AAAA/B,IAAAA,CAAAA,MAAA0T,KAEJ3R,KAAA2R,EAAE,mCAAmC,GAAC1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,CAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,CAAA;AAAAqC,MAAAA;AAAArC,IAAAA,CAAAA,MAAA+B,MADzCM,SAAAA,wBAAC,MAAQ,EAAA,IAAA,KAAI,OAAA,MAAY,MAAC,GACvBN,UACH,GAAA,CAAA,GAAO/B,EAAAA,CAAAA,IAAA+B,IAAA/B,EAAAA,CAAAA,IAAAqC,MAAAA,KAAArC,EAAA,CAAA;AAAA2C,MAAAA;AAAA3C,IAAAA,CAAAA,MAAA0T,KAOF/Q,KAAA+Q,EAAE,oCAAoC,GAAC1T,EAAAA,CAAAA,IAAA0T,GAAA1T,EAAAA,EAAAA,IAAA2C,MAAAA,KAAA3C,EAAA,EAAA;AAAAgD,MAAAA;AAAAhD,IAAAA,EAAAA,MAAA2C,MAN5CK,SAAC,wBAAA,MAAA,EAAQ,IAAA,KAAI,OAAA,MAAY,MAAC,GACxB,cAAA,wBAAA,KAAA,EACO,MAAA,0EACE,QAAA,UACH,KAAA,cAEHL,UACH,GAAA,CAAA,EACF,CAAA,GAAO3C,EAAAA,EAAAA,IAAA2C,IAAA3C,EAAAA,EAAAA,IAAAgD,MAAAA,KAAAhD,EAAA,EAAA;AAAAqD,MAAAA;AAAArD,SAAAA,EAAAuB,EAAAA,MAAAA,MAAAvB,EAAAA,EAAAA,MAAAqC,MAAArC,EAAA,EAAA,MAAAgD,MAzBnBK,SAAAA,wBAAC,MAAY,EAAA,QAAA,QACX,cAAC,wBAAA,MAAA,EAAW,OAAA,UAAgB,QAAA,QAAe,SAAA,UAAkB,SAAA,GAAU,QAAA,UACrE,cAAC,wBAAA,WAAA,EAAiB,OAAC,GACjB,cAAA,wBAAC,MAAA,EAAc,SAAC,GAAU,QAAA,GAAW,QAAA,GAAQ,MAAA,WAC3C,cAAA,yBAAC,MACCpC,EAAAA,UAAAA;IAAAA;QAAAA,yBAKC,OAAY,EAAA,MAAC,GAAc,YAAA,GAAU,OAAA,GACpCM,UAAAA;MAAAA;MAGAc;MAGAW;IAAAA,EASF,CAAA;EACF,EAAA,CAAA,EACF,CAAA,EAAA,CACF,EAAA,CACF,EACF,CAAA,GAAOhD,EAAAA,EAAAA,IAAAuB,IAAAvB,EAAAA,EAAAA,IAAAqC,IAAArC,EAAAA,EAAAA,IAAAgD,IAAAhD,EAAAA,EAAAA,IAAAqD,MAAAA,KAAArD,EAAA,EAAA,GA/BPqD;AA+BO;ACpBX,IAAM+R,gBAAgBrV,CAAA,UAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GACpB;IAAA+L;IAAAqJ;EAAAA,IAAmCtV,OACnC;IAAAuV;EAA4BC,IAAAA,eAE5BC,GAAAA,YAAkBC,aAAazJ,YAAYqJ,cAAc,WAAWC,iBAAiB,GACrFI,SAAeC,UAAAA,GACf;IAAAjC;EAAAA,IAAYC,eAAAC,wBAAuC,GACnDgC,gBAAsB,EAACJ,uCAASK,cAAA,EAAgBL,uCAASM,QACzDC,iBAAsBP,uCAAS7R,aAAa6R,uCAASM,WAAWN,uCAASK,YACzE9J,aAAmB2J,OAAMxO,IAAKmO,YAAY,GAE1C;IAAAW;IAAAC,WAAAC;EAAAA,IAAkDC,mBAAA;IAAAC,SAAA;IAAArK;IAAAiK,OAGzCD;EACR,CAAA,GAEDM,gBAAsBT,gBAClBlC,EAAE,uCAAqC;IAAA3H,aACzBA,yCAAUuK,WAAWvK,yCAAUnF;EAC5C,CAAA,KACDoP,+BAAKM,UAAW5C,EAAE,0CAA0C,GAEhE6C,UAAgBf,UAASgB,SAAAA,CAAWN,uBACpCO,WAAiBC,0BAA0BL,aAAa;AAACpV,MAAAA;AAAAjB,IAAAyW,CAAAA,MAAAA,YAAAzW,EAAAA,CAAAA,MAAAuW,WAC/CtV,KAAAA,MAAA;AACHsV,gBAAO7B,SAAA4B,QAEKG;EAClBzW,GAAAA,EAAAA,CAAAA,IAAAyW,UAAAzW,EAAAA,CAAAA,IAAAuW,SAAAvW,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA;AAAAmB,MAAAA;AAAA,SAAAnB,EAAAqW,CAAAA,MAAAA,iBAAArW,EAAAA,CAAAA,MAAAyW,YAAAzW,EAAA,CAAA,MAAAuW,WAAEpV,KAACkV,CAAAA,eAAeE,SAASE,QAAQ,GAACzW,EAAAA,CAAAA,IAAAqW,eAAArW,EAAAA,CAAAA,IAAAyW,UAAAzW,EAAAA,CAAAA,IAAAuW,SAAAvW,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA,OAJrCuQ,wBAAUtP,IAIPE,EAAkC,GAAC;AAAA;AA7BxC,IAkCMwV,mBAAmB5W,CAAA,UAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GACvB;IAAAqW;EAAgBvW,IAAAA,OAChB0W,WAAiBC,0BAA0BJ,KAAK;AAACrV,MAAAA;AAAAjB,IAAAA,CAAAA,MAAAyW,YACvCxV,KAAAA,MAAA;AAAAyT,aAAA4B,QAESG;EAAQ,GAC1BzW,EAAAA,CAAAA,IAAAyW,UAAAzW,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA;AAAAmB,MAAAA;AAAA,SAAAnB,EAAAyW,CAAAA,MAAAA,YAAAzW,EAAAA,CAAAA,MAAAsW,SAAEnV,KAAA,CAACsV,UAAUH,KAAK,GAACtW,EAAAA,CAAAA,IAAAyW,UAAAzW,EAAAA,CAAAA,IAAAsW,OAAAtW,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA,OAHpBuQ,wBAAUtP,IAGPE,EAAiB,GAAC;AAAA;AAxCvB,IA4CayV,iBAAiB7W,CAAA,UAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GAC5B;IAAAgR;EAAAA,IAAwBlR;AAAK,MAExBkR,EAAAA,+CAAa7P;AAAA,WAAA;AAElB,QAAAC,WAAiB4P,cAAcA,cAAa7P,SAAW,CAAA;AAGnDyV,MAAAA,cAAcxV,QAAQ,GAAC;AAAAJ,QAAAA;AAAA,WAAAjB,EAAA,CAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAClB9P,UAAA,wBAAC,kBAAmB,CAAA,CAAA,GAAAjB,EAAAA,CAAAA,IAAAiB,OAAAA,MAAAjB,EAAA,CAAA,GAApBiB;EAAAA;AAIL6V,MAAAA,eAAezV,QAAQ,GAAC;AAAA,QAEtBA,qCAAQiV,OAAA;AAAArV,UAAAA;AAAAjB,aAAAA,EAAA,CAAA,MAAAqB,SAAAiV,SACHrV,UAAC,wBAAA,kBAAA,EAAwB,OAAAI,SAAQiV,MAAU,CAAA,GAAAtW,EAAA,CAAA,IAAAqB,SAAAiV,OAAAtW,EAAAA,CAAAA,IAAAiB,OAAAA,MAAAjB,EAAA,CAAA,GAA3CiB;IAAAA;AAA2CA,QAAAA;AAAA,WAAAjB,EAAA,CAAA,MAAAqB,SAAAqD,QAAAnB,MAAAvD,EAAAqB,CAAAA,MAAAA,SAAAqD,QAAAjB,QAI7CxC,UAAC,wBAAA,eAAA,EAA0B,YAAAI,SAAQqD,QAAAnB,IAA2B,cAAAlC,SAAQqD,QAAAjB,KAAiB,CAAA,GAAAzD,EAAAqB,CAAAA,IAAAA,SAAAqD,QAAAnB,IAAAvD,EAAAqB,CAAAA,IAAAA,SAAAqD,QAAAjB,MAAAzD,EAAAA,CAAAA,IAAAiB,OAAAA,MAAAjB,EAAA,CAAA,GAAvFiB;EAAAA;AAIuB,QAAAA,KAAAI,qCAAQiV;AAAOnV,MAAAA;AAAAnB,SAAAA,EAAAA,CAAAA,MAAAiB,MAAxCE,SAAC,wBAAA,kBAAA,EAAwB,OAAAF,GAAAA,CAAmB,GAAAjB,EAAAA,CAAAA,IAAAiB,IAAAjB,EAAAA,CAAAA,IAAAmB,MAAAA,KAAAnB,EAAA,CAAA,GAA5CmB;AAA4C;AAWrD,SAAAuV,0BAAAK,aAAA;AAAA,QAAA/W,QAAAC,iCAAA,CAAA,GACE+W,yBAA+BnG,iBAAAA,EAAkB5G,iBAAAqM;AAAuBrV,MAAAA;AAAA,SAAAjB,EAAA+W,CAAAA,MAAAA,eAAA/W,EAAAA,CAAAA,MAAAgX,0BACjE/V,KAAA,CAAC8V,aAAaC,sBAAsB,EAAA7Q,OAAA5B,OAAyB,GAACvE,EAAAA,CAAAA,IAAA+W,aAAA/W,EAAAA,CAAAA,IAAAgX,wBAAAhX,EAAAA,CAAAA,IAAAiB,MAAAA,KAAAjB,EAAA,CAAA,GAA9DiB,GAA8DwI,KAAM,KAAK;AAAC;AAFnF,SAAAlF,QAAA+R,OAAA;AAEiEA,SAAAA;AAAK;AAItE,SAASQ,eAAelM,MAAgE;AAC/EA,SAAAA,SAASkH,gBAAgBlH,KAAKnH,SAAS;AAChD;AAEA,SAASoT,cAAcjM,MAAmE;AACxF,SAAOA,SAASkH;AAClB;AC5FA,IAAMmF,mBAAmBhE,GAAOiE,UAAU;;;;AAA1C,IAKMC,eAAeC,SAAS,OAAO;AALrC,IAUaC,oBAAgB5E,mBAAK,SAAAxR,IAAA;;AAAAjB,QAAAA,QAAAC,iCAAA,EAAA,GAAuB;IAAAqX;EAAAA,IAAArW,IACvD;IAAAsW,MAAAC;EAA0BC,IAAAA,SAC1B/B,GAAAA,SAAeC,UAAAA,GACf;IAAA+B;IAAAC;EAAA,IAA8C9G,iBAAAA,GAC9C;IAAAG;IAAAC;EAAAA,IAAuCP,iBAAiB,GAIxDkH,oBAA0B/W,eAAA0D,OAE1B,GACA;IAAAsT,QAAA1W;EAAA,IAEI2W,SAAAA,GADM;IAAAC;EAAAA,IAAA5W,IAGV,CAAA6W,eAAAC,gBAAA,QAA0C/H,uBAAAA,IAAoC;AAAC3O,MAAAA;AAAAvB,IAAAA,CAAAA,MAAA2X,sBAExCpW,KAAAA,MAAMoW,mBAAAA,IAAuB,GAAC3X,EAAAA,CAAAA,IAAA2X,oBAAA3X,EAAAA,CAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,CAAA;AAArE,QAAAkY,qBAA2B3W;AAAiEQ,MAAAA;AAAA/B,IAAAA,CAAAA,MAAA2X,sBACvD5V,KAAAA,MAAM4V,mBAAAA,KAAwB,GAAC3X,EAAAA,CAAAA,IAAA2X,oBAAA3X,EAAAA,CAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,CAAA;AAApE,QAAAmY,mBAAyBpW;AAAkE,MAAAM,IAAAM;AAAA3C,IAAAsX,CAAAA,MAAAA,gBAAAtX,EAAAA,CAAAA,MAAAiR,iBAEjF5O,KAAAA,MAAA;AAIJ4O,kBAAa7P,UACfkW,aAAarG,aAAa;EAE3BtO,GAAAA,KAAA,CAAC2U,cAAcrG,aAAa,GAACjR,EAAAA,CAAAA,IAAAsX,cAAAtX,EAAAA,CAAAA,IAAAiR,eAAAjR,EAAAA,CAAAA,IAAAqC,IAAArC,EAAAA,CAAAA,IAAA2C,OAAAN,KAAArC,EAAA,CAAA,GAAA2C,KAAA3C,EAAA,CAAA,QAPhCuQ,wBAAUlO,IAOPM,EAA6B;AAAC,MAAAK,IAAAK;AAsB4D,MAtB5DrD,EAAAA,CAAAA,MAAAwX,aAEvBxU,KAAAA,MAAA;AACR,UAAAoV,sBAAAC,CAAA,UAAA;AAEMlB,mBAAakB,KAAK,MACpBA,MAAKC,eAAAA,GAELd,UAAS;QAAAe,UAAA;QAAAhV,IAEH;QAAmBiV,QACf;QAAMlC,OACP;QAAmCmC,UAAA;MAAA,CAE3C;IAAC;AAINC,WAAAA,OAAAA,iBAAwB,WAAWN,mBAAmB,GAAC,MAC1C9E,OAAAqF,oBAA2B,WAAWP,mBAAmB;EAAA,GACrE/U,KAAAA,CAACmU,SAAS,GAACxX,EAAAA,CAAAA,IAAAwX,WAAAxX,EAAAA,CAAAA,IAAAgD,IAAAhD,EAAAA,EAAAA,IAAAqD,OAAAL,KAAAhD,EAAA,CAAA,GAAAqD,KAAArD,EAAA,EAAA,QAlBduQ,wBAAUvN,IAkBPK,EAAW,GAE+E,GAA7DqS,YAAMkD,cAANlD,mBAAMmD,MAAAC,KAAAC,mCAEV;AAAAzV,QAAAA;AAAA,WAAAtD,EAAA,EAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KACnBzN,UAAA,wBAAC,uBAAwB,CAAA,CAAA,GAAAtD,EAAAA,EAAAA,IAAAsD,OAAAA,MAAAtD,EAAA,EAAA,GAAzBsD;EAAAA;AAIkBA,QAAAA,KAAA0U,iBAAqB,MAGlClU,KAAA4T,kBAAexT,SAAe,QAC5BH,MAAAgU,MAAK,CAAA;AAAG/T,MAAAA;AAAAhE,IAAAA,EAAAA,MAAAgR,iBAIjBhN,MAAAgN,cAAaxG,IAAA4F,QAoCd,GAACpQ,EAAAA,EAAAA,IAAAgR,eAAAhR,EAAAA,EAAAA,IAAAgE,OAAAA,MAAAhE,EAAA,EAAA;AAAAmE,MAAAA;AAAAnE,IAAAA,EAAAA,MAAA4X,qBAAA5X,EAAAgR,EAAAA,MAAAA,cAAA5P,UAGA+C,MAAA6M,cAAa5P,UAAAA,KAAgBwW,yBAAAA,wBAC3B,aAAoB,EAAA,SAAA,kBACtB,CAAA,GAAA5X,EAAAA,EAAAA,IAAA4X,mBAAA5X,EAAA,EAAA,IAAAgR,cAAA5P,QAAApB,EAAAA,EAAAA,IAAAmE,OAAAA,MAAAnE,EAAA,EAAA;AAAAoE,MAAAA;AAAApE,IAAAA,EAAAA,MAAAkY,sBAAAlY,EAAAmY,EAAAA,MAAAA,oBAAAnY,EAAA+X,EAAAA,MAAAA,MAAAA,CAAAA,KAAA/X,EAAA,EAAA,MAAAgE,OAAAhE,EAAAA,EAAAA,MAAAmE,OAAAnE,EAAA,EAAA,MAAA8D,MAhDHM,UAAC,yBAAA,kBAAA,EACO,MAAA,GACE,QAAAN,IACE,UAAAC,KACEmU,YAAiB,oBACnBC,UAAe,kBAExBnU,UAAAA;IAAAA;IAuCAG;EAAAA,EAGH,CAAA,GAAmBnE,EAAAA,EAAAA,IAAAkY,oBAAAlY,EAAAA,EAAAA,IAAAmY,kBAAAnY,EAAAA,EAAAA,IAAA+X,MAAA,CAAA,GAAA/X,EAAAA,EAAAA,IAAAgE,KAAAhE,EAAAA,EAAAA,IAAAmE,KAAAnE,EAAAA,EAAAA,IAAA8D,IAAA9D,EAAAA,EAAAA,IAAAoE,OAAAA,MAAApE,EAAA,EAAA;AAAAqE,MAAAA;AAAArE,IAAAA,EAAAA,MAAAiR,iBACnB5M,UAAAA,wBAAC,gBAAA,EAA8B4M,cAAAA,CAAiB,GAAAjR,EAAAA,EAAAA,IAAAiR,eAAAjR,EAAAA,EAAAA,IAAAqE,OAAAA,MAAArE,EAAA,EAAA;AAAAwE,MAAAA;AAAAxE,IAAA,EAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAChDvM,UAAA,wBAAA,OAAA,EAAiB,eAAA,IAAQyT,KAAe,iBAAK,CAAA,GAAAjY,EAAAA,EAAAA,IAAAwE,OAAAA,MAAAxE,EAAA,EAAA;AAAA8E,MAAAA;AAAA,SAAA9E,EAAAoE,EAAAA,MAAAA,OAAApE,EAAAA,EAAAA,MAAAqE,OAAArE,EAAA,EAAA,MAAAsD,MApD/CwB,UAAC,yBAAA,gBAAA,EAAwB,SAAAxB,IACvBc,UAAAA;IAAAA;IAkDAC;IACAG;EAAAA,EAAAA,CACF,GAAiBxE,EAAAA,EAAAA,IAAAoE,KAAApE,EAAAA,EAAAA,IAAAqE,KAAArE,EAAAA,EAAAA,IAAAsD,IAAAtD,EAAAA,EAAAA,IAAA8E,OAAAA,MAAA9E,EAAA,EAAA,GArDjB8E;AAqDiB,CAEpB;AA/GiC,SAAAP,QAAA3D,aAAA;AASD,SAAA,OAAOA,YAAWkJ,UAAY;AAAQ;AATrC,SAAAsG,SAAAnP,IAAA;AAiEvB,QAAA;IAAA2Q;IAAAC;IAAAvQ;IAAAqQ;IAAApI,KAAA+K;IAAA1J;IAAAxK,OAAA4Y;IAAA3Y,QAAA4Y;IAAAzP;IAAAlJ;IAAAC;IAAAwR;EAAAA,IAAA9Q;AAaA,aACE,wBAAA,uBAAA,EACE2J,UAAIkH,SAAAA,mBAAAA,wBACF,aAAqBwC,EAAAA,SAAe9K,MAAgBuI,SAAAA,CAAQ,QAE5D,wBAAA,mBAAA,EACSH,QACItQ,YACL0X,OAAAA,WACDpO,MACOiH,aACLF,QACC2C,SACD2E,QAAS,YACR3Y,SACHkJ,MACIuI,UACIxR,aAElB,CAAA,EAAA,GAAA,GAlBgBqK,SAAIkH,eAAoB,YAAYlH,KAAInH,IAAK,IAAIuV,SAAS,EAmB5E;AAAW;ACjHhB,SAAAE,sBAAAjY,IAAA;AAAAjB,QAAAA,QAAAC,iCAAA,EAAA,GAA+B;IAAAkZ,MAAAhY;EAAAA,IAAAF,IAAO;IAAAyD;EAAAA,IAAAvD,IAC3C;IAAAiY,kBAAAC;EAAoCC,IAAAA,aACpCC,GAAAA,CAAAA,WAAA,IAAsBF;AAAO9X,MAAAA;AAAAvB,IAAAA,CAAAA,MAAA0E,WACoBnD,KAAAmD,WAAa,CAAA1E,GAAAA,EAAAA,CAAAA,IAAA0E,SAAA1E,EAAAA,CAAAA,IAAAuB,MAAAA,KAAAvB,EAAA,CAAA;AAA9D,QAAA;IAAAwZ;IAAAC;IAAAC;EAAAA,IAAiDnY;AAAaQ,MAAAA;AAAA/B,IAAA,CAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAM3DhP,KAAA,CAAA,GAAE/B,EAAAA,CAAAA,IAAA+B,MAAAA,KAAA/B,EAAA,CAAA,OAHLuQ,wBAAAH,QAGGrO,EAAE;AAACM,MAAAA;AAAArC,IAAA,CAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAEiD1O,KAAA;IAAAsO,OAAA;EAAA,GAAa3Q,EAAAA,CAAAA,IAAAqC,MAAAA,KAAArC,EAAA,CAAA;AAApE,QAAA,CAAA2C,IAAAiO,QAAA,QAA4BV,uBAA2B7N,EAAa,GAA7D;IAAAsO;EAAAA,IAAAhO;AAAO,MAGVgO,OAAK;AAAA3N,QAAAA;AAAAhD,WAAAA,EAAAA,CAAAA,MAAA2Q,SAAS3N,UAAC,wBAAA,gBAAA,EAAsB2N,MAAAA,CAAS,GAAA3Q,EAAAA,CAAAA,IAAA2Q,OAAA3Q,EAAAA,CAAAA,IAAAgD,OAAAA,MAAAhD,EAAA,CAAA,GAAhCgD;EAAAA;AAIQA,QAAAA,KAAAwW,UAAUD,YAAW3S;AAAK,MAAAvD,IAAAC;AAAAtD,IAAA,CAAA,MAAA8Q,OAAAC,IAAA,2BAAA,KAE5C1N,SAAC,wBAAA,eAAA,EAA4BsW,cAAAA,eAAkB,CAAA,GAC/CrW,SAAA,wBAAC,gBAAiB,CAAA,CAAA,GAAAtD,EAAAA,CAAAA,IAAAqD,IAAArD,EAAAA,CAAAA,IAAAsD,OAAAD,KAAArD,EAAA,CAAA,GAAAsD,KAAAtD,EAAA,CAAA;AAAA8D,MAAAA;AAAA9D,IAAAyZ,CAAAA,MAAAA,uBAAAzZ,EAAAA,CAAAA,MAAA0Z,aAFpB5V,SAAA,yBAAC,uBAA2C2V,EAAAA,qBAAgCC,WAC1ErW,UAAAA;IAAAA;IACAC;EAAAA,EACF,CAAA,GAAwBtD,EAAAA,CAAAA,IAAAyZ,qBAAAzZ,EAAAA,CAAAA,IAAA0Z,WAAA1Z,EAAAA,EAAAA,IAAA8D,MAAAA,KAAA9D,EAAA,EAAA;AAAA+D,MAAAA;AAAA,SAAA/D,EAAAgD,EAAAA,MAAAA,MAAAhD,EAAAA,EAAAA,MAAA8D,MAL5BC,UAAAA,wBAAC,eAAuB6M,EAAAA,SAAAA,UACtB,cAAA,wBAAC,gBAAA,EAAqB,MAAA5N,IACpBc,UAIF,GAAA,CAAA,EAAA,CACF,GAAgB9D,EAAAA,EAAAA,IAAAgD,IAAAhD,EAAAA,EAAAA,IAAA8D,IAAA9D,EAAAA,EAAAA,IAAA+D,OAAAA,MAAA/D,EAAA,EAAA,GAPhB+D;AAOgB;AAxBb,SAAAqM,SAAA;yBAOc,CAAA,GAAC7L;AAAA;AAPf,SAAAA,QAAA;AAQUoV,SAAAA,eAAAA,CAAAA,CAAiB;AAAC;", "names": ["emptyArray", "PaneRouterProvider", "props", "$", "_c", "children", "flatIndex", "index", "params", "payload", "siblingIndex", "navigate", "navigateIntent", "resolvePathFromState", "useRouter", "routerState", "useRouterState", "panes", "expand", "usePaneLayout", "t0", "routerPaneGroups", "t1", "length", "lastPane", "groupIndex", "t2", "modifier", "currentGroup", "currentItem", "nextGroup", "nextPanes", "slice", "createNextRouterState", "t3", "modifier_0", "nextRouterState_0", "setTimeout", "nextRouterState", "modifyCurrentGroup", "t4", "nextParams", "nextRouterState_1", "siblings", "item", "createPathWithParams", "t5", "nextPayload", "siblings_0", "item_0", "setPayload", "t6", "nextParams_0", "siblings_1", "item_1", "setParams", "t7", "t8", "id", "parentRefPath", "type", "template", "version", "pathToString", "handleEditReference", "t9", "t10", "t11", "BackLink", "undefined", "t12", "t13", "t14", "opts", "_temp", "t15", "element", "options", "siblings_3", "item_3", "duplicatedItem", "t16", "viewId", "restParams", "omit", "view", "t17", "hasGroupSiblings", "groupLength", "routerPanesState", "ChildLink", "ReferenceChildLink", "ParameterizedLink", "replaceCurrent", "closeCurrent", "closeCurrentAndAfter", "duplicateCurrent", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "t18", "siblings_2", "item_2", "filter", "sibling", "PaneResolutionError", "Error", "constructor", "message", "context", "helpId", "cause", "name", "randomIdCache", "WeakMap", "assignId", "obj", "cachedValue", "get", "nanoid", "set", "isPromise", "thing", "then", "isSerializable", "isRecord", "serialize", "rethrowWithPaneResolutionErrors", "next", "unresolvedPane", "e", "wrapWithPublishReplay", "args", "pipe", "publishReplay", "refCount", "createPaneResolver", "middleware", "resolvePane", "isObservable", "from", "switchMap", "result", "observableOf", "bind<PERSON>ache", "memoBind", "<PERSON><PERSON><PERSON>", "boundMethods", "Map", "bound", "method", "bind", "resolveIntent", "resolvedPaneCache", "nextFn", "key", "path", "join", "cachedResolvedPane", "fallbackEditorPanes", "traverse", "currentId", "intent", "parent", "levelIndex", "structureContext", "targetId", "schemaTypeName", "otherParams", "resolvedPane", "firstValueFrom", "splitIndex", "map", "i", "depthIndex", "canHandleIntent", "pane", "child", "items", "Promise", "all", "nextLevelIndex", "resolve", "_id", "flat", "closestPaneToRoot", "rootPaneNode", "sort", "a", "b", "fallback<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeId", "replace", "resolveDocumentNode", "defaultDocumentBuilder", "schemaType", "documentId", "initialValueTemplate", "hashContext", "JSON", "stringify", "parentId", "serializeOptionsIndex", "serializeOptions", "serializeOptionsPath", "hashResolvedPaneMeta", "meta", "normalized", "routerPaneSibling", "paneNode", "resolvePaneTree", "flattenedRouterPanes", "current", "rest", "resolvedPaneMeta", "loadingPanes", "restIndex", "_", "currentIndex", "nextStream", "startsWith", "concat", "nextResolvedPanes", "console", "warn", "generateHelpUrl", "createResolvedPaneNodeStream", "routerPanesStream", "initialCacheState", "cacheKeysByFlatIndex", "NEVER", "rawRouterPanes", "routerPanes", "flatMap", "routerPaneGroup", "startWith", "pairwise", "prev", "curr", "prevValue", "currValue", "isEqual", "diffIndex", "scan", "acc", "beforeDiffIndex", "afterDiffIndex", "keysToKeep", "Set", "keySet", "Array", "keysToDelete", "has", "delete", "cacheKeySet", "add", "nextPane", "prevPane", "distinctUntilChanged", "nextValue", "useRouterPanesStream", "routerStateSubject", "useState", "asObservable", "_temp2", "routerPanes$", "state", "useEffect", "_routerState", "ReplaySubject", "useResolvedPanes", "error", "setError", "useStructureTool", "Symbol", "for", "paneDataItems", "resolvedPanes", "data", "setData", "subscription", "_temp5", "subscribe", "unsubscribe", "reduce", "_temp3", "groupsLen", "itemId", "active", "childItemId", "LOADING_PANE", "selected", "_temp4", "pane_0", "ensureDocumentIdAndType", "documentStore", "uuid", "resolvedType", "resolveTypeForDocument", "EMPTY_RECORD", "IntentResolver", "memo", "maybeIntent", "useDocumentStore", "cancelled", "effect", "catch", "intentName", "PathSegment", "styled", "span", "formatStack", "stack", "RegExp", "window", "location", "host", "StructureError", "t", "useTranslation", "structureLocaleNamespace", "showStack", "SerializeError", "includes", "handleReload", "SyncIcon", "segment", "reload", "<PERSON><PERSON><PERSON>", "isSelected", "paneKey", "paneMap", "component", "lazy", "document", "documentList", "list", "StructureToolPane", "PaneComponent", "prevParams", "prevPayload", "keys", "Object", "NoDocumentTypesScreen", "DocumentTitle", "documentType", "selectedReleaseId", "usePerspective", "editState", "useEditState", "schema", "useSchema", "isNewDocument", "published", "draft", "documentValue", "value", "isLoading", "previewValueIsLoading", "useValuePreview", "enabled", "documentTitle", "title", "settled", "ready", "newTitle", "useConstructDocumentTitle", "PassthroughTitle", "StructureTitle", "isLoadingPane", "isDocumentPane", "activeTitle", "structureToolBaseTitle", "StyledPaneLayout", "PaneLayout", "isSaveHotkey", "isHotkey", "StructureTool", "onPaneChange", "push", "pushToast", "useToast", "layoutCollapsed", "setLayoutCollapsed", "isResolvingIntent", "sanity", "useTheme", "media", "portalElement", "setPortalElement", "handleRootCollapse", "handleRootExpand", "handleGlobalKeyDown", "event", "preventDefault", "closable", "status", "duration", "addEventListener", "removeEventListener", "_original", "types", "some", "_isCustomDocumentTypeDefinition", "paneIndex", "paneParams", "StructureToolBoundary", "tool", "unstable_sources", "sources", "useWorkspace", "firstSource", "source", "defaultDocumentNode", "structure", "setActivePanes"]}
import {
  BackLink,
  ChildLink,
  LOADING_PANE,
  LoadingPane,
  Pane,
  PaneContent,
  PaneHeader$1,
  PaneLayout,
  ParameterizedLink,
  ReferenceChildLink,
  SerializeError,
  StructureToolProvider,
  setActivePanes,
  structureLocaleNamespace,
  usePaneLayout,
  useStructureTool
} from "./chunk-2QT23K2R.js";
import "./chunk-OV2JUDPU.js";
import {
  Button,
  ErrorBoundary,
  NEVER,
  PaneRouterContext,
  ReplaySubject,
  SourceProvider,
  Translate,
  _isCustomDocumentTypeDefinition,
  concat,
  distinctUntilChanged,
  firstValueFrom,
  from,
  generateHelpUrl,
  isHotkey,
  isObservable,
  isRecord$4,
  map,
  nanoid,
  of,
  pairwise,
  publishReplay,
  refCount,
  require_isEqual,
  require_omit,
  scan,
  startWith,
  switchMap,
  toString,
  useDocumentPreview,
  useDocumentStore,
  useEditState,
  usePerspective,
  useRouter,
  useRouterState,
  useSchema,
  useTranslation,
  useWorkspace,
  v4_default
} from "./chunk-HKF2AZUV.js";
import {
  Box,
  Card,
  Code,
  Container,
  Flex,
  Heading,
  PortalProvider,
  Stack,
  Text,
  dt,
  useTheme,
  useToast
} from "./chunk-AWGNGER7.js";
import "./chunk-KSAIUMCL.js";
import {
  require_dist
} from "./chunk-JX5BQTZ6.js";
import "./chunk-QCUVG3AT.js";
import "./chunk-N7YGFUAT.js";
import {
  SyncIcon,
  WarningOutlineIcon
} from "./chunk-HOFXQ7MC.js";
import {
  require_jsx_runtime
} from "./chunk-ZQZQC55M.js";
import {
  require_react
} from "./chunk-BD3T7BTJ.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// node_modules/sanity/lib/_chunks-es/index3.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react_compiler_runtime = __toESM(require_dist(), 1);
var import_react = __toESM(require_react(), 1);
var import_omit = __toESM(require_omit(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var emptyArray = [];
function PaneRouterProvider(props) {
  const $ = (0, import_react_compiler_runtime.c)(60), {
    children,
    flatIndex,
    index,
    params,
    payload,
    siblingIndex
  } = props, {
    navigate,
    navigateIntent,
    resolvePathFromState
  } = useRouter(), routerState = useRouterState(), {
    panes,
    expand
  } = usePaneLayout();
  let t0;
  t0 = (routerState == null ? void 0 : routerState.panes) || emptyArray;
  const routerPaneGroups = t0;
  let t1;
  t1 = panes == null ? void 0 : panes[panes.length - 2];
  const lastPane = t1, groupIndex = index - 1;
  let t2;
  $[0] !== groupIndex || $[1] !== routerPaneGroups || $[2] !== routerState || $[3] !== siblingIndex ? (t2 = (modifier) => {
    const currentGroup = routerPaneGroups[groupIndex] || [], currentItem = currentGroup[siblingIndex], nextGroup = modifier(currentGroup, currentItem), nextPanes = [...routerPaneGroups.slice(0, groupIndex), nextGroup, ...routerPaneGroups.slice(groupIndex + 1)];
    return {
      ...routerState,
      panes: nextPanes
    };
  }, $[0] = groupIndex, $[1] = routerPaneGroups, $[2] = routerState, $[3] = siblingIndex, $[4] = t2) : t2 = $[4];
  const createNextRouterState = t2;
  let t3;
  $[5] !== createNextRouterState || $[6] !== navigate ? (t3 = (modifier_0) => {
    const nextRouterState_0 = createNextRouterState(modifier_0);
    return setTimeout(() => navigate(nextRouterState_0), 0), nextRouterState_0;
  }, $[5] = createNextRouterState, $[6] = navigate, $[7] = t3) : t3 = $[7];
  const modifyCurrentGroup = t3;
  let t4;
  $[8] !== createNextRouterState || $[9] !== resolvePathFromState || $[10] !== siblingIndex ? (t4 = (nextParams) => {
    const nextRouterState_1 = createNextRouterState((siblings, item) => [...siblings.slice(0, siblingIndex), {
      ...item,
      params: nextParams
    }, ...siblings.slice(siblingIndex + 1)]);
    return resolvePathFromState(nextRouterState_1);
  }, $[8] = createNextRouterState, $[9] = resolvePathFromState, $[10] = siblingIndex, $[11] = t4) : t4 = $[11];
  const createPathWithParams = t4;
  let t5;
  $[12] !== modifyCurrentGroup || $[13] !== siblingIndex ? (t5 = (nextPayload) => {
    modifyCurrentGroup((siblings_0, item_0) => [...siblings_0.slice(0, siblingIndex), {
      ...item_0,
      payload: nextPayload
    }, ...siblings_0.slice(siblingIndex + 1)]);
  }, $[12] = modifyCurrentGroup, $[13] = siblingIndex, $[14] = t5) : t5 = $[14];
  const setPayload = t5;
  let t6;
  $[15] !== modifyCurrentGroup || $[16] !== siblingIndex ? (t6 = (nextParams_0) => {
    modifyCurrentGroup((siblings_1, item_1) => [...siblings_1.slice(0, siblingIndex), {
      ...item_1,
      params: nextParams_0
    }, ...siblings_1.slice(siblingIndex + 1)]);
  }, $[15] = modifyCurrentGroup, $[16] = siblingIndex, $[17] = t6) : t6 = $[17];
  const setParams = t6;
  let t7;
  $[18] !== groupIndex || $[19] !== navigate || $[20] !== routerPaneGroups ? (t7 = (t82) => {
    const {
      id,
      parentRefPath,
      type,
      template,
      version
    } = t82;
    navigate({
      panes: [...routerPaneGroups.slice(0, groupIndex + 1), [{
        id,
        params: {
          template: template.id,
          parentRefPath: toString(parentRefPath),
          type,
          version
        },
        payload: template.params
      }]]
    });
  }, $[18] = groupIndex, $[19] = navigate, $[20] = routerPaneGroups, $[21] = t7) : t7 = $[21];
  const handleEditReference = t7;
  let t8;
  const t9 = routerPaneGroups[groupIndex] ? routerPaneGroups[groupIndex].length > 1 : false, t10 = routerPaneGroups[groupIndex] ? routerPaneGroups[groupIndex].length : 0, t11 = flatIndex ? BackLink : void 0;
  let t12, t13;
  $[22] !== modifyCurrentGroup ? (t12 = (t142) => {
    const opts = t142 === void 0 ? {} : t142;
    modifyCurrentGroup(() => [{
      id: opts.id || "",
      payload: opts.payload,
      params: opts.params || {}
    }]);
  }, t13 = () => {
    modifyCurrentGroup(_temp$6);
  }, $[22] = modifyCurrentGroup, $[23] = t12, $[24] = t13) : (t12 = $[23], t13 = $[24]);
  let t14;
  $[25] !== expand || $[26] !== groupIndex || $[27] !== lastPane || $[28] !== navigate || $[29] !== routerPaneGroups ? (t14 = (t152) => {
    (t152 === void 0 || t152) && lastPane && expand(lastPane.element), navigate({
      panes: routerPaneGroups.slice(0, groupIndex)
    });
  }, $[25] = expand, $[26] = groupIndex, $[27] = lastPane, $[28] = navigate, $[29] = routerPaneGroups, $[30] = t14) : t14 = $[30];
  let t15;
  $[31] !== modifyCurrentGroup || $[32] !== siblingIndex ? (t15 = (options) => {
    modifyCurrentGroup((siblings_3, item_3) => {
      const duplicatedItem = {
        ...item_3,
        payload: (options == null ? void 0 : options.payload) || item_3.payload,
        params: (options == null ? void 0 : options.params) || item_3.params
      };
      return [...siblings_3.slice(0, siblingIndex), duplicatedItem, ...siblings_3.slice(siblingIndex)];
    });
  }, $[31] = modifyCurrentGroup, $[32] = siblingIndex, $[33] = t15) : t15 = $[33];
  let t16;
  $[34] !== params || $[35] !== setParams ? (t16 = (viewId) => {
    const restParams = (0, import_omit.default)(params, "view");
    return setParams(viewId ? {
      ...restParams,
      view: viewId
    } : restParams);
  }, $[34] = params, $[35] = setParams, $[36] = t16) : t16 = $[36];
  let t17;
  $[37] !== createPathWithParams || $[38] !== flatIndex || $[39] !== groupIndex || $[40] !== handleEditReference || $[41] !== navigateIntent || $[42] !== params || $[43] !== payload || $[44] !== routerPaneGroups || $[45] !== setParams || $[46] !== setPayload || $[47] !== siblingIndex || $[48] !== t10 || $[49] !== t11 || $[50] !== t12 || $[51] !== t13 || $[52] !== t14 || $[53] !== t15 || $[54] !== t16 || $[55] !== t9 ? (t17 = {
    index: flatIndex,
    groupIndex,
    siblingIndex,
    payload,
    params,
    hasGroupSiblings: t9,
    groupLength: t10,
    routerPanesState: routerPaneGroups,
    ChildLink,
    BackLink: t11,
    ReferenceChildLink,
    handleEditReference,
    ParameterizedLink,
    replaceCurrent: t12,
    closeCurrent: t13,
    closeCurrentAndAfter: t14,
    duplicateCurrent: t15,
    setView: t16,
    setParams,
    setPayload,
    createPathWithParams,
    navigateIntent
  }, $[37] = createPathWithParams, $[38] = flatIndex, $[39] = groupIndex, $[40] = handleEditReference, $[41] = navigateIntent, $[42] = params, $[43] = payload, $[44] = routerPaneGroups, $[45] = setParams, $[46] = setPayload, $[47] = siblingIndex, $[48] = t10, $[49] = t11, $[50] = t12, $[51] = t13, $[52] = t14, $[53] = t15, $[54] = t16, $[55] = t9, $[56] = t17) : t17 = $[56], t8 = t17;
  const ctx = t8;
  let t18;
  return $[57] !== children || $[58] !== ctx ? (t18 = (0, import_jsx_runtime.jsx)(PaneRouterContext.Provider, { value: ctx, children }), $[57] = children, $[58] = ctx, $[59] = t18) : t18 = $[59], t18;
}
function _temp$6(siblings_2, item_2) {
  return siblings_2.length > 1 ? siblings_2.filter((sibling) => sibling !== item_2) : siblings_2;
}
var PaneResolutionError = class extends Error {
  constructor({
    message,
    context,
    helpId,
    cause
  }) {
    super(message), this.name = "PaneResolutionError", this.context = context, this.helpId = helpId, this.cause = cause;
  }
};
var randomIdCache = /* @__PURE__ */ new WeakMap();
function assignId(obj) {
  const cachedValue = randomIdCache.get(obj);
  if (cachedValue) return cachedValue;
  const id = nanoid();
  return randomIdCache.set(obj, id), id;
}
var isPromise = (thing) => !!thing && typeof (thing == null ? void 0 : thing.then) == "function";
var isSerializable = (thing) => isRecord$4(thing) ? typeof thing.serialize == "function" : false;
var rethrowWithPaneResolutionErrors = (next) => (unresolvedPane, context, flatIndex) => {
  try {
    return next(unresolvedPane, context, flatIndex);
  } catch (e) {
    throw e instanceof PaneResolutionError ? e : new PaneResolutionError({
      message: typeof (e == null ? void 0 : e.message) == "string" ? e.message : "",
      context,
      cause: e
    });
  }
};
var wrapWithPublishReplay = (next) => (...args) => next(...args).pipe(
  // need to add publishReplay + refCount to ensure new subscribers always
  // get an emission. without this, memoized observables may get stuck
  // waiting for their first emissions resulting in a loading pane
  publishReplay(1),
  refCount()
);
function createPaneResolver(middleware) {
  const resolvePane = rethrowWithPaneResolutionErrors(wrapWithPublishReplay(middleware((unresolvedPane, context, flatIndex) => {
    if (!unresolvedPane)
      throw new PaneResolutionError({
        message: "Pane returned no child",
        context,
        helpId: "structure-item-returned-no-child"
      });
    return isPromise(unresolvedPane) || isObservable(unresolvedPane) ? from(unresolvedPane).pipe(switchMap((result) => resolvePane(result, context, flatIndex))) : isSerializable(unresolvedPane) ? resolvePane(unresolvedPane.serialize(context), context, flatIndex) : typeof unresolvedPane == "function" ? resolvePane(unresolvedPane(context.id, context), context, flatIndex) : of(unresolvedPane);
  })));
  return resolvePane;
}
var bindCache = /* @__PURE__ */ new WeakMap();
function memoBind(obj, methodKey) {
  const boundMethods = bindCache.get(obj) || /* @__PURE__ */ new Map();
  if (boundMethods) {
    const bound2 = boundMethods.get(methodKey);
    if (bound2) return bound2;
  }
  const method = obj[methodKey];
  if (typeof method != "function")
    throw new Error(`Expected property \`${methodKey}\` to be a function but got ${typeof method} instead.`);
  const bound = method.bind(obj);
  return boundMethods.set(methodKey, bound), bindCache.set(obj, boundMethods), bound;
}
async function resolveIntent(options) {
  const resolvedPaneCache = /* @__PURE__ */ new Map(), resolvePane = createPaneResolver((nextFn) => (unresolvedPane, context, flatIndex) => {
    const key = unresolvedPane && `${assignId(unresolvedPane)}-${context.path.join("__")}`, cachedResolvedPane = key && resolvedPaneCache.get(key);
    if (cachedResolvedPane) return cachedResolvedPane;
    const result = nextFn(unresolvedPane, context, flatIndex);
    return key && resolvedPaneCache.set(key, result), result;
  }), fallbackEditorPanes = [[{
    id: `__edit__${options.params.id}`,
    params: {
      ...(0, import_omit.default)(options.params, ["id"]),
      type: options.params.type
    },
    payload: options.payload
  }]];
  async function traverse({
    currentId,
    flatIndex,
    intent,
    params,
    parent: parent2,
    path,
    payload,
    unresolvedPane,
    levelIndex,
    structureContext
  }) {
    var _a;
    if (!unresolvedPane) return [];
    const {
      id: targetId,
      type: schemaTypeName,
      ...otherParams
    } = params, resolvedPane = await firstValueFrom(resolvePane(unresolvedPane, {
      id: currentId,
      splitIndex: 0,
      parent: parent2,
      path,
      index: flatIndex,
      params: {},
      payload: void 0,
      structureContext
    }, flatIndex));
    return resolvedPane.type === "document" && resolvedPane.id === targetId ? [{
      panes: [...path.slice(0, path.length - 1).map((i) => [{
        id: i
      }]), [{
        id: targetId,
        params: otherParams,
        payload
      }]],
      depthIndex: path.length,
      levelIndex
    }] : (
      // if the resolve pane's `canHandleIntent` returns true, then resolve
      ((_a = resolvedPane.canHandleIntent) == null ? void 0 : _a.call(resolvedPane, intent, params, {
        pane: resolvedPane,
        index: flatIndex
      })) || // if the pane's `canHandleIntent` did not return true, then match against
      // this default case. we will resolve the intent if:
      resolvedPane.type === "documentList" && // 1. the schema type matches (this required for the document to render)
      resolvedPane.schemaTypeName === schemaTypeName && // 2. the filter is the default filter.
      //
      // NOTE: this case is to prevent false positive matches where the user
      // has configured a more specific filter for a particular type. In that
      // case, the user can implement their own `canHandleIntent` function
      resolvedPane.options.filter === "_type == $type" ? [{
        panes: [
          // map the current path to router panes
          ...path.map((id) => [{
            id
          }]),
          // then augment with the intents IDs and params
          [{
            id: params.id,
            params: otherParams,
            payload
          }]
        ],
        depthIndex: path.length,
        levelIndex
      }] : resolvedPane.type === "list" && resolvedPane.child && resolvedPane.items ? (await Promise.all(resolvedPane.items.map((item, nextLevelIndex) => item.type === "divider" ? Promise.resolve([]) : traverse({
        currentId: item._id || item.id,
        flatIndex: flatIndex + 1,
        intent,
        params,
        parent: resolvedPane,
        path: [...path, item.id],
        payload,
        unresolvedPane: typeof resolvedPane.child == "function" ? memoBind(resolvedPane, "child") : resolvedPane.child,
        levelIndex: nextLevelIndex,
        structureContext
      })))).flat() : []
    );
  }
  const closestPaneToRoot = (await traverse({
    currentId: "root",
    flatIndex: 0,
    levelIndex: 0,
    intent: options.intent,
    params: options.params,
    parent: null,
    path: [],
    payload: options.payload,
    unresolvedPane: options.rootPaneNode,
    structureContext: options.structureContext
  })).sort((a, b) => a.depthIndex === b.depthIndex ? a.levelIndex - b.levelIndex : a.depthIndex - b.depthIndex)[0];
  return closestPaneToRoot ? closestPaneToRoot.panes : fallbackEditorPanes;
}
var fallbackEditorChild = (nodeId, context) => {
  const id = nodeId.replace(/^__edit__/, ""), {
    params,
    payload,
    structureContext: {
      resolveDocumentNode
    }
  } = context, {
    type,
    template
  } = params;
  if (!type)
    throw new Error(`Document type for document with ID ${id} was not provided in the router params.`);
  let defaultDocumentBuilder = resolveDocumentNode({
    schemaType: type,
    documentId: id
  }).id("editor");
  return template && (defaultDocumentBuilder = defaultDocumentBuilder.initialValueTemplate(template, payload)), defaultDocumentBuilder.serialize();
};
function hashContext(context) {
  var _a, _b;
  return `contextHash(${JSON.stringify({
    id: context.id,
    parentId: parent && assignId(parent),
    path: context.path,
    index: context.index,
    splitIndex: context.splitIndex,
    serializeOptionsIndex: (_a = context.serializeOptions) == null ? void 0 : _a.index,
    serializeOptionsPath: (_b = context.serializeOptions) == null ? void 0 : _b.path
  })})`;
}
var hashResolvedPaneMeta = (meta) => {
  const normalized = {
    type: meta.type,
    id: meta.routerPaneSibling.id,
    params: meta.routerPaneSibling.params || {},
    payload: meta.routerPaneSibling.payload || null,
    flatIndex: meta.flatIndex,
    groupIndex: meta.groupIndex,
    siblingIndex: meta.siblingIndex,
    path: meta.path,
    paneNode: meta.type === "resolvedMeta" ? assignId(meta.paneNode) : null
  };
  return `metaHash(${JSON.stringify(normalized)})`;
};
function resolvePaneTree({
  unresolvedPane,
  flattenedRouterPanes,
  parent: parent2,
  path,
  resolvePane,
  structureContext
}) {
  const [current, ...rest] = flattenedRouterPanes, next = rest[0], context = {
    id: current.routerPaneSibling.id,
    splitIndex: current.siblingIndex,
    parent: parent2,
    path: [...path, current.routerPaneSibling.id],
    index: current.flatIndex,
    params: current.routerPaneSibling.params || {},
    payload: current.routerPaneSibling.payload,
    structureContext
  };
  try {
    return resolvePane(unresolvedPane, context, current.flatIndex).pipe(
      // this switch map receives a resolved pane
      switchMap((paneNode) => {
        const resolvedPaneMeta = {
          type: "resolvedMeta",
          ...current,
          paneNode,
          path: context.path
        }, loadingPanes = rest.map((i, restIndex) => ({
          type: "loading",
          path: [...context.path, ...rest.slice(restIndex).map((_, currentIndex) => `[${i.flatIndex + currentIndex}]`)],
          paneNode: null,
          ...i
        }));
        if (!rest.length)
          return of([resolvedPaneMeta]);
        let nextStream;
        return (
          /* the fallback editor case */
          (next == null ? void 0 : next.routerPaneSibling.id.startsWith("__edit__")) ? nextStream = resolvePaneTree({
            unresolvedPane: fallbackEditorChild,
            flattenedRouterPanes: rest,
            parent: parent2,
            path: context.path,
            resolvePane,
            structureContext
          }) : current.groupIndex === (next == null ? void 0 : next.groupIndex) ? nextStream = resolvePaneTree({
            unresolvedPane,
            flattenedRouterPanes: rest,
            parent: parent2,
            path,
            resolvePane,
            structureContext
          }) : nextStream = resolvePaneTree({
            unresolvedPane: typeof paneNode.child == "function" ? memoBind(paneNode, "child") : paneNode.child,
            flattenedRouterPanes: rest,
            parent: paneNode,
            path: context.path,
            resolvePane,
            structureContext
          }), concat(
            // we emit the loading panes first in a concat (this emits immediately)
            of([resolvedPaneMeta, ...loadingPanes]),
            // then whenever the next stream is done, the results will be combined.
            nextStream.pipe(map((nextResolvedPanes) => [resolvedPaneMeta, ...nextResolvedPanes]))
          )
        );
      })
    );
  } catch (e) {
    if (e instanceof PaneResolutionError && (e.context && console.warn(`Pane resolution error at index ${e.context.index}${e.context.splitIndex > 0 ? ` for split pane index ${e.context.splitIndex}` : ""}: ${e.message}${e.helpId ? ` - see ${generateHelpUrl(e.helpId)}` : ""}`, e), e.helpId === "structure-item-returned-no-child"))
      return of([]);
    throw e;
  }
}
function createResolvedPaneNodeStream({
  routerPanesStream,
  rootPaneNode,
  initialCacheState = {
    cacheKeysByFlatIndex: [],
    flattenedRouterPanes: [],
    resolvedPaneCache: /* @__PURE__ */ new Map(),
    resolvePane: () => NEVER
  },
  structureContext
}) {
  return routerPanesStream.pipe(
    // add in implicit "root" router pane
    map((rawRouterPanes) => [[{
      id: "root"
    }], ...rawRouterPanes]),
    // create flattened router panes
    map((routerPanes) => routerPanes.flatMap((routerPaneGroup, groupIndex) => routerPaneGroup.map((routerPaneSibling, siblingIndex) => ({
      routerPaneSibling,
      groupIndex,
      siblingIndex
    }))).map((i, index) => ({
      ...i,
      flatIndex: index
    }))),
    // calculate a "diffIndex" used for clearing the memo cache
    startWith([]),
    pairwise(),
    map(([prev, curr]) => {
      for (let i = 0; i < curr.length; i++) {
        const prevValue = prev[i], currValue = curr[i];
        if (!(0, import_isEqual.default)(prevValue, currValue))
          return {
            flattenedRouterPanes: curr,
            diffIndex: i
          };
      }
      return {
        flattenedRouterPanes: curr,
        diffIndex: curr.length
      };
    }),
    // create the memoized `resolvePane` function and manage the memo cache
    scan((acc, next) => {
      const {
        cacheKeysByFlatIndex,
        resolvedPaneCache
      } = acc, {
        flattenedRouterPanes,
        diffIndex
      } = next, beforeDiffIndex = cacheKeysByFlatIndex.slice(0, diffIndex + 1), afterDiffIndex = cacheKeysByFlatIndex.slice(diffIndex + 1), keysToKeep = new Set(beforeDiffIndex.flatMap((keySet) => Array.from(keySet))), keysToDelete = afterDiffIndex.flatMap((keySet) => Array.from(keySet)).filter((key) => !keysToKeep.has(key));
      for (const key of keysToDelete)
        resolvedPaneCache.delete(key);
      return {
        flattenedRouterPanes,
        cacheKeysByFlatIndex,
        resolvedPaneCache,
        resolvePane: createPaneResolver((nextFn) => (unresolvedPane, context, flatIndex) => {
          const key = unresolvedPane && `${assignId(unresolvedPane)}-${hashContext(context)}`, cachedResolvedPane = key && resolvedPaneCache.get(key);
          if (cachedResolvedPane) return cachedResolvedPane;
          const result = nextFn(unresolvedPane, context, flatIndex);
          if (!key) return result;
          const cacheKeySet = cacheKeysByFlatIndex[flatIndex] || /* @__PURE__ */ new Set();
          return cacheKeySet.add(key), cacheKeysByFlatIndex[flatIndex] = cacheKeySet, resolvedPaneCache.set(key, result), result;
        })
      };
    }, initialCacheState),
    // run the memoized, recursive resolving
    switchMap(({
      flattenedRouterPanes,
      resolvePane
    }) => resolvePaneTree({
      unresolvedPane: rootPaneNode,
      flattenedRouterPanes,
      parent: null,
      path: [],
      resolvePane,
      structureContext
    }))
  ).pipe(
    // this diffs the previous emission with the current one. if there is a new
    // loading pane at the same position where a previous pane already had a
    // resolved value (looking at the IDs to compare), then return the previous
    // pane instead of the loading pane
    scan((prev, next) => next.map((nextPane, index) => {
      const prevPane = prev[index];
      return !prevPane || nextPane.type !== "loading" ? nextPane : prevPane.routerPaneSibling.id === nextPane.routerPaneSibling.id ? prevPane : nextPane;
    }), []),
    // this prevents duplicate emissions
    distinctUntilChanged((prev, next) => {
      if (prev.length !== next.length) return false;
      for (let i = 0; i < next.length; i++) {
        const prevValue = prev[i], nextValue = next[i];
        if (hashResolvedPaneMeta(prevValue) !== hashResolvedPaneMeta(nextValue))
          return false;
      }
      return true;
    })
  );
}
function useRouterPanesStream() {
  const $ = (0, import_react_compiler_runtime.c)(6), [routerStateSubject] = (0, import_react.useState)(_temp$5);
  let t0, t1;
  $[0] !== routerStateSubject ? (t1 = routerStateSubject.asObservable().pipe(map(_temp2$3)), $[0] = routerStateSubject, $[1] = t1) : t1 = $[1], t0 = t1;
  const routerPanes$ = t0, {
    state: routerState
  } = useRouter();
  let t2, t3;
  return $[2] !== routerState || $[3] !== routerStateSubject ? (t2 = () => {
    routerStateSubject.next(routerState);
  }, t3 = [routerState, routerStateSubject], $[2] = routerState, $[3] = routerStateSubject, $[4] = t2, $[5] = t3) : (t2 = $[4], t3 = $[5]), (0, import_react.useEffect)(t2, t3), routerPanes$;
}
function _temp2$3(_routerState) {
  return (_routerState == null ? void 0 : _routerState.panes) || [];
}
function _temp$5() {
  return new ReplaySubject(1);
}
function useResolvedPanes() {
  const $ = (0, import_react_compiler_runtime.c)(6), [error, setError] = (0, import_react.useState)();
  if (error)
    throw error;
  const {
    structureContext,
    rootPaneNode
  } = useStructureTool();
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = {
    paneDataItems: [],
    resolvedPanes: [],
    routerPanes: []
  }, $[0] = t0) : t0 = $[0];
  const [data, setData] = (0, import_react.useState)(t0), routerPanesStream = useRouterPanesStream();
  let t1, t2;
  return $[1] !== rootPaneNode || $[2] !== routerPanesStream || $[3] !== structureContext ? (t1 = () => {
    const subscription = createResolvedPaneNodeStream({
      rootPaneNode,
      routerPanesStream,
      structureContext
    }).pipe(map(_temp5)).subscribe({
      next: (result) => setData(result),
      error: (e) => setError(e)
    });
    return () => subscription.unsubscribe();
  }, t2 = [rootPaneNode, routerPanesStream, structureContext], $[1] = rootPaneNode, $[2] = routerPanesStream, $[3] = structureContext, $[4] = t1, $[5] = t2) : (t1 = $[4], t2 = $[5]), (0, import_react.useEffect)(t1, t2), data;
}
function _temp5(resolvedPanes) {
  const routerPanes = resolvedPanes.reduce(_temp3, []), groupsLen = routerPanes.length, paneDataItems = resolvedPanes.map((pane) => {
    const {
      groupIndex,
      flatIndex,
      siblingIndex,
      routerPaneSibling,
      path
    } = pane, itemId = routerPaneSibling.id, nextGroup = routerPanes[groupIndex + 1];
    return {
      active: groupIndex === groupsLen - 2,
      childItemId: (nextGroup == null ? void 0 : nextGroup[0].id) ?? null,
      index: flatIndex,
      itemId: routerPaneSibling.id,
      groupIndex,
      key: `${pane.type === "loading" ? "unknown" : pane.paneNode.id}-${itemId}-${siblingIndex}`,
      pane: pane.type === "loading" ? LOADING_PANE : pane.paneNode,
      params: routerPaneSibling.params || {},
      path: path.join(";"),
      payload: routerPaneSibling.payload,
      selected: flatIndex === resolvedPanes.length - 1,
      siblingIndex
    };
  });
  return {
    paneDataItems,
    routerPanes,
    resolvedPanes: paneDataItems.map(_temp4)
  };
}
function _temp4(pane_0) {
  return pane_0.pane;
}
function _temp3(acc, next) {
  const currentGroup = acc[next.groupIndex] || [];
  return currentGroup[next.siblingIndex] = next.routerPaneSibling, acc[next.groupIndex] = currentGroup, acc;
}
async function ensureDocumentIdAndType(documentStore, id, type) {
  if (id && type) return {
    id,
    type
  };
  if (!id && type) return {
    id: v4_default(),
    type
  };
  if (id && !type) {
    const resolvedType = await firstValueFrom(documentStore.resolveTypeForDocument(id));
    return {
      id,
      type: resolvedType
    };
  }
  throw new PaneResolutionError({
    message: "Neither document `id` or `type` was provided when trying to resolve intent."
  });
}
var EMPTY_RECORD = {};
var IntentResolver = (0, import_react.memo)(function() {
  const $ = (0, import_react_compiler_runtime.c)(7), {
    navigate
  } = useRouter(), maybeIntent = useRouterState(_temp$4), {
    rootPaneNode,
    structureContext
  } = useStructureTool(), documentStore = useDocumentStore(), [error, setError] = (0, import_react.useState)(null);
  if (error)
    throw error;
  let t0, t1;
  return $[0] !== documentStore || $[1] !== maybeIntent || $[2] !== navigate || $[3] !== rootPaneNode || $[4] !== structureContext ? (t0 = () => {
    if (maybeIntent) {
      const {
        intent,
        params,
        payload
      } = maybeIntent;
      let cancelled = false;
      return async function() {
        const {
          id,
          type
        } = await ensureDocumentIdAndType(documentStore, typeof params.id == "string" ? params.id : void 0, typeof params.type == "string" ? params.type : void 0);
        if (cancelled)
          return;
        const panes = await resolveIntent({
          intent,
          params: {
            ...params,
            id,
            type
          },
          payload,
          rootPaneNode,
          structureContext
        });
        cancelled || navigate({
          panes
        }, {
          replace: true
        });
      }().catch(setError), () => {
        cancelled = true;
      };
    }
  }, t1 = [documentStore, maybeIntent, navigate, rootPaneNode, structureContext], $[0] = documentStore, $[1] = maybeIntent, $[2] = navigate, $[3] = rootPaneNode, $[4] = structureContext, $[5] = t0, $[6] = t1) : (t0 = $[5], t1 = $[6]), (0, import_react.useEffect)(t0, t1), null;
});
function _temp$4(routerState) {
  const intentName = typeof routerState.intent == "string" ? routerState.intent : void 0;
  return intentName ? {
    intent: intentName,
    params: isRecord$4(routerState.params) ? routerState.params : EMPTY_RECORD,
    payload: routerState.payload
  } : void 0;
}
var PathSegment = dt.span`
  &:not(:last-child)::after {
    content: ' ➝ ';
    opacity: 0.5;
  }
`;
function formatStack(stack) {
  return stack.replace(/\(\.\.\.\)\./g, `(...)
  .`).replace(/__WEBPACK_IMPORTED_MODULE_\d+_+/g, "").replace(/___default\./g, ".").replace(new RegExp(` \\(https?:\\/\\/${window.location.host}`, "g"), " (");
}
function StructureError(t0) {
  const $ = (0, import_react_compiler_runtime.c)(37), {
    error
  } = t0;
  if (!(error instanceof PaneResolutionError))
    throw error;
  const {
    cause
  } = error, {
    t
  } = useTranslation(structureLocaleNamespace), stack = (cause == null ? void 0 : cause.stack) || error.stack, showStack = stack && !(cause instanceof SerializeError) && !error.message.includes("Module build failed:");
  let t1;
  $[0] !== cause ? (t1 = cause instanceof SerializeError ? cause.path : [], $[0] = cause, $[1] = t1) : t1 = $[1];
  const path = t1, helpId = cause instanceof SerializeError && cause.helpId || error.helpId, handleReload = _temp$3;
  let t2;
  $[2] !== t ? (t2 = t("structure-error.header.text"), $[2] = t, $[3] = t2) : t2 = $[3];
  let t3;
  $[4] !== t2 ? (t3 = (0, import_jsx_runtime.jsx)(Heading, { as: "h2", children: t2 }), $[4] = t2, $[5] = t3) : t3 = $[5];
  let t4;
  $[6] !== path || $[7] !== t ? (t4 = path.length > 0 && (0, import_jsx_runtime.jsxs)(Stack, { space: 2, children: [
    (0, import_jsx_runtime.jsx)(Text, { size: 1, weight: "medium", children: t("structure-error.structure-path.label") }),
    (0, import_jsx_runtime.jsx)(Code, { children: path.slice(1).map(_temp2$2) })
  ] }), $[6] = path, $[7] = t, $[8] = t4) : t4 = $[8];
  let t5;
  $[9] !== t ? (t5 = t("structure-error.error.label"), $[9] = t, $[10] = t5) : t5 = $[10];
  let t6;
  $[11] !== t5 ? (t6 = (0, import_jsx_runtime.jsx)(Text, { size: 1, weight: "medium", children: t5 }), $[11] = t5, $[12] = t6) : t6 = $[12];
  let t7;
  $[13] !== error.message || $[14] !== showStack || $[15] !== stack ? (t7 = showStack ? formatStack(stack) : error.message, $[13] = error.message, $[14] = showStack, $[15] = stack, $[16] = t7) : t7 = $[16];
  let t8;
  $[17] !== t7 ? (t8 = (0, import_jsx_runtime.jsx)(Code, { children: t7 }), $[17] = t7, $[18] = t8) : t8 = $[18];
  let t9;
  $[19] !== t6 || $[20] !== t8 ? (t9 = (0, import_jsx_runtime.jsxs)(Stack, { marginTop: 4, space: 2, children: [
    t6,
    t8
  ] }), $[19] = t6, $[20] = t8, $[21] = t9) : t9 = $[21];
  let t10;
  $[22] !== helpId || $[23] !== t ? (t10 = helpId && (0, import_jsx_runtime.jsx)(Box, { marginTop: 4, children: (0, import_jsx_runtime.jsx)(Text, { children: (0, import_jsx_runtime.jsx)("a", { href: generateHelpUrl(helpId), rel: "noopener noreferrer", target: "_blank", children: t("structure-error.docs-link.text") }) }) }), $[22] = helpId, $[23] = t, $[24] = t10) : t10 = $[24];
  let t11;
  $[25] !== t ? (t11 = t("structure-error.reload-button.text"), $[25] = t, $[26] = t11) : t11 = $[26];
  let t12;
  $[27] !== t11 ? (t12 = (0, import_jsx_runtime.jsx)(Box, { marginTop: 4, children: (0, import_jsx_runtime.jsx)(Button, { text: t11, icon: SyncIcon, tone: "primary", onClick: handleReload }) }), $[27] = t11, $[28] = t12) : t12 = $[28];
  let t13;
  $[29] !== t10 || $[30] !== t12 || $[31] !== t4 || $[32] !== t9 ? (t13 = (0, import_jsx_runtime.jsxs)(Card, { marginTop: 4, padding: 4, radius: 2, overflow: "auto", shadow: 1, tone: "inherit", children: [
    t4,
    t9,
    t10,
    t12
  ] }), $[29] = t10, $[30] = t12, $[31] = t4, $[32] = t9, $[33] = t13) : t13 = $[33];
  let t14;
  return $[34] !== t13 || $[35] !== t3 ? (t14 = (0, import_jsx_runtime.jsx)(Card, { height: "fill", overflow: "auto", padding: 4, sizing: "border", tone: "critical", children: (0, import_jsx_runtime.jsxs)(Container, { children: [
    t3,
    t13
  ] }) }), $[34] = t13, $[35] = t3, $[36] = t14) : t14 = $[36], t14;
}
function _temp2$2(segment, i) {
  return (0, import_jsx_runtime.jsx)(PathSegment, { children: segment }, `${segment}-${i}`);
}
function _temp$3() {
  window.location.reload();
}
function UnknownPane(props) {
  const $ = (0, import_react_compiler_runtime.c)(14), {
    isSelected,
    pane,
    paneKey
  } = props;
  let t0;
  $[0] !== pane ? (t0 = isRecord$4(pane) && pane.type || null, $[0] = pane, $[1] = t0) : t0 = $[1];
  const type = t0, {
    t
  } = useTranslation(structureLocaleNamespace);
  let t1;
  $[2] !== t ? (t1 = t("panes.unknown-pane-type.title"), $[2] = t, $[3] = t1) : t1 = $[3];
  let t2;
  $[4] !== t1 ? (t2 = (0, import_jsx_runtime.jsx)(PaneHeader$1, { title: t1 }), $[4] = t1, $[5] = t2) : t2 = $[5];
  let t3;
  $[6] !== t || $[7] !== type ? (t3 = (0, import_jsx_runtime.jsx)(PaneContent, { children: (0, import_jsx_runtime.jsx)(Box, { padding: 4, children: typeof type == "string" ? (0, import_jsx_runtime.jsx)(Text, { as: "p", muted: true, children: (0, import_jsx_runtime.jsx)(Translate, { t, i18nKey: "panes.unknown-pane-type.unknown-type.text", values: {
    type
  } }) }) : (0, import_jsx_runtime.jsx)(Text, { as: "p", muted: true, children: (0, import_jsx_runtime.jsx)(Translate, { t, i18nKey: "panes.unknown-pane-type.missing-type.text" }) }) }) }), $[6] = t, $[7] = type, $[8] = t3) : t3 = $[8];
  let t4;
  return $[9] !== isSelected || $[10] !== paneKey || $[11] !== t2 || $[12] !== t3 ? (t4 = (0, import_jsx_runtime.jsxs)(Pane, { id: paneKey, selected: isSelected, children: [
    t2,
    t3
  ] }), $[9] = isSelected, $[10] = paneKey, $[11] = t2, $[12] = t3, $[13] = t4) : t4 = $[13], t4;
}
var paneMap = {
  component: (0, import_react.lazy)(() => import("./_chunks-es-K7SYRYZQ.js")),
  document: (0, import_react.lazy)(() => import("./pane-WN4C4EAD.js").then(function(n) {
    return n.pane;
  })),
  documentList: (0, import_react.lazy)(() => import("./pane-WN4C4EAD.js").then(function(n) {
    return n.pane$1;
  })),
  list: (0, import_react.lazy)(() => import("./index2-TOIUXRSL.js"))
};
var StructureToolPane = (0, import_react.memo)(function(props) {
  const $ = (0, import_react_compiler_runtime.c)(23), {
    active,
    childItemId,
    groupIndex,
    index,
    itemId,
    pane,
    paneKey,
    params,
    payload,
    path,
    selected,
    siblingIndex
  } = props, PaneComponent = paneMap[pane.type] || UnknownPane;
  let t0;
  $[0] !== paneKey || $[1] !== path || $[2] !== selected ? (t0 = (0, import_jsx_runtime.jsx)(LoadingPane, { paneKey, path, selected }), $[0] = paneKey, $[1] = path, $[2] = selected, $[3] = t0) : t0 = $[3];
  const t1 = childItemId || "";
  let t2;
  $[4] !== PaneComponent || $[5] !== active || $[6] !== index || $[7] !== itemId || $[8] !== pane || $[9] !== paneKey || $[10] !== selected || $[11] !== t1 ? (t2 = (0, import_jsx_runtime.jsx)(PaneComponent, { childItemId: t1, index, itemId, isActive: active, isSelected: selected, paneKey, pane }), $[4] = PaneComponent, $[5] = active, $[6] = index, $[7] = itemId, $[8] = pane, $[9] = paneKey, $[10] = selected, $[11] = t1, $[12] = t2) : t2 = $[12];
  let t3;
  $[13] !== t0 || $[14] !== t2 ? (t3 = (0, import_jsx_runtime.jsx)(import_react.Suspense, { fallback: t0, children: t2 }), $[13] = t0, $[14] = t2, $[15] = t3) : t3 = $[15];
  let t4;
  return $[16] !== groupIndex || $[17] !== index || $[18] !== params || $[19] !== payload || $[20] !== siblingIndex || $[21] !== t3 ? (t4 = (0, import_jsx_runtime.jsx)(PaneRouterProvider, { flatIndex: index, index: groupIndex, params, payload, siblingIndex, children: t3 }), $[16] = groupIndex, $[17] = index, $[18] = params, $[19] = payload, $[20] = siblingIndex, $[21] = t3, $[22] = t4) : t4 = $[22], t4;
}, ({
  params: prevParams = {},
  payload: prevPayload = null,
  ...prev
}, {
  params: nextParams = {},
  payload: nextPayload = null,
  ...next
}) => {
  if (!(0, import_isEqual.default)(prevParams, nextParams) || !(0, import_isEqual.default)(prevPayload, nextPayload)) return false;
  const keys = /* @__PURE__ */ new Set([...Object.keys(prev), ...Object.keys(next)]);
  for (const key of keys)
    if (prev[key] !== next[key]) return false;
  return true;
});
function NoDocumentTypesScreen() {
  const $ = (0, import_react_compiler_runtime.c)(17), {
    t
  } = useTranslation(structureLocaleNamespace);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = (0, import_jsx_runtime.jsx)(Box, { children: (0, import_jsx_runtime.jsx)(Text, { size: 1, children: (0, import_jsx_runtime.jsx)(WarningOutlineIcon, {}) }) }), $[0] = t0) : t0 = $[0];
  let t1;
  $[1] !== t ? (t1 = t("no-document-types-screen.title"), $[1] = t, $[2] = t1) : t1 = $[2];
  let t2;
  $[3] !== t1 ? (t2 = (0, import_jsx_runtime.jsx)(Text, { as: "h1", size: 1, weight: "medium", children: t1 }), $[3] = t1, $[4] = t2) : t2 = $[4];
  let t3;
  $[5] !== t ? (t3 = t("no-document-types-screen.subtitle"), $[5] = t, $[6] = t3) : t3 = $[6];
  let t4;
  $[7] !== t3 ? (t4 = (0, import_jsx_runtime.jsx)(Text, { as: "p", muted: true, size: 1, children: t3 }), $[7] = t3, $[8] = t4) : t4 = $[8];
  let t5;
  $[9] !== t ? (t5 = t("no-document-types-screen.link-text"), $[9] = t, $[10] = t5) : t5 = $[10];
  let t6;
  $[11] !== t5 ? (t6 = (0, import_jsx_runtime.jsx)(Text, { as: "p", muted: true, size: 1, children: (0, import_jsx_runtime.jsx)("a", { href: "https://www.sanity.io/docs/create-a-schema-and-configure-sanity-studio", target: "_blank", rel: "noreferrer", children: t5 }) }), $[11] = t5, $[12] = t6) : t6 = $[12];
  let t7;
  return $[13] !== t2 || $[14] !== t4 || $[15] !== t6 ? (t7 = (0, import_jsx_runtime.jsx)(Card, { height: "fill", children: (0, import_jsx_runtime.jsx)(Flex, { align: "center", height: "fill", justify: "center", padding: 4, sizing: "border", children: (0, import_jsx_runtime.jsx)(Container, { width: 0, children: (0, import_jsx_runtime.jsx)(Card, { padding: 4, radius: 2, shadow: 1, tone: "caution", children: (0, import_jsx_runtime.jsxs)(Flex, { children: [
    t0,
    (0, import_jsx_runtime.jsxs)(Stack, { flex: 1, marginLeft: 3, space: 3, children: [
      t2,
      t4,
      t6
    ] })
  ] }) }) }) }) }), $[13] = t2, $[14] = t4, $[15] = t6, $[16] = t7) : t7 = $[16], t7;
}
var DocumentTitle = (props) => {
  const $ = (0, import_react_compiler_runtime.c)(7), {
    documentId,
    documentType
  } = props, {
    selectedReleaseId
  } = usePerspective(), editState = useEditState(documentId, documentType, "default", selectedReleaseId), schema = useSchema(), {
    t
  } = useTranslation(structureLocaleNamespace), isNewDocument = !(editState == null ? void 0 : editState.published) && !(editState == null ? void 0 : editState.draft), documentValue = (editState == null ? void 0 : editState.version) || (editState == null ? void 0 : editState.draft) || (editState == null ? void 0 : editState.published), schemaType = schema.get(documentType), {
    value,
    isLoading: previewValueIsLoading
  } = useDocumentPreview({
    enabled: true,
    schemaType,
    value: documentValue
  }), documentTitle = isNewDocument ? t("browser-document-title.new-document", {
    schemaType: (schemaType == null ? void 0 : schemaType.title) || (schemaType == null ? void 0 : schemaType.name)
  }) : (value == null ? void 0 : value.title) || t("browser-document-title.untitled-document"), settled = editState.ready && !previewValueIsLoading, newTitle = useConstructDocumentTitle(documentTitle);
  let t0;
  $[0] !== newTitle || $[1] !== settled ? (t0 = () => {
    settled && (document.title = newTitle);
  }, $[0] = newTitle, $[1] = settled, $[2] = t0) : t0 = $[2];
  let t1;
  return $[3] !== documentTitle || $[4] !== newTitle || $[5] !== settled ? (t1 = [documentTitle, settled, newTitle], $[3] = documentTitle, $[4] = newTitle, $[5] = settled, $[6] = t1) : t1 = $[6], (0, import_react.useEffect)(t0, t1), null;
};
var PassthroughTitle = (props) => {
  const $ = (0, import_react_compiler_runtime.c)(5), {
    title
  } = props, newTitle = useConstructDocumentTitle(title);
  let t0;
  $[0] !== newTitle ? (t0 = () => {
    document.title = newTitle;
  }, $[0] = newTitle, $[1] = t0) : t0 = $[1];
  let t1;
  return $[2] !== newTitle || $[3] !== title ? (t1 = [newTitle, title], $[2] = newTitle, $[3] = title, $[4] = t1) : t1 = $[4], (0, import_react.useEffect)(t0, t1), null;
};
var StructureTitle = (props) => {
  const $ = (0, import_react_compiler_runtime.c)(8), {
    resolvedPanes
  } = props;
  if (!(resolvedPanes == null ? void 0 : resolvedPanes.length))
    return null;
  const lastPane = resolvedPanes[resolvedPanes.length - 1];
  if (isLoadingPane(lastPane)) {
    let t02;
    return $[0] === Symbol.for("react.memo_cache_sentinel") ? (t02 = (0, import_jsx_runtime.jsx)(PassthroughTitle, {}), $[0] = t02) : t02 = $[0], t02;
  }
  if (isDocumentPane(lastPane)) {
    if (lastPane == null ? void 0 : lastPane.title) {
      let t03;
      return $[1] !== lastPane.title ? (t03 = (0, import_jsx_runtime.jsx)(PassthroughTitle, { title: lastPane.title }), $[1] = lastPane.title, $[2] = t03) : t03 = $[2], t03;
    }
    let t02;
    return $[3] !== lastPane.options.id || $[4] !== lastPane.options.type ? (t02 = (0, import_jsx_runtime.jsx)(DocumentTitle, { documentId: lastPane.options.id, documentType: lastPane.options.type }), $[3] = lastPane.options.id, $[4] = lastPane.options.type, $[5] = t02) : t02 = $[5], t02;
  }
  const t0 = lastPane == null ? void 0 : lastPane.title;
  let t1;
  return $[6] !== t0 ? (t1 = (0, import_jsx_runtime.jsx)(PassthroughTitle, { title: t0 }), $[6] = t0, $[7] = t1) : t1 = $[7], t1;
};
function useConstructDocumentTitle(activeTitle) {
  const $ = (0, import_react_compiler_runtime.c)(3), structureToolBaseTitle = useStructureTool().structureContext.title;
  let t0;
  return $[0] !== activeTitle || $[1] !== structureToolBaseTitle ? (t0 = [activeTitle, structureToolBaseTitle].filter(_temp$2), $[0] = activeTitle, $[1] = structureToolBaseTitle, $[2] = t0) : t0 = $[2], t0.join(" | ");
}
function _temp$2(title) {
  return title;
}
function isDocumentPane(pane) {
  return pane !== LOADING_PANE && pane.type === "document";
}
function isLoadingPane(pane) {
  return pane === LOADING_PANE;
}
var StyledPaneLayout = dt(PaneLayout)`
  min-height: 100%;
  min-width: 320px;
`;
var isSaveHotkey = isHotkey("mod+s");
var StructureTool = (0, import_react.memo)(function(t0) {
  var _a;
  const $ = (0, import_react_compiler_runtime.c)(31), {
    onPaneChange
  } = t0, {
    push: pushToast
  } = useToast(), schema = useSchema(), {
    layoutCollapsed,
    setLayoutCollapsed
  } = useStructureTool(), {
    paneDataItems,
    resolvedPanes
  } = useResolvedPanes(), isResolvingIntent = useRouterState(_temp$1), {
    sanity: t1
  } = useTheme(), {
    media
  } = t1, [portalElement, setPortalElement] = (0, import_react.useState)(null);
  let t2;
  $[0] !== setLayoutCollapsed ? (t2 = () => setLayoutCollapsed(true), $[0] = setLayoutCollapsed, $[1] = t2) : t2 = $[1];
  const handleRootCollapse = t2;
  let t3;
  $[2] !== setLayoutCollapsed ? (t3 = () => setLayoutCollapsed(false), $[2] = setLayoutCollapsed, $[3] = t3) : t3 = $[3];
  const handleRootExpand = t3;
  let t4, t5;
  $[4] !== onPaneChange || $[5] !== resolvedPanes ? (t4 = () => {
    resolvedPanes.length && onPaneChange(resolvedPanes);
  }, t5 = [onPaneChange, resolvedPanes], $[4] = onPaneChange, $[5] = resolvedPanes, $[6] = t4, $[7] = t5) : (t4 = $[6], t5 = $[7]), (0, import_react.useEffect)(t4, t5);
  let t6, t7;
  if ($[8] !== pushToast ? (t6 = () => {
    const handleGlobalKeyDown = (event) => {
      isSaveHotkey(event) && (event.preventDefault(), pushToast({
        closable: true,
        id: "auto-save-message",
        status: "info",
        title: "Your work is automatically saved!",
        duration: 4e3
      }));
    };
    return window.addEventListener("keydown", handleGlobalKeyDown), () => window.removeEventListener("keydown", handleGlobalKeyDown);
  }, t7 = [pushToast], $[8] = pushToast, $[9] = t6, $[10] = t7) : (t6 = $[9], t7 = $[10]), (0, import_react.useEffect)(t6, t7), !((_a = schema._original) == null ? void 0 : _a.types.some(_isCustomDocumentTypeDefinition))) {
    let t82;
    return $[11] === Symbol.for("react.memo_cache_sentinel") ? (t82 = (0, import_jsx_runtime.jsx)(NoDocumentTypesScreen, {}), $[11] = t82) : t82 = $[11], t82;
  }
  const t8 = portalElement || null, t9 = layoutCollapsed ? void 0 : "fill", t10 = media[1];
  let t11;
  $[12] !== paneDataItems ? (t11 = paneDataItems.map(_temp2$1), $[12] = paneDataItems, $[13] = t11) : t11 = $[13];
  let t12;
  $[14] !== isResolvingIntent || $[15] !== paneDataItems.length ? (t12 = paneDataItems.length <= 1 && isResolvingIntent && (0, import_jsx_runtime.jsx)(LoadingPane, { paneKey: "intent-resolver" }), $[14] = isResolvingIntent, $[15] = paneDataItems.length, $[16] = t12) : t12 = $[16];
  let t13;
  $[17] !== handleRootCollapse || $[18] !== handleRootExpand || $[19] !== media[1] || $[20] !== t11 || $[21] !== t12 || $[22] !== t9 ? (t13 = (0, import_jsx_runtime.jsxs)(StyledPaneLayout, { flex: 1, height: t9, minWidth: t10, onCollapse: handleRootCollapse, onExpand: handleRootExpand, children: [
    t11,
    t12
  ] }), $[17] = handleRootCollapse, $[18] = handleRootExpand, $[19] = media[1], $[20] = t11, $[21] = t12, $[22] = t9, $[23] = t13) : t13 = $[23];
  let t14;
  $[24] !== resolvedPanes ? (t14 = (0, import_jsx_runtime.jsx)(StructureTitle, { resolvedPanes }), $[24] = resolvedPanes, $[25] = t14) : t14 = $[25];
  let t15;
  $[26] === Symbol.for("react.memo_cache_sentinel") ? (t15 = (0, import_jsx_runtime.jsx)("div", { "data-portal": "", ref: setPortalElement }), $[26] = t15) : t15 = $[26];
  let t16;
  return $[27] !== t13 || $[28] !== t14 || $[29] !== t8 ? (t16 = (0, import_jsx_runtime.jsxs)(PortalProvider, { element: t8, children: [
    t13,
    t14,
    t15
  ] }), $[27] = t13, $[28] = t14, $[29] = t8, $[30] = t16) : t16 = $[30], t16;
});
function _temp$1(routerState) {
  return typeof routerState.intent == "string";
}
function _temp2$1(t0) {
  const {
    active,
    childItemId,
    groupIndex,
    itemId,
    key: paneKey,
    pane,
    index: paneIndex,
    params: paneParams,
    path,
    payload,
    siblingIndex,
    selected
  } = t0;
  return (0, import_jsx_runtime.jsx)(import_react.Fragment, { children: pane === LOADING_PANE ? (0, import_jsx_runtime.jsx)(LoadingPane, { paneKey, path, selected }) : (0, import_jsx_runtime.jsx)(StructureToolPane, { active, groupIndex, index: paneIndex, pane, childItemId, itemId, paneKey, params: paneParams, payload, path, selected, siblingIndex }) }, `${pane === LOADING_PANE ? "loading" : pane.type}-${paneIndex}`);
}
function StructureToolBoundary(t0) {
  const $ = (0, import_react_compiler_runtime.c)(14), {
    tool: t1
  } = t0, {
    options
  } = t1, {
    unstable_sources: sources
  } = useWorkspace(), [firstSource] = sources;
  let t2;
  $[0] !== options ? (t2 = options || {}, $[0] = options, $[1] = t2) : t2 = $[1];
  const {
    source,
    defaultDocumentNode,
    structure
  } = t2;
  let t3;
  $[2] === Symbol.for("react.memo_cache_sentinel") ? (t3 = [], $[2] = t3) : t3 = $[2], (0, import_react.useEffect)(_temp2, t3);
  let t4;
  $[3] === Symbol.for("react.memo_cache_sentinel") ? (t4 = {
    error: null
  }, $[3] = t4) : t4 = $[3];
  const [t5, setError] = (0, import_react.useState)(t4), {
    error
  } = t5;
  if (error) {
    let t62;
    return $[4] !== error ? (t62 = (0, import_jsx_runtime.jsx)(StructureError, { error }), $[4] = error, $[5] = t62) : t62 = $[5], t62;
  }
  const t6 = source || firstSource.name;
  let t7, t8;
  $[6] === Symbol.for("react.memo_cache_sentinel") ? (t7 = (0, import_jsx_runtime.jsx)(StructureTool, { onPaneChange: setActivePanes }), t8 = (0, import_jsx_runtime.jsx)(IntentResolver, {}), $[6] = t7, $[7] = t8) : (t7 = $[6], t8 = $[7]);
  let t9;
  $[8] !== defaultDocumentNode || $[9] !== structure ? (t9 = (0, import_jsx_runtime.jsxs)(StructureToolProvider, { defaultDocumentNode, structure, children: [
    t7,
    t8
  ] }), $[8] = defaultDocumentNode, $[9] = structure, $[10] = t9) : t9 = $[10];
  let t10;
  return $[11] !== t6 || $[12] !== t9 ? (t10 = (0, import_jsx_runtime.jsx)(ErrorBoundary, { onCatch: setError, children: (0, import_jsx_runtime.jsx)(SourceProvider, { name: t6, children: t9 }) }), $[11] = t6, $[12] = t9, $[13] = t10) : t10 = $[13], t10;
}
function _temp2() {
  return setActivePanes([]), _temp;
}
function _temp() {
  return setActivePanes([]);
}
export {
  StructureToolBoundary as default
};
//# sourceMappingURL=index3-54VWYSD2.js.map

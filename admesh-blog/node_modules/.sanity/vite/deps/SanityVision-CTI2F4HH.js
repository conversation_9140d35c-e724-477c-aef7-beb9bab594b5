import {
  defaultKeymap,
  esm_default,
  highlightSelectionMatches,
  history,
  historyKeymap
} from "./chunk-J4YCMODO.js";
import {
  javascriptLanguage
} from "./chunk-24MG4Z6R.js";
import {
  closeBrackets
} from "./chunk-SGSHY4V2.js";
import "./chunk-DT6GHPDN.js";
import {
  EditorSelection,
  EditorView,
  HighlightStyle,
  bracketMatching,
  defaultHighlightStyle,
  drawSelection,
  highlightActiveLine,
  highlightActiveLineGutter,
  highlightSpecialChars,
  indentOnInput,
  keymap,
  lineNumbers,
  syntaxHighlighting,
  tags
} from "./chunk-EOZ53ZFO.js";
import {
  visionLocaleNamespace
} from "./chunk-UDCS6EKI.js";
import {
  JsonInspector
} from "./chunk-74TI2G72.js";
import {
  ContextMenuButton,
  IntentLink,
  Translate,
  isHotkey,
  map,
  require_debounce,
  require_isEqual,
  require_quick_lru,
  startWith,
  useClient,
  useDataset,
  useDateTimeFormat,
  useKeyValueStore,
  usePerspective,
  useTranslation,
  v4_default
} from "./chunk-6XMXOQIO.js";
import {
  Badge,
  Box,
  Button,
  Card,
  Code,
  Container,
  Flex,
  Grid,
  Heading,
  Hotkeys,
  Inline,
  Label,
  Menu,
  MenuButton,
  MenuItem,
  Popover,
  Select,
  Spinner,
  Stack,
  Text,
  TextInput,
  Tooltip,
  dt,
  hues,
  lt,
  rem,
  useClickOutsideEvent,
  useEffectEvent,
  useTheme,
  useToast
} from "./chunk-3HD5UKUW.js";
import "./chunk-4HFGZHGE.js";
import {
  require_dist
} from "./chunk-CJ3ODOED.js";
import "./chunk-DWYNPLUJ.js";
import "./chunk-NB2E7ZMB.js";
import {
  AddIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  DocumentSheetIcon,
  ErrorOutlineIcon,
  HelpCircleIcon,
  LinkIcon,
  PlayIcon,
  SearchIcon,
  StopIcon,
  TrashIcon
} from "./chunk-V3YD7LXU.js";
import {
  require_jsx_runtime
} from "./chunk-EVVIBKQA.js";
import {
  require_react
} from "./chunk-3GN7GPJI.js";
import {
  __commonJS,
  __publicField,
  __toESM
} from "./chunk-OCBYBPSH.js";

// ../node_modules/json-2-csv/lib/constants.js
var require_constants = __commonJS({
  "../node_modules/json-2-csv/lib/constants.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.excelBOM = exports.defaultCsv2JsonOptions = exports.defaultJson2CsvOptions = exports.errors = void 0;
    exports.errors = {
      optionsRequired: "Options were not passed and are required.",
      json2csv: {
        cannotCallOn: "Cannot call json2csv on",
        dataCheckFailure: "Data provided was not an array of documents.",
        notSameSchema: "Not all documents have the same schema."
      },
      csv2json: {
        cannotCallOn: "Cannot call csv2json on",
        dataCheckFailure: "CSV is not a string."
      }
    };
    exports.defaultJson2CsvOptions = {
      arrayIndexesAsKeys: false,
      checkSchemaDifferences: false,
      delimiter: {
        field: ",",
        wrap: '"',
        eol: "\n"
      },
      emptyFieldValue: void 0,
      escapeHeaderNestedDots: true,
      excelBOM: false,
      excludeKeys: [],
      expandNestedObjects: true,
      expandArrayObjects: false,
      prependHeader: true,
      preventCsvInjection: false,
      sortHeader: false,
      trimFieldValues: false,
      trimHeaderFields: false,
      unwindArrays: false,
      useDateIso8601Format: false,
      useLocaleFormat: false,
      wrapBooleans: false
    };
    exports.defaultCsv2JsonOptions = {
      delimiter: {
        field: ",",
        wrap: '"',
        eol: "\n"
      },
      excelBOM: false,
      preventCsvInjection: false,
      trimFieldValues: false,
      trimHeaderFields: false
    };
    exports.excelBOM = "\uFEFF";
  }
});

// ../node_modules/doc-path/lib/path.js
var require_path = __commonJS({
  "../node_modules/doc-path/lib/path.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.setPath = exports.evaluatePath = void 0;
    function evaluatePath(obj, kp) {
      if (!obj) {
        return null;
      }
      const { dotIndex, key: key2, remaining } = state(kp);
      const kpVal = typeof obj === "object" && kp in obj ? obj[kp] : void 0;
      const keyVal = typeof obj === "object" && key2 in obj ? obj[key2] : void 0;
      if (dotIndex >= 0 && typeof obj === "object" && !(kp in obj)) {
        const { key: nextKey } = state(remaining);
        const nextKeyAsInt = parseInt(nextKey);
        if (Array.isArray(keyVal) && isNaN(nextKeyAsInt)) {
          return keyVal.map((doc) => evaluatePath(doc, remaining));
        }
        return evaluatePath(keyVal, remaining);
      } else if (Array.isArray(obj)) {
        const keyAsInt = parseInt(key2);
        if (kp === key2 && dotIndex === -1 && !isNaN(keyAsInt)) {
          return keyVal;
        }
        return obj.map((doc) => evaluatePath(doc, kp));
      } else if (dotIndex >= 0 && kp !== key2 && typeof obj === "object" && key2 in obj) {
        return evaluatePath(keyVal, remaining);
      } else if (dotIndex === -1 && typeof obj === "object" && key2 in obj && !(kp in obj)) {
        return keyVal;
      }
      return kpVal;
    }
    exports.evaluatePath = evaluatePath;
    function setPath(obj, kp, v) {
      if (!obj) {
        throw new Error("No object was provided.");
      } else if (!kp) {
        throw new Error("No keyPath was provided.");
      }
      return _sp(obj, kp, v);
    }
    exports.setPath = setPath;
    function _sp(obj, kp, v) {
      const { dotIndex, key: key2, remaining } = state(kp);
      if (kp.startsWith("__proto__") || kp.startsWith("constructor") || kp.startsWith("prototype")) {
        return obj;
      }
      if (dotIndex >= 0) {
        const keyAsInt = parseInt(key2);
        if (typeof obj === "object" && obj !== null && !(key2 in obj) && Array.isArray(obj) && !isNaN(keyAsInt)) {
          obj[key2] = obj[key2] ?? {};
          _sp(obj[key2], remaining, v);
          return obj;
        } else if (typeof obj === "object" && obj !== null && !(key2 in obj) && Array.isArray(obj)) {
          obj.forEach((doc) => _sp(doc, kp, v));
          return obj;
        } else if (typeof obj === "object" && obj !== null && !(key2 in obj) && !Array.isArray(obj)) {
          const { key: nextKey } = state(remaining);
          const nextKeyAsInt = parseInt(nextKey);
          if (!isNaN(nextKeyAsInt)) {
            obj[key2] = [];
          } else if (remaining === "") {
            obj[kp] = v;
            return obj;
          } else {
            obj[key2] = {};
          }
        }
        _sp(obj[key2], remaining, v);
      } else if (Array.isArray(obj)) {
        const keyAsInt = parseInt(key2);
        if (kp === key2 && dotIndex === -1 && !isNaN(keyAsInt)) {
          obj[key2] = v;
          return obj;
        }
        obj.forEach((doc) => _sp(doc, remaining, v));
        return obj;
      } else {
        obj[key2] = v;
      }
      return obj;
    }
    function state(kp) {
      const dotIndex = findFirstNonEscapedDotIndex(kp);
      return {
        dotIndex,
        key: kp.slice(0, dotIndex >= 0 ? dotIndex : void 0).replace(/\\./g, "."),
        remaining: kp.slice(dotIndex + 1)
      };
    }
    function findFirstNonEscapedDotIndex(kp) {
      for (let i = 0; i < kp.length; i++) {
        const previousChar = i > 0 ? kp[i - 1] : "", currentChar = kp[i];
        if (currentChar === "." && previousChar !== "\\")
          return i;
      }
      return -1;
    }
  }
});

// ../node_modules/deeks/lib/utils.js
var require_utils = __commonJS({
  "../node_modules/deeks/lib/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isDocumentToRecurOn = exports.flatten = exports.unique = void 0;
    function unique(array) {
      return [...new Set(array)];
    }
    exports.unique = unique;
    function flatten(array) {
      return [].concat(...array);
    }
    exports.flatten = flatten;
    function isDocumentToRecurOn(val) {
      return typeof val === "object" && val !== null && !Array.isArray(val) && Object.keys(val).length;
    }
    exports.isDocumentToRecurOn = isDocumentToRecurOn;
  }
});

// ../node_modules/deeks/lib/types.js
var require_types = __commonJS({
  "../node_modules/deeks/lib/types.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// ../node_modules/deeks/lib/deeks.js
var require_deeks = __commonJS({
  "../node_modules/deeks/lib/deeks.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.deepKeysFromList = exports.deepKeys = void 0;
    var utils = __importStar(require_utils());
    __exportStar(require_types(), exports);
    function deepKeys(object, options) {
      const parsedOptions = mergeOptions(options);
      if (typeof object === "object" && object !== null) {
        return generateDeepKeysList("", object, parsedOptions);
      }
      return [];
    }
    exports.deepKeys = deepKeys;
    function deepKeysFromList(list, options) {
      const parsedOptions = mergeOptions(options);
      return list.map((document2) => {
        if (typeof document2 === "object" && document2 !== null) {
          return deepKeys(document2, parsedOptions);
        }
        return [];
      });
    }
    exports.deepKeysFromList = deepKeysFromList;
    function generateDeepKeysList(heading, data, options) {
      const keys = Object.keys(data).map((currentKey) => {
        const keyName = buildKeyName(heading, escapeNestedDotsIfSpecified(currentKey, options));
        if (options.expandNestedObjects && utils.isDocumentToRecurOn(data[currentKey]) || options.arrayIndexesAsKeys && Array.isArray(data[currentKey]) && data[currentKey].length) {
          return generateDeepKeysList(keyName, data[currentKey], options);
        } else if (options.expandArrayObjects && Array.isArray(data[currentKey])) {
          return processArrayKeys(data[currentKey], keyName, options);
        } else if (options.ignoreEmptyArrays && Array.isArray(data[currentKey]) && !data[currentKey].length) {
          return [];
        }
        return keyName;
      });
      return utils.flatten(keys);
    }
    function processArrayKeys(subArray, currentKeyPath, options) {
      let subArrayKeys = deepKeysFromList(subArray, options);
      if (!subArray.length) {
        return options.ignoreEmptyArraysWhenExpanding ? [] : [currentKeyPath];
      } else if (subArray.length && utils.flatten(subArrayKeys).length === 0) {
        return [currentKeyPath];
      } else {
        subArrayKeys = subArrayKeys.map((schemaKeys) => {
          if (Array.isArray(schemaKeys) && schemaKeys.length === 0) {
            return [currentKeyPath];
          }
          return schemaKeys.map((subKey) => buildKeyName(currentKeyPath, escapeNestedDotsIfSpecified(subKey, options)));
        });
        return utils.unique(utils.flatten(subArrayKeys));
      }
    }
    function escapeNestedDotsIfSpecified(key2, options) {
      if (options.escapeNestedDots) {
        return key2.replace(/\./g, "\\.");
      }
      return key2;
    }
    function buildKeyName(upperKeyName, currentKeyName) {
      if (upperKeyName) {
        return upperKeyName + "." + currentKeyName;
      }
      return currentKeyName;
    }
    function mergeOptions(options) {
      return {
        arrayIndexesAsKeys: false,
        expandNestedObjects: true,
        expandArrayObjects: false,
        ignoreEmptyArraysWhenExpanding: false,
        escapeNestedDots: false,
        ignoreEmptyArrays: false,
        ...options ?? {}
      };
    }
  }
});

// ../node_modules/json-2-csv/lib/utils.js
var require_utils2 = __commonJS({
  "../node_modules/json-2-csv/lib/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isInvalid = exports.flatten = exports.unique = exports.arrayDifference = exports.isError = exports.isUndefined = exports.isNull = exports.isObject = exports.isString = exports.isNumber = exports.unwind = exports.getNCharacters = exports.removeEmptyFields = exports.isEmptyField = exports.computeSchemaDifferences = exports.isDateRepresentation = exports.isStringRepresentation = exports.deepCopy = exports.validate = exports.buildC2JOptions = exports.buildJ2COptions = void 0;
    var doc_path_1 = require_path();
    var constants_1 = require_constants();
    var dateStringRegex = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/;
    var MAX_ARRAY_LENGTH = 1e5;
    function buildJ2COptions(opts) {
      var _a, _b, _c;
      return {
        ...constants_1.defaultJson2CsvOptions,
        ...opts,
        delimiter: {
          field: ((_a = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _a.field) ?? constants_1.defaultJson2CsvOptions.delimiter.field,
          wrap: ((_b = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _b.wrap) || constants_1.defaultJson2CsvOptions.delimiter.wrap,
          eol: ((_c = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _c.eol) || constants_1.defaultJson2CsvOptions.delimiter.eol
        },
        fieldTitleMap: /* @__PURE__ */ Object.create({})
      };
    }
    exports.buildJ2COptions = buildJ2COptions;
    function buildC2JOptions(opts) {
      var _a, _b, _c;
      return {
        ...constants_1.defaultCsv2JsonOptions,
        ...opts,
        delimiter: {
          field: ((_a = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _a.field) ?? constants_1.defaultCsv2JsonOptions.delimiter.field,
          wrap: ((_b = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _b.wrap) || constants_1.defaultCsv2JsonOptions.delimiter.wrap,
          eol: ((_c = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _c.eol) || constants_1.defaultCsv2JsonOptions.delimiter.eol
        }
      };
    }
    exports.buildC2JOptions = buildC2JOptions;
    function validate(data, validationFn, errorMessages) {
      if (!data)
        throw new Error(`${errorMessages.cannotCallOn} ${data}.`);
      if (!validationFn(data))
        throw new Error(errorMessages.dataCheckFailure);
      return true;
    }
    exports.validate = validate;
    function deepCopy(obj) {
      return JSON.parse(JSON.stringify(obj));
    }
    exports.deepCopy = deepCopy;
    function isStringRepresentation(fieldValue, options) {
      const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];
      return firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap;
    }
    exports.isStringRepresentation = isStringRepresentation;
    function isDateRepresentation(fieldValue) {
      return dateStringRegex.test(fieldValue);
    }
    exports.isDateRepresentation = isDateRepresentation;
    function computeSchemaDifferences(schemaA, schemaB) {
      return arrayDifference(schemaA, schemaB).concat(arrayDifference(schemaB, schemaA));
    }
    exports.computeSchemaDifferences = computeSchemaDifferences;
    function isEmptyField(fieldValue) {
      return isUndefined(fieldValue) || isNull(fieldValue) || fieldValue === "";
    }
    exports.isEmptyField = isEmptyField;
    function removeEmptyFields(fields) {
      return fields.filter((field) => !isEmptyField(field));
    }
    exports.removeEmptyFields = removeEmptyFields;
    function getNCharacters(str, start, n) {
      return str.substring(start, start + n);
    }
    exports.getNCharacters = getNCharacters;
    function unwindItem(accumulator, item, fieldPath) {
      const valueToUnwind = (0, doc_path_1.evaluatePath)(item, fieldPath);
      let cloned = deepCopy(item);
      if (Array.isArray(valueToUnwind) && valueToUnwind.length) {
        valueToUnwind.forEach((val) => {
          cloned = deepCopy(item);
          accumulator.push((0, doc_path_1.setPath)(cloned, fieldPath, val));
        });
      } else if (Array.isArray(valueToUnwind) && valueToUnwind.length === 0) {
        (0, doc_path_1.setPath)(cloned, fieldPath, "");
        accumulator.push(cloned);
      } else {
        accumulator.push(cloned);
      }
    }
    function unwind(array, field) {
      const result = [];
      array.forEach((item) => {
        unwindItem(result, item, field);
      });
      return result;
    }
    exports.unwind = unwind;
    function isNumber(value) {
      return !isNaN(Number(value));
    }
    exports.isNumber = isNumber;
    function isString(value) {
      return typeof value === "string";
    }
    exports.isString = isString;
    function isObject(value) {
      return typeof value === "object";
    }
    exports.isObject = isObject;
    function isNull(value) {
      return value === null;
    }
    exports.isNull = isNull;
    function isUndefined(value) {
      return typeof value === "undefined";
    }
    exports.isUndefined = isUndefined;
    function isError(value) {
      return Object.prototype.toString.call(value) === "[object Error]";
    }
    exports.isError = isError;
    function arrayDifference(a, b) {
      return a.filter((x) => !b.includes(x));
    }
    exports.arrayDifference = arrayDifference;
    function unique(array) {
      return [...new Set(array)];
    }
    exports.unique = unique;
    function flatten(array) {
      if (array.flat) {
        return array.flat();
      }
      if (array.length > MAX_ARRAY_LENGTH) {
        let safeArray = [];
        for (let a = 0; a < array.length; a += MAX_ARRAY_LENGTH) {
          safeArray = safeArray.concat(...array.slice(a, a + MAX_ARRAY_LENGTH));
        }
        return safeArray;
      }
      return array.reduce((accumulator, value) => accumulator.concat(value), []);
    }
    exports.flatten = flatten;
    function isInvalid(parsedJson) {
      return parsedJson === Infinity || parsedJson === -Infinity;
    }
    exports.isInvalid = isInvalid;
  }
});

// ../node_modules/json-2-csv/lib/json2csv.js
var require_json2csv = __commonJS({
  "../node_modules/json-2-csv/lib/json2csv.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Json2Csv = void 0;
    var doc_path_1 = require_path();
    var deeks_1 = require_deeks();
    var constants_1 = require_constants();
    var utils = __importStar(require_utils2());
    var Json2Csv = function(options) {
      const wrapDelimiterCheckRegex = new RegExp(options.delimiter.wrap, "g"), crlfSearchRegex = /\r?\n|\r/, customValueParser = options.parseValue && typeof options.parseValue === "function" ? options.parseValue : null, expandingWithoutUnwinding = options.expandArrayObjects && !options.unwindArrays, deeksOptions = {
        arrayIndexesAsKeys: options.arrayIndexesAsKeys,
        expandNestedObjects: options.expandNestedObjects,
        expandArrayObjects: expandingWithoutUnwinding,
        ignoreEmptyArraysWhenExpanding: expandingWithoutUnwinding,
        escapeNestedDots: true
      };
      function getFieldNameList(data) {
        return (0, deeks_1.deepKeysFromList)(data, deeksOptions);
      }
      function processSchemas(documentSchemas) {
        if (documentSchemas.length === 0) {
          return [];
        }
        if (options.checkSchemaDifferences) {
          return checkSchemaDifferences(documentSchemas);
        } else {
          const uniqueFieldNames = utils.unique(utils.flatten(documentSchemas));
          return uniqueFieldNames;
        }
      }
      function checkSchemaDifferences(documentSchemas) {
        const firstDocSchema = documentSchemas[0], restOfDocumentSchemas = documentSchemas.slice(1), schemaDifferences = computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas);
        if (schemaDifferences) {
          throw new Error(constants_1.errors.json2csv.notSameSchema);
        }
        return firstDocSchema;
      }
      function computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas) {
        return restOfDocumentSchemas.reduce((schemaDifferences, documentSchema) => {
          const numberOfDifferences = utils.computeSchemaDifferences(firstDocSchema, documentSchema).length;
          return numberOfDifferences > 0 ? schemaDifferences + 1 : schemaDifferences;
        }, 0);
      }
      function filterExcludedKeys(keyPaths) {
        if (options.excludeKeys) {
          return keyPaths.filter((keyPath) => {
            for (const excludedKey of options.excludeKeys) {
              const regex = excludedKey instanceof RegExp ? excludedKey : new RegExp(`^${excludedKey}`);
              if (excludedKey === keyPath || keyPath.match(regex)) {
                return false;
              }
            }
            return true;
          });
        }
        return keyPaths;
      }
      function sortHeaderFields(fieldNames) {
        if (options.sortHeader && typeof options.sortHeader === "function") {
          return fieldNames.sort(options.sortHeader);
        } else if (options.sortHeader) {
          return fieldNames.sort();
        }
        return fieldNames;
      }
      function trimHeaderFields(params) {
        if (options.trimHeaderFields) {
          params.headerFields = params.headerFields.map((field) => field.split(".").map((component) => component.trim()).join("."));
        }
        return params;
      }
      function wrapHeaderFields(params) {
        if (options.prependHeader) {
          params.headerFields = params.headerFields.map(function(headingKey) {
            return wrapFieldValueIfNecessary(headingKey);
          });
        }
        return params;
      }
      function generateCsvHeader(params) {
        const fieldTitleMapKeys = Object.keys(options.fieldTitleMap);
        params.header = params.headerFields.map(function(field) {
          let headerKey = field;
          if (fieldTitleMapKeys.includes(field)) {
            headerKey = options.fieldTitleMap[field];
          } else if (!options.escapeHeaderNestedDots) {
            headerKey = headerKey.replace(/\\\./g, ".");
          }
          return headerKey;
        }).join(options.delimiter.field);
        return params;
      }
      function convertKeysToHeaderFields() {
        if (!options.keys)
          return [];
        return options.keys.map((key2) => {
          if (typeof key2 === "object" && "field" in key2) {
            options.fieldTitleMap[key2.field] = key2.title ?? key2.field;
            return key2.field;
          }
          return key2;
        });
      }
      function extractWildcardMatchKeys() {
        if (!options.keys)
          return [];
        return options.keys.flatMap((item) => {
          if (typeof item === "string") {
            return [];
          } else if (item == null ? void 0 : item.wildcardMatch) {
            return item.field;
          }
          return [];
        });
      }
      function retrieveHeaderFields(data) {
        const wildcardMatchKeys = extractWildcardMatchKeys();
        const keyStrings = convertKeysToHeaderFields();
        const fieldNames = getFieldNameList(data);
        const processed = processSchemas(fieldNames);
        if (options.keys) {
          options.keys = keyStrings;
          const matchedKeys = keyStrings.flatMap((userProvidedKey) => {
            if (!wildcardMatchKeys.includes(userProvidedKey)) {
              return userProvidedKey;
            }
            const matches = [];
            const regex = new RegExp(`^${userProvidedKey}`);
            for (const detectedKey of processed) {
              if (userProvidedKey === detectedKey || detectedKey.match(regex)) {
                matches.push(detectedKey);
              }
            }
            return matches;
          });
          if (!options.unwindArrays) {
            const filtered2 = filterExcludedKeys(matchedKeys);
            return sortHeaderFields(filtered2);
          }
        }
        const filtered = filterExcludedKeys(processed);
        return sortHeaderFields(filtered);
      }
      function stillNeedsUnwind(params) {
        for (const record of params.records) {
          for (const field of params.headerFields) {
            const value = (0, doc_path_1.evaluatePath)(record, field);
            if (Array.isArray(value)) {
              return true;
            }
          }
        }
        return false;
      }
      function unwindRecordsIfNecessary(params, finalPass = false) {
        if (options.unwindArrays) {
          params.headerFields.forEach((headerField) => {
            params.records = utils.unwind(params.records, headerField);
          });
          params.headerFields = retrieveHeaderFields(params.records);
          if (stillNeedsUnwind(params)) {
            return unwindRecordsIfNecessary(params, finalPass);
          }
          if (!finalPass) {
            return unwindRecordsIfNecessary(params, true);
          }
          if (options.keys) {
            const userSelectedFields = convertKeysToHeaderFields();
            params.headerFields = filterExcludedKeys(userSelectedFields);
          }
          return params;
        }
        return params;
      }
      function processRecords(params) {
        params.recordString = params.records.map((record) => {
          const recordFieldData = retrieveRecordFieldData(record, params.headerFields), processedRecordData = recordFieldData.map((fieldValue) => {
            fieldValue = trimRecordFieldValue(fieldValue);
            fieldValue = preventCsvInjection(fieldValue);
            let stringified = customValueParser ? customValueParser(fieldValue, recordFieldValueToString) : recordFieldValueToString(fieldValue);
            stringified = wrapFieldValueIfNecessary(stringified);
            return stringified;
          });
          return generateCsvRowFromRecord(processedRecordData);
        }).join(options.delimiter.eol);
        return params;
      }
      function processRecordFieldDataForExpandedArrayObject(recordFieldValue) {
        const filteredRecordFieldValue = utils.removeEmptyFields(recordFieldValue);
        if (!recordFieldValue.length || !filteredRecordFieldValue.length) {
          return options.emptyFieldValue || "";
        } else if (filteredRecordFieldValue.length === 1) {
          return filteredRecordFieldValue[0];
        }
        return recordFieldValue;
      }
      function retrieveRecordFieldData(record, fields) {
        const recordValues = [];
        fields.forEach((field) => {
          let recordFieldValue = (0, doc_path_1.evaluatePath)(record, field);
          if (!utils.isUndefined(options.emptyFieldValue) && utils.isEmptyField(recordFieldValue)) {
            recordFieldValue = options.emptyFieldValue;
          } else if (options.expandArrayObjects && Array.isArray(recordFieldValue)) {
            recordFieldValue = processRecordFieldDataForExpandedArrayObject(recordFieldValue);
          }
          recordValues.push(recordFieldValue);
        });
        return recordValues;
      }
      function recordFieldValueToString(fieldValue) {
        const isDate = fieldValue instanceof Date;
        if (fieldValue === null || Array.isArray(fieldValue) || typeof fieldValue === "object" && !isDate) {
          return JSON.stringify(fieldValue);
        } else if (typeof fieldValue === "undefined") {
          return "undefined";
        } else if (isDate && options.useDateIso8601Format) {
          return fieldValue.toISOString();
        } else {
          return !options.useLocaleFormat ? fieldValue.toString() : fieldValue.toLocaleString();
        }
      }
      function trimRecordFieldValue(fieldValue) {
        if (options.trimFieldValues) {
          if (Array.isArray(fieldValue)) {
            return fieldValue.map(trimRecordFieldValue);
          } else if (typeof fieldValue === "string") {
            return fieldValue.trim();
          }
          return fieldValue;
        }
        return fieldValue;
      }
      function preventCsvInjection(fieldValue) {
        if (options.preventCsvInjection) {
          if (Array.isArray(fieldValue)) {
            return fieldValue.map(preventCsvInjection);
          } else if (typeof fieldValue === "string" && !utils.isNumber(fieldValue)) {
            return fieldValue.replace(/^[=+\-@\t\r]+/g, "");
          }
          return fieldValue;
        }
        return fieldValue;
      }
      function wrapFieldValueIfNecessary(fieldValue) {
        const wrapDelimiter = options.delimiter.wrap;
        if (fieldValue.includes(options.delimiter.wrap)) {
          fieldValue = fieldValue.replace(wrapDelimiterCheckRegex, wrapDelimiter + wrapDelimiter);
        }
        if (fieldValue.includes(options.delimiter.field) || fieldValue.includes(options.delimiter.wrap) || fieldValue.match(crlfSearchRegex) || options.wrapBooleans && (fieldValue === "true" || fieldValue === "false")) {
          fieldValue = wrapDelimiter + fieldValue + wrapDelimiter;
        }
        return fieldValue;
      }
      function generateCsvRowFromRecord(recordFieldValues) {
        return recordFieldValues.join(options.delimiter.field);
      }
      function generateCsvFromComponents(params) {
        const header = params.header, records = params.recordString, csv = (options.excelBOM ? constants_1.excelBOM : "") + (options.prependHeader ? header + options.delimiter.eol : "") + records;
        return csv;
      }
      function convert(data) {
        if (!Array.isArray(data)) {
          data = [data];
        }
        const headerFields = {
          headerFields: retrieveHeaderFields(data),
          records: data,
          header: "",
          recordString: ""
        };
        const unwinded = unwindRecordsIfNecessary(headerFields);
        const processed = processRecords(unwinded);
        const wrapped = wrapHeaderFields(processed);
        const trimmed = trimHeaderFields(wrapped);
        const generated = generateCsvHeader(trimmed);
        return generateCsvFromComponents(generated);
      }
      return {
        convert
      };
    };
    exports.Json2Csv = Json2Csv;
  }
});

// ../node_modules/json-2-csv/lib/csv2json.js
var require_csv2json = __commonJS({
  "../node_modules/json-2-csv/lib/csv2json.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Csv2Json = void 0;
    var doc_path_1 = require_path();
    var constants_1 = require_constants();
    var utils = __importStar(require_utils2());
    var Csv2Json = function(options) {
      const escapedWrapDelimiterRegex = new RegExp(options.delimiter.wrap + options.delimiter.wrap, "g"), excelBOMRegex = new RegExp("^" + constants_1.excelBOM), valueParserFn = options.parseValue && typeof options.parseValue === "function" ? options.parseValue : JSON.parse;
      function processHeaderKey(headerKey) {
        headerKey = removeWrapDelimitersFromValue(headerKey);
        if (options.trimHeaderFields) {
          return headerKey.split(".").map((component) => component.trim()).join(".");
        }
        return headerKey;
      }
      function retrieveHeading(lines) {
        let headerFields = [];
        if (options.headerFields) {
          headerFields = options.headerFields.map((headerField, index) => ({
            value: processHeaderKey(headerField),
            index
          }));
        } else {
          const headerRow = lines[0];
          headerFields = headerRow.map((headerKey, index) => ({
            value: processHeaderKey(headerKey),
            index
          }));
          if (options.keys) {
            const keys = options.keys;
            headerFields = headerFields.filter((headerKey) => keys.includes(headerKey.value));
          }
        }
        return {
          lines,
          headerFields,
          recordLines: []
        };
      }
      function stripExcelBOM(csv) {
        if (options.excelBOM) {
          return csv.replace(excelBOMRegex, "");
        }
        return csv;
      }
      function splitLines(csv) {
        const lines = [], lastCharacterIndex = csv.length - 1, eolDelimiterLength = options.delimiter.eol.length, stateVariables = {
          insideWrapDelimiter: false,
          parsingValue: true,
          justParsedDoubleQuote: false,
          startIndex: 0
        };
        let splitLine = [], character, charBefore, charAfter, nextNChar, index = 0;
        while (index < csv.length) {
          character = csv[index];
          charBefore = index ? csv[index - 1] : "";
          charAfter = index < lastCharacterIndex ? csv[index + 1] : "";
          nextNChar = utils.getNCharacters(csv, index, eolDelimiterLength);
          if ((nextNChar === options.delimiter.eol && !stateVariables.insideWrapDelimiter || index === lastCharacterIndex) && charBefore === options.delimiter.field) {
            if (nextNChar === options.delimiter.eol && stateVariables.startIndex === index) {
              splitLine.push("");
            } else if (character === options.delimiter.field) {
              splitLine.push("");
            } else {
              splitLine.push(csv.substring(stateVariables.startIndex));
            }
            splitLine.push("");
            lines.push(splitLine);
            splitLine = [];
            stateVariables.startIndex = index + eolDelimiterLength;
            stateVariables.parsingValue = true;
            stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;
          } else if (index === lastCharacterIndex && character === options.delimiter.field) {
            const parsedValue = csv.substring(stateVariables.startIndex, index);
            splitLine.push(parsedValue);
            splitLine.push("");
            lines.push(splitLine);
          } else if (index === lastCharacterIndex || nextNChar === options.delimiter.eol && // if we aren't inside wrap delimiters or if we are but the character before was a wrap delimiter and we didn't just see two
          (!stateVariables.insideWrapDelimiter || stateVariables.insideWrapDelimiter && charBefore === options.delimiter.wrap && !stateVariables.justParsedDoubleQuote)) {
            const toIndex = index !== lastCharacterIndex || charBefore === options.delimiter.wrap ? index : void 0;
            splitLine.push(csv.substring(stateVariables.startIndex, toIndex));
            lines.push(splitLine);
            splitLine = [];
            stateVariables.startIndex = index + eolDelimiterLength;
            stateVariables.parsingValue = true;
            stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;
          } else if (character === options.delimiter.wrap && charBefore === options.delimiter.field && !stateVariables.insideWrapDelimiter && !stateVariables.parsingValue) {
            stateVariables.startIndex = index;
            stateVariables.insideWrapDelimiter = true;
            stateVariables.parsingValue = true;
            if (utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {
              index += options.delimiter.eol.length + 1;
            }
          } else if (charBefore === options.delimiter.field && character === options.delimiter.wrap && charAfter === options.delimiter.eol) {
            splitLine.push(csv.substring(stateVariables.startIndex, index - 1));
            stateVariables.startIndex = index;
            stateVariables.parsingValue = true;
            stateVariables.insideWrapDelimiter = true;
            stateVariables.justParsedDoubleQuote = true;
            index += 1;
          } else if ((charBefore !== options.delimiter.wrap || stateVariables.justParsedDoubleQuote && charBefore === options.delimiter.wrap) && character === options.delimiter.wrap && utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {
            stateVariables.insideWrapDelimiter = false;
            stateVariables.parsingValue = false;
          } else if (character === options.delimiter.wrap && (index === 0 || utils.getNCharacters(csv, index - eolDelimiterLength, eolDelimiterLength) === options.delimiter.eol && !stateVariables.insideWrapDelimiter)) {
            stateVariables.insideWrapDelimiter = true;
            stateVariables.parsingValue = true;
            stateVariables.startIndex = index;
          } else if (character === options.delimiter.wrap && charAfter === options.delimiter.field && stateVariables.insideWrapDelimiter) {
            splitLine.push(csv.substring(stateVariables.startIndex, index + 1));
            stateVariables.startIndex = index + 2;
            stateVariables.insideWrapDelimiter = false;
            stateVariables.parsingValue = false;
          } else if (character === options.delimiter.wrap && charBefore === options.delimiter.field && !stateVariables.insideWrapDelimiter && stateVariables.parsingValue) {
            splitLine.push(csv.substring(stateVariables.startIndex, index - 1));
            stateVariables.insideWrapDelimiter = true;
            stateVariables.parsingValue = true;
            stateVariables.startIndex = index;
          } else if (character === options.delimiter.wrap && charAfter === options.delimiter.wrap && index !== stateVariables.startIndex) {
            index += 2;
            stateVariables.justParsedDoubleQuote = true;
            continue;
          } else if (character === options.delimiter.field && charBefore !== options.delimiter.wrap && charAfter !== options.delimiter.wrap && !stateVariables.insideWrapDelimiter && stateVariables.parsingValue) {
            splitLine.push(csv.substring(stateVariables.startIndex, index));
            stateVariables.startIndex = index + 1;
          } else if (character === options.delimiter.field && charBefore === options.delimiter.wrap && charAfter !== options.delimiter.wrap && !stateVariables.parsingValue) {
            stateVariables.insideWrapDelimiter = false;
            stateVariables.parsingValue = true;
            stateVariables.startIndex = index + 1;
          }
          index++;
          stateVariables.justParsedDoubleQuote = false;
        }
        return lines;
      }
      function retrieveRecordLines(params) {
        if (options.headerFields) {
          params.recordLines = params.lines;
        } else {
          params.recordLines = params.lines.splice(1);
        }
        return params;
      }
      function retrieveRecordValueFromLine(headerField, line2) {
        const value = line2[headerField.index];
        return processRecordValue(value);
      }
      function processRecordValue(fieldValue) {
        const parsedJson = parseValue(fieldValue);
        if (!utils.isError(parsedJson) && !utils.isInvalid(parsedJson)) {
          return parsedJson;
        } else if (fieldValue === "undefined") {
          return void 0;
        }
        return fieldValue;
      }
      function trimRecordValue(fieldValue) {
        if (options.trimFieldValues && fieldValue !== null) {
          return fieldValue.trim();
        }
        return fieldValue;
      }
      function createDocument(headerFields, line2) {
        return headerFields.reduce((document2, headerField) => {
          const value = retrieveRecordValueFromLine(headerField, line2);
          try {
            return (0, doc_path_1.setPath)(document2, headerField.value, value);
          } catch (error) {
            return document2;
          }
        }, {});
      }
      function removeWrapDelimitersFromValue(fieldValue) {
        const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];
        if (firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap) {
          return fieldValue.length <= 2 ? "" : fieldValue.substring(1, lastIndex);
        }
        return fieldValue;
      }
      function unescapeWrapDelimiterInField(fieldValue) {
        return fieldValue.replace(escapedWrapDelimiterRegex, options.delimiter.wrap);
      }
      function transformRecordLines(params) {
        return params.recordLines.reduce((generatedJsonObjects, line2) => {
          line2 = line2.map((fieldValue) => {
            fieldValue = removeWrapDelimitersFromValue(fieldValue);
            fieldValue = unescapeWrapDelimiterInField(fieldValue);
            fieldValue = trimRecordValue(fieldValue);
            return fieldValue;
          });
          const generatedDocument = createDocument(params.headerFields, line2);
          return generatedJsonObjects.concat(generatedDocument);
        }, []);
      }
      function parseValue(value) {
        try {
          if (utils.isStringRepresentation(value, options) && !utils.isDateRepresentation(value)) {
            return value;
          }
          const parsedJson = valueParserFn(value);
          if (Array.isArray(parsedJson)) {
            return parsedJson.map(trimRecordValue);
          }
          return parsedJson;
        } catch (err) {
          return err;
        }
      }
      function convert(data) {
        const stripped = stripExcelBOM(data);
        const split = splitLines(stripped);
        const heading = retrieveHeading(split);
        const lines = retrieveRecordLines(heading);
        return transformRecordLines(lines);
      }
      return {
        convert
      };
    };
    exports.Csv2Json = Csv2Json;
  }
});

// ../node_modules/json-2-csv/lib/converter.js
var require_converter = __commonJS({
  "../node_modules/json-2-csv/lib/converter.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.csv2json = exports.json2csv = void 0;
    var constants_1 = require_constants();
    var json2csv_1 = require_json2csv();
    var csv2json_1 = require_csv2json();
    var utils_1 = require_utils2();
    function json2csv2(data, options) {
      const builtOptions = (0, utils_1.buildJ2COptions)(options ?? {});
      (0, utils_1.validate)(data, utils_1.isObject, constants_1.errors.json2csv);
      return (0, json2csv_1.Json2Csv)(builtOptions).convert(data);
    }
    exports.json2csv = json2csv2;
    function csv2json(data, options) {
      const builtOptions = (0, utils_1.buildC2JOptions)(options ?? {});
      (0, utils_1.validate)(data, utils_1.isString, constants_1.errors.csv2json);
      return (0, csv2json_1.Csv2Json)(builtOptions).convert(data);
    }
    exports.csv2json = csv2json;
  }
});

// ../node_modules/@sanity/vision/lib/_chunks-es/SanityVision.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react_compiler_runtime = __toESM(require_dist(), 1);
var import_react2 = __toESM(require_react(), 1);

// ../node_modules/@rexxars/react-split-pane/dist/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var __defProp$1 = Object.defineProperty;
var __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;
var __hasOwnProp$1 = Object.prototype.hasOwnProperty;
var __propIsEnum$1 = Object.prototype.propertyIsEnumerable;
var __defNormalProp$1 = (obj, key2, value) => key2 in obj ? __defProp$1(obj, key2, { enumerable: true, configurable: true, writable: true, value }) : obj[key2] = value;
var __spreadValues$1 = (a, b) => {
  for (var prop in b || (b = {}))
    __hasOwnProp$1.call(b, prop) && __defNormalProp$1(a, prop, b[prop]);
  if (__getOwnPropSymbols$1)
    for (var prop of __getOwnPropSymbols$1(b))
      __propIsEnum$1.call(b, prop) && __defNormalProp$1(a, prop, b[prop]);
  return a;
};
var Pane = function(props) {
  const { children, className, split, style: styleProps, size, eleRef } = props;
  let style = {
    flex: 1,
    position: "relative",
    outline: "none"
  };
  size !== void 0 && (split === "vertical" ? style.width = size : (style.height = size, style.display = "flex"), style.flex = "none"), style = __spreadValues$1(__spreadValues$1({}, style), styleProps);
  const classes = ["Pane", split, className].filter(Boolean).join(" ");
  return (0, import_jsx_runtime.jsx)("div", { role: "region", ref: eleRef, className: classes, style, children });
};
var RESIZER_DEFAULT_CLASSNAME = "Resizer";
var Resizer = function(props) {
  const {
    className = RESIZER_DEFAULT_CLASSNAME,
    onClick,
    onDoubleClick,
    onMouseDown,
    onTouchEnd,
    onTouchStart,
    resizerClassName,
    split,
    style
  } = props, classes = [resizerClassName, split, className].filter(Boolean).join(" ");
  return (0, import_jsx_runtime.jsx)(
    "span",
    {
      role: "separator",
      className: classes,
      style,
      onMouseDown: (event) => onMouseDown(event.nativeEvent),
      onTouchStart: (event) => {
        event.preventDefault(), onTouchStart(event.nativeEvent);
      },
      onTouchEnd: (event) => {
        event.preventDefault(), onTouchEnd(event.nativeEvent);
      },
      onClick: (event) => {
        onClick && (event.preventDefault(), onClick(event.nativeEvent));
      },
      onDoubleClick: (event) => {
        onDoubleClick && (event.preventDefault(), onDoubleClick(event.nativeEvent));
      }
    }
  );
};
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key2, value) => key2 in obj ? __defProp(obj, key2, { enumerable: true, configurable: true, writable: true, value }) : obj[key2] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    __hasOwnProp.call(b, prop) && __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b))
      __propIsEnum.call(b, prop) && __defNormalProp(a, prop, b[prop]);
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __publicField2 = (obj, key2, value) => __defNormalProp(obj, typeof key2 != "symbol" ? key2 + "" : key2, value);
var BASE_STYLES = {
  display: "flex",
  flex: 1,
  height: "100%",
  position: "absolute",
  outline: "none",
  overflow: "hidden",
  MozUserSelect: "text",
  WebkitUserSelect: "text",
  msUserSelect: "text",
  userSelect: "text"
};
var VERTICAL_STYLES = __spreadProps(__spreadValues({}, BASE_STYLES), {
  flexDirection: "row",
  left: 0,
  right: 0
});
var HORIZONTAL_STYLES = __spreadProps(__spreadValues({}, BASE_STYLES), {
  bottom: 0,
  flexDirection: "column",
  minHeight: "100%",
  top: 0,
  width: "100%"
});
var EMPTY_STYLES = {};
var _SplitPane = class _SplitPane2 extends import_react.Component {
  constructor(props) {
    super(props), __publicField2(this, "pane1", null), __publicField2(this, "pane2", null), __publicField2(this, "splitPane", null), this.onMouseDown = this.onMouseDown.bind(this), this.onTouchStart = this.onTouchStart.bind(this), this.onMouseMove = this.onMouseMove.bind(this), this.onTouchMove = this.onTouchMove.bind(this), this.onMouseUp = this.onMouseUp.bind(this);
    const { size, defaultSize, minSize, maxSize, primary } = props, initialSize = size !== void 0 ? size : getDefaultSize(defaultSize, minSize, maxSize, void 0);
    this.state = {
      active: false,
      resized: false,
      pane1Size: primary === "first" ? initialSize : void 0,
      pane2Size: primary === "second" ? initialSize : void 0,
      // these are props that are needed in static functions. ie: gDSFP
      instanceProps: {
        size
      }
    };
  }
  componentDidMount() {
    document.addEventListener("mouseup", this.onMouseUp), document.addEventListener("mousemove", this.onMouseMove), document.addEventListener("touchmove", this.onTouchMove), this.setState(_SplitPane2.getSizeUpdate(this.props, this.state));
  }
  static getDerivedStateFromProps(nextProps, prevState) {
    return _SplitPane2.getSizeUpdate(nextProps, prevState);
  }
  componentWillUnmount() {
    document.removeEventListener("mouseup", this.onMouseUp), document.removeEventListener("mousemove", this.onMouseMove), document.removeEventListener("touchmove", this.onTouchMove);
  }
  onMouseDown(event) {
    this.onTouchStart(__spreadProps(__spreadValues({}, event), {
      touches: [{ clientX: event.clientX, clientY: event.clientY }]
    }));
  }
  onTouchStart(event) {
    const { allowResize, onDragStarted, split } = this.props;
    if (allowResize) {
      unFocus(document, window);
      const position = split === "vertical" ? event.touches[0].clientX : event.touches[0].clientY;
      typeof onDragStarted == "function" && onDragStarted(), this.setState({
        active: true,
        position
      });
    }
  }
  onMouseMove(event) {
    const eventWithTouches = Object.assign({}, event, {
      touches: [{ clientX: event.clientX, clientY: event.clientY }]
    });
    this.onTouchMove(eventWithTouches);
  }
  onTouchMove(event) {
    if (!this.state.active || !this.props.allowResize)
      return;
    const { position = 0 } = this.state, {
      maxSize,
      minSize = _SplitPane2.defaultProps.minSize,
      onChange,
      split = _SplitPane2.defaultProps.split,
      step
    } = this.props;
    unFocus(document, window);
    const isPrimaryFirst = this.props.primary === "first", ref = isPrimaryFirst ? this.pane1 : this.pane2, ref2 = isPrimaryFirst ? this.pane2 : this.pane1;
    if (!ref || !ref2 || !ref.getBoundingClientRect)
      return;
    const node = ref, node2 = ref2, width = node.getBoundingClientRect().width, height = node.getBoundingClientRect().height, current = split === "vertical" ? event.touches[0].clientX : event.touches[0].clientY, size = split === "vertical" ? width : height;
    let positionDelta = position - current;
    if (step) {
      if (Math.abs(positionDelta) < step)
        return;
      positionDelta = ~~(positionDelta / step) * step;
    }
    let sizeDelta = isPrimaryFirst ? positionDelta : -positionDelta;
    const pane1Order = parseInt(window.getComputedStyle(node).order), pane2Order = parseInt(window.getComputedStyle(node2).order);
    pane1Order > pane2Order && (sizeDelta = -sizeDelta);
    let newMaxSize = maxSize;
    this.splitPane && maxSize !== void 0 && maxSize <= 0 && (split === "vertical" ? newMaxSize = this.splitPane.getBoundingClientRect().width + maxSize : newMaxSize = this.splitPane.getBoundingClientRect().height + maxSize);
    let newSize = size - sizeDelta;
    const newPosition = position - positionDelta;
    minSize && newSize < minSize ? newSize = minSize : newMaxSize !== void 0 && newSize > newMaxSize ? newSize = newMaxSize : this.setState({
      position: newPosition,
      resized: true
    }), onChange && onChange(newSize);
    const sizeState = isPrimaryFirst ? { pane1Size: newSize, pane2Size: void 0 } : { pane2Size: newSize, pane1Size: void 0 };
    this.setState(__spreadValues({ draggedSize: newSize }, sizeState));
  }
  onMouseUp() {
    if (!this.state.active || !this.props.allowResize)
      return;
    const { onDragFinished } = this.props, { draggedSize } = this.state;
    typeof draggedSize < "u" && typeof onDragFinished == "function" && onDragFinished(draggedSize), this.setState({ active: false });
  }
  // we have to check values since gDSFP is called on every render and more in StrictMode
  static getSizeUpdate(props, state) {
    const { instanceProps } = state;
    if (instanceProps.size === props.size && props.size !== void 0)
      return {};
    const newSize = props.size !== void 0 ? props.size : getDefaultSize(
      props.defaultSize,
      props.minSize,
      props.maxSize,
      state.draggedSize
    ), sizeState = props.primary === "first" ? { pane1Size: newSize, pane2Size: void 0 } : { pane2Size: newSize, pane1Size: void 0 };
    return __spreadProps(__spreadValues(__spreadValues({}, sizeState), typeof props.size > "u" ? {} : { draggedSize: newSize }), {
      instanceProps: { size: props.size }
    });
  }
  render() {
    const {
      allowResize,
      children,
      className,
      onResizerClick,
      onResizerDoubleClick,
      paneClassName,
      pane1ClassName,
      pane2ClassName,
      paneStyle,
      pane1Style: pane1StyleProps,
      pane2Style: pane2StyleProps,
      resizerClassName = RESIZER_DEFAULT_CLASSNAME,
      resizerStyle,
      split,
      style: styleProps
    } = this.props, { pane1Size, pane2Size } = this.state, disabledClass = allowResize ? "" : "disabled", resizerClassNamesIncludingDefault = resizerClassName && `${resizerClassName} ${RESIZER_DEFAULT_CLASSNAME}`, notNullChildren = removeNullChildren(children), baseStyles = split === "vertical" ? VERTICAL_STYLES : HORIZONTAL_STYLES, style = styleProps ? __spreadValues(__spreadValues({}, baseStyles), styleProps) : baseStyles, classes = ["SplitPane", className, split, disabledClass].filter(Boolean).join(" "), pane1Style = coalesceOnEmpty(
      __spreadValues(__spreadValues({}, paneStyle), pane1StyleProps),
      EMPTY_STYLES
    ), pane2Style = coalesceOnEmpty(
      __spreadValues(__spreadValues({}, paneStyle), pane2StyleProps),
      EMPTY_STYLES
    ), pane1Classes = ["Pane1", paneClassName, pane1ClassName].join(" "), pane2Classes = ["Pane2", paneClassName, pane2ClassName].join(" ");
    return (0, import_jsx_runtime.jsxs)(
      "div",
      {
        "data-testid": "split-pane",
        className: classes,
        style,
        ref: (node) => {
          this.splitPane = node;
        },
        children: [
          (0, import_jsx_runtime.jsx)(
            Pane,
            {
              className: pane1Classes,
              eleRef: (node) => {
                this.pane1 = node;
              },
              size: pane1Size,
              split,
              style: pane1Style,
              children: notNullChildren[0]
            },
            "pane1"
          ),
          (0, import_jsx_runtime.jsx)(
            Resizer,
            {
              className: disabledClass,
              onClick: onResizerClick,
              onDoubleClick: onResizerDoubleClick,
              onMouseDown: this.onMouseDown,
              onTouchStart: this.onTouchStart,
              onTouchEnd: this.onMouseUp,
              resizerClassName: resizerClassNamesIncludingDefault,
              split: split || "vertical",
              style: resizerStyle || EMPTY_STYLES
            },
            "resizer"
          ),
          (0, import_jsx_runtime.jsx)(
            Pane,
            {
              className: pane2Classes,
              eleRef: (node) => {
                this.pane2 = node;
              },
              size: pane2Size,
              split,
              style: pane2Style,
              children: notNullChildren[1]
            },
            "pane2"
          )
        ]
      }
    );
  }
};
__publicField2(_SplitPane, "defaultProps", {
  allowResize: true,
  minSize: 50,
  primary: "first",
  split: "vertical",
  paneClassName: "",
  pane1ClassName: "",
  pane2ClassName: ""
});
var SplitPane = _SplitPane;
function unFocus(document2, window2) {
  var _a;
  if ("selection" in document2 && typeof document2.selection == "object" && document2.selection && "empty" in document2.selection && typeof document2.selection.empty == "function")
    try {
      document2.selection.empty();
    } catch (e) {
    }
  else if (typeof window2 < "u" && typeof window2.getSelection == "function")
    try {
      (_a = window2.getSelection()) == null || _a.removeAllRanges();
    } catch (e) {
    }
}
function getDefaultSize(defaultSize, minSize, maxSize, draggedSize) {
  if (typeof draggedSize == "number") {
    const min = typeof minSize == "number" ? minSize : 0, max = typeof maxSize == "number" && maxSize >= 0 ? maxSize : 1 / 0;
    return Math.max(min, Math.min(max, draggedSize));
  }
  return defaultSize !== void 0 ? defaultSize : minSize;
}
function removeNullChildren(children) {
  return import_react.Children.toArray(children).filter((c3) => c3);
}
function isEmptyish(obj) {
  return obj === null || typeof obj > "u" || Object.keys(obj).length === 0;
}
function coalesceOnEmpty(obj, useOnEmpty) {
  return isEmptyish(obj) ? useOnEmpty : obj;
}

// ../node_modules/@sanity/vision/lib/_chunks-es/SanityVision.mjs
var import_debounce = __toESM(require_debounce(), 1);

// ../node_modules/json5/dist/index.mjs
var Space_Separator = /[\u1680\u2000-\u200A\u202F\u205F\u3000]/;
var ID_Start = /[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/;
var ID_Continue = /[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/;
var unicode = {
  Space_Separator,
  ID_Start,
  ID_Continue
};
var util = {
  isSpaceSeparator(c3) {
    return typeof c3 === "string" && unicode.Space_Separator.test(c3);
  },
  isIdStartChar(c3) {
    return typeof c3 === "string" && (c3 >= "a" && c3 <= "z" || c3 >= "A" && c3 <= "Z" || c3 === "$" || c3 === "_" || unicode.ID_Start.test(c3));
  },
  isIdContinueChar(c3) {
    return typeof c3 === "string" && (c3 >= "a" && c3 <= "z" || c3 >= "A" && c3 <= "Z" || c3 >= "0" && c3 <= "9" || c3 === "$" || c3 === "_" || c3 === "‌" || c3 === "‍" || unicode.ID_Continue.test(c3));
  },
  isDigit(c3) {
    return typeof c3 === "string" && /[0-9]/.test(c3);
  },
  isHexDigit(c3) {
    return typeof c3 === "string" && /[0-9A-Fa-f]/.test(c3);
  }
};
var source;
var parseState;
var stack;
var pos;
var line;
var column;
var token;
var key;
var root;
var parse = function parse2(text, reviver) {
  source = String(text);
  parseState = "start";
  stack = [];
  pos = 0;
  line = 1;
  column = 0;
  token = void 0;
  key = void 0;
  root = void 0;
  do {
    token = lex();
    parseStates[parseState]();
  } while (token.type !== "eof");
  if (typeof reviver === "function") {
    return internalize({ "": root }, "", reviver);
  }
  return root;
};
function internalize(holder, name, reviver) {
  const value = holder[name];
  if (value != null && typeof value === "object") {
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const key2 = String(i);
        const replacement = internalize(value, key2, reviver);
        if (replacement === void 0) {
          delete value[key2];
        } else {
          Object.defineProperty(value, key2, {
            value: replacement,
            writable: true,
            enumerable: true,
            configurable: true
          });
        }
      }
    } else {
      for (const key2 in value) {
        const replacement = internalize(value, key2, reviver);
        if (replacement === void 0) {
          delete value[key2];
        } else {
          Object.defineProperty(value, key2, {
            value: replacement,
            writable: true,
            enumerable: true,
            configurable: true
          });
        }
      }
    }
  }
  return reviver.call(holder, name, value);
}
var lexState;
var buffer;
var doubleQuote;
var sign;
var c;
function lex() {
  lexState = "default";
  buffer = "";
  doubleQuote = false;
  sign = 1;
  for (; ; ) {
    c = peek();
    const token2 = lexStates[lexState]();
    if (token2) {
      return token2;
    }
  }
}
function peek() {
  if (source[pos]) {
    return String.fromCodePoint(source.codePointAt(pos));
  }
}
function read() {
  const c3 = peek();
  if (c3 === "\n") {
    line++;
    column = 0;
  } else if (c3) {
    column += c3.length;
  } else {
    column++;
  }
  if (c3) {
    pos += c3.length;
  }
  return c3;
}
var lexStates = {
  default() {
    switch (c) {
      case "	":
      case "\v":
      case "\f":
      case " ":
      case " ":
      case "\uFEFF":
      case "\n":
      case "\r":
      case "\u2028":
      case "\u2029":
        read();
        return;
      case "/":
        read();
        lexState = "comment";
        return;
      case void 0:
        read();
        return newToken("eof");
    }
    if (util.isSpaceSeparator(c)) {
      read();
      return;
    }
    return lexStates[parseState]();
  },
  comment() {
    switch (c) {
      case "*":
        read();
        lexState = "multiLineComment";
        return;
      case "/":
        read();
        lexState = "singleLineComment";
        return;
    }
    throw invalidChar(read());
  },
  multiLineComment() {
    switch (c) {
      case "*":
        read();
        lexState = "multiLineCommentAsterisk";
        return;
      case void 0:
        throw invalidChar(read());
    }
    read();
  },
  multiLineCommentAsterisk() {
    switch (c) {
      case "*":
        read();
        return;
      case "/":
        read();
        lexState = "default";
        return;
      case void 0:
        throw invalidChar(read());
    }
    read();
    lexState = "multiLineComment";
  },
  singleLineComment() {
    switch (c) {
      case "\n":
      case "\r":
      case "\u2028":
      case "\u2029":
        read();
        lexState = "default";
        return;
      case void 0:
        read();
        return newToken("eof");
    }
    read();
  },
  value() {
    switch (c) {
      case "{":
      case "[":
        return newToken("punctuator", read());
      case "n":
        read();
        literal("ull");
        return newToken("null", null);
      case "t":
        read();
        literal("rue");
        return newToken("boolean", true);
      case "f":
        read();
        literal("alse");
        return newToken("boolean", false);
      case "-":
      case "+":
        if (read() === "-") {
          sign = -1;
        }
        lexState = "sign";
        return;
      case ".":
        buffer = read();
        lexState = "decimalPointLeading";
        return;
      case "0":
        buffer = read();
        lexState = "zero";
        return;
      case "1":
      case "2":
      case "3":
      case "4":
      case "5":
      case "6":
      case "7":
      case "8":
      case "9":
        buffer = read();
        lexState = "decimalInteger";
        return;
      case "I":
        read();
        literal("nfinity");
        return newToken("numeric", Infinity);
      case "N":
        read();
        literal("aN");
        return newToken("numeric", NaN);
      case '"':
      case "'":
        doubleQuote = read() === '"';
        buffer = "";
        lexState = "string";
        return;
    }
    throw invalidChar(read());
  },
  identifierNameStartEscape() {
    if (c !== "u") {
      throw invalidChar(read());
    }
    read();
    const u = unicodeEscape();
    switch (u) {
      case "$":
      case "_":
        break;
      default:
        if (!util.isIdStartChar(u)) {
          throw invalidIdentifier();
        }
        break;
    }
    buffer += u;
    lexState = "identifierName";
  },
  identifierName() {
    switch (c) {
      case "$":
      case "_":
      case "‌":
      case "‍":
        buffer += read();
        return;
      case "\\":
        read();
        lexState = "identifierNameEscape";
        return;
    }
    if (util.isIdContinueChar(c)) {
      buffer += read();
      return;
    }
    return newToken("identifier", buffer);
  },
  identifierNameEscape() {
    if (c !== "u") {
      throw invalidChar(read());
    }
    read();
    const u = unicodeEscape();
    switch (u) {
      case "$":
      case "_":
      case "‌":
      case "‍":
        break;
      default:
        if (!util.isIdContinueChar(u)) {
          throw invalidIdentifier();
        }
        break;
    }
    buffer += u;
    lexState = "identifierName";
  },
  sign() {
    switch (c) {
      case ".":
        buffer = read();
        lexState = "decimalPointLeading";
        return;
      case "0":
        buffer = read();
        lexState = "zero";
        return;
      case "1":
      case "2":
      case "3":
      case "4":
      case "5":
      case "6":
      case "7":
      case "8":
      case "9":
        buffer = read();
        lexState = "decimalInteger";
        return;
      case "I":
        read();
        literal("nfinity");
        return newToken("numeric", sign * Infinity);
      case "N":
        read();
        literal("aN");
        return newToken("numeric", NaN);
    }
    throw invalidChar(read());
  },
  zero() {
    switch (c) {
      case ".":
        buffer += read();
        lexState = "decimalPoint";
        return;
      case "e":
      case "E":
        buffer += read();
        lexState = "decimalExponent";
        return;
      case "x":
      case "X":
        buffer += read();
        lexState = "hexadecimal";
        return;
    }
    return newToken("numeric", sign * 0);
  },
  decimalInteger() {
    switch (c) {
      case ".":
        buffer += read();
        lexState = "decimalPoint";
        return;
      case "e":
      case "E":
        buffer += read();
        lexState = "decimalExponent";
        return;
    }
    if (util.isDigit(c)) {
      buffer += read();
      return;
    }
    return newToken("numeric", sign * Number(buffer));
  },
  decimalPointLeading() {
    if (util.isDigit(c)) {
      buffer += read();
      lexState = "decimalFraction";
      return;
    }
    throw invalidChar(read());
  },
  decimalPoint() {
    switch (c) {
      case "e":
      case "E":
        buffer += read();
        lexState = "decimalExponent";
        return;
    }
    if (util.isDigit(c)) {
      buffer += read();
      lexState = "decimalFraction";
      return;
    }
    return newToken("numeric", sign * Number(buffer));
  },
  decimalFraction() {
    switch (c) {
      case "e":
      case "E":
        buffer += read();
        lexState = "decimalExponent";
        return;
    }
    if (util.isDigit(c)) {
      buffer += read();
      return;
    }
    return newToken("numeric", sign * Number(buffer));
  },
  decimalExponent() {
    switch (c) {
      case "+":
      case "-":
        buffer += read();
        lexState = "decimalExponentSign";
        return;
    }
    if (util.isDigit(c)) {
      buffer += read();
      lexState = "decimalExponentInteger";
      return;
    }
    throw invalidChar(read());
  },
  decimalExponentSign() {
    if (util.isDigit(c)) {
      buffer += read();
      lexState = "decimalExponentInteger";
      return;
    }
    throw invalidChar(read());
  },
  decimalExponentInteger() {
    if (util.isDigit(c)) {
      buffer += read();
      return;
    }
    return newToken("numeric", sign * Number(buffer));
  },
  hexadecimal() {
    if (util.isHexDigit(c)) {
      buffer += read();
      lexState = "hexadecimalInteger";
      return;
    }
    throw invalidChar(read());
  },
  hexadecimalInteger() {
    if (util.isHexDigit(c)) {
      buffer += read();
      return;
    }
    return newToken("numeric", sign * Number(buffer));
  },
  string() {
    switch (c) {
      case "\\":
        read();
        buffer += escape();
        return;
      case '"':
        if (doubleQuote) {
          read();
          return newToken("string", buffer);
        }
        buffer += read();
        return;
      case "'":
        if (!doubleQuote) {
          read();
          return newToken("string", buffer);
        }
        buffer += read();
        return;
      case "\n":
      case "\r":
        throw invalidChar(read());
      case "\u2028":
      case "\u2029":
        separatorChar(c);
        break;
      case void 0:
        throw invalidChar(read());
    }
    buffer += read();
  },
  start() {
    switch (c) {
      case "{":
      case "[":
        return newToken("punctuator", read());
    }
    lexState = "value";
  },
  beforePropertyName() {
    switch (c) {
      case "$":
      case "_":
        buffer = read();
        lexState = "identifierName";
        return;
      case "\\":
        read();
        lexState = "identifierNameStartEscape";
        return;
      case "}":
        return newToken("punctuator", read());
      case '"':
      case "'":
        doubleQuote = read() === '"';
        lexState = "string";
        return;
    }
    if (util.isIdStartChar(c)) {
      buffer += read();
      lexState = "identifierName";
      return;
    }
    throw invalidChar(read());
  },
  afterPropertyName() {
    if (c === ":") {
      return newToken("punctuator", read());
    }
    throw invalidChar(read());
  },
  beforePropertyValue() {
    lexState = "value";
  },
  afterPropertyValue() {
    switch (c) {
      case ",":
      case "}":
        return newToken("punctuator", read());
    }
    throw invalidChar(read());
  },
  beforeArrayValue() {
    if (c === "]") {
      return newToken("punctuator", read());
    }
    lexState = "value";
  },
  afterArrayValue() {
    switch (c) {
      case ",":
      case "]":
        return newToken("punctuator", read());
    }
    throw invalidChar(read());
  },
  end() {
    throw invalidChar(read());
  }
};
function newToken(type, value) {
  return {
    type,
    value,
    line,
    column
  };
}
function literal(s) {
  for (const c3 of s) {
    const p = peek();
    if (p !== c3) {
      throw invalidChar(read());
    }
    read();
  }
}
function escape() {
  const c3 = peek();
  switch (c3) {
    case "b":
      read();
      return "\b";
    case "f":
      read();
      return "\f";
    case "n":
      read();
      return "\n";
    case "r":
      read();
      return "\r";
    case "t":
      read();
      return "	";
    case "v":
      read();
      return "\v";
    case "0":
      read();
      if (util.isDigit(peek())) {
        throw invalidChar(read());
      }
      return "\0";
    case "x":
      read();
      return hexEscape();
    case "u":
      read();
      return unicodeEscape();
    case "\n":
    case "\u2028":
    case "\u2029":
      read();
      return "";
    case "\r":
      read();
      if (peek() === "\n") {
        read();
      }
      return "";
    case "1":
    case "2":
    case "3":
    case "4":
    case "5":
    case "6":
    case "7":
    case "8":
    case "9":
      throw invalidChar(read());
    case void 0:
      throw invalidChar(read());
  }
  return read();
}
function hexEscape() {
  let buffer2 = "";
  let c3 = peek();
  if (!util.isHexDigit(c3)) {
    throw invalidChar(read());
  }
  buffer2 += read();
  c3 = peek();
  if (!util.isHexDigit(c3)) {
    throw invalidChar(read());
  }
  buffer2 += read();
  return String.fromCodePoint(parseInt(buffer2, 16));
}
function unicodeEscape() {
  let buffer2 = "";
  let count = 4;
  while (count-- > 0) {
    const c3 = peek();
    if (!util.isHexDigit(c3)) {
      throw invalidChar(read());
    }
    buffer2 += read();
  }
  return String.fromCodePoint(parseInt(buffer2, 16));
}
var parseStates = {
  start() {
    if (token.type === "eof") {
      throw invalidEOF();
    }
    push();
  },
  beforePropertyName() {
    switch (token.type) {
      case "identifier":
      case "string":
        key = token.value;
        parseState = "afterPropertyName";
        return;
      case "punctuator":
        pop();
        return;
      case "eof":
        throw invalidEOF();
    }
  },
  afterPropertyName() {
    if (token.type === "eof") {
      throw invalidEOF();
    }
    parseState = "beforePropertyValue";
  },
  beforePropertyValue() {
    if (token.type === "eof") {
      throw invalidEOF();
    }
    push();
  },
  beforeArrayValue() {
    if (token.type === "eof") {
      throw invalidEOF();
    }
    if (token.type === "punctuator" && token.value === "]") {
      pop();
      return;
    }
    push();
  },
  afterPropertyValue() {
    if (token.type === "eof") {
      throw invalidEOF();
    }
    switch (token.value) {
      case ",":
        parseState = "beforePropertyName";
        return;
      case "}":
        pop();
    }
  },
  afterArrayValue() {
    if (token.type === "eof") {
      throw invalidEOF();
    }
    switch (token.value) {
      case ",":
        parseState = "beforeArrayValue";
        return;
      case "]":
        pop();
    }
  },
  end() {
  }
};
function push() {
  let value;
  switch (token.type) {
    case "punctuator":
      switch (token.value) {
        case "{":
          value = {};
          break;
        case "[":
          value = [];
          break;
      }
      break;
    case "null":
    case "boolean":
    case "numeric":
    case "string":
      value = token.value;
      break;
  }
  if (root === void 0) {
    root = value;
  } else {
    const parent = stack[stack.length - 1];
    if (Array.isArray(parent)) {
      parent.push(value);
    } else {
      Object.defineProperty(parent, key, {
        value,
        writable: true,
        enumerable: true,
        configurable: true
      });
    }
  }
  if (value !== null && typeof value === "object") {
    stack.push(value);
    if (Array.isArray(value)) {
      parseState = "beforeArrayValue";
    } else {
      parseState = "beforePropertyName";
    }
  } else {
    const current = stack[stack.length - 1];
    if (current == null) {
      parseState = "end";
    } else if (Array.isArray(current)) {
      parseState = "afterArrayValue";
    } else {
      parseState = "afterPropertyValue";
    }
  }
}
function pop() {
  stack.pop();
  const current = stack[stack.length - 1];
  if (current == null) {
    parseState = "end";
  } else if (Array.isArray(current)) {
    parseState = "afterArrayValue";
  } else {
    parseState = "afterPropertyValue";
  }
}
function invalidChar(c3) {
  if (c3 === void 0) {
    return syntaxError(`JSON5: invalid end of input at ${line}:${column}`);
  }
  return syntaxError(`JSON5: invalid character '${formatChar(c3)}' at ${line}:${column}`);
}
function invalidEOF() {
  return syntaxError(`JSON5: invalid end of input at ${line}:${column}`);
}
function invalidIdentifier() {
  column -= 5;
  return syntaxError(`JSON5: invalid identifier character at ${line}:${column}`);
}
function separatorChar(c3) {
  console.warn(`JSON5: '${formatChar(c3)}' in strings is not valid ECMAScript; consider escaping`);
}
function formatChar(c3) {
  const replacements = {
    "'": "\\'",
    '"': '\\"',
    "\\": "\\\\",
    "\b": "\\b",
    "\f": "\\f",
    "\n": "\\n",
    "\r": "\\r",
    "	": "\\t",
    "\v": "\\v",
    "\0": "\\0",
    "\u2028": "\\u2028",
    "\u2029": "\\u2029"
  };
  if (replacements[c3]) {
    return replacements[c3];
  }
  if (c3 < " ") {
    const hexString = c3.charCodeAt(0).toString(16);
    return "\\x" + ("00" + hexString).substring(hexString.length);
  }
  return c3;
}
function syntaxError(message) {
  const err = new SyntaxError(message);
  err.lineNumber = line;
  err.columnNumber = column;
  return err;
}
var stringify = function stringify2(value, replacer, space) {
  const stack2 = [];
  let indent = "";
  let propertyList;
  let replacerFunc;
  let gap = "";
  let quote;
  if (replacer != null && typeof replacer === "object" && !Array.isArray(replacer)) {
    space = replacer.space;
    quote = replacer.quote;
    replacer = replacer.replacer;
  }
  if (typeof replacer === "function") {
    replacerFunc = replacer;
  } else if (Array.isArray(replacer)) {
    propertyList = [];
    for (const v of replacer) {
      let item;
      if (typeof v === "string") {
        item = v;
      } else if (typeof v === "number" || v instanceof String || v instanceof Number) {
        item = String(v);
      }
      if (item !== void 0 && propertyList.indexOf(item) < 0) {
        propertyList.push(item);
      }
    }
  }
  if (space instanceof Number) {
    space = Number(space);
  } else if (space instanceof String) {
    space = String(space);
  }
  if (typeof space === "number") {
    if (space > 0) {
      space = Math.min(10, Math.floor(space));
      gap = "          ".substr(0, space);
    }
  } else if (typeof space === "string") {
    gap = space.substr(0, 10);
  }
  return serializeProperty("", { "": value });
  function serializeProperty(key2, holder) {
    let value2 = holder[key2];
    if (value2 != null) {
      if (typeof value2.toJSON5 === "function") {
        value2 = value2.toJSON5(key2);
      } else if (typeof value2.toJSON === "function") {
        value2 = value2.toJSON(key2);
      }
    }
    if (replacerFunc) {
      value2 = replacerFunc.call(holder, key2, value2);
    }
    if (value2 instanceof Number) {
      value2 = Number(value2);
    } else if (value2 instanceof String) {
      value2 = String(value2);
    } else if (value2 instanceof Boolean) {
      value2 = value2.valueOf();
    }
    switch (value2) {
      case null:
        return "null";
      case true:
        return "true";
      case false:
        return "false";
    }
    if (typeof value2 === "string") {
      return quoteString(value2, false);
    }
    if (typeof value2 === "number") {
      return String(value2);
    }
    if (typeof value2 === "object") {
      return Array.isArray(value2) ? serializeArray(value2) : serializeObject(value2);
    }
    return void 0;
  }
  function quoteString(value2) {
    const quotes = {
      "'": 0.1,
      '"': 0.2
    };
    const replacements = {
      "'": "\\'",
      '"': '\\"',
      "\\": "\\\\",
      "\b": "\\b",
      "\f": "\\f",
      "\n": "\\n",
      "\r": "\\r",
      "	": "\\t",
      "\v": "\\v",
      "\0": "\\0",
      "\u2028": "\\u2028",
      "\u2029": "\\u2029"
    };
    let product = "";
    for (let i = 0; i < value2.length; i++) {
      const c3 = value2[i];
      switch (c3) {
        case "'":
        case '"':
          quotes[c3]++;
          product += c3;
          continue;
        case "\0":
          if (util.isDigit(value2[i + 1])) {
            product += "\\x00";
            continue;
          }
      }
      if (replacements[c3]) {
        product += replacements[c3];
        continue;
      }
      if (c3 < " ") {
        let hexString = c3.charCodeAt(0).toString(16);
        product += "\\x" + ("00" + hexString).substring(hexString.length);
        continue;
      }
      product += c3;
    }
    const quoteChar = quote || Object.keys(quotes).reduce((a, b) => quotes[a] < quotes[b] ? a : b);
    product = product.replace(new RegExp(quoteChar, "g"), replacements[quoteChar]);
    return quoteChar + product + quoteChar;
  }
  function serializeObject(value2) {
    if (stack2.indexOf(value2) >= 0) {
      throw TypeError("Converting circular structure to JSON5");
    }
    stack2.push(value2);
    let stepback = indent;
    indent = indent + gap;
    let keys = propertyList || Object.keys(value2);
    let partial = [];
    for (const key2 of keys) {
      const propertyString = serializeProperty(key2, value2);
      if (propertyString !== void 0) {
        let member = serializeKey(key2) + ":";
        if (gap !== "") {
          member += " ";
        }
        member += propertyString;
        partial.push(member);
      }
    }
    let final;
    if (partial.length === 0) {
      final = "{}";
    } else {
      let properties;
      if (gap === "") {
        properties = partial.join(",");
        final = "{" + properties + "}";
      } else {
        let separator = ",\n" + indent;
        properties = partial.join(separator);
        final = "{\n" + indent + properties + ",\n" + stepback + "}";
      }
    }
    stack2.pop();
    indent = stepback;
    return final;
  }
  function serializeKey(key2) {
    if (key2.length === 0) {
      return quoteString(key2, true);
    }
    const firstChar = String.fromCodePoint(key2.codePointAt(0));
    if (!util.isIdStartChar(firstChar)) {
      return quoteString(key2, true);
    }
    for (let i = firstChar.length; i < key2.length; i++) {
      if (!util.isIdContinueChar(String.fromCodePoint(key2.codePointAt(i)))) {
        return quoteString(key2, true);
      }
    }
    return key2;
  }
  function serializeArray(value2) {
    if (stack2.indexOf(value2) >= 0) {
      throw TypeError("Converting circular structure to JSON5");
    }
    stack2.push(value2);
    let stepback = indent;
    indent = indent + gap;
    let partial = [];
    for (let i = 0; i < value2.length; i++) {
      const propertyString = serializeProperty(String(i), value2);
      partial.push(propertyString !== void 0 ? propertyString : "null");
    }
    let final;
    if (partial.length === 0) {
      final = "[]";
    } else {
      if (gap === "") {
        let properties = partial.join(",");
        final = "[" + properties + "]";
      } else {
        let separator = ",\n" + indent;
        let properties = partial.join(separator);
        final = "[\n" + indent + properties + ",\n" + stepback + "]";
      }
    }
    stack2.pop();
    indent = stepback;
    return final;
  }
};
var JSON5 = {
  parse,
  stringify
};
var lib = JSON5;
var dist_default = lib;

// ../node_modules/@sanity/vision/lib/_chunks-es/SanityVision.mjs
var import_isEqual = __toESM(require_isEqual(), 1);
var import_json_2_csv = __toESM(require_converter(), 1);
var import_quick_lru = __toESM(require_quick_lru(), 1);
var API_VERSIONS = ["v1", "vX", "v2021-03-25", "v2021-10-21", "v2022-03-07", "v2025-02-19", `v${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}`];
var [DEFAULT_API_VERSION] = API_VERSIONS.slice(-1);
function DelayedSpinner(props) {
  const $ = (0, import_react_compiler_runtime.c)(5), [show, setShow] = (0, import_react2.useState)(false);
  let t0, t1;
  $[0] !== props.delay ? (t0 = () => {
    const timer = setTimeout(() => setShow(true), props.delay || 500);
    return () => clearTimeout(timer);
  }, t1 = [props.delay], $[0] = props.delay, $[1] = t0, $[2] = t1) : (t0 = $[1], t1 = $[2]), (0, import_react2.useEffect)(t0, t1);
  let t2;
  return $[3] !== show ? (t2 = show ? (0, import_jsx_runtime2.jsx)(Spinner, { muted: true, size: 4 }) : null, $[3] = show, $[4] = t2) : t2 = $[4], t2;
}
var codemirrorExtensions = [[javascriptLanguage], lineNumbers(), highlightActiveLine(), highlightActiveLineGutter(), highlightSelectionMatches(), highlightSpecialChars(), indentOnInput(), bracketMatching(), closeBrackets(), history(), drawSelection(), syntaxHighlighting(defaultHighlightStyle, {
  fallback: true
}), keymap.of([
  // Override the default keymap for Mod-Enter to not insert a new line, we have a custom event handler for executing queries
  {
    key: "Mod-Enter",
    run: () => true
  },
  // Add the default keymap and history keymap
  defaultKeymap,
  historyKeymap
].flat().filter(Boolean))];
function useCodemirrorTheme(theme) {
  const $ = (0, import_react_compiler_runtime.c)(7);
  let t0, t1;
  $[0] !== theme ? (t1 = createTheme(theme), $[0] = theme, $[1] = t1) : t1 = $[1], t0 = t1;
  const cmTheme = t0;
  let t2, t3;
  $[2] !== theme ? (t3 = syntaxHighlighting(createHighlight(theme)), $[2] = theme, $[3] = t3) : t3 = $[3], t2 = t3;
  const cmHighlight = t2;
  let t4;
  return $[4] !== cmHighlight || $[5] !== cmTheme ? (t4 = [cmTheme, cmHighlight], $[4] = cmHighlight, $[5] = cmTheme, $[6] = t4) : t4 = $[6], t4;
}
function createTheme(theme) {
  const {
    color,
    fonts
  } = theme.sanity, card = color.card.enabled, cursor = hues.blue[color.dark ? 400 : 500].hex, selection = hues.gray[theme.sanity.color.dark ? 900 : 100].hex;
  return EditorView.theme({
    "&": {
      color: card.fg,
      backgroundColor: card.bg
    },
    ".cm-content": {
      caretColor: cursor
    },
    ".cm-editor": {
      fontFamily: fonts.code.family,
      fontSize: rem(fonts.code.sizes[1].fontSize),
      lineHeight: "inherit"
    },
    ".cm-cursor, .cm-dropCursor": {
      borderLeftColor: cursor
    },
    "&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection": {
      backgroundColor: selection
    },
    ".cm-panels": {
      backgroundColor: card.bg,
      color: card.fg
    },
    ".cm-panels.cm-panels-top": {
      borderBottom: `2px solid ${card.border}`
    },
    ".cm-panels.cm-panels-bottom": {
      borderTop: `2px solid ${card.border}`
    }
  }, {
    dark: color.dark
  });
}
function createHighlight(theme) {
  const c22 = theme.sanity.color.base, s = theme.sanity.color.syntax;
  return HighlightStyle.define([{
    tag: tags.keyword,
    color: s.keyword
  }, {
    tag: [tags.propertyName, tags.name, tags.deleted, tags.character, tags.macroName],
    color: s.property
  }, {
    tag: [tags.function(tags.variableName), tags.labelName],
    color: s.function
  }, {
    tag: [tags.color, tags.constant(tags.name), tags.standard(tags.name)],
    color: s.variable
  }, {
    tag: [tags.definition(tags.name), tags.separator],
    color: s.constant
  }, {
    tag: [tags.typeName, tags.className, tags.number, tags.changed, tags.annotation, tags.modifier, tags.self, tags.namespace],
    color: s.number
  }, {
    tag: [tags.operator, tags.operatorKeyword, tags.url, tags.escape, tags.regexp, tags.link, tags.special(tags.string)],
    color: s.operator
  }, {
    tag: [tags.meta, tags.comment],
    color: s.comment
  }, {
    tag: tags.strong,
    fontWeight: "bold"
  }, {
    tag: tags.emphasis,
    fontStyle: "italic"
  }, {
    tag: tags.strikethrough,
    textDecoration: "line-through"
  }, {
    tag: tags.heading,
    fontWeight: "bold",
    color: s.property
  }, {
    tag: [tags.atom, tags.bool, tags.special(tags.variableName)],
    color: s.boolean
  }, {
    tag: [tags.processingInstruction, tags.string, tags.inserted],
    color: s.string
  }, {
    tag: tags.invalid,
    color: c22.fg
  }]);
}
var EditorRoot = dt.div`
  width: 100%;
  box-sizing: border-box;
  height: 100%;
  overflow: hidden;
  overflow: clip;
  position: relative;
  display: flex;

  & .cm-theme {
    width: 100%;
  }

  & .cm-editor {
    height: 100%;

    font-size: 16px;
    line-height: 21px;
  }

  & .cm-line {
    padding-left: ${({
  theme
}) => rem(theme.sanity.space[3])};
  }

  & .cm-content {
    border-right-width: ${({
  theme
}) => rem(theme.sanity.space[4])} !important;
    padding-top: ${({
  theme
}) => rem(theme.sanity.space[5])};
  }
`;
var VisionCodeMirror = (0, import_react2.forwardRef)((props, ref) => {
  const $ = (0, import_react_compiler_runtime.c)(7), [initialValue] = (0, import_react2.useState)(props.initialValue), sanityTheme = useTheme(), theme = useCodemirrorTheme(sanityTheme), codeMirrorRef = (0, import_react2.useRef)(null);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = (newContent) => {
    var _a;
    const editorView = (_a = codeMirrorRef.current) == null ? void 0 : _a.view;
    if (!editorView)
      return;
    const currentDoc = editorView.state.doc.toString();
    newContent !== currentDoc && editorView.dispatch({
      changes: {
        from: 0,
        to: currentDoc.length,
        insert: newContent
      },
      selection: EditorSelection.cursor(newContent.length)
    });
  }, $[0] = t0) : t0 = $[0];
  const resetEditorContent = t0;
  let t1, t2;
  $[1] === Symbol.for("react.memo_cache_sentinel") ? (t1 = () => ({
    resetEditorContent
  }), t2 = [resetEditorContent], $[1] = t1, $[2] = t2) : (t1 = $[1], t2 = $[2]), (0, import_react2.useImperativeHandle)(ref, t1, t2);
  let t3;
  return $[3] !== initialValue || $[4] !== props.onChange || $[5] !== theme ? (t3 = (0, import_jsx_runtime2.jsx)(EditorRoot, { children: (0, import_jsx_runtime2.jsx)(esm_default, { ref: codeMirrorRef, basicSetup: false, theme, extensions: codemirrorExtensions, value: initialValue, onChange: props.onChange }) }), $[3] = initialValue, $[4] = props.onChange, $[5] = theme, $[6] = t3) : t3 = $[6], t3;
});
VisionCodeMirror.displayName = "VisionCodeMirror";
var SUPPORTED_PERSPECTIVES = ["pinnedRelease", "raw", "published", "drafts"];
var VIRTUAL_PERSPECTIVES = ["pinnedRelease"];
function isSupportedPerspective(p) {
  return SUPPORTED_PERSPECTIVES.includes(p);
}
function isVirtualPerspective(maybeVirtualPerspective) {
  return typeof maybeVirtualPerspective == "string" && VIRTUAL_PERSPECTIVES.includes(maybeVirtualPerspective);
}
function hasPinnedPerspective({
  selectedPerspectiveName
}) {
  return typeof selectedPerspectiveName < "u";
}
function getActivePerspective({
  visionPerspective,
  perspectiveStack
}) {
  return visionPerspective !== "pinnedRelease" ? visionPerspective : perspectiveStack;
}
function encodeQueryString(query, params = {}, options = {}) {
  const searchParams = new URLSearchParams();
  searchParams.set("query", query);
  for (const [key2, value] of Object.entries(params))
    searchParams.set(`$${key2}`, JSON.stringify(value));
  for (const [key2, value] of Object.entries(options))
    value && searchParams.set(key2, `${value}`);
  return `?${searchParams}`;
}
function isPlainObject(obj) {
  return !!obj && typeof obj == "object" && Object.prototype.toString.call(obj) === "[object Object]";
}
var hasLocalStorage = supportsLocalStorage();
var keyPrefix = "sanityVision:";
function clearLocalStorage() {
  if (hasLocalStorage)
    for (let i = 0; i < localStorage.length; i++) {
      const key2 = localStorage.key(i);
      (key2 == null ? void 0 : key2.startsWith(keyPrefix)) && localStorage.removeItem(key2);
    }
}
function getLocalStorage(namespace) {
  const storageKey = `${keyPrefix}${namespace}`;
  let loadedState = null;
  return {
    get,
    set,
    merge
  };
  function get(key2, defaultVal) {
    const state = ensureState();
    return typeof state[key2] > "u" ? defaultVal : state[key2];
  }
  function set(key2, value) {
    const state = ensureState();
    return state[key2] = value, localStorage.setItem(storageKey, JSON.stringify(loadedState)), value;
  }
  function merge(props) {
    const state = {
      ...ensureState(),
      ...props
    };
    return localStorage.setItem(storageKey, JSON.stringify(state)), state;
  }
  function ensureState() {
    return loadedState === null && (loadedState = loadState()), loadedState;
  }
  function loadState() {
    if (!hasLocalStorage)
      return {};
    try {
      const stored = JSON.parse(localStorage.getItem(storageKey) || "{}");
      return isPlainObject(stored) ? stored : {};
    } catch {
      return {};
    }
  }
}
function supportsLocalStorage() {
  const mod = "lsCheck";
  try {
    return localStorage.setItem(mod, mod), localStorage.removeItem(mod), true;
  } catch {
    return false;
  }
}
function parseApiQueryString(qs) {
  const params = {}, options = {};
  for (const [key2, value] of qs.entries()) {
    if (key2[0] === "$") {
      params[key2.slice(1)] = JSON.parse(value);
      continue;
    }
    if (key2 === "perspective") {
      options[key2] = value;
      continue;
    }
  }
  return {
    query: qs.get("query") || "",
    params,
    options
  };
}
function prefixApiVersion(version) {
  return version[0] !== "v" && version !== "other" ? `v${version}` : version;
}
function validateApiVersion(apiVersion) {
  const parseableApiVersion = apiVersion.replace(/^v/, "").trim().toUpperCase();
  return parseableApiVersion.length > 0 && (parseableApiVersion === "X" || parseableApiVersion === "1" || /^\d{4}-\d{2}-\d{2}$/.test(parseableApiVersion) && !isNaN(Date.parse(parseableApiVersion)));
}
function tryParseParams(val, t) {
  try {
    const parsed = val ? dist_default.parse(val) : {};
    return typeof parsed == "object" && parsed && !Array.isArray(parsed) ? parsed : {};
  } catch (err) {
    return err.message = `${t("params.error.params-invalid-json")}:

${err.message.replace("JSON5:", "")}`, err;
  }
}
var Root = dt(Flex)`
  .sidebarPanes .Pane {
    overflow-y: auto;
    overflow-x: hidden;
  }

  & .Resizer {
    background: var(--card-border-color);
    opacity: 1;
    z-index: 1;
    box-sizing: border-box;
    background-clip: padding-box;
    border: solid transparent;
  }

  & .Resizer:hover {
    border-color: var(--card-shadow-ambient-color);
  }

  & .Resizer.horizontal {
    height: 11px;
    margin: -5px 0;
    border-width: 5px 0;
    cursor: row-resize;
    width: 100%;
    z-index: 4;
  }

  & .Resizer.vertical {
    width: 11px;
    margin: 0 -5px;
    border-width: 0 5px;
    cursor: col-resize;
    z-index: 2; /* To prevent the resizer from being hidden behind CodeMirror scroll area */
  }

  .Resizer.disabled {
    cursor: not-allowed;
  }

  .Resizer.disabled:hover {
    border-color: transparent;
  }
`;
Root.displayName = "Root";
var Header = dt(Card)`
  border-bottom: 1px solid var(--card-border-color);
`;
var StyledLabel = dt(Label)`
  flex: 1;
`;
var SplitpaneContainer = dt(Box)`
  position: relative;
`;
var QueryCopyLink = dt.a`
  cursor: pointer;
  margin-right: auto;
`;
var InputBackgroundContainer = dt(Box)`
  position: absolute;
  top: 1rem;
  left: 0;
  padding: 0;
  margin: 0;
  z-index: 10;
  right: 0;

  ${StyledLabel} {
    user-select: none;
  }
`;
var InputBackgroundContainerLeft = dt(InputBackgroundContainer)`
  // This is so its aligned with the gutters of CodeMirror
  left: 33px;
`;
var InputContainer = dt(Card)`
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
`;
var ResultOuterContainer = dt(Flex)`
  height: 100%;
`;
var ResultInnerContainer = dt(Box)`
  position: relative;
`;
var ResultContainer = dt(Card)`
  height: 100%;
  width: 100%;
  position: absolute;
  max-width: 100%;

  ${({
  $isInvalid
}) => $isInvalid && lt`
      &:after {
        background-color: var(--card-bg-color);
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 100%;
      }
    `}
`;
var Result = dt(Box)`
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 20;
`;
var ResultFooter = dt(Flex)`
  border-top: 1px solid var(--card-border-color);
`;
var TimingsCard = dt(Card)`
  position: relative;
`;
dt(Box)`
  width: 100%;
  height: 100%;
`;
var TimingsTextContainer = dt(Flex)`
  height: 100%;
  min-height: ${({
  theme
}) => rem(theme.sanity.space[3] * 2 + theme.sanity.fonts.text.sizes[2].lineHeight - theme.sanity.fonts.text.sizes[2].ascenderHeight - theme.sanity.fonts.text.sizes[2].descenderHeight)};
`;
var DownloadsCard = dt(Card)`
  position: relative;
`;
var SaveResultLabel = dt(Text)`
  transform: initial;
  &:before,
  &:after {
    content: none;
  }
  > span {
    display: flex !important;
    gap: ${({
  theme
}) => rem(theme.sanity.space[3])};
    align-items: center;
  }
`;
var ControlsContainer = dt(Box)`
  border-top: 1px solid var(--card-border-color);
`;
var defaultValue$1 = `{
  
}`;
function ParamsEditor(props) {
  const $ = (0, import_react_compiler_runtime.c)(33), {
    onChange,
    paramsError,
    hasValidParams,
    editorRef
  } = props, {
    t
  } = useTranslation(visionLocaleNamespace);
  let t0;
  $[0] !== props.value || $[1] !== t ? (t0 = parseParams(props.value, t), $[0] = props.value, $[1] = t, $[2] = t0) : t0 = $[2];
  const {
    raw: value,
    error,
    parsed,
    valid
  } = t0, [isValid, setValid] = (0, import_react2.useState)(valid), [init, setInit] = (0, import_react2.useState)(false);
  let t1, t2;
  $[3] !== error || $[4] !== init || $[5] !== isValid || $[6] !== onChange || $[7] !== parsed || $[8] !== value ? (t1 = () => {
    init || (onChange({
      parsed,
      raw: value,
      valid: isValid,
      error
    }), setInit(true));
  }, t2 = [error, init, isValid, onChange, parsed, value], $[3] = error, $[4] = init, $[5] = isValid, $[6] = onChange, $[7] = parsed, $[8] = value, $[9] = t1, $[10] = t2) : (t1 = $[9], t2 = $[10]), (0, import_react2.useEffect)(t1, t2);
  let t3;
  $[11] !== onChange || $[12] !== t ? (t3 = (newValue) => {
    const event = parseParams(newValue, t);
    setValid(event.valid), onChange(event);
  }, $[11] = onChange, $[12] = t, $[13] = t3) : t3 = $[13];
  const handleChangeRaw = t3;
  let t4, t5;
  $[14] !== handleChangeRaw ? (t5 = (0, import_debounce.default)(handleChangeRaw, 333), $[14] = handleChangeRaw, $[15] = t5) : t5 = $[15], t4 = t5;
  const handleChange = t4, t6 = hasValidParams ? "default" : "critical";
  let t7;
  $[16] !== t ? (t7 = t("params.label"), $[16] = t, $[17] = t7) : t7 = $[17];
  let t8;
  $[18] !== t7 ? (t8 = (0, import_jsx_runtime2.jsx)(StyledLabel, { muted: true, children: t7 }), $[18] = t7, $[19] = t8) : t8 = $[19];
  let t9;
  $[20] !== paramsError ? (t9 = paramsError && (0, import_jsx_runtime2.jsx)(Tooltip, { animate: true, placement: "top", portal: true, content: (0, import_jsx_runtime2.jsx)(Text, { size: 1, children: paramsError }), children: (0, import_jsx_runtime2.jsx)(Box, { padding: 1, marginX: 2, children: (0, import_jsx_runtime2.jsx)(Text, { children: (0, import_jsx_runtime2.jsx)(ErrorOutlineIcon, {}) }) }) }), $[20] = paramsError, $[21] = t9) : t9 = $[21];
  let t10;
  $[22] !== t8 || $[23] !== t9 ? (t10 = (0, import_jsx_runtime2.jsx)(InputBackgroundContainerLeft, { children: (0, import_jsx_runtime2.jsxs)(Flex, { children: [
    t8,
    t9
  ] }) }), $[22] = t8, $[23] = t9, $[24] = t10) : t10 = $[24];
  const t11 = props.value || defaultValue$1;
  let t12;
  $[25] !== editorRef || $[26] !== handleChange || $[27] !== t11 ? (t12 = (0, import_jsx_runtime2.jsx)(VisionCodeMirror, { ref: editorRef, initialValue: t11, onChange: handleChange }), $[25] = editorRef, $[26] = handleChange, $[27] = t11, $[28] = t12) : t12 = $[28];
  let t13;
  return $[29] !== t10 || $[30] !== t12 || $[31] !== t6 ? (t13 = (0, import_jsx_runtime2.jsxs)(Card, { flex: 1, tone: t6, "data-testid": "params-editor", children: [
    t10,
    t12
  ] }), $[29] = t10, $[30] = t12, $[31] = t6, $[32] = t13) : t13 = $[32], t13;
}
function parseParams(value, t) {
  const parsedParams = tryParseParams(value, t), params = parsedParams instanceof Error ? {} : parsedParams, validationError = parsedParams instanceof Error ? parsedParams.message : void 0;
  return {
    parsed: params,
    raw: value,
    valid: !validationError,
    error: validationError
  };
}
var STORED_QUERIES_NAMESPACE = "studio.vision-tool.saved-queries";
var defaultValue = {
  queries: []
};
var keyValueStoreKey = STORED_QUERIES_NAMESPACE;
function useSavedQueries() {
  const keyValueStore = useKeyValueStore(), [value, setValue] = (0, import_react2.useState)(defaultValue), [saving, setSaving] = (0, import_react2.useState)(false), [deleting, setDeleting] = (0, import_react2.useState)([]), [saveQueryError, setSaveQueryError] = (0, import_react2.useState)(), [deleteQueryError, setDeleteQueryError] = (0, import_react2.useState)(), [error, setError] = (0, import_react2.useState)(), queries = (0, import_react2.useMemo)(() => keyValueStore.getKey(keyValueStoreKey), [keyValueStore]);
  (0, import_react2.useEffect)(() => {
    const sub = queries.pipe(startWith(defaultValue), map((data) => data || defaultValue)).subscribe({
      next: setValue,
      error: (err) => setError(err)
    });
    return () => sub == null ? void 0 : sub.unsubscribe();
  }, [queries, keyValueStore]);
  const saveQuery = (0, import_react2.useCallback)((query) => {
    setSaving(true), setSaveQueryError(void 0);
    try {
      const newQueries = [{
        ...query,
        _key: v4_default()
      }, ...value.queries];
      setValue({
        queries: newQueries
      }), keyValueStore.setKey(keyValueStoreKey, {
        queries: newQueries
      });
    } catch (err_0) {
      setSaveQueryError(err_0);
    } finally {
      setSaving(false);
    }
  }, [keyValueStore, value.queries]), updateQuery = (0, import_react2.useCallback)((query_0) => {
    setSaving(true), setSaveQueryError(void 0);
    try {
      const updatedQueries = value.queries.map((q) => q._key === query_0._key ? {
        ...q,
        ...query_0
      } : q);
      setValue({
        queries: updatedQueries
      }), keyValueStore.setKey(keyValueStoreKey, {
        queries: updatedQueries
      });
    } catch (err_1) {
      setSaveQueryError(err_1);
    } finally {
      setSaving(false);
    }
  }, [keyValueStore, value.queries]), deleteQuery = (0, import_react2.useCallback)((key2) => {
    setDeleting((prev) => [...prev, key2]), setDeleteQueryError(void 0);
    try {
      const filteredQueries = value.queries.filter((q_0) => q_0._key !== key2);
      setValue({
        queries: filteredQueries
      }), keyValueStore.setKey(keyValueStoreKey, {
        queries: filteredQueries
      });
    } catch (err_2) {
      setDeleteQueryError(err_2);
    } finally {
      setDeleting((prev) => prev.filter((k) => k !== key2));
    }
  }, [keyValueStore, value.queries]);
  return {
    queries: value.queries,
    saveQuery,
    updateQuery,
    deleteQuery,
    saving,
    deleting,
    saveQueryError,
    deleteQueryError,
    error
  };
}
var FixedHeader = dt(Stack)`
  position: sticky;
  top: 0;
  background: ${({
  theme
}) => theme.sanity.color.base.bg};
  z-index: 1;
`;
var ScrollContainer = dt(Box)`
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({
  theme
}) => theme.sanity.color.base.border};
    border-radius: 4px;
  }
`;
function QueryRecall(t0) {
  const $ = (0, import_react_compiler_runtime.c)(80), {
    url,
    getStateFromUrl,
    setStateFromParsedUrl,
    currentQuery,
    currentParams,
    generateUrl
  } = t0, toast = useToast(), {
    saveQuery,
    updateQuery,
    queries,
    deleteQuery,
    saving,
    saveQueryError
  } = useSavedQueries(), {
    t
  } = useTranslation(visionLocaleNamespace);
  let t1;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t1 = {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true
  }, $[0] = t1) : t1 = $[0];
  const formatDate = useDateTimeFormat(t1), [editingKey, setEditingKey] = (0, import_react2.useState)(null), [editingTitle, setEditingTitle] = (0, import_react2.useState)("");
  let t2;
  $[1] === Symbol.for("react.memo_cache_sentinel") ? (t2 = {}, $[1] = t2) : t2 = $[1];
  const [optimisticTitles, setOptimisticTitles] = (0, import_react2.useState)(t2), [searchQuery, setSearchQuery] = (0, import_react2.useState)(""), [selectedUrl, setSelectedUrl] = (0, import_react2.useState)(url);
  let t3;
  $[2] !== currentParams || $[3] !== currentQuery || $[4] !== formatDate || $[5] !== generateUrl || $[6] !== getStateFromUrl || $[7] !== queries || $[8] !== saveQuery || $[9] !== saveQueryError || $[10] !== t || $[11] !== toast ? (t3 = async () => {
    const newUrl = generateUrl(currentQuery, currentParams);
    if (queries == null ? void 0 : queries.some((q) => {
      const savedQueryObj = getStateFromUrl(q.url);
      return savedQueryObj && savedQueryObj.query === currentQuery && (0, import_isEqual.default)(savedQueryObj.params, currentParams);
    })) {
      const duplicateQuery = queries == null ? void 0 : queries.find((q_0) => {
        const savedQueryObj_0 = getStateFromUrl(q_0.url);
        return savedQueryObj_0 && savedQueryObj_0.query === currentQuery && (0, import_isEqual.default)(savedQueryObj_0.params, currentParams);
      });
      toast.push({
        closable: true,
        status: "warning",
        title: t("save-query.already-saved"),
        description: `${duplicateQuery == null ? void 0 : duplicateQuery.title} - ${formatDate.format(new Date((duplicateQuery == null ? void 0 : duplicateQuery.savedAt) || ""))}`
      });
      return;
    }
    newUrl && (await saveQuery({
      url: newUrl,
      savedAt: (/* @__PURE__ */ new Date()).toISOString(),
      title: "Untitled"
    }), setSelectedUrl(newUrl)), saveQueryError ? toast.push({
      closable: true,
      status: "error",
      title: t("save-query.error"),
      description: saveQueryError.message
    }) : toast.push({
      closable: true,
      status: "success",
      title: t("save-query.success")
    });
  }, $[2] = currentParams, $[3] = currentQuery, $[4] = formatDate, $[5] = generateUrl, $[6] = getStateFromUrl, $[7] = queries, $[8] = saveQuery, $[9] = saveQueryError, $[10] = t, $[11] = toast, $[12] = t3) : t3 = $[12];
  const handleSave = t3;
  let t4;
  $[13] !== t || $[14] !== toast || $[15] !== updateQuery ? (t4 = async (query, newTitle) => {
    setEditingKey(null), setOptimisticTitles((prev) => ({
      ...prev,
      [query._key]: newTitle
    }));
    try {
      await updateQuery({
        ...query,
        title: newTitle
      }), setOptimisticTitles((prev_1) => {
        const next_0 = {
          ...prev_1
        };
        return delete next_0[query._key], next_0;
      });
    } catch (t52) {
      const err = t52;
      setOptimisticTitles((prev_0) => {
        const next = {
          ...prev_0
        };
        return delete next[query._key], next;
      }), toast.push({
        closable: true,
        status: "error",
        title: t("save-query.error"),
        description: err.message
      });
    }
  }, $[13] = t, $[14] = toast, $[15] = updateQuery, $[16] = t4) : t4 = $[16];
  const handleTitleSave = t4;
  let t5;
  $[17] !== currentParams || $[18] !== currentQuery || $[19] !== formatDate || $[20] !== generateUrl || $[21] !== getStateFromUrl || $[22] !== queries || $[23] !== t || $[24] !== toast || $[25] !== updateQuery ? (t5 = async (query_0) => {
    const newUrl_0 = generateUrl(currentQuery, currentParams);
    if (queries == null ? void 0 : queries.some((q_1) => {
      if (q_1._key === query_0._key)
        return false;
      const savedQueryObj_1 = getStateFromUrl(q_1.url);
      return savedQueryObj_1 && savedQueryObj_1.query === currentQuery && (0, import_isEqual.default)(savedQueryObj_1.params, currentParams);
    })) {
      const duplicateQuery_0 = queries == null ? void 0 : queries.find((q_2) => {
        if (q_2._key === query_0._key)
          return false;
        const savedQueryObj_2 = getStateFromUrl(q_2.url);
        return savedQueryObj_2 && savedQueryObj_2.query === currentQuery && (0, import_isEqual.default)(savedQueryObj_2.params, currentParams);
      });
      toast.push({
        closable: true,
        status: "warning",
        title: t("save-query.already-saved"),
        description: `${duplicateQuery_0 == null ? void 0 : duplicateQuery_0.title} - ${formatDate.format(new Date((duplicateQuery_0 == null ? void 0 : duplicateQuery_0.savedAt) || ""))}`
      });
      return;
    }
    try {
      await updateQuery({
        ...query_0,
        url: newUrl_0,
        savedAt: (/* @__PURE__ */ new Date()).toISOString()
      }), setSelectedUrl(newUrl_0), toast.push({
        closable: true,
        status: "success",
        title: t("save-query.success")
      });
    } catch (t62) {
      const err_0 = t62;
      toast.push({
        closable: true,
        status: "error",
        title: t("save-query.error"),
        description: err_0.message
      });
    }
  }, $[17] = currentParams, $[18] = currentQuery, $[19] = formatDate, $[20] = generateUrl, $[21] = getStateFromUrl, $[22] = queries, $[23] = t, $[24] = toast, $[25] = updateQuery, $[26] = t5) : t5 = $[26];
  const handleUpdate = t5;
  let T0, T1, t6, t7, t8;
  if ($[27] !== currentParams || $[28] !== currentQuery || $[29] !== deleteQuery || $[30] !== editingKey || $[31] !== editingTitle || $[32] !== formatDate || $[33] !== getStateFromUrl || $[34] !== handleSave || $[35] !== handleTitleSave || $[36] !== handleUpdate || $[37] !== optimisticTitles || $[38] !== queries || $[39] !== saving || $[40] !== searchQuery || $[41] !== selectedUrl || $[42] !== setStateFromParsedUrl || $[43] !== t) {
    const filteredQueries = queries == null ? void 0 : queries.filter((q_3) => {
      var _a;
      return (_a = q_3 == null ? void 0 : q_3.title) == null ? void 0 : _a.toLowerCase().includes(searchQuery.toLowerCase());
    });
    T1 = ScrollContainer;
    let t92;
    $[49] === Symbol.for("react.memo_cache_sentinel") ? (t92 = {
      textTransform: "capitalize"
    }, $[49] = t92) : t92 = $[49];
    let t102;
    $[50] !== t ? (t102 = t("label.saved-queries"), $[50] = t, $[51] = t102) : t102 = $[51];
    let t11;
    $[52] !== t102 ? (t11 = (0, import_jsx_runtime2.jsx)(Text, { weight: "semibold", style: t92, size: 4, children: t102 }), $[52] = t102, $[53] = t11) : t11 = $[53];
    let t12;
    $[54] !== t ? (t12 = t("action.save-query"), $[54] = t, $[55] = t12) : t12 = $[55];
    let t13;
    $[56] !== handleSave || $[57] !== saving || $[58] !== t12 ? (t13 = (0, import_jsx_runtime2.jsx)(Button, { label: t12, icon: AddIcon, disabled: saving, onClick: handleSave, mode: "bleed" }), $[56] = handleSave, $[57] = saving, $[58] = t12, $[59] = t13) : t13 = $[59];
    let t14;
    $[60] !== t11 || $[61] !== t13 ? (t14 = (0, import_jsx_runtime2.jsxs)(Flex, { padding: 3, paddingTop: 4, paddingBottom: 0, justify: "space-between", align: "center", children: [
      t11,
      t13
    ] }), $[60] = t11, $[61] = t13, $[62] = t14) : t14 = $[62];
    let t15;
    $[63] !== t ? (t15 = t("label.search-queries"), $[63] = t, $[64] = t15) : t15 = $[64];
    let t16;
    $[65] === Symbol.for("react.memo_cache_sentinel") ? (t16 = (event) => setSearchQuery(event.currentTarget.value), $[65] = t16) : t16 = $[65];
    let t17;
    $[66] !== searchQuery || $[67] !== t15 ? (t17 = (0, import_jsx_runtime2.jsx)(Box, { padding: 3, paddingTop: 0, children: (0, import_jsx_runtime2.jsx)(TextInput, { placeholder: t15, icon: SearchIcon, value: searchQuery, onChange: t16 }) }), $[66] = searchQuery, $[67] = t15, $[68] = t17) : t17 = $[68], $[69] !== t14 || $[70] !== t17 ? (t8 = (0, import_jsx_runtime2.jsxs)(FixedHeader, { space: 3, children: [
      t14,
      t17
    ] }), $[69] = t14, $[70] = t17, $[71] = t8) : t8 = $[71], T0 = Stack, t6 = 3, t7 = filteredQueries == null ? void 0 : filteredQueries.map((q_4) => {
      const queryObj = getStateFromUrl(q_4.url), isSelected = selectedUrl === q_4.url, areQueriesEqual = queryObj && currentQuery === queryObj.query && (0, import_isEqual.default)(currentParams, queryObj.params), isEdited = isSelected && !areQueriesEqual;
      return (0, import_jsx_runtime2.jsx)(Card, { width: "fill", padding: 4, border: true, tone: isSelected ? "positive" : "default", onClick: () => {
        setSelectedUrl(q_4.url);
        const parsedUrl = getStateFromUrl(q_4.url);
        parsedUrl && setStateFromParsedUrl(parsedUrl);
      }, style: {
        position: "relative"
      }, children: (0, import_jsx_runtime2.jsxs)(Stack, { space: 3, children: [
        (0, import_jsx_runtime2.jsxs)(Flex, { justify: "space-between", align: "center", children: [
          (0, import_jsx_runtime2.jsxs)(Flex, { align: "center", gap: 2, paddingRight: 1, children: [
            editingKey === q_4._key ? (0, import_jsx_runtime2.jsx)(TextInput, { value: editingTitle, onChange: (event_0) => setEditingTitle(event_0.currentTarget.value), onKeyDown: (event_1) => {
              event_1.key === "Enter" ? handleTitleSave(q_4, editingTitle) : event_1.key === "Escape" && setEditingKey(null);
            }, onBlur: () => handleTitleSave(q_4, editingTitle), autoFocus: true, style: {
              maxWidth: "170px",
              height: "24px"
            } }) : (0, import_jsx_runtime2.jsx)(Text, { weight: "bold", size: 3, textOverflow: "ellipsis", style: {
              maxWidth: "170px",
              cursor: "pointer",
              padding: "4px 0"
            }, title: optimisticTitles[q_4._key] || q_4.title || q_4._key.slice(q_4._key.length - 5, q_4._key.length), onClick: () => {
              setEditingKey(q_4._key), setEditingTitle(q_4.title || q_4._key.slice(0, 5));
            }, children: optimisticTitles[q_4._key] || q_4.title || q_4._key.slice(q_4._key.length - 5, q_4._key.length) }),
            isEdited && (0, import_jsx_runtime2.jsx)(Box, { style: {
              width: "6px",
              height: "6px",
              borderRadius: "50%",
              backgroundColor: "var(--card-focus-ring-color)"
            } })
          ] }),
          (0, import_jsx_runtime2.jsx)(MenuButton, { button: (0, import_jsx_runtime2.jsx)(ContextMenuButton, {}), id: `${q_4._key}-menu`, menu: (0, import_jsx_runtime2.jsx)(Menu, { children: (0, import_jsx_runtime2.jsx)(MenuItem, { tone: "critical", padding: 3, icon: TrashIcon, text: t("action.delete"), onClick: (event_2) => {
            event_2.stopPropagation(), deleteQuery(q_4._key);
          } }) }), popover: {
            portal: true,
            placement: "bottom-end",
            tone: "default"
          } })
        ] }),
        (0, import_jsx_runtime2.jsx)(Code, { muted: true, children: queryObj == null ? void 0 : queryObj.query.split("{")[0] }),
        (0, import_jsx_runtime2.jsx)(Flex, { align: "center", gap: 1, children: (0, import_jsx_runtime2.jsx)(Text, { size: 1, muted: true, children: formatDate.format(new Date(q_4.savedAt || "")) }) }),
        isEdited && (0, import_jsx_runtime2.jsx)(Button, { mode: "ghost", tone: "default", size: 1, padding: 2, style: {
          height: "24px",
          position: "absolute",
          right: "16px",
          bottom: "16px",
          fontSize: "12px"
        }, text: t("action.update"), onClick: (e) => {
          e.stopPropagation(), handleUpdate(q_4);
        } })
      ] }) }, q_4._key);
    }), $[27] = currentParams, $[28] = currentQuery, $[29] = deleteQuery, $[30] = editingKey, $[31] = editingTitle, $[32] = formatDate, $[33] = getStateFromUrl, $[34] = handleSave, $[35] = handleTitleSave, $[36] = handleUpdate, $[37] = optimisticTitles, $[38] = queries, $[39] = saving, $[40] = searchQuery, $[41] = selectedUrl, $[42] = setStateFromParsedUrl, $[43] = t, $[44] = T0, $[45] = T1, $[46] = t6, $[47] = t7, $[48] = t8;
  } else
    T0 = $[44], T1 = $[45], t6 = $[46], t7 = $[47], t8 = $[48];
  let t9;
  $[72] !== T0 || $[73] !== t6 || $[74] !== t7 ? (t9 = (0, import_jsx_runtime2.jsx)(T0, { paddingY: t6, children: t7 }), $[72] = T0, $[73] = t6, $[74] = t7, $[75] = t9) : t9 = $[75];
  let t10;
  return $[76] !== T1 || $[77] !== t8 || $[78] !== t9 ? (t10 = (0, import_jsx_runtime2.jsxs)(T1, { children: [
    t8,
    t9
  ] }), $[76] = T1, $[77] = t8, $[78] = t9, $[79] = t10) : t10 = $[79], t10;
}
function narrowBreakpoint() {
  return typeof window < "u" && window.innerWidth > 600;
}
function calculatePaneSizeOptions(height) {
  let rootHeight = height;
  return rootHeight || (rootHeight = typeof window < "u" && typeof document < "u" ? document.body.getBoundingClientRect().height - 60 : 0), {
    defaultSize: rootHeight / (narrowBreakpoint() ? 2 : 1),
    size: rootHeight > 550 ? void 0 : rootHeight * 0.4,
    allowResize: rootHeight > 550,
    minSize: Math.min(170, Math.max(170, rootHeight / 2)),
    maxSize: rootHeight > 650 ? rootHeight * 0.7 : rootHeight * 0.6
  };
}
function usePaneSize(t0) {
  const $ = (0, import_react_compiler_runtime.c)(7), {
    visionRootRef
  } = t0, [isNarrowBreakpoint, setIsNarrowBreakpoint] = (0, import_react2.useState)(_temp$3), [paneSizeOptions, setPaneSizeOptions] = (0, import_react2.useState)(_temp2$1);
  let t1;
  $[0] !== visionRootRef.current ? (t1 = () => {
    if (!visionRootRef.current)
      return;
    const handleResize = (entries) => {
      setIsNarrowBreakpoint(narrowBreakpoint());
      const entry = entries == null ? void 0 : entries[0];
      entry && setPaneSizeOptions(calculatePaneSizeOptions(entry.contentRect.height));
    }, resizeObserver = new ResizeObserver(handleResize);
    return resizeObserver.observe(visionRootRef.current), () => {
      resizeObserver.disconnect();
    };
  }, $[0] = visionRootRef.current, $[1] = t1) : t1 = $[1];
  let t2;
  $[2] !== visionRootRef ? (t2 = [visionRootRef], $[2] = visionRootRef, $[3] = t2) : t2 = $[3], (0, import_react2.useEffect)(t1, t2);
  let t3;
  return $[4] !== isNarrowBreakpoint || $[5] !== paneSizeOptions ? (t3 = {
    paneSizeOptions,
    isNarrowBreakpoint
  }, $[4] = isNarrowBreakpoint, $[5] = paneSizeOptions, $[6] = t3) : t3 = $[6], t3;
}
function _temp2$1() {
  return calculatePaneSizeOptions(void 0);
}
function _temp$3() {
  return narrowBreakpoint();
}
function VisionGuiControls(t0) {
  const $ = (0, import_react_compiler_runtime.c)(30), {
    hasValidParams,
    listenInProgress,
    queryInProgress,
    onQueryExecution,
    onListenExecution
  } = t0, {
    t
  } = useTranslation(visionLocaleNamespace);
  let t1;
  $[0] !== t ? (t1 = t("params.error.params-invalid-json"), $[0] = t, $[1] = t1) : t1 = $[1];
  let t2;
  $[2] !== t1 ? (t2 = (0, import_jsx_runtime2.jsx)(Card, { radius: 4, children: (0, import_jsx_runtime2.jsx)(Text, { size: 1, muted: true, children: t1 }) }), $[2] = t1, $[3] = t2) : t2 = $[3];
  let t3;
  $[4] === Symbol.for("react.memo_cache_sentinel") ? (t3 = (0, import_jsx_runtime2.jsx)(Card, { radius: 4, children: (0, import_jsx_runtime2.jsx)(Hotkeys, { keys: ["Ctrl", "Enter"] }) }), $[4] = t3) : t3 = $[4];
  const t4 = queryInProgress ? StopIcon : PlayIcon, t5 = listenInProgress || !hasValidParams, t6 = queryInProgress ? "positive" : "primary";
  let t7;
  $[5] !== queryInProgress || $[6] !== t ? (t7 = t(queryInProgress ? "action.query-cancel" : "action.query-execute"), $[5] = queryInProgress, $[6] = t, $[7] = t7) : t7 = $[7];
  let t8;
  $[8] !== onQueryExecution || $[9] !== t4 || $[10] !== t5 || $[11] !== t6 || $[12] !== t7 ? (t8 = (0, import_jsx_runtime2.jsx)(Box, { flex: 1, children: (0, import_jsx_runtime2.jsx)(Tooltip, { content: t3, placement: "top", portal: true, children: (0, import_jsx_runtime2.jsx)(Button, { width: "fill", onClick: onQueryExecution, type: "button", icon: t4, disabled: t5, tone: t6, text: t7 }) }) }), $[8] = onQueryExecution, $[9] = t4, $[10] = t5, $[11] = t6, $[12] = t7, $[13] = t8) : t8 = $[13];
  const t9 = listenInProgress ? StopIcon : PlayIcon;
  let t10;
  $[14] !== listenInProgress || $[15] !== t ? (t10 = t(listenInProgress ? "action.listen-cancel" : "action.listen-execute"), $[14] = listenInProgress, $[15] = t, $[16] = t10) : t10 = $[16];
  const t11 = !hasValidParams, t12 = listenInProgress ? "positive" : "default";
  let t13;
  $[17] !== onListenExecution || $[18] !== t10 || $[19] !== t11 || $[20] !== t12 || $[21] !== t9 ? (t13 = (0, import_jsx_runtime2.jsx)(Box, { flex: 1, marginLeft: 3, children: (0, import_jsx_runtime2.jsx)(Button, { width: "fill", onClick: onListenExecution, type: "button", icon: t9, text: t10, mode: "ghost", disabled: t11, tone: t12 }) }), $[17] = onListenExecution, $[18] = t10, $[19] = t11, $[20] = t12, $[21] = t9, $[22] = t13) : t13 = $[22];
  let t14;
  $[23] !== t13 || $[24] !== t8 ? (t14 = (0, import_jsx_runtime2.jsxs)(Flex, { justify: "space-evenly", children: [
    t8,
    t13
  ] }), $[23] = t13, $[24] = t8, $[25] = t14) : t14 = $[25];
  let t15;
  return $[26] !== hasValidParams || $[27] !== t14 || $[28] !== t2 ? (t15 = (0, import_jsx_runtime2.jsx)(ControlsContainer, { children: (0, import_jsx_runtime2.jsx)(Card, { padding: 3, paddingX: 3, children: (0, import_jsx_runtime2.jsx)(Tooltip, { content: t2, placement: "top", disabled: hasValidParams, portal: true, children: t14 }) }) }), $[26] = hasValidParams, $[27] = t14, $[28] = t2, $[29] = t15) : t15 = $[29], t15;
}
var PerspectivePopoverContent = dt(Box)`
  /* This limits the width of the popover content */
  max-width: 240px;
`;
var PerspectivePopoverLink = dt.a`
  cursor: pointer;
  margin-right: auto;
`;
var Dot = dt.div`
  width: 4px;
  height: 4px;
  border-radius: 3px;
  box-shadow: 0 0 0 1px var(--card-bg-color);
  background-color: ${({
  $tone
}) => `var(--card-badge-${$tone}-dot-color)`};
`;
function PerspectivePopover() {
  const $ = (0, import_react_compiler_runtime.c)(39), [open, setOpen] = (0, import_react2.useState)(false), buttonRef = (0, import_react2.useRef)(null), popoverRef = (0, import_react2.useRef)(null);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = () => setOpen(_temp$2), $[0] = t0) : t0 = $[0];
  const handleClick = t0, {
    t
  } = useTranslation(visionLocaleNamespace);
  let t1, t2;
  $[1] === Symbol.for("react.memo_cache_sentinel") ? (t1 = () => setOpen(false), t2 = () => [buttonRef.current, popoverRef.current], $[1] = t1, $[2] = t2) : (t1 = $[1], t2 = $[2]), useClickOutsideEvent(t1, t2);
  let t3;
  $[3] !== t ? (t3 = t("settings.perspectives.title"), $[3] = t, $[4] = t3) : t3 = $[4];
  let t4;
  $[5] !== t3 ? (t4 = (0, import_jsx_runtime2.jsx)(Inline, { space: 2, children: (0, import_jsx_runtime2.jsx)(Text, { weight: "medium", children: t3 }) }), $[5] = t3, $[6] = t4) : t4 = $[6];
  let t5;
  $[7] !== t ? (t5 = t("settings.perspectives.description"), $[7] = t, $[8] = t5) : t5 = $[8];
  let t6;
  $[9] !== t5 ? (t6 = (0, import_jsx_runtime2.jsx)(Card, { children: (0, import_jsx_runtime2.jsx)(Text, { muted: true, children: t5 }) }), $[9] = t5, $[10] = t6) : t6 = $[10];
  let t7;
  $[11] !== t ? (t7 = t("label.new"), $[11] = t, $[12] = t7) : t7 = $[12];
  let t8;
  $[13] !== t7 ? (t8 = (0, import_jsx_runtime2.jsx)(Box, { children: (0, import_jsx_runtime2.jsx)(Badge, { tone: "primary", children: t7 }) }), $[13] = t7, $[14] = t8) : t8 = $[14];
  let t9;
  $[15] !== t ? (t9 = (0, import_jsx_runtime2.jsx)(Text, { muted: true, children: (0, import_jsx_runtime2.jsx)(Translate, { t, i18nKey: "settings.perspective.preview-drafts-renamed-to-drafts.description" }) }), $[15] = t, $[16] = t9) : t9 = $[16];
  let t10;
  $[17] !== t8 || $[18] !== t9 ? (t10 = (0, import_jsx_runtime2.jsx)(Card, { children: (0, import_jsx_runtime2.jsxs)(Stack, { space: 2, children: [
    t8,
    t9
  ] }) }), $[17] = t8, $[18] = t9, $[19] = t10) : t10 = $[19];
  let t11;
  $[20] !== t ? (t11 = null, $[20] = t, $[21] = t11) : t11 = $[21];
  let t12;
  $[22] !== t ? (t12 = t("settings.perspectives.action.docs-link"), $[22] = t, $[23] = t12) : t12 = $[23];
  let t13;
  $[24] !== t12 ? (t13 = (0, import_jsx_runtime2.jsx)(Card, { children: (0, import_jsx_runtime2.jsx)(Text, { children: (0, import_jsx_runtime2.jsxs)(PerspectivePopoverLink, { href: "https://sanity.io/docs/perspectives", target: "_blank", children: [
    t12,
    " →"
  ] }) }) }), $[24] = t12, $[25] = t13) : t13 = $[25];
  let t14;
  $[26] !== t10 || $[27] !== t11 || $[28] !== t13 || $[29] !== t4 || $[30] !== t6 ? (t14 = (0, import_jsx_runtime2.jsx)(PerspectivePopoverContent, { children: (0, import_jsx_runtime2.jsxs)(Stack, { space: 4, children: [
    t4,
    t6,
    t10,
    t11,
    t13
  ] }) }), $[26] = t10, $[27] = t11, $[28] = t13, $[29] = t4, $[30] = t6, $[31] = t14) : t14 = $[31];
  let t15;
  $[32] === Symbol.for("react.memo_cache_sentinel") ? (t15 = (0, import_jsx_runtime2.jsx)(Dot, { $tone: "primary" }), $[32] = t15) : t15 = $[32];
  let t16;
  $[33] !== open ? (t16 = (0, import_jsx_runtime2.jsx)(Button, { icon: HelpCircleIcon, mode: "bleed", padding: 2, paddingRight: 1, tone: "primary", fontSize: 1, ref: buttonRef, onClick: handleClick, selected: open, children: t15 }), $[33] = open, $[34] = t16) : t16 = $[34];
  let t17;
  return $[35] !== open || $[36] !== t14 || $[37] !== t16 ? (t17 = (0, import_jsx_runtime2.jsx)(Popover, { content: t14, placement: "bottom-start", portal: true, padding: 3, ref: popoverRef, open, children: t16 }), $[35] = open, $[36] = t14, $[37] = t16, $[38] = t17) : t17 = $[38], t17;
}
function _temp$2(o) {
  return !o;
}
var PinnedReleasePerspectiveOption = (t0) => {
  const $ = (0, import_react_compiler_runtime.c)(9), {
    pinnedPerspective,
    t
  } = t0, name = typeof pinnedPerspective.selectedPerspective == "object" ? pinnedPerspective.selectedPerspective.metadata.title : pinnedPerspective.selectedPerspectiveName;
  let t1;
  $[0] !== pinnedPerspective || $[1] !== t ? (t1 = hasPinnedPerspective(pinnedPerspective) ? `(${t("settings.perspectives.pinned-release-label")})` : t("settings.perspectives.pinned-release-label"), $[0] = pinnedPerspective, $[1] = t, $[2] = t1) : t1 = $[2];
  const label = t1;
  let t2, t3;
  $[3] !== label || $[4] !== name ? (t3 = [name, label].filter(_temp$1), $[3] = label, $[4] = name, $[5] = t3) : t3 = $[5], t2 = t3.join(" ");
  const text = t2, t4 = !hasPinnedPerspective(pinnedPerspective);
  let t5;
  return $[6] !== t4 || $[7] !== text ? (t5 = (0, import_jsx_runtime2.jsx)("option", { value: "pinnedRelease", disabled: t4, children: text }), $[6] = t4, $[7] = text, $[8] = t5) : t5 = $[8], t5;
};
function VisionGuiHeader(t0) {
  const $ = (0, import_react_compiler_runtime.c)(64), {
    onChangeDataset,
    dataset,
    customApiVersion,
    apiVersion,
    onChangeApiVersion,
    datasets,
    customApiVersionElementRef,
    onCustomApiVersionChange,
    isValidApiVersion,
    onChangePerspective,
    url,
    perspective
  } = t0, pinnedPerspective = usePerspective(), {
    t
  } = useTranslation(visionLocaleNamespace), operationUrlElement = (0, import_react2.useRef)(null);
  let t1;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t1 = () => {
    const el = operationUrlElement.current;
    if (el)
      try {
        el.select(), document.execCommand("copy");
      } catch {
        console.error("Unable to copy to clipboard :(");
      }
  }, $[0] = t1) : t1 = $[0];
  const handleCopyUrl = t1;
  let t2;
  $[1] === Symbol.for("react.memo_cache_sentinel") ? (t2 = [1, 4, 8, 12], $[1] = t2) : t2 = $[1];
  let t3;
  $[2] !== t ? (t3 = t("settings.dataset-label"), $[2] = t, $[3] = t3) : t3 = $[3];
  let t4;
  $[4] !== t3 ? (t4 = (0, import_jsx_runtime2.jsx)(Card, { paddingTop: 2, paddingBottom: 3, children: (0, import_jsx_runtime2.jsx)(StyledLabel, { children: t3 }) }), $[4] = t3, $[5] = t4) : t4 = $[5];
  let t5;
  $[6] !== datasets ? (t5 = datasets.map(_temp2), $[6] = datasets, $[7] = t5) : t5 = $[7];
  let t6;
  $[8] !== dataset || $[9] !== onChangeDataset || $[10] !== t5 ? (t6 = (0, import_jsx_runtime2.jsx)(Select, { value: dataset, onChange: onChangeDataset, children: t5 }), $[8] = dataset, $[9] = onChangeDataset, $[10] = t5, $[11] = t6) : t6 = $[11];
  let t7;
  $[12] !== t4 || $[13] !== t6 ? (t7 = (0, import_jsx_runtime2.jsx)(Box, { padding: 1, column: 2, children: (0, import_jsx_runtime2.jsxs)(Stack, { children: [
    t4,
    t6
  ] }) }), $[12] = t4, $[13] = t6, $[14] = t7) : t7 = $[14];
  let t8;
  $[15] !== t ? (t8 = t("settings.api-version-label"), $[15] = t, $[16] = t8) : t8 = $[16];
  let t9;
  $[17] !== t8 ? (t9 = (0, import_jsx_runtime2.jsx)(Card, { paddingTop: 2, paddingBottom: 3, children: (0, import_jsx_runtime2.jsx)(StyledLabel, { children: t8 }) }), $[17] = t8, $[18] = t9) : t9 = $[18];
  const t10 = customApiVersion === false ? apiVersion : "other";
  let t11;
  $[19] === Symbol.for("react.memo_cache_sentinel") ? (t11 = API_VERSIONS.map(_temp3), $[19] = t11) : t11 = $[19];
  let t12;
  $[20] !== t ? (t12 = t("settings.other-api-version-label"), $[20] = t, $[21] = t12) : t12 = $[21];
  let t13;
  $[22] !== t12 ? (t13 = (0, import_jsx_runtime2.jsx)("option", { value: "other", children: t12 }, "other"), $[22] = t12, $[23] = t13) : t13 = $[23];
  let t14;
  $[24] !== onChangeApiVersion || $[25] !== t10 || $[26] !== t13 ? (t14 = (0, import_jsx_runtime2.jsxs)(Select, { "data-testid": "api-version-selector", value: t10, onChange: onChangeApiVersion, children: [
    t11,
    t13
  ] }), $[24] = onChangeApiVersion, $[25] = t10, $[26] = t13, $[27] = t14) : t14 = $[27];
  let t15;
  $[28] !== t14 || $[29] !== t9 ? (t15 = (0, import_jsx_runtime2.jsx)(Box, { padding: 1, column: 2, children: (0, import_jsx_runtime2.jsxs)(Stack, { children: [
    t9,
    t14
  ] }) }), $[28] = t14, $[29] = t9, $[30] = t15) : t15 = $[30];
  let t16;
  $[31] !== customApiVersion || $[32] !== customApiVersionElementRef || $[33] !== isValidApiVersion || $[34] !== onCustomApiVersionChange || $[35] !== t ? (t16 = customApiVersion !== false && (0, import_jsx_runtime2.jsx)(Box, { padding: 1, column: 2, children: (0, import_jsx_runtime2.jsxs)(Stack, { children: [
    (0, import_jsx_runtime2.jsx)(Card, { paddingTop: 2, paddingBottom: 3, children: (0, import_jsx_runtime2.jsx)(StyledLabel, { textOverflow: "ellipsis", children: t("settings.custom-api-version-label") }) }),
    (0, import_jsx_runtime2.jsx)(TextInput, { ref: customApiVersionElementRef, value: customApiVersion, onChange: onCustomApiVersionChange, customValidity: isValidApiVersion ? void 0 : t("settings.error.invalid-api-version"), maxLength: 11 })
  ] }) }), $[31] = customApiVersion, $[32] = customApiVersionElementRef, $[33] = isValidApiVersion, $[34] = onCustomApiVersionChange, $[35] = t, $[36] = t16) : t16 = $[36];
  let t17;
  $[37] !== t ? (t17 = t("settings.perspective-label"), $[37] = t, $[38] = t17) : t17 = $[38];
  let t18;
  $[39] !== t17 ? (t18 = (0, import_jsx_runtime2.jsx)(Box, { children: (0, import_jsx_runtime2.jsx)(StyledLabel, { children: t17 }) }), $[39] = t17, $[40] = t18) : t18 = $[40];
  let t19;
  $[41] === Symbol.for("react.memo_cache_sentinel") ? (t19 = (0, import_jsx_runtime2.jsx)(Box, { children: (0, import_jsx_runtime2.jsx)(PerspectivePopover, {}) }), $[41] = t19) : t19 = $[41];
  let t20;
  $[42] !== t18 ? (t20 = (0, import_jsx_runtime2.jsx)(Card, { paddingBottom: 1, children: (0, import_jsx_runtime2.jsxs)(Inline, { space: 1, children: [
    t18,
    t19
  ] }) }), $[42] = t18, $[43] = t20) : t20 = $[43];
  const t21 = perspective || "default";
  let t22;
  $[44] !== pinnedPerspective || $[45] !== t ? (t22 = SUPPORTED_PERSPECTIVES.map((perspectiveName) => perspectiveName === "pinnedRelease" ? (0, import_jsx_runtime2.jsxs)(import_react2.Fragment, { children: [
    (0, import_jsx_runtime2.jsx)(PinnedReleasePerspectiveOption, { pinnedPerspective, t }),
    (0, import_jsx_runtime2.jsx)("option", { value: "default", children: t("settings.perspectives.default") }, "default"),
    (0, import_jsx_runtime2.jsx)("hr", {})
  ] }, "pinnedRelease") : (0, import_jsx_runtime2.jsx)("option", { children: perspectiveName }, perspectiveName)), $[44] = pinnedPerspective, $[45] = t, $[46] = t22) : t22 = $[46];
  let t23;
  $[47] !== onChangePerspective || $[48] !== t21 || $[49] !== t22 ? (t23 = (0, import_jsx_runtime2.jsx)(Select, { value: t21, onChange: onChangePerspective, children: t22 }), $[47] = onChangePerspective, $[48] = t21, $[49] = t22, $[50] = t23) : t23 = $[50];
  let t24;
  $[51] !== t20 || $[52] !== t23 ? (t24 = (0, import_jsx_runtime2.jsx)(Box, { padding: 1, column: 2, children: (0, import_jsx_runtime2.jsxs)(Stack, { children: [
    t20,
    t23
  ] }) }), $[51] = t20, $[52] = t23, $[53] = t24) : t24 = $[53];
  let t25;
  $[54] !== customApiVersion || $[55] !== t || $[56] !== url ? (t25 = typeof url == "string" ? (0, import_jsx_runtime2.jsx)(Box, { padding: 1, flex: 1, column: customApiVersion === false ? 6 : 4, children: (0, import_jsx_runtime2.jsxs)(Stack, { children: [
    (0, import_jsx_runtime2.jsx)(Card, { paddingTop: 2, paddingBottom: 3, children: (0, import_jsx_runtime2.jsxs)(StyledLabel, { children: [
      t("query.url"),
      " ",
      (0, import_jsx_runtime2.jsxs)(QueryCopyLink, { onClick: handleCopyUrl, children: [
        "[",
        t("action.copy-url-to-clipboard"),
        "]"
      ] })
    ] }) }),
    (0, import_jsx_runtime2.jsxs)(Flex, { flex: 1, gap: 1, children: [
      (0, import_jsx_runtime2.jsx)(Box, { flex: 1, children: (0, import_jsx_runtime2.jsx)(TextInput, { readOnly: true, type: "url", ref: operationUrlElement, value: url }) }),
      (0, import_jsx_runtime2.jsx)(Tooltip, { content: t("action.copy-url-to-clipboard"), children: (0, import_jsx_runtime2.jsx)(Button, { "aria-label": t("action.copy-url-to-clipboard"), type: "button", mode: "ghost", icon: CopyIcon, onClick: handleCopyUrl }) })
    ] })
  ] }) }) : (0, import_jsx_runtime2.jsx)(Box, { flex: 1 }), $[54] = customApiVersion, $[55] = t, $[56] = url, $[57] = t25) : t25 = $[57];
  let t26;
  return $[58] !== t15 || $[59] !== t16 || $[60] !== t24 || $[61] !== t25 || $[62] !== t7 ? (t26 = (0, import_jsx_runtime2.jsx)(Header, { paddingX: 3, paddingY: 2, children: (0, import_jsx_runtime2.jsxs)(Grid, { columns: t2, children: [
    t7,
    t15,
    t16,
    t24,
    t25
  ] }) }), $[58] = t15, $[59] = t16, $[60] = t24, $[61] = t25, $[62] = t7, $[63] = t26) : t26 = $[63], t26;
}
function _temp3(version) {
  return (0, import_jsx_runtime2.jsx)("option", { children: version }, version);
}
function _temp2(ds) {
  return (0, import_jsx_runtime2.jsx)("option", { children: ds }, ds);
}
function _temp$1(value) {
  return typeof value < "u";
}
function getBlobUrl(content, mimeType) {
  return URL.createObjectURL(new Blob([content], {
    type: mimeType
  }));
}
function getMemoizedBlobUrlResolver(mimeType, stringEncoder) {
  return /* @__PURE__ */ (() => {
    let prevResult = "", prevContent = "";
    return (input) => {
      const content = stringEncoder(input);
      if (!(typeof content != "string" || content === ""))
        return content === prevContent || (prevContent = content, prevResult && URL.revokeObjectURL(prevResult), prevResult = getBlobUrl(content, mimeType)), prevResult;
    };
  })();
}
var getJsonBlobUrl = getMemoizedBlobUrlResolver("application/json", (input) => JSON.stringify(input, null, 2));
var getCsvBlobUrl = getMemoizedBlobUrlResolver("text/csv", (input) => (0, import_json_2_csv.json2csv)(Array.isArray(input) ? input : [input]).trim());
var ErrorCode = dt(Code)`
  color: ${({
  theme
}) => theme.sanity.color.muted.critical.enabled.fg};
`;
function QueryErrorDetails(t0) {
  const $ = (0, import_react_compiler_runtime.c)(7), {
    error
  } = t0, {
    t
  } = useTranslation(visionLocaleNamespace);
  if (!("details" in error))
    return null;
  const details = {
    ...error.details,
    ...mapToLegacyDetails(error.details)
  };
  if (!details.line)
    return null;
  const t1 = `${details.line}
${dashLine(details.column, details.columnEnd)}`;
  let t2;
  $[0] !== t1 ? (t2 = (0, import_jsx_runtime2.jsx)(ErrorCode, { size: 1, children: t1 }), $[0] = t1, $[1] = t2) : t2 = $[1];
  const t3 = `${t("query.error.line")}:   ${details.lineNumber}
${t("query.error.column")}: ${details.column}`;
  let t4;
  $[2] !== t3 ? (t4 = (0, import_jsx_runtime2.jsx)(Box, { marginTop: 4, children: (0, import_jsx_runtime2.jsx)(ErrorCode, { size: 1, children: t3 }) }), $[2] = t3, $[3] = t4) : t4 = $[3];
  let t5;
  return $[4] !== t2 || $[5] !== t4 ? (t5 = (0, import_jsx_runtime2.jsxs)("div", { children: [
    t2,
    t4
  ] }), $[4] = t2, $[5] = t4, $[6] = t5) : t5 = $[6], t5;
}
function mapToLegacyDetails(details) {
  if (!details || typeof details.query != "string" || typeof details.start != "number")
    return {};
  const {
    query,
    start,
    end
  } = details, lineStart = query.slice(0, start).lastIndexOf(`
`) + 1, lineNumber = (query.slice(0, lineStart).match(/\n/g) || []).length, line2 = query.slice(lineStart, query.indexOf(`
`, lineStart)), column2 = start - lineStart, columnEnd = typeof end == "number" ? end - lineStart : void 0;
  return {
    line: line2,
    lineNumber,
    column: column2,
    columnEnd
  };
}
function dashLine(column2, columnEnd) {
  const line2 = "-".repeat(column2), hats = "^".repeat(columnEnd ? columnEnd - column2 : 1);
  return `${line2}${hats}`;
}
function QueryErrorDialog(props) {
  const $ = (0, import_react_compiler_runtime.c)(7);
  let t0;
  $[0] !== props.error.message ? (t0 = (0, import_jsx_runtime2.jsx)(ErrorCode, { size: 1, children: props.error.message }), $[0] = props.error.message, $[1] = t0) : t0 = $[1];
  let t1;
  $[2] !== props.error ? (t1 = (0, import_jsx_runtime2.jsx)(QueryErrorDetails, { error: props.error }), $[2] = props.error, $[3] = t1) : t1 = $[3];
  let t2;
  return $[4] !== t0 || $[5] !== t1 ? (t2 = (0, import_jsx_runtime2.jsxs)(Stack, { space: 5, marginTop: 2, children: [
    t0,
    t1
  ] }), $[4] = t0, $[5] = t1, $[6] = t2) : t2 = $[6], t2;
}
var ResultViewWrapper = dt.div(({
  theme
}) => {
  const {
    color,
    fonts,
    space
  } = theme.sanity;
  return lt`
    & .json-inspector,
    & .json-inspector .json-inspector__selection {
      font-family: ${fonts.code.family};
      font-size: ${fonts.code.sizes[2].fontSize}px;
      line-height: ${fonts.code.sizes[2].lineHeight}px;
      color: var(--card-code-fg-color);
    }

    & .json-inspector .json-inspector__leaf {
      padding-left: ${rem(space[4])};
    }

    & .json-inspector .json-inspector__leaf.json-inspector__leaf_root {
      padding-top: ${rem(space[0])};
      padding-left: 0;
    }

    & .json-inspector > .json-inspector__leaf_root > .json-inspector__line > .json-inspector__key {
      display: none;
    }

    & .json-inspector .json-inspector__line {
      display: block;
      position: relative;
      cursor: default;
    }

    & .json-inspector .json-inspector__line::after {
      content: '';
      position: absolute;
      top: 0;
      left: -200px;
      right: -50px;
      bottom: 0;
      z-index: -1;
      pointer-events: none;
    }

    & .json-inspector .json-inspector__line:hover::after {
      background: var(--card-code-bg-color);
    }

    & .json-inspector .json-inspector__leaf_composite > .json-inspector__line {
      cursor: pointer;
    }

    & .json-inspector .json-inspector__leaf_composite > .json-inspector__line::before {
      content: '▸ ';
      margin-left: calc(0px - ${rem(space[4])});
      font-size: ${fonts.code.sizes[2].fontSize}px;
      line-height: ${fonts.code.sizes[2].lineHeight}px;
    }

    &
      .json-inspector
      .json-inspector__leaf_expanded.json-inspector__leaf_composite
      > .json-inspector__line::before {
      content: '▾ ';
      font-size: ${fonts.code.sizes[2].fontSize}px;
      line-height: ${fonts.code.sizes[2].lineHeight}px;
    }

    & .json-inspector .json-inspector__radio,
    & .json-inspector .json-inspector__flatpath {
      display: none;
    }

    & .json-inspector .json-inspector__value {
      margin-left: ${rem(space[4] / 2)};
    }

    &
      .json-inspector
      > .json-inspector__leaf_root
      > .json-inspector__line
      > .json-inspector__key
      + .json-inspector__value {
      margin: 0;
    }

    & .json-inspector .json-inspector__key {
      color: ${color.syntax.property};
    }

    & .json-inspector .json-inspector__value_helper,
    & .json-inspector .json-inspector__value_null {
      color: ${color.syntax.constant};
    }

    & .json-inspector .json-inspector__not-found {
      padding-top: ${rem(space[2])};
    }

    & .json-inspector .json-inspector__value_string {
      color: ${color.syntax.string};
      word-break: break-word;
    }

    & .json-inspector .json-inspector__value_boolean {
      color: ${color.syntax.boolean};
    }

    & .json-inspector .json-inspector__value_number {
      color: ${color.syntax.number};
    }

    & .json-inspector .json-inspector__show-original {
      display: inline-block;
      padding: 0 6px;
      cursor: pointer;
    }

    & .json-inspector .json-inspector__show-original:hover {
      color: inherit;
    }

    & .json-inspector .json-inspector__show-original::before {
      content: '↔';
    }

    & .json-inspector .json-inspector__show-original:hover::after {
      content: ' expand';
    }
  `;
});
var lru = new import_quick_lru.default({
  maxSize: 5e4
});
function ResultView(props) {
  const $ = (0, import_react_compiler_runtime.c)(7), {
    data,
    datasetName
  } = props, workspaceDataset = useDataset();
  if (isRecord(data) || Array.isArray(data)) {
    const t02 = workspaceDataset === datasetName ? DocumentEditLabel : void 0;
    let t12;
    return $[0] !== data || $[1] !== t02 ? (t12 = (0, import_jsx_runtime2.jsx)(ResultViewWrapper, { children: (0, import_jsx_runtime2.jsx)(JsonInspector, { data, search: false, isExpanded, onClick: toggleExpanded, interactiveLabel: t02 }) }), $[0] = data, $[1] = t02, $[2] = t12) : t12 = $[2], t12;
  }
  let t0;
  $[3] !== data ? (t0 = JSON.stringify(data), $[3] = data, $[4] = t0) : t0 = $[4];
  let t1;
  return $[5] !== t0 ? (t1 = (0, import_jsx_runtime2.jsx)(Code, { language: "json", children: t0 }), $[5] = t0, $[6] = t1) : t1 = $[6], t1;
}
function DocumentEditLabel(props) {
  const $ = (0, import_react_compiler_runtime.c)(5);
  if (props.isKey || !props.keypath.endsWith("_id") && !props.keypath.endsWith("_ref"))
    return null;
  let t0;
  $[0] !== props.value ? (t0 = {
    id: props.value
  }, $[0] = props.value, $[1] = t0) : t0 = $[1];
  let t1;
  $[2] === Symbol.for("react.memo_cache_sentinel") ? (t1 = (0, import_jsx_runtime2.jsx)(LinkIcon, {}), $[2] = t1) : t1 = $[2];
  let t2;
  return $[3] !== t0 ? (t2 = (0, import_jsx_runtime2.jsx)(IntentLink, { intent: "edit", params: t0, children: t1 }), $[3] = t0, $[4] = t2) : t2 = $[4], t2;
}
function isExpanded(keyPath, value) {
  const cached = lru.get(keyPath);
  if (typeof cached == "boolean")
    return cached;
  const segments = keyPath.split(".", 4);
  return segments.length === 4 ? false : Array.isArray(value) ? true : isRecord(value) && !segments.some((key2) => isArrayKeyOverLimit(key2));
}
function toggleExpanded(event) {
  const {
    path
  } = event, current = lru.get(path);
  current !== void 0 && lru.set(path, !current);
}
function isRecord(value) {
  return value !== null && typeof value == "object" && !Array.isArray(value);
}
var numeric = /^\d+$/;
function isArrayKeyOverLimit(segment, limit = 10) {
  return numeric.test(segment) && parseInt(segment, 10) > limit;
}
function preventSave(evt) {
  return evt.preventDefault();
}
function SaveCsvButton(t0) {
  const $ = (0, import_react_compiler_runtime.c)(9), {
    blobUrl
  } = t0, {
    t
  } = useTranslation(visionLocaleNamespace), isDisabled = !blobUrl, t1 = isDisabled ? void 0 : "query-result.csv", t2 = isDisabled ? preventSave : void 0;
  let t3;
  $[0] !== blobUrl || $[1] !== isDisabled || $[2] !== t1 || $[3] !== t2 ? (t3 = (0, import_jsx_runtime2.jsx)(Button, { as: "a", disabled: isDisabled, download: t1, href: blobUrl, icon: DocumentSheetIcon, mode: "ghost", onClick: t2, text: "CSV", tone: "default" }), $[0] = blobUrl, $[1] = isDisabled, $[2] = t1, $[3] = t2, $[4] = t3) : t3 = $[4];
  const button = t3;
  let t4;
  return $[5] !== button || $[6] !== isDisabled || $[7] !== t ? (t4 = isDisabled ? (0, import_jsx_runtime2.jsx)(Tooltip, { content: t("result.save-result-as-csv.not-csv-encodable"), placement: "top", children: button }) : button, $[5] = button, $[6] = isDisabled, $[7] = t, $[8] = t4) : t4 = $[8], t4;
}
function SaveJsonButton(t0) {
  const $ = (0, import_react_compiler_runtime.c)(2), {
    blobUrl
  } = t0;
  let t1;
  return $[0] !== blobUrl ? (t1 = (0, import_jsx_runtime2.jsx)(Button, { as: "a", download: "query-result.json", href: blobUrl, icon: DocumentSheetIcon, mode: "ghost", text: "JSON", tone: "default" }), $[0] = blobUrl, $[1] = t1) : t1 = $[1], t1;
}
function VisionGuiResult(t0) {
  const $ = (0, import_react_compiler_runtime.c)(67), {
    error,
    queryInProgress,
    queryResult,
    listenInProgress,
    listenMutations,
    dataset,
    queryTime,
    e2eTime
  } = t0, {
    t
  } = useTranslation(visionLocaleNamespace), hasResult = !error && !queryInProgress && typeof queryResult < "u";
  let t1;
  $[0] !== hasResult || $[1] !== queryResult ? (t1 = hasResult ? getJsonBlobUrl(queryResult) : "", $[0] = hasResult, $[1] = queryResult, $[2] = t1) : t1 = $[2];
  const jsonUrl = t1;
  let t2;
  $[3] !== hasResult || $[4] !== queryResult ? (t2 = hasResult ? getCsvBlobUrl(queryResult) : "", $[3] = hasResult, $[4] = queryResult, $[5] = t2) : t2 = $[5];
  const csvUrl = t2, t3 = error ? "critical" : "default", t4 = !!error;
  let t5;
  $[6] !== t ? (t5 = t("result.label"), $[6] = t, $[7] = t5) : t5 = $[7];
  let t6;
  $[8] !== t5 ? (t6 = (0, import_jsx_runtime2.jsx)(InputBackgroundContainer, { children: (0, import_jsx_runtime2.jsx)(Box, { marginLeft: 3, children: (0, import_jsx_runtime2.jsx)(StyledLabel, { muted: true, children: t5 }) }) }), $[8] = t5, $[9] = t6) : t6 = $[9];
  let t7;
  $[10] !== listenInProgress || $[11] !== listenMutations || $[12] !== queryInProgress ? (t7 = (queryInProgress || listenInProgress && listenMutations.length === 0) && (0, import_jsx_runtime2.jsx)(Box, { marginTop: 3, children: (0, import_jsx_runtime2.jsx)(DelayedSpinner, {}) }), $[10] = listenInProgress, $[11] = listenMutations, $[12] = queryInProgress, $[13] = t7) : t7 = $[13];
  let t8;
  $[14] !== error ? (t8 = error && (0, import_jsx_runtime2.jsx)(QueryErrorDialog, { error }), $[14] = error, $[15] = t8) : t8 = $[15];
  let t9;
  $[16] !== dataset || $[17] !== hasResult || $[18] !== queryResult ? (t9 = hasResult && (0, import_jsx_runtime2.jsx)(ResultView, { data: queryResult, datasetName: dataset }), $[16] = dataset, $[17] = hasResult, $[18] = queryResult, $[19] = t9) : t9 = $[19];
  let t10;
  $[20] !== dataset || $[21] !== listenInProgress || $[22] !== listenMutations ? (t10 = listenInProgress && listenMutations.length > 0 && (0, import_jsx_runtime2.jsx)(ResultView, { data: listenMutations, datasetName: dataset }), $[20] = dataset, $[21] = listenInProgress, $[22] = listenMutations, $[23] = t10) : t10 = $[23];
  let t11;
  $[24] !== t10 || $[25] !== t7 || $[26] !== t8 || $[27] !== t9 ? (t11 = (0, import_jsx_runtime2.jsxs)(Box, { padding: 3, paddingTop: 5, children: [
    t7,
    t8,
    t9,
    t10
  ] }), $[24] = t10, $[25] = t7, $[26] = t8, $[27] = t9, $[28] = t11) : t11 = $[28];
  let t12;
  $[29] !== t11 || $[30] !== t6 ? (t12 = (0, import_jsx_runtime2.jsxs)(Result, { overflow: "auto", children: [
    t6,
    t11
  ] }), $[29] = t11, $[30] = t6, $[31] = t12) : t12 = $[31];
  let t13;
  $[32] !== t12 || $[33] !== t3 || $[34] !== t4 ? (t13 = (0, import_jsx_runtime2.jsx)(ResultInnerContainer, { flex: 1, children: (0, import_jsx_runtime2.jsx)(ResultContainer, { flex: 1, overflow: "hidden", tone: t3, $isInvalid: t4, children: t12 }) }), $[32] = t12, $[33] = t3, $[34] = t4, $[35] = t13) : t13 = $[35];
  let t14;
  $[36] === Symbol.for("react.memo_cache_sentinel") ? (t14 = ["column", "column", "row"], $[36] = t14) : t14 = $[36];
  let t15;
  $[37] !== t ? (t15 = t("result.execution-time-label"), $[37] = t, $[38] = t15) : t15 = $[38];
  let t16;
  $[39] !== queryTime || $[40] !== t ? (t16 = typeof queryTime == "number" ? `${queryTime}ms` : t("result.timing-not-applicable"), $[39] = queryTime, $[40] = t, $[41] = t16) : t16 = $[41];
  let t17;
  $[42] !== t15 || $[43] !== t16 ? (t17 = (0, import_jsx_runtime2.jsx)(Box, { children: (0, import_jsx_runtime2.jsxs)(Text, { muted: true, children: [
    t15,
    ":",
    " ",
    t16
  ] }) }), $[42] = t15, $[43] = t16, $[44] = t17) : t17 = $[44];
  let t18;
  $[45] !== t ? (t18 = t("result.end-to-end-time-label"), $[45] = t, $[46] = t18) : t18 = $[46];
  let t19;
  $[47] !== e2eTime || $[48] !== t ? (t19 = typeof e2eTime == "number" ? `${e2eTime}ms` : t("result.timing-not-applicable"), $[47] = e2eTime, $[48] = t, $[49] = t19) : t19 = $[49];
  let t20;
  $[50] !== t18 || $[51] !== t19 ? (t20 = (0, import_jsx_runtime2.jsx)(Box, { marginLeft: 4, children: (0, import_jsx_runtime2.jsxs)(Text, { muted: true, children: [
    t18,
    ":",
    " ",
    t19
  ] }) }), $[50] = t18, $[51] = t19, $[52] = t20) : t20 = $[52];
  let t21;
  $[53] !== t17 || $[54] !== t20 ? (t21 = (0, import_jsx_runtime2.jsx)(TimingsCard, { paddingX: 4, paddingY: 3, sizing: "border", children: (0, import_jsx_runtime2.jsxs)(TimingsTextContainer, { align: "center", children: [
    t17,
    t20
  ] }) }), $[53] = t17, $[54] = t20, $[55] = t21) : t21 = $[55];
  let t22;
  $[56] !== csvUrl || $[57] !== hasResult || $[58] !== jsonUrl || $[59] !== t ? (t22 = hasResult && (0, import_jsx_runtime2.jsx)(DownloadsCard, { paddingX: 4, paddingY: 3, sizing: "border", children: (0, import_jsx_runtime2.jsx)(SaveResultLabel, { muted: true, children: (0, import_jsx_runtime2.jsx)(Translate, { components: {
    SaveResultButtons: () => (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, { children: [
      (0, import_jsx_runtime2.jsx)(SaveJsonButton, { blobUrl: jsonUrl }),
      (0, import_jsx_runtime2.jsx)(SaveCsvButton, { blobUrl: csvUrl })
    ] })
  }, i18nKey: "result.save-result-as-format", t }) }) }), $[56] = csvUrl, $[57] = hasResult, $[58] = jsonUrl, $[59] = t, $[60] = t22) : t22 = $[60];
  let t23;
  $[61] !== t21 || $[62] !== t22 ? (t23 = (0, import_jsx_runtime2.jsxs)(ResultFooter, { justify: "space-between", direction: t14, children: [
    t21,
    t22
  ] }), $[61] = t21, $[62] = t22, $[63] = t23) : t23 = $[63];
  let t24;
  return $[64] !== t13 || $[65] !== t23 ? (t24 = (0, import_jsx_runtime2.jsxs)(ResultOuterContainer, { direction: "column", "data-testid": "vision-result", children: [
    t13,
    t23
  ] }), $[64] = t13, $[65] = t23, $[66] = t24) : t24 = $[66], t24;
}
function nodeContains(node, other) {
  return !node || !other ? false : node === other || !!(node.compareDocumentPosition(other) & 16);
}
var sanityUrl = /\.(?:api|apicdn)\.sanity\.(?:io|work)\/(vX|v1|v\d{4}-\d\d-\d\d)\/.*?(?:query|listen)\/(.*?)\?(.*)/;
var isRunHotkey = (event) => isHotkey("ctrl+enter", event) || isHotkey("mod+enter", event);
function VisionGui(props) {
  const {
    datasets,
    config,
    projectId,
    defaultDataset
  } = props, toast = useToast(), {
    t
  } = useTranslation(visionLocaleNamespace), {
    perspectiveStack
  } = usePerspective(), defaultApiVersion = prefixApiVersion(`${config.defaultApiVersion}`), editorQueryRef = (0, import_react2.useRef)(null), editorParamsRef = (0, import_react2.useRef)(null), visionRootRef = (0, import_react2.useRef)(null), customApiVersionElementRef = (0, import_react2.useRef)(null), querySubscriptionRef = (0, import_react2.useRef)(void 0), listenSubscriptionRef = (0, import_react2.useRef)(void 0), [localStorage2] = (0, import_react2.useState)(() => getLocalStorage(projectId || "default")), {
    storedDataset,
    storedApiVersion,
    storedQuery,
    storedParams,
    storedPerspective
  } = (0, import_react2.useMemo)(() => ({
    storedDataset: localStorage2.get("dataset", defaultDataset),
    storedApiVersion: localStorage2.get("apiVersion", defaultApiVersion),
    storedQuery: localStorage2.get("query", ""),
    storedParams: localStorage2.get("params", `{
  
}`),
    storedPerspective: localStorage2.get("perspective", void 0)
  }), [defaultDataset, defaultApiVersion, localStorage2]), [dataset, setDataset] = (0, import_react2.useState)(() => datasets.includes(storedDataset) ? storedDataset : datasets.includes(defaultDataset) ? defaultDataset : datasets[0]), [apiVersion, setApiVersion] = (0, import_react2.useState)(() => API_VERSIONS.includes(storedApiVersion) ? storedApiVersion : DEFAULT_API_VERSION), [customApiVersion, setCustomApiVersion] = (0, import_react2.useState)(() => API_VERSIONS.includes(storedApiVersion) ? false : storedApiVersion), [perspective, setPerspectiveState] = (0, import_react2.useState)(storedPerspective || "raw"), isValidApiVersion = customApiVersion ? validateApiVersion(customApiVersion) : true, [url, setUrl] = (0, import_react2.useState)(void 0), [query, setQuery] = (0, import_react2.useState)(() => typeof storedQuery == "string" ? storedQuery : ""), [params, setParams] = (0, import_react2.useState)(() => parseParams(storedParams, t)), [queryResult, setQueryResult] = (0, import_react2.useState)(void 0), [listenMutations, setListenMutations] = (0, import_react2.useState)([]), [error, setError] = (0, import_react2.useState)(void 0), [queryTime, setQueryTime] = (0, import_react2.useState)(void 0), [e2eTime, setE2eTime] = (0, import_react2.useState)(void 0), [queryInProgress, setQueryInProgress] = (0, import_react2.useState)(false), [listenInProgress, setListenInProgress] = (0, import_react2.useState)(false), [isQueryRecallCollapsed, setIsQueryRecallCollapsed] = (0, import_react2.useState)(false), {
    paneSizeOptions,
    isNarrowBreakpoint
  } = usePaneSize({
    visionRootRef
  }), _client = useClient({
    apiVersion: isValidApiVersion && customApiVersion ? customApiVersion : apiVersion
  }), client = (0, import_react2.useMemo)(() => _client.withConfig({
    apiVersion: isValidApiVersion && customApiVersion ? customApiVersion : apiVersion,
    perspective: getActivePerspective({
      visionPerspective: perspective,
      perspectiveStack
    }),
    dataset,
    allowReconfigure: true
  }), [perspectiveStack, perspective, customApiVersion, apiVersion, dataset, _client, isValidApiVersion]), cancelQuerySubscription = (0, import_react2.useCallback)(() => {
    querySubscriptionRef.current && (querySubscriptionRef.current.unsubscribe(), querySubscriptionRef.current = void 0);
  }, []), cancelListenerSubscription = (0, import_react2.useCallback)(() => {
    listenSubscriptionRef.current && (listenSubscriptionRef.current.unsubscribe(), listenSubscriptionRef.current = void 0);
  }, []), handleQueryExecution = (0, import_react2.useCallback)((options) => {
    if (queryInProgress) {
      cancelQuerySubscription(), cancelListenerSubscription(), setQueryInProgress(false);
      return;
    }
    const context = {
      query: (options == null ? void 0 : options.query) || query,
      dataset: (options == null ? void 0 : options.dataset) || dataset,
      params: parseParams(JSON.stringify((options == null ? void 0 : options.params) || params.parsed, null, 2), t),
      perspective: getActivePerspective({
        visionPerspective: (options == null ? void 0 : options.perspective) || perspective,
        perspectiveStack
      }),
      apiVersion: (options == null ? void 0 : options.apiVersion) || (customApiVersion && isValidApiVersion ? customApiVersion : apiVersion)
    };
    if (localStorage2.set("query", context.query), localStorage2.set("params", context.params.raw), cancelListenerSubscription(), setQueryInProgress(!context.params.error && !!context.query), setListenInProgress(false), setListenMutations([]), setError(context.params.error ? new Error(context.params.error) : void 0), setQueryResult(void 0), setQueryTime(void 0), setE2eTime(void 0), context.params.error)
      return;
    const urlQueryOpts = {
      perspective: context.perspective ?? []
    }, ctxClient = client.withConfig({
      apiVersion: context.apiVersion,
      dataset: context.dataset,
      perspective: context.perspective
    }), newUrl = ctxClient.getUrl(ctxClient.getDataUrl("query", encodeQueryString(context.query, context.params.parsed, urlQueryOpts)));
    setUrl(newUrl);
    const queryStart = Date.now();
    querySubscriptionRef.current = ctxClient.observable.fetch(context.query, context.params.parsed, {
      filterResponse: false,
      tag: "vision"
    }).subscribe({
      next: (res) => {
        setQueryTime(res.ms), setE2eTime(Date.now() - queryStart), setQueryResult(res.result), setQueryInProgress(false), setError(void 0);
      },
      error: (err) => {
        setError(err), setQueryInProgress(false);
      }
    });
  }, [queryInProgress, query, dataset, params.parsed, t, perspective, perspectiveStack, customApiVersion, isValidApiVersion, apiVersion, localStorage2, cancelListenerSubscription, client, cancelQuerySubscription]), setPerspective = (0, import_react2.useCallback)((newPerspective) => {
    newPerspective !== void 0 && !isSupportedPerspective(newPerspective) || (setPerspectiveState(newPerspective), localStorage2.set("perspective", newPerspective), handleQueryExecution({
      perspective: newPerspective
    }));
  }, [localStorage2, handleQueryExecution]), handleChangeDataset = (0, import_react2.useCallback)((evt) => {
    const newDataset = evt.target.value;
    localStorage2.set("dataset", newDataset), setDataset(newDataset), handleQueryExecution({
      dataset: newDataset
    });
  }, [localStorage2, handleQueryExecution]), handleChangeApiVersion = (0, import_react2.useCallback)((evt_0) => {
    var _a;
    const newApiVersion = evt_0.target.value;
    if ((newApiVersion == null ? void 0 : newApiVersion.toLowerCase()) === "other") {
      setCustomApiVersion("v"), (_a = customApiVersionElementRef.current) == null ? void 0 : _a.focus();
      return;
    }
    setApiVersion(newApiVersion), setCustomApiVersion(false), localStorage2.set("apiVersion", newApiVersion), handleQueryExecution({
      apiVersion: newApiVersion
    });
  }, [localStorage2, handleQueryExecution]), handleCustomApiVersionChange = (0, import_react2.useCallback)((evt_1) => {
    const newCustomApiVersion = evt_1.target.value || "";
    setCustomApiVersion(newCustomApiVersion || "v"), validateApiVersion(newCustomApiVersion) && (setApiVersion(newCustomApiVersion), localStorage2.set("apiVersion", newCustomApiVersion), handleQueryExecution({
      apiVersion: newCustomApiVersion
    }));
  }, [localStorage2, handleQueryExecution]), handleChangePerspective = (0, import_react2.useCallback)((evt_2) => {
    const newPerspective_0 = evt_2.target.value;
    setPerspective(newPerspective_0 === "default" ? void 0 : newPerspective_0);
  }, [setPerspective]), handleListenerEvent = (0, import_react2.useCallback)((evt_3) => {
    evt_3.type === "mutation" && setListenMutations((prevMutations) => prevMutations.length === 50 ? [evt_3, ...prevMutations.slice(0, 49)] : [evt_3, ...prevMutations]);
  }, []), handleListenExecution = (0, import_react2.useCallback)(() => {
    if (listenInProgress) {
      cancelListenerSubscription(), setListenInProgress(false);
      return;
    }
    const newUrl_0 = client.getDataUrl("listen", encodeQueryString(query, params.parsed, {})), shouldExecute = !params.error && query.trim().length > 0;
    localStorage2.set("query", query), localStorage2.set("params", params.raw), cancelQuerySubscription(), setUrl(newUrl_0), setListenMutations([]), setQueryInProgress(false), setQueryResult(void 0), setListenInProgress(shouldExecute), setError(params.error ? new Error(params.error) : void 0), setQueryTime(void 0), setE2eTime(void 0), shouldExecute && (listenSubscriptionRef.current = client.listen(query, params.parsed, {
      events: ["mutation", "welcome"],
      includeAllVersions: true
    }).subscribe({
      next: handleListenerEvent,
      error: (err_0) => {
        setError(err_0), setListenInProgress(false);
      }
    }));
  }, [listenInProgress, params, query, localStorage2, cancelQuerySubscription, handleListenerEvent, cancelListenerSubscription, client]), handleParamsChange = (0, import_react2.useCallback)((value) => {
    setParams(value), localStorage2.set("params", value.raw);
  }, [localStorage2]), getStateFromUrl = (0, import_react2.useCallback)((data) => {
    const match = data.match(sanityUrl);
    if (!match)
      return null;
    const [, usedApiVersion, usedDataset, urlQuery] = match, qs = new URLSearchParams(urlQuery), parts = parseApiQueryString(qs);
    if (!parts) return null;
    let newApiVersion_0, newCustomApiVersion_0;
    validateApiVersion(usedApiVersion) && (API_VERSIONS.includes(usedApiVersion) ? (newApiVersion_0 = usedApiVersion, newCustomApiVersion_0 = false) : newCustomApiVersion_0 = usedApiVersion);
    const newPerspective_1 = isSupportedPerspective(parts.options.perspective) && !isVirtualPerspective(parts.options.perspective) ? parts.options.perspective : void 0;
    return newPerspective_1 && (!isSupportedPerspective(parts.options.perspective) || isVirtualPerspective(parts.options.perspective)) && toast.push({
      closable: true,
      id: "vision-paste-unsupported-perspective",
      status: "warning",
      title: 'Perspective in pasted url is currently not supported. Falling back to "raw"'
    }), {
      query: parts.query,
      params: parts.params,
      rawParams: JSON.stringify(parts.params, null, 2),
      dataset: datasets.includes(usedDataset) ? usedDataset : dataset,
      apiVersion: newApiVersion_0 || apiVersion,
      customApiVersion: newCustomApiVersion_0,
      perspective: newPerspective_1 || perspective,
      url: data
    };
  }, [datasets, dataset, apiVersion, perspective, toast]), setStateFromParsedUrl = (0, import_react2.useCallback)((parsedUrlObj) => {
    var _a, _b;
    setDataset(parsedUrlObj.dataset), setQuery(parsedUrlObj.query), setParams({
      parsed: parsedUrlObj.params,
      raw: parsedUrlObj.rawParams,
      valid: true,
      error: void 0
    }), setApiVersion(parsedUrlObj.apiVersion), parsedUrlObj.customApiVersion && setCustomApiVersion(parsedUrlObj.customApiVersion), setPerspectiveState(parsedUrlObj.perspective), setUrl(parsedUrlObj.url), (_a = editorQueryRef.current) == null ? void 0 : _a.resetEditorContent(parsedUrlObj.query), (_b = editorParamsRef.current) == null ? void 0 : _b.resetEditorContent(parsedUrlObj.rawParams), localStorage2.merge({
      query: parsedUrlObj.query,
      params: parsedUrlObj.rawParams,
      dataset: parsedUrlObj.dataset,
      apiVersion: parsedUrlObj.customApiVersion || parsedUrlObj.apiVersion,
      perspective: parsedUrlObj.perspective
    }), handleQueryExecution(parsedUrlObj);
  }, [localStorage2, handleQueryExecution]), handlePaste = (0, import_react2.useCallback)((evt_4) => {
    if (!evt_4.clipboardData)
      return;
    const data_0 = evt_4.clipboardData.getData("text/plain");
    evt_4.preventDefault();
    const urlState = getStateFromUrl(data_0);
    urlState && (setStateFromParsedUrl(urlState), toast.push({
      closable: true,
      id: "vision-paste",
      status: "info",
      title: "Parsed URL to query"
    }));
  }, [getStateFromUrl, setStateFromParsedUrl, toast]), handleKeyDown = (0, import_react2.useCallback)((event) => {
    const isWithinRoot = visionRootRef.current && nodeContains(visionRootRef.current, event.target);
    isRunHotkey(event) && isWithinRoot && params.valid && (handleQueryExecution(), event.preventDefault(), event.stopPropagation());
  }, [params.valid, handleQueryExecution]);
  (0, import_react2.useEffect)(() => (window.document.addEventListener("paste", handlePaste), window.document.addEventListener("keydown", handleKeyDown), () => {
    window.document.removeEventListener("paste", handlePaste), window.document.removeEventListener("keydown", handleKeyDown);
  }), [handleKeyDown, handlePaste]), (0, import_react2.useEffect)(() => () => {
    cancelQuerySubscription(), cancelListenerSubscription();
  }, [cancelQuerySubscription, cancelListenerSubscription]);
  const handleStudioPerspectiveChange = useEffectEvent((stack2) => {
    stack2.length > 0 && setPerspective("pinnedRelease");
  });
  (0, import_react2.useEffect)(() => {
    handleStudioPerspectiveChange(perspectiveStack);
  }, [perspectiveStack]);
  const generateUrl = (0, import_react2.useCallback)((queryString, queryParams) => {
    const urlQueryOpts_0 = {
      perspective: getActivePerspective({
        visionPerspective: perspective,
        perspectiveStack
      }) ?? []
    };
    return client.getUrl(client.getDataUrl("query", encodeQueryString(queryString, queryParams, urlQueryOpts_0)));
  }, [client, perspective, perspectiveStack]);
  return (0, import_jsx_runtime2.jsxs)(Root, { direction: "column", height: "fill", ref: visionRootRef, sizing: "border", overflow: "hidden", "data-testid": "vision-root", children: [
    (0, import_jsx_runtime2.jsx)(VisionGuiHeader, { apiVersion, customApiVersion, dataset, datasets, onChangeDataset: handleChangeDataset, onChangeApiVersion: handleChangeApiVersion, customApiVersionElementRef, onCustomApiVersionChange: handleCustomApiVersionChange, isValidApiVersion, onChangePerspective: handleChangePerspective, url, perspective }),
    (0, import_jsx_runtime2.jsx)(SplitpaneContainer, { flex: "auto", children: (0, import_jsx_runtime2.jsxs)(SplitPane, { minSize: 800, defaultSize: window.innerWidth - 275, size: isQueryRecallCollapsed ? window.innerWidth : window.innerWidth - 275, maxSize: -225, primary: "first", children: [
      (0, import_jsx_runtime2.jsx)(Box, { height: "stretch", flex: 1, children: (0, import_jsx_runtime2.jsxs)(
        SplitPane,
        {
          className: "sidebarPanes",
          split: isNarrowBreakpoint ? "vertical" : "horizontal",
          minSize: 300,
          children: [
            (0, import_jsx_runtime2.jsx)(Box, { height: "stretch", flex: 1, children: (0, import_jsx_runtime2.jsxs)(SplitPane, { className: "sidebarPanes", split: "horizontal", defaultSize: isNarrowBreakpoint ? paneSizeOptions.defaultSize : paneSizeOptions.minSize, size: paneSizeOptions.size, allowResize: paneSizeOptions.allowResize, minSize: isNarrowBreakpoint ? paneSizeOptions.minSize : 100, maxSize: paneSizeOptions.maxSize, primary: "first", children: [
              (0, import_jsx_runtime2.jsx)(InputContainer, { display: "flex", "data-testid": "vision-query-editor", children: (0, import_jsx_runtime2.jsxs)(Box, { flex: 1, children: [
                (0, import_jsx_runtime2.jsx)(InputBackgroundContainerLeft, { children: (0, import_jsx_runtime2.jsx)(Flex, { children: (0, import_jsx_runtime2.jsx)(StyledLabel, { muted: true, children: t("query.label") }) }) }),
                (0, import_jsx_runtime2.jsx)(VisionCodeMirror, { initialValue: query, onChange: setQuery, ref: editorQueryRef })
              ] }) }),
              (0, import_jsx_runtime2.jsxs)(InputContainer, { display: "flex", children: [
                (0, import_jsx_runtime2.jsx)(ParamsEditor, { value: params.raw, onChange: handleParamsChange, paramsError: params.error, hasValidParams: params.valid, editorRef: editorParamsRef }),
                (0, import_jsx_runtime2.jsx)(VisionGuiControls, { hasValidParams: params.valid, queryInProgress, listenInProgress, onQueryExecution: handleQueryExecution, onListenExecution: handleListenExecution })
              ] })
            ] }) }),
            (0, import_jsx_runtime2.jsx)(VisionGuiResult, { error, queryInProgress, queryResult, listenInProgress, listenMutations, dataset, queryTime, e2eTime })
          ]
        }
      ) }),
      (0, import_jsx_runtime2.jsxs)(Box, { style: {
        position: "relative",
        height: "100%"
      }, children: [
        (0, import_jsx_runtime2.jsx)(Button, { mode: "ghost", padding: 2, style: {
          position: "absolute",
          left: -32,
          top: "50%",
          transform: "translateY(-50%)",
          zIndex: 100,
          pointerEvents: "auto"
        }, onClick: () => setIsQueryRecallCollapsed(!isQueryRecallCollapsed), children: (0, import_jsx_runtime2.jsx)("div", { style: {
          display: "flex",
          alignItems: "center",
          height: "100%"
        }, children: isQueryRecallCollapsed ? (0, import_jsx_runtime2.jsx)(ChevronLeftIcon, {}) : (0, import_jsx_runtime2.jsx)(ChevronRightIcon, {}) }) }),
        (0, import_jsx_runtime2.jsx)(QueryRecall, { url, getStateFromUrl, setStateFromParsedUrl, currentQuery: query, currentParams: params.parsed || {}, generateUrl })
      ] })
    ] }) })
  ] });
}
function useDatasets(client) {
  const $ = (0, import_react_compiler_runtime.c)(7);
  let t0;
  $[0] !== client ? (t0 = client.config(), $[0] = client, $[1] = t0) : t0 = $[1];
  const projectId = t0.projectId, [datasets, setDatasets] = (0, import_react2.useState)();
  let t1;
  $[2] !== client.observable.datasets ? (t1 = () => {
    const datasets$ = client.observable.datasets.list().subscribe({
      next: (result) => setDatasets(result.map(_temp)),
      error: (err) => setDatasets(err)
    });
    return () => datasets$.unsubscribe();
  }, $[2] = client.observable.datasets, $[3] = t1) : t1 = $[3];
  let t2;
  return $[4] !== client || $[5] !== projectId ? (t2 = [client, projectId], $[4] = client, $[5] = projectId, $[6] = t2) : t2 = $[6], (0, import_react2.useEffect)(t1, t2), datasets || void 0;
}
function _temp(ds) {
  return ds.name;
}
function VisionContainer(props) {
  const $ = (0, import_react_compiler_runtime.c)(15), loadedDatasets = useDatasets(props.client);
  if (!loadedDatasets) {
    let t02;
    return $[0] === Symbol.for("react.memo_cache_sentinel") ? (t02 = (0, import_jsx_runtime2.jsx)(Flex, { align: "center", height: "fill", justify: "center", children: (0, import_jsx_runtime2.jsx)(DelayedSpinner, {}) }), $[0] = t02) : t02 = $[0], t02;
  }
  let t0;
  $[1] !== loadedDatasets || $[2] !== props.client ? (t0 = loadedDatasets instanceof Error ? [props.client.config().dataset || "production"] : loadedDatasets, $[1] = loadedDatasets, $[2] = props.client, $[3] = t0) : t0 = $[3];
  const datasets = t0;
  let t1;
  $[4] !== props.client ? (t1 = props.client.config(), $[4] = props.client, $[5] = t1) : t1 = $[5];
  const projectId = t1.projectId;
  let t2;
  $[6] !== datasets || $[7] !== props.client || $[8] !== props.config.defaultDataset ? (t2 = props.config.defaultDataset || props.client.config().dataset || datasets[0], $[6] = datasets, $[7] = props.client, $[8] = props.config.defaultDataset, $[9] = t2) : t2 = $[9];
  const defaultDataset = t2;
  let t3;
  return $[10] !== datasets || $[11] !== defaultDataset || $[12] !== projectId || $[13] !== props ? (t3 = (0, import_jsx_runtime2.jsx)(VisionGui, { ...props, datasets, projectId, defaultDataset }, projectId), $[10] = datasets, $[11] = defaultDataset, $[12] = projectId, $[13] = props, $[14] = t3) : t3 = $[14], t3;
}
var VisionErrorBoundary = class extends import_react2.Component {
  constructor(props) {
    super(props);
    __publicField(this, "handleRetryRender", () => this.setState((prev) => ({
      error: null,
      numRetries: prev.numRetries + 1
    })));
    __publicField(this, "handleRetryWithCacheClear", () => {
      clearLocalStorage(), this.handleRetryRender();
    });
    this.state = {
      error: null,
      numRetries: 0
    };
  }
  static getDerivedStateFromError(error) {
    return {
      error: error instanceof Error ? error.message : `${error}`
    };
  }
  render() {
    if (!this.state.error)
      return this.props.children;
    const message = this.state.error, withCacheClear = this.state.numRetries > 0;
    return (0, import_jsx_runtime2.jsx)(Card, { height: "fill", overflow: "auto", paddingY: [4, 5, 6, 7], paddingX: 4, sizing: "border", tone: "critical", children: (0, import_jsx_runtime2.jsx)(Container, { width: 3, children: (0, import_jsx_runtime2.jsxs)(Stack, { space: 4, children: [
      (0, import_jsx_runtime2.jsx)("div", { children: (0, import_jsx_runtime2.jsx)(Button, { onClick: withCacheClear ? this.handleRetryWithCacheClear : this.handleRetryRender, text: withCacheClear ? "Clear cache and retry" : "Retry", tone: "default" }) }),
      (0, import_jsx_runtime2.jsx)(Heading, { children: "An error occurred" }),
      (0, import_jsx_runtime2.jsx)(Card, { border: true, radius: 2, overflow: "auto", padding: 4, tone: "inherit", children: (0, import_jsx_runtime2.jsx)(Stack, { space: 4, children: message && (0, import_jsx_runtime2.jsx)(Code, { size: 1, children: (0, import_jsx_runtime2.jsxs)("strong", { children: [
        "Error: ",
        message
      ] }) }) }) })
    ] }) }) });
  }
};
function SanityVision(props) {
  const $ = (0, import_react_compiler_runtime.c)(6);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = {
    apiVersion: "1"
  }, $[0] = t0) : t0 = $[0];
  const client = useClient(t0);
  let t1;
  $[1] !== props.tool.options ? (t1 = {
    defaultApiVersion: DEFAULT_API_VERSION,
    ...props.tool.options
  }, $[1] = props.tool.options, $[2] = t1) : t1 = $[2];
  const config = t1;
  let t2;
  return $[3] !== client || $[4] !== config ? (t2 = (0, import_jsx_runtime2.jsx)(VisionErrorBoundary, { children: (0, import_jsx_runtime2.jsx)(VisionContainer, { client, config }) }), $[3] = client, $[4] = config, $[5] = t2) : t2 = $[5], t2;
}
export {
  SanityVision as default
};
/*! Bundled license information:

doc-path/lib/path.js:
  (**
   * @license MIT
   * doc-path <https://github.com/mrodrig/doc-path>
   * Copyright (c) 2015-present, Michael Rodrigues.
   *)
*/
//# sourceMappingURL=SanityVision-CTI2F4HH.js.map

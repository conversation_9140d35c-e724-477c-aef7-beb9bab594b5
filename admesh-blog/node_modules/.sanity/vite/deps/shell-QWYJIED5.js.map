{"version": 3, "sources": ["../../../../../node_modules/@codemirror/legacy-modes/mode/shell.js"], "sourcesContent": ["var words = {};\nfunction define(style, dict) {\n  for(var i = 0; i < dict.length; i++) {\n    words[dict[i]] = style;\n  }\n};\n\nvar commonAtoms = [\"true\", \"false\"];\nvar commonKeywords = [\"if\", \"then\", \"do\", \"else\", \"elif\", \"while\", \"until\", \"for\", \"in\", \"esac\", \"fi\",\n                      \"fin\", \"fil\", \"done\", \"exit\", \"set\", \"unset\", \"export\", \"function\"];\nvar commonCommands = [\"ab\", \"awk\", \"bash\", \"beep\", \"cat\", \"cc\", \"cd\", \"chown\", \"chmod\", \"chroot\", \"clear\",\n                      \"cp\", \"curl\", \"cut\", \"diff\", \"echo\", \"find\", \"gawk\", \"gcc\", \"get\", \"git\", \"grep\", \"hg\", \"kill\", \"killall\",\n                      \"ln\", \"ls\", \"make\", \"mkdir\", \"openssl\", \"mv\", \"nc\", \"nl\", \"node\", \"npm\", \"ping\", \"ps\", \"restart\", \"rm\",\n                      \"rmdir\", \"sed\", \"service\", \"sh\", \"shopt\", \"shred\", \"source\", \"sort\", \"sleep\", \"ssh\", \"start\", \"stop\",\n                      \"su\", \"sudo\", \"svn\", \"tee\", \"telnet\", \"top\", \"touch\", \"vi\", \"vim\", \"wall\", \"wc\", \"wget\", \"who\", \"write\",\n                      \"yes\", \"zsh\"];\n\ndefine('atom', commonAtoms);\ndefine('keyword', commonKeywords);\ndefine('builtin', commonCommands);\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    stream.next();\n    return null;\n  }\n  if (ch === '\\'' || ch === '\"' || ch === '`') {\n    state.tokens.unshift(tokenString(ch, ch === \"`\" ? \"quote\" : \"string\"));\n    return tokenize(stream, state);\n  }\n  if (ch === '#') {\n    if (sol && stream.eat('!')) {\n      stream.skipToEnd();\n      return 'meta'; // 'comment'?\n    }\n    stream.skipToEnd();\n    return 'comment';\n  }\n  if (ch === '$') {\n    state.tokens.unshift(tokenDollar);\n    return tokenize(stream, state);\n  }\n  if (ch === '+' || ch === '=') {\n    return 'operator';\n  }\n  if (ch === '-') {\n    stream.eat('-');\n    stream.eatWhile(/\\w/);\n    return 'attribute';\n  }\n  if (ch == \"<\") {\n    if (stream.match(\"<<\")) return \"operator\"\n    var heredoc = stream.match(/^<-?\\s*(?:['\"]([^'\"]*)['\"]|([^'\"\\s]*))/)\n    if (heredoc) {\n      state.tokens.unshift(tokenHeredoc(heredoc[1] || heredoc[2]))\n      return 'string.special'\n    }\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/\\d/);\n    if(stream.eol() || !/\\w/.test(stream.peek())) {\n      return 'number';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  if (stream.peek() === '=' && /\\w+/.test(cur)) return 'def';\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenString(quote, style) {\n  var close = quote == \"(\" ? \")\" : quote == \"{\" ? \"}\" : quote\n  return function(stream, state) {\n    var next, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === close && !escaped) {\n        state.tokens.shift();\n        break;\n      } else if (next === '$' && !escaped && quote !== \"'\" && stream.peek() != close) {\n        escaped = true;\n        stream.backUp(1);\n        state.tokens.unshift(tokenDollar);\n        break;\n      } else if (!escaped && quote !== close && next === quote) {\n        state.tokens.unshift(tokenString(quote, style))\n        return tokenize(stream, state)\n      } else if (!escaped && /['\"]/.test(next) && !/['\"]/.test(quote)) {\n        state.tokens.unshift(tokenStringStart(next, \"string\"));\n        stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    return style;\n  };\n};\n\nfunction tokenStringStart(quote, style) {\n  return function(stream, state) {\n    state.tokens[0] = tokenString(quote, style)\n    stream.next()\n    return tokenize(stream, state)\n  }\n}\n\nvar tokenDollar = function(stream, state) {\n  if (state.tokens.length > 1) stream.eat('$');\n  var ch = stream.next()\n  if (/['\"({]/.test(ch)) {\n    state.tokens[0] = tokenString(ch, ch == \"(\" ? \"quote\" : ch == \"{\" ? \"def\" : \"string\");\n    return tokenize(stream, state);\n  }\n  if (!/\\d/.test(ch)) stream.eatWhile(/\\w/);\n  state.tokens.shift();\n  return 'def';\n};\n\nfunction tokenHeredoc(delim) {\n  return function(stream, state) {\n    if (stream.sol() && stream.string == delim) state.tokens.shift()\n    stream.skipToEnd()\n    return \"string.special\"\n  }\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const shell = {\n  name: \"shell\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  },\n  languageData: {\n    autocomplete: commonAtoms.concat(commonKeywords, commonCommands),\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]},\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,QAAQ,CAAC;AACb,SAAS,OAAO,OAAO,MAAM;AAC3B,WAAQ,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACnC,UAAM,KAAK,CAAC,CAAC,IAAI;AAAA,EACnB;AACF;AAEA,IAAI,cAAc,CAAC,QAAQ,OAAO;AAClC,IAAI,iBAAiB;AAAA,EAAC;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAM;AAAA,EAAQ;AAAA,EAC3E;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAU;AACxF,IAAI,iBAAiB;AAAA,EAAC;AAAA,EAAM;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAC5E;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAQ;AAAA,EAChG;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAW;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAW;AAAA,EAClG;AAAA,EAAS;AAAA,EAAO;AAAA,EAAW;AAAA,EAAM;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAS;AAAA,EAC9F;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAAO;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAO;AAAA,EAChG;AAAA,EAAO;AAAK;AAElC,OAAO,QAAQ,WAAW;AAC1B,OAAO,WAAW,cAAc;AAChC,OAAO,WAAW,cAAc;AAEhC,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,SAAS,EAAG,QAAO;AAE9B,MAAI,MAAM,OAAO,IAAI;AACrB,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,OAAO,MAAM;AACf,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AACA,MAAI,OAAO,OAAQ,OAAO,OAAO,OAAO,KAAK;AAC3C,UAAM,OAAO,QAAQ,YAAY,IAAI,OAAO,MAAM,UAAU,QAAQ,CAAC;AACrE,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACA,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,OAAO,IAAI,GAAG,GAAG;AAC1B,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAK;AACd,UAAM,OAAO,QAAQ,WAAW;AAChC,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACA,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAK;AACd,WAAO,IAAI,GAAG;AACd,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,MAAM,IAAI,EAAG,QAAO;AAC/B,QAAI,UAAU,OAAO,MAAM,wCAAwC;AACnE,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,aAAa,QAAQ,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC;AAC3D,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,IAAI;AACpB,QAAG,OAAO,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,KAAK,CAAC,GAAG;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,SAAS,OAAO;AACvB,MAAI,MAAM,OAAO,QAAQ;AACzB,MAAI,OAAO,KAAK,MAAM,OAAO,MAAM,KAAK,GAAG,EAAG,QAAO;AACrD,SAAO,MAAM,eAAe,GAAG,IAAI,MAAM,GAAG,IAAI;AAClD;AAEA,SAAS,YAAY,OAAO,OAAO;AACjC,MAAI,QAAQ,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;AACtD,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,UAAU;AACpB,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,SAAS,SAAS,CAAC,SAAS;AAC9B,cAAM,OAAO,MAAM;AACnB;AAAA,MACF,WAAW,SAAS,OAAO,CAAC,WAAW,UAAU,OAAO,OAAO,KAAK,KAAK,OAAO;AAC9E,kBAAU;AACV,eAAO,OAAO,CAAC;AACf,cAAM,OAAO,QAAQ,WAAW;AAChC;AAAA,MACF,WAAW,CAAC,WAAW,UAAU,SAAS,SAAS,OAAO;AACxD,cAAM,OAAO,QAAQ,YAAY,OAAO,KAAK,CAAC;AAC9C,eAAO,SAAS,QAAQ,KAAK;AAAA,MAC/B,WAAW,CAAC,WAAW,OAAO,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,GAAG;AAC/D,cAAM,OAAO,QAAQ,iBAAiB,MAAM,QAAQ,CAAC;AACrD,eAAO,OAAO,CAAC;AACf;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,SAAS;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,OAAO,OAAO;AACtC,SAAO,SAAS,QAAQ,OAAO;AAC7B,UAAM,OAAO,CAAC,IAAI,YAAY,OAAO,KAAK;AAC1C,WAAO,KAAK;AACZ,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACF;AAEA,IAAI,cAAc,SAAS,QAAQ,OAAO;AACxC,MAAI,MAAM,OAAO,SAAS,EAAG,QAAO,IAAI,GAAG;AAC3C,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,SAAS,KAAK,EAAE,GAAG;AACrB,UAAM,OAAO,CAAC,IAAI,YAAY,IAAI,MAAM,MAAM,UAAU,MAAM,MAAM,QAAQ,QAAQ;AACpF,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACA,MAAI,CAAC,KAAK,KAAK,EAAE,EAAG,QAAO,SAAS,IAAI;AACxC,QAAM,OAAO,MAAM;AACnB,SAAO;AACT;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,KAAK,OAAO,UAAU,MAAO,OAAM,OAAO,MAAM;AAC/D,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAS,QAAQ,OAAO;AAC/B,UAAQ,MAAM,OAAO,CAAC,KAAK,WAAY,QAAQ,KAAK;AACtD;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AAAC,WAAO,EAAC,QAAO,CAAC,EAAC;AAAA,EAAE;AAAA,EAC3C,OAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AAAA,EACA,cAAc;AAAA,IACZ,cAAc,YAAY,OAAO,gBAAgB,cAAc;AAAA,IAC/D,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,IACxD,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}
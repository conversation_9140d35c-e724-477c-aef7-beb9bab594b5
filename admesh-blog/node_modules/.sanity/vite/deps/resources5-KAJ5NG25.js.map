{"version": 3, "sources": ["../../../../../node_modules/sanity/src/core/create/i18n/resources.ts"], "sourcesContent": ["import {defineLocalesResources} from '../../i18n'\n\n/**\n * Defined locale strings for the Create integration feature, in US English.\n *\n * @internal\n */\nconst createLocaleStrings = defineLocalesResources('create', {\n  /** CTA in \"Start writing in Create\" dialog: Learn more */\n  'start-in-create-dialog.cta.learn-more': 'Learn more.',\n  /** Text for the document pane banner informing users that the document is linked to Sanity Create */\n  'studio-create-link-banner.text': 'This document is linked to Sanity Create',\n\n  /** Tooltip for Create Link button */\n  'create-link-info.tooltip': 'Learn more',\n  /** Text above header in Create Link info popover */\n  'create-link-info-popover.eyebrow-title': 'Sanity Create',\n  /** Text in badge above header in Create Link info popover */\n  'create-link-info-popover.eyebrow-badge': 'Early access',\n  /** Header in Create Link info popover */\n  'create-link-info-popover.header': 'Idea-first authoring',\n  /** Informational text in Create Link info popover */\n  'create-link-info-popover.text':\n    'Write naturally in an AI-powered editor. Your content automatically maps to Studio fields as you type.',\n\n  /** Edit in Create button text */\n  'edit-in-create-button.text': 'Edit with Sanity Create',\n  /** Unlink document from Sanity Create button text */\n  'unlink-from-create-button.text': 'Unlink',\n\n  /** Unlink from Create dialog header */\n  'unlink-from-create-dialog.header': 'Switch editing to Studio?',\n  /** Unlink from Create dialog – first informational paragraph */\n  'unlink-from-create-dialog.first-paragraph':\n    'You’re unlinking “<strong>{{title}}</strong>” from Sanity Create so it can be edited here.',\n  /** Unlink from Create dialog – second informational paragraph */\n  'unlink-from-create-dialog.second-paragraph':\n    'You’ll keep your content in both places. Any new changes in Sanity Create will stop syncing to this Studio.',\n  /** Unlink from Create dialog: Cancel button text */\n  'unlink-from-create-dialog.cancel.text': 'Cancel',\n  /** Unlink from Create dialog: Document title used if no other title can be determined */\n  'unlink-from-create-dialog.document.untitled.text': 'Untitled',\n  /** Unlink from Create dialog: Unlink button text */\n  'unlink-from-create-dialog.unlink.text': 'Unlink now',\n})\n\n/**\n * @alpha\n */\nexport type CreateLocaleResourceKeys = keyof typeof createLocaleStrings\n\nexport default createLocaleStrings\n"], "mappings": ";;;;;;;;;;;;;;AAOMA,IAAAA,sBAAsBC,uBAAuB,UAAU;;EAE3D,yCAAyC;;EAEzC,kCAAkC;;EAGlC,4BAA4B;;EAE5B,0CAA0C;;EAE1C,0CAA0C;;EAE1C,mCAAmC;;EAEnC,iCACE;;EAGF,8BAA8B;;EAE9B,kCAAkC;;EAGlC,oCAAoC;;EAEpC,6CACE;;EAEF,8CACE;;EAEF,yCAAyC;;EAEzC,oDAAoD;;EAEpD,yCAAyC;AAC3C,CAAC;", "names": ["createLocaleStrings", "defineLocalesResources"]}
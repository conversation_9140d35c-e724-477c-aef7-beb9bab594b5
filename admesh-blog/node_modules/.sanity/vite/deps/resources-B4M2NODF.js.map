{"version": 3, "sources": ["../../../@sanity/vision/src/i18n/resources.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\nimport {defineLocalesResources} from 'sanity'\n\n/**\n * Defined locale strings for the vision tool, in US English.\n *\n * @internal\n */\nconst visionLocaleStrings = defineLocalesResources('vision', {\n  /** Label for action \"Copy to clipboard\", tied to the \"Query URL\" field. Also used for accessibility purposes on button */\n  'action.copy-url-to-clipboard': 'Copy to clipboard',\n  /** Label for deleting a query */\n  'action.delete': 'Delete',\n  /** Label for editing a query's title */\n  'action.edit-title': 'Edit title',\n  /** Label for stopping an ongoing listen operation */\n  'action.listen-cancel': 'Stop',\n  /** Label for setting up a listener */\n  'action.listen-execute': 'Listen',\n  /** Label for query loading table */\n  'action.load-queries': 'Load queries',\n  /** Label for loading a query */\n  'action.load-query': 'Load query',\n  /** Label for cancelling an ongoing query */\n  'action.query-cancel': 'Cancel',\n  /** Label for executing the query, eg doing a fetch */\n  'action.query-execute': 'Fetch',\n  /** Label for saving a query */\n  'action.save-query': 'Save query',\n  /** Label for updating a query */\n  'action.update': 'Update',\n\n  /** Label for actions user can take */\n  'label.actions': 'Actions',\n  /** Label for saved queries that have been edited */\n  'label.edited': 'Edited',\n\n  /**\n   * Some features has a \"New\" label indicating that the feature was recently introduced.\n   * This defines what the text of that label is. Keep it short and sweet.\n   */\n  'label.new': 'New',\n  /** Label for query type \"personal\" */\n  'label.personal': 'Personal',\n  /** Label for savedAt date */\n  'label.saved-at': 'Saved at',\n  /** Saved queries */\n  'label.saved-queries': 'Saved queries',\n  /** Search queries */\n  'label.search-queries': 'Search queries',\n  /** Share query */\n  'label.share': 'Share',\n  /** Label for saved query type \"team\" */\n  'label.team': 'Team',\n\n  /** Error message for when the \"Params\" input are not a valid json */\n  'params.error.params-invalid-json': 'Parameters are not valid JSON',\n  /** Label for \"Params\" (parameters) editor/input */\n  'params.label': 'Params',\n\n  /** Label for 'Column' indicator when there is an error within the query */\n  'query.error.column': 'Column',\n  /** Label for 'Line' indicator when there is an error within the query */\n  'query.error.line': 'Line',\n  /** Label for \"Query\" editor/input */\n  'query.label': 'Query',\n  /** Label for the \"Query URL\" field, shown after executing a query, and allows for copying */\n  'query.url': 'Query URL',\n\n  /** Label for \"End to End time\" information of the fetched query */\n  'result.end-to-end-time-label': 'End-to-end',\n  /** Label for \"Execution time\" information of the fetched query */\n  'result.execution-time-label': 'Execution',\n  /** Label for \"Result\" explorer/view */\n  'result.label': 'Result',\n  /** Tooltip text shown when the query result is not encodable as CSV */\n  'result.save-result-as-csv.not-csv-encodable': 'Result cannot be encoded as CSV',\n  /** Label for \"Save result as\" result action */\n  'result.save-result-as-format': 'Save result as <SaveResultButtons/>',\n  /**\n   * \"Not applicable\" message for when there is no Execution time or End to End time information\n   * available for the query (eg when the query has not been executed, or errored)\n   */\n  'result.timing-not-applicable': 'n/a',\n\n  /** Query already saved error label */\n  'save-query.already-saved': 'Query already saved',\n  /** Save error label */\n  'save-query.error': 'Error saving query',\n  /** Save success label */\n  'save-query.success': 'Query saved',\n\n  /** Label for the \"API version\" dropdown in settings */\n  'settings.api-version-label': 'API version',\n  /** Label for the \"Custom API version\" input in settings, shown when \"other\" is chosen as API version */\n  'settings.custom-api-version-label': 'Custom API version',\n  /** Label for the \"Dataset\" dropdown in vision settings */\n  'settings.dataset-label': 'Dataset',\n  /** Error label for when the API version in 'Custom API version' input is invalid */\n  'settings.error.invalid-api-version': 'Invalid API version',\n  /** Label for the \"other\" versions within the \"API version\" dropdown */\n  'settings.other-api-version-label': 'Other',\n  /**\n   * Label for the \"Perspective\" dropdown in vision settings\n   * @see {@link https://www.sanity.io/docs/perspectives}\n   */\n  'settings.perspective-label': 'Perspective',\n  /** Notification about previewDrafts to drafts rename */\n  'settings.perspective.preview-drafts-renamed-to-drafts.description':\n    'The \"<code>previewDrafts</code>\" perspective has been renamed to \"<code>drafts</code>\" and is now deprecated. This change is effective for all versions with perspective support (>= v2021-03-25).',\n  /** Call to action to read the docs related to \"Perspectives\" */\n  'settings.perspectives.action.docs-link': 'Read docs',\n  /** Option for selecting default perspective */\n  'settings.perspectives.default': 'No perspective (API default)',\n  /** Description for popover that explains what \"Perspectives\" are */\n  'settings.perspectives.description':\n    'Perspectives allow your query to run against different \"views\" of the content in your dataset',\n  /** Description for upcoming default perspective change */\n  'settings.perspectives.new-default.description':\n    'The default perspective will change from \"<code>raw</code>\" to \"<code>published</code>\" in an upcoming API version. Please consult docs for more details.',\n  /** Label for the pinned release perspective */\n  'settings.perspectives.pinned-release-label': 'Pinned release',\n  /** Title for popover that explains what \"Perspectives\" are */\n  'settings.perspectives.title': 'Perspectives',\n} as const)\n\n/**\n * @alpha\n */\nexport type VisionLocaleResourceKeys = keyof typeof visionLocaleStrings\n\nexport default visionLocaleStrings\n"], "mappings": ";;;;;;;;;;;;;;AAQMA,IAAAA,sBAAsBC,uBAAuB,UAAU;;EAE3D,gCAAgC;;EAEhC,iBAAiB;;EAEjB,qBAAqB;;EAErB,wBAAwB;;EAExB,yBAAyB;;EAEzB,uBAAuB;;EAEvB,qBAAqB;;EAErB,uBAAuB;;EAEvB,wBAAwB;;EAExB,qBAAqB;;EAErB,iBAAiB;;EAGjB,iBAAiB;;EAEjB,gBAAgB;;;;;EAMhB,aAAa;;EAEb,kBAAkB;;EAElB,kBAAkB;;EAElB,uBAAuB;;EAEvB,wBAAwB;;EAExB,eAAe;;EAEf,cAAc;;EAGd,oCAAoC;;EAEpC,gBAAgB;;EAGhB,sBAAsB;;EAEtB,oBAAoB;;EAEpB,eAAe;;EAEf,aAAa;;EAGb,gCAAgC;;EAEhC,+BAA+B;;EAE/B,gBAAgB;;EAEhB,+CAA+C;;EAE/C,gCAAgC;;;;;EAKhC,gCAAgC;;EAGhC,4BAA4B;;EAE5B,oBAAoB;;EAEpB,sBAAsB;;EAGtB,8BAA8B;;EAE9B,qCAAqC;;EAErC,0BAA0B;;EAE1B,sCAAsC;;EAEtC,oCAAoC;;;;;EAKpC,8BAA8B;;EAE9B,qEACE;;EAEF,0CAA0C;;EAE1C,iCAAiC;;EAEjC,qCACE;;EAEF,iDACE;;EAEF,8CAA8C;;EAE9C,+BAA+B;AACjC,CAAU;", "names": ["visionLocaleStrings", "defineLocalesResources"]}
import {
  require_Refractor
} from "./chunk-KSAIUMCL.js";
import {
  require_dist
} from "./chunk-JX5BQTZ6.js";
import {
  require_jsx_runtime
} from "./chunk-ZQZQC55M.js";
import "./chunk-BD3T7BTJ.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// node_modules/@sanity/ui/dist/_chunks-es/refractor.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react_compiler_runtime = __toESM(require_dist(), 1);
var import_react_refractor = __toESM(require_Refractor(), 1);
function LazyRefractor(props) {
  const $ = (0, import_react_compiler_runtime.c)(13), {
    language: languageProp,
    value
  } = props, language = typeof languageProp == "string" ? languageProp : void 0;
  let t0;
  $[0] !== language ? (t0 = language ? import_react_refractor.default.hasLanguage(language) : false, $[0] = language, $[1] = t0) : t0 = $[1];
  const registered = t0;
  let t1;
  $[2] !== language || $[3] !== registered || $[4] !== value ? (t1 = !(language && registered) && (0, import_jsx_runtime.jsx)("code", { children: value }), $[2] = language, $[3] = registered, $[4] = value, $[5] = t1) : t1 = $[5];
  let t2;
  $[6] !== language || $[7] !== registered || $[8] !== value ? (t2 = language && registered && (0, import_jsx_runtime.jsx)(import_react_refractor.default, { inline: true, language, value: String(value) }), $[6] = language, $[7] = registered, $[8] = value, $[9] = t2) : t2 = $[9];
  let t3;
  return $[10] !== t1 || $[11] !== t2 ? (t3 = (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    t1,
    t2
  ] }), $[10] = t1, $[11] = t2, $[12] = t3) : t3 = $[12], t3;
}
LazyRefractor.displayName = "LazyRefractor";
export {
  LazyRefractor as default
};
//# sourceMappingURL=refractor-B6ELIFUO.js.map

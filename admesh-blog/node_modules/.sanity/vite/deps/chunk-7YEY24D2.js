import {
  <PERSON><PERSON><PERSON>,
  definePlugin,
  defineType,
  set,
  setIfMissing,
  unset
} from "./chunk-6XMXOQIO.js";
import {
  Box,
  Card,
  Flex,
  Label,
  Select,
  Stack,
  Text,
  dt,
  lt
} from "./chunk-3HD5UKUW.js";
import {
  CodeBlockIcon
} from "./chunk-V3YD7LXU.js";
import {
  require_jsx_runtime
} from "./chunk-EVVIBKQA.js";
import {
  require_react
} from "./chunk-3GN7GPJI.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// ../node_modules/@sanity/code-input/lib/_chunks-es/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var CodeMirrorProxy = (0, import_react.lazy)(() => import("./CodeMirrorProxy-JMCWFYBW.js"));
function useCodeMirror() {
  const [mounted, setMounted] = (0, import_react.useState)(false);
  return (0, import_react.useEffect)(() => {
    requestAnimationFrame(() => setMounted(true));
  }, []), mounted ? CodeMirrorProxy : null;
}
var SUPPORTED_LANGUAGES = [
  { title: "Batch file", value: "batchfile" },
  { title: "C#", value: "csharp" },
  { title: "CSS", value: "css" },
  { title: "Go", value: "golang" },
  { title: "GROQ", value: "groq" },
  { title: "HTML", value: "html" },
  { title: "Java", value: "java" },
  { title: "JavaScript", value: "javascript" },
  { title: "JSON", value: "json" },
  { title: "JSX", value: "jsx" },
  { title: "Markdown", value: "markdown" },
  { title: "MySQL", value: "mysql" },
  { title: "PHP", value: "php" },
  { title: "Plain text", value: "text" },
  { title: "Python", value: "python" },
  { title: "Ruby", value: "ruby" },
  { title: "SASS", value: "sass" },
  { title: "SCSS", value: "scss" },
  { title: "sh", value: "sh" },
  { title: "TSX", value: "tsx" },
  { title: "TypeScript", value: "typescript" },
  { title: "XML", value: "xml" },
  { title: "YAML", value: "yaml" }
];
var LANGUAGE_ALIASES = { js: "javascript" };
var PATH_CODE = ["code"];
var defaultLanguageMode = "text";
function useLanguageMode(schemaType, value) {
  var _a, _b, _c, _d, _e;
  const languages = useLanguageAlternatives(schemaType), fixedLanguage = (_a = schemaType.options) == null ? void 0 : _a.language, language = (_c = (_b = value == null ? void 0 : value.language) != null ? _b : fixedLanguage) != null ? _c : defaultLanguageMode, configured = languages.find((entry) => entry.value === language), languageMode = (_e = (_d = configured == null ? void 0 : configured.mode) != null ? _d : resolveAliasedLanguage(language)) != null ? _e : defaultLanguageMode;
  return { language, languageMode, languages };
}
function resolveAliasedLanguage(lang) {
  var _a;
  return (_a = lang && LANGUAGE_ALIASES[lang]) != null ? _a : lang;
}
function useLanguageAlternatives(type) {
  return (0, import_react.useMemo)(() => {
    var _a;
    const languageAlternatives = (_a = type.options) == null ? void 0 : _a.languageAlternatives;
    if (!languageAlternatives)
      return SUPPORTED_LANGUAGES;
    if (!Array.isArray(languageAlternatives))
      throw new Error(
        `'options.languageAlternatives' should be an array, got ${typeof languageAlternatives}`
      );
    return languageAlternatives.reduce((acc, { title, value: val, mode }) => {
      const alias = LANGUAGE_ALIASES[val];
      return alias ? (console.warn(
        `'options.languageAlternatives' lists a language with value "%s", which is an alias of "%s" - please replace the value to read "%s"`,
        val,
        alias,
        alias
      ), acc.concat({ title, value: alias, mode })) : acc.concat({ title, value: val, mode });
    }, []);
  }, [type]);
}
var __defProp$1 = Object.defineProperty;
var __defProps$1 = Object.defineProperties;
var __getOwnPropDescs$1 = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols$1 = Object.getOwnPropertySymbols;
var __hasOwnProp$1 = Object.prototype.hasOwnProperty;
var __propIsEnum$1 = Object.prototype.propertyIsEnumerable;
var __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues$1 = (a, b) => {
  for (var prop in b || (b = {}))
    __hasOwnProp$1.call(b, prop) && __defNormalProp$1(a, prop, b[prop]);
  if (__getOwnPropSymbols$1)
    for (var prop of __getOwnPropSymbols$1(b))
      __propIsEnum$1.call(b, prop) && __defNormalProp$1(a, prop, b[prop]);
  return a;
};
var __spreadProps$1 = (a, b) => __defProps$1(a, __getOwnPropDescs$1(b));
function LanguageInput(props) {
  const { language, languages, onChange, elementProps } = props, handleChange = (0, import_react.useCallback)(
    (e) => {
      const newValue = e.currentTarget.value;
      onChange(newValue ? set(newValue) : unset());
    },
    [onChange]
  );
  return (0, import_jsx_runtime.jsx)(Select, __spreadProps$1(__spreadValues$1({}, elementProps), { value: language, onChange: handleChange, children: languages.map((lang) => (0, import_jsx_runtime.jsx)("option", { value: lang.value, children: lang.title }, lang.value)) }));
}
function LanguageField(props) {
  const { member, languages, language, renderItem, renderField, renderPreview } = props, renderInput = (0, import_react.useCallback)(
    ({ elementProps, onChange }) => (0, import_jsx_runtime.jsx)(
      LanguageInput,
      {
        onChange,
        elementProps,
        language,
        languages
      }
    ),
    [languages, language]
  );
  return (0, import_jsx_runtime.jsx)(
    MemberField,
    {
      member,
      renderItem,
      renderField,
      renderInput,
      renderPreview
    }
  );
}
function focusRingBorderStyle(border) {
  return `inset 0 0 0 ${border.width}px ${border.color}`;
}
function focusRingStyle(opts) {
  const { base, border, focusRing } = opts, focusRingOutsetWidth = focusRing.offset + focusRing.width, focusRingInsetWidth = 0 - focusRing.offset, bgColor = base ? base.bg : "var(--card-bg-color)";
  return [
    focusRingInsetWidth > 0 && `inset 0 0 0 ${focusRingInsetWidth}px var(--card-focus-ring-color)`,
    border && focusRingBorderStyle(border),
    focusRingInsetWidth < 0 && `0 0 0 ${0 - focusRingInsetWidth}px ${bgColor}`,
    focusRingOutsetWidth > 0 && `0 0 0 ${focusRingOutsetWidth}px var(--card-focus-ring-color)`
  ].filter(Boolean).join(",");
}
function useFieldMember(members, fieldName) {
  return (0, import_react.useMemo)(
    () => members.find(
      (member) => member.kind === "field" && member.name === fieldName
    ),
    [members, fieldName]
  );
}
var EditorContainer = dt(Card)(({ theme }) => {
  const { focusRing, input } = theme.sanity, base = theme.sanity.color.base, border = {
    color: theme.sanity.color.input.default.enabled.border,
    width: input.border.width
  };
  return lt`
    --input-box-shadow: ${focusRingBorderStyle(border)};

    box-shadow: var(--input-box-shadow);
    height: 250px;
    min-height: 80px;
    overflow-y: auto;
    position: relative;
    resize: vertical;
    z-index: 0;

    & > .cm-theme {
      height: 100%;
    }

    &:focus-within {
      --input-box-shadow: ${focusRingStyle({
    base,
    border,
    focusRing
  })};
    }
  `;
});
function CodeInput(props) {
  var _a;
  const {
    members,
    elementProps,
    onChange,
    readOnly,
    renderField,
    renderInput,
    renderItem,
    renderPreview,
    schemaType: type,
    value,
    onPathFocus
  } = props, languageFieldMember = useFieldMember(members, "language"), filenameMember = useFieldMember(members, "filename"), codeFieldMember = useFieldMember(members, "code"), handleCodeFocus = (0, import_react.useCallback)(() => {
    onPathFocus(PATH_CODE);
  }, [onPathFocus]), onHighlightChange = (0, import_react.useCallback)(
    (lines) => onChange(set(lines, ["highlightedLines"])),
    [onChange]
  ), handleCodeChange = (0, import_react.useCallback)(
    (code) => {
      var _a2;
      const path = PATH_CODE, fixedLanguage = (_a2 = type.options) == null ? void 0 : _a2.language;
      onChange([
        setIfMissing({ _type: type.name, language: fixedLanguage }),
        code ? set(code, path) : unset(path)
      ]);
    },
    [onChange, type]
  ), { languages, language, languageMode } = useLanguageMode(props.schemaType, props.value), CodeMirror = useCodeMirror(), renderCodeInput = (0, import_react.useCallback)(
    (inputProps) => (0, import_jsx_runtime.jsx)(EditorContainer, { border: true, overflow: "hidden", radius: 1, sizing: "border", readOnly, children: CodeMirror && (0, import_jsx_runtime.jsx)(
      import_react.Suspense,
      {
        fallback: (0, import_jsx_runtime.jsx)(Box, { padding: 3, children: (0, import_jsx_runtime.jsx)(Text, { children: "Loading code editor..." }) }),
        children: (0, import_jsx_runtime.jsx)(
          CodeMirror,
          {
            languageMode,
            onChange: handleCodeChange,
            value: inputProps.value,
            highlightLines: value == null ? void 0 : value.highlightedLines,
            onHighlightChange,
            readOnly,
            onFocus: handleCodeFocus,
            onBlur: elementProps.onBlur
          }
        )
      }
    ) }),
    [
      CodeMirror,
      handleCodeChange,
      handleCodeFocus,
      onHighlightChange,
      languageMode,
      elementProps.onBlur,
      readOnly,
      value
    ]
  );
  return (0, import_jsx_runtime.jsxs)(Stack, { space: 4, children: [
    languageFieldMember && (0, import_jsx_runtime.jsx)(
      LanguageField,
      {
        member: languageFieldMember,
        language,
        languages,
        renderField,
        renderItem,
        renderInput,
        renderPreview
      }
    ),
    ((_a = type.options) == null ? void 0 : _a.withFilename) && filenameMember && (0, import_jsx_runtime.jsx)(
      MemberField,
      {
        member: filenameMember,
        renderItem,
        renderField,
        renderInput,
        renderPreview
      }
    ),
    codeFieldMember && (0, import_jsx_runtime.jsx)(
      MemberField,
      {
        member: codeFieldMember,
        renderInput: renderCodeInput,
        renderItem,
        renderField,
        renderPreview
      }
    )
  ] });
}
function getMedia(language) {
  if (language === "jsx")
    return (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 128 128", children: (0, import_jsx_runtime.jsxs)("g", { fill: "#61DAFB", children: [
      (0, import_jsx_runtime.jsx)("circle", { cx: "64", cy: "64", r: "11.4" }),
      (0, import_jsx_runtime.jsx)("path", { d: "M107.3 45.2c-2.2-.8-4.5-1.6-6.9-2.3.6-2.4 1.1-4.8 1.5-7.1 2.1-13.2-.2-22.5-6.6-26.1-1.9-1.1-4-1.6-6.4-1.6-7 0-15.9 5.2-24.9 13.9-9-8.7-17.9-13.9-24.9-13.9-2.4 0-4.5.5-6.4 1.6-6.4 3.7-8.7 13-6.6 26.1.4 2.3.9 4.7 1.5 7.1-2.4.7-4.7 1.4-6.9 2.3C8.2 50 1.4 56.6 1.4 64s6.9 14 19.3 18.8c2.2.8 4.5 1.6 6.9 2.3-.6 2.4-1.1 4.8-1.5 7.1-2.1 13.2.2 22.5 6.6 26.1 1.9 1.1 4 1.6 6.4 1.6 7.1 0 16-5.2 24.9-13.9 9 8.7 17.9 13.9 24.9 13.9 2.4 0 4.5-.5 6.4-1.6 6.4-3.7 8.7-13 6.6-26.1-.4-2.3-.9-4.7-1.5-7.1 2.4-.7 4.7-1.4 6.9-2.3 12.5-4.8 19.3-11.4 19.3-18.8s-6.8-14-19.3-18.8zM92.5 14.7c4.1 2.4 5.5 9.8 3.8 20.3-.3 2.1-.8 4.3-1.4 6.6-5.2-1.2-10.7-2-16.5-2.5-3.4-4.8-6.9-9.1-10.4-13 7.4-7.3 14.9-12.3 21-12.3 1.3 0 2.5.3 3.5.9zM81.3 74c-1.8 3.2-3.9 6.4-6.1 9.6-3.7.3-7.4.4-11.2.4-3.9 0-7.6-.1-11.2-.4-2.2-3.2-4.2-6.4-6-9.6-1.9-3.3-3.7-6.7-5.3-10 1.6-3.3 3.4-6.7 5.3-10 1.8-3.2 3.9-6.4 6.1-9.6 3.7-.3 7.4-.4 11.2-.4 3.9 0 7.6.1 11.2.4 2.2 3.2 4.2 6.4 6 9.6 1.9 3.3 3.7 6.7 5.3 10-1.7 3.3-3.4 6.6-5.3 10zm8.3-3.3c1.5 3.5 2.7 6.9 3.8 10.3-3.4.8-7 1.4-10.8 1.9 1.2-1.9 2.5-3.9 3.6-6 1.2-2.1 2.3-4.2 3.4-6.2zM64 97.8c-2.4-2.6-4.7-5.4-6.9-8.3 2.3.1 4.6.2 6.9.2 2.3 0 4.6-.1 6.9-.2-2.2 2.9-4.5 5.7-6.9 8.3zm-18.6-15c-3.8-.5-7.4-1.1-10.8-1.9 1.1-3.3 2.3-6.8 3.8-10.3 1.1 2 2.2 4.1 3.4 6.1 1.2 2.2 2.4 4.1 3.6 6.1zm-7-25.5c-1.5-3.5-2.7-6.9-3.8-10.3 3.4-.8 7-1.4 10.8-1.9-1.2 1.9-2.5 3.9-3.6 6-1.2 2.1-2.3 4.2-3.4 6.2zM64 30.2c2.4 2.6 4.7 5.4 6.9 8.3-2.3-.1-4.6-.2-6.9-.2-2.3 0-4.6.1-6.9.2 2.2-2.9 4.5-5.7 6.9-8.3zm22.2 21l-3.6-6c3.8.5 7.4 1.1 10.8 1.9-1.1 3.3-2.3 6.8-3.8 10.3-1.1-2.1-2.2-4.2-3.4-6.2zM31.7 35c-1.7-10.5-.3-17.9 3.8-20.3 1-.6 2.2-.9 3.5-.9 6 0 13.5 4.9 21 12.3-3.5 3.8-7 8.2-10.4 13-5.8.5-11.3 1.4-16.5 2.5-.6-2.3-1-4.5-1.4-6.6zM7 64c0-4.7 5.7-9.7 15.7-13.4 2-.8 4.2-1.5 6.4-2.1 1.6 5 3.6 10.3 6 15.6-2.4 5.3-4.5 10.5-6 15.5C15.3 75.6 7 69.6 7 64zm28.5 49.3c-4.1-2.4-5.5-9.8-3.8-20.3.3-2.1.8-4.3 1.4-6.6 5.2 1.2 10.7 2 16.5 2.5 3.4 4.8 6.9 9.1 10.4 13-7.4 7.3-14.9 12.3-21 12.3-1.3 0-2.5-.3-3.5-.9zM96.3 93c1.7 10.5.3 17.9-3.8 20.3-1 .6-2.2.9-3.5.9-6 0-13.5-4.9-21-12.3 3.5-3.8 7-8.2 10.4-13 5.8-.5 11.3-1.4 16.5-2.5.6 2.3 1 4.5 1.4 6.6zm9-15.6c-2 .8-4.2 1.5-6.4 2.1-1.6-5-3.6-10.3-6-15.6 2.4-5.3 4.5-10.5 6-15.5 13.8 4 22.1 10 22.1 15.6 0 4.7-5.8 9.7-15.7 13.4z" })
    ] }) });
  if (language === "javascript")
    return (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 128 128", children: [
      (0, import_jsx_runtime.jsx)("path", { fill: "#F0DB4F", d: "M1.408 1.408h125.184v125.185H1.408z" }),
      (0, import_jsx_runtime.jsx)(
        "path",
        {
          fill: "#323330",
          d: "M116.347 96.736c-.917-5.711-4.641-10.508-15.672-14.981-3.832-1.761-8.104-3.022-9.377-5.926-.452-1.69-.512-2.642-.226-3.665.821-3.32 4.784-4.355 7.925-3.403 2.023.678 3.938 2.237 5.093 4.724 5.402-3.498 5.391-3.475 9.163-5.879-1.381-2.141-2.118-3.129-3.022-4.045-3.249-3.629-7.676-5.498-14.756-5.355l-3.688.477c-3.534.893-6.902 2.748-8.877 5.235-5.926 6.724-4.236 18.492 2.975 23.335 7.104 5.332 17.54 6.545 18.873 11.531 1.297 6.104-4.486 8.08-10.234 7.378-4.236-.881-6.592-3.034-9.139-6.949-4.688 2.713-4.688 2.713-9.508 5.485 1.143 2.499 2.344 3.63 4.26 5.795 9.068 9.198 31.76 8.746 35.83-5.176.165-.478 1.261-3.666.38-8.581zM69.462 58.943H57.753l-.048 30.272c0 6.438.333 12.34-.714 14.149-1.713 3.558-6.152 3.117-8.175 2.427-2.059-1.012-3.106-2.451-4.319-4.485-.333-.584-.583-1.036-.667-1.071l-9.52 5.83c1.583 3.249 3.915 6.069 6.902 7.901 4.462 2.678 10.459 3.499 16.731 2.059 4.082-1.189 7.604-3.652 9.448-7.401 2.666-4.915 2.094-10.864 2.07-17.444.06-10.735.001-21.468.001-32.237z"
        }
      )
    ] });
  if (language === "php")
    return (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 800 400", children: (0, import_jsx_runtime.jsx)("g", { transform: "translate(-44.632 -141.55)", children: (0, import_jsx_runtime.jsxs)("g", { transform: "matrix(8.3528 0 0 8.3119 -727.13 -3759.5)", children: [
      (0, import_jsx_runtime.jsx)("path", { d: "m99.974 479.48h14.204c4.1693 0.0354 7.1903 1.2367 9.063 3.604 1.8726 2.3674 2.491 5.6004 1.855 9.699-0.24737 1.8727-0.79504 3.71-1.643 5.512-0.8127 1.802-1.9434 3.4273-3.392 4.876-1.7667 1.8373-3.657 3.0033-5.671 3.498-2.014 0.49467-4.0987 0.742-6.254 0.742h-6.36l-2.014 10.07h-7.367l7.579-38.001m6.201 6.042-3.18 15.9c0.21198 0.0353 0.42398 0.053 0.636 0.053h0.742c3.392 0.0353 6.2186-0.30033 8.48-1.007 2.2613-0.74199 3.7806-3.3213 4.558-7.738 0.63597-3.71-0.00003-5.8476-1.908-6.413-1.8727-0.56531-4.2224-0.83031-7.049-0.795-0.42402 0.0353-0.83035 0.053-1.219 0.053-0.35335 0.00002-0.72435 0.00002-1.113 0l0.053-0.053" }),
      (0, import_jsx_runtime.jsx)("path", { d: "m133.49 469.36h7.314l-2.067 10.123h6.572c3.604 0.0707 6.2893 0.81269 8.056 2.226 1.802 1.4134 2.332 4.0987 1.59 8.056l-3.551 17.649h-7.42l3.392-16.854c0.35328-1.7666 0.2473-3.021-0.318-3.763-0.56536-0.74198-1.7844-1.113-3.657-1.113l-5.883-0.053-4.346 21.783h-7.314l7.632-38.054" }),
      (0, import_jsx_runtime.jsx)("path", { d: "m162.81 479.48h14.204c4.1693 0.0354 7.1903 1.2367 9.063 3.604 1.8726 2.3674 2.491 5.6004 1.855 9.699-0.24737 1.8727-0.79503 3.71-1.643 5.512-0.8127 1.802-1.9434 3.4273-3.392 4.876-1.7667 1.8373-3.657 3.0033-5.671 3.498-2.014 0.49467-4.0987 0.742-6.254 0.742h-6.36l-2.014 10.07h-7.367l7.579-38.001m6.201 6.042-3.18 15.9c0.21199 0.0353 0.42399 0.053 0.636 0.053h0.742c3.392 0.0353 6.2186-0.30033 8.48-1.007 2.2613-0.74199 3.7806-3.3213 4.558-7.738 0.63597-3.71-0.00003-5.8476-1.908-6.413-1.8727-0.56531-4.2224-0.83031-7.049-0.795-0.42402 0.0353-0.83035 0.053-1.219 0.053-0.35335 0.00002-0.72435 0.00002-1.113 0l0.053-0.053" })
    ] }) }) });
  if (language === "json")
    return (0, import_jsx_runtime.jsxs)(
      "svg",
      {
        xmlns: "http://www.w3.org/2000/svg",
        xmlnsXlink: "http://www.w3.org/1999/xlink",
        viewBox: "0 0 160 160",
        children: [
          (0, import_jsx_runtime.jsxs)("defs", { children: [
            (0, import_jsx_runtime.jsxs)("linearGradient", { id: "a", children: [
              (0, import_jsx_runtime.jsx)("stop", { offset: "0" }),
              (0, import_jsx_runtime.jsx)("stop", { offset: "1", stopColor: "#fff" })
            ] }),
            (0, import_jsx_runtime.jsx)(
              "linearGradient",
              {
                x1: "-553.27",
                y1: "525.908",
                x2: "-666.116",
                y2: "413.045",
                id: "c",
                xlinkHref: "#a",
                gradientUnits: "userSpaceOnUse",
                gradientTransform: "matrix(.99884 0 0 .9987 689.008 -388.844)"
              }
            ),
            (0, import_jsx_runtime.jsx)(
              "linearGradient",
              {
                x1: "-666.117",
                y1: "413.045",
                x2: "-553.27",
                y2: "525.908",
                id: "b",
                xlinkHref: "#a",
                gradientUnits: "userSpaceOnUse",
                gradientTransform: "matrix(.99884 0 0 .9987 689.008 -388.844)"
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              d: "M79.865 119.1c35.397 48.255 70.04-13.469 69.988-50.587-.06-43.886-44.54-68.414-70.017-68.414C38.943.1 0 33.895 0 80.135 0 131.531 44.64 160 79.836 160c-7.965-1.147-34.507-6.834-34.863-67.967-.24-41.346 13.487-57.865 34.805-50.599.477.177 23.514 9.265 23.514 38.95 0 29.56-23.427 38.716-23.427 38.716z",
              style: { marker: "none" },
              color: "#000",
              fill: "url(#b)",
              fillRule: "evenodd",
              overflow: "visible"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "path",
            {
              d: "M79.823 41.4C56.433 33.34 27.78 52.618 27.78 91.23c0 63.048 46.72 68.77 52.384 68.77C121.057 160 160 126.204 160 79.964 160 28.568 115.36.1 80.164.1c9.749-1.35 52.541 10.55 52.541 69.037 0 38.141-31.953 58.905-52.735 50.033-.478-.177-23.514-9.264-23.514-38.95 0-29.56 23.367-38.818 23.367-38.818z",
              style: { marker: "none" },
              color: "#000",
              fill: "url(#c)",
              fillRule: "evenodd",
              overflow: "visible"
            }
          )
        ]
      }
    );
}
var PreviewContainer = dt(Box)`
  position: relative;
`;
function PreviewCode(props) {
  const { selection, schemaType: type } = props, { languageMode } = useLanguageMode(type, props.selection), CodeMirror = useCodeMirror();
  return (0, import_jsx_runtime.jsx)(PreviewContainer, { children: (0, import_jsx_runtime.jsxs)(Card, { padding: 4, children: [
    selection != null && selection.filename || selection != null && selection.language ? (0, import_jsx_runtime.jsx)(
      Card,
      {
        paddingBottom: 4,
        marginBottom: selection.code ? 4 : 0,
        borderBottom: !!selection.code,
        children: (0, import_jsx_runtime.jsxs)(Flex, { align: "center", justify: "flex-end", children: [
          selection != null && selection.filename ? (0, import_jsx_runtime.jsx)(Box, { flex: 1, children: (0, import_jsx_runtime.jsx)(Text, { children: (0, import_jsx_runtime.jsx)("code", { children: selection.filename }) }) }) : null,
          selection != null && selection.language ? (0, import_jsx_runtime.jsx)(Label, { muted: true, children: selection.language }) : null
        ] })
      }
    ) : null,
    CodeMirror && (0, import_jsx_runtime.jsx)(import_react.Suspense, { fallback: (0, import_jsx_runtime.jsx)(Card, { padding: 2, children: "Loading code preview..." }), children: (0, import_jsx_runtime.jsx)(
      CodeMirror,
      {
        readOnly: true,
        editable: false,
        value: (selection == null ? void 0 : selection.code) || "",
        highlightLines: (selection == null ? void 0 : selection.highlightedLines) || [],
        basicSetup: {
          lineNumbers: false,
          foldGutter: false,
          highlightSelectionMatches: false,
          highlightActiveLineGutter: false,
          highlightActiveLine: false
        },
        languageMode
      }
    ) })
  ] }) });
}
var codeTypeName = "code";
var codeSchema = defineType({
  name: "code",
  type: "object",
  title: "Code",
  components: { input: CodeInput, preview: PreviewCode },
  icon: CodeBlockIcon,
  fields: [
    {
      name: "language",
      title: "Language",
      type: "string"
    },
    {
      name: "filename",
      title: "Filename",
      type: "string"
    },
    {
      title: "Code",
      name: "code",
      type: "text"
    },
    {
      title: "Highlighted lines",
      name: "highlightedLines",
      type: "array",
      of: [
        {
          type: "number",
          title: "Highlighted line"
        }
      ]
    }
  ],
  preview: {
    select: {
      language: "language",
      code: "code",
      filename: "filename",
      highlightedLines: "highlightedLines"
    },
    prepare: (value) => ({
      title: value.filename || (value.language || "unknown").toUpperCase(),
      media: getMedia(value == null ? void 0 : value.language),
      selection: value
    })
  }
});
var CodeInputConfigContext = (0, import_react.createContext)(void 0);
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    __hasOwnProp.call(b, prop) && __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b))
      __propIsEnum.call(b, prop) && __defNormalProp(a, prop, b[prop]);
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var codeInput = definePlugin((config) => {
  const codeModes = config && config.codeModes, basePlugin = {
    name: "@sanity/code-input",
    schema: { types: [codeSchema] }
  };
  return codeModes ? __spreadProps(__spreadValues({}, basePlugin), {
    form: {
      components: {
        input: (props) => props.id !== "root" ? props.renderDefault(props) : (0, import_jsx_runtime.jsx)(CodeInputConfigContext.Provider, { value: config, children: props.renderDefault(props) })
      }
    }
  }) : basePlugin;
});

export {
  PreviewCode,
  codeTypeName,
  codeSchema,
  CodeInputConfigContext,
  codeInput
};
//# sourceMappingURL=chunk-7YEY24D2.js.map

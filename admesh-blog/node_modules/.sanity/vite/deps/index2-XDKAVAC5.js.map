{"version": 3, "sources": ["../../../../../node_modules/sanity/src/structure/panes/list/ListPaneContent.tsx", "../../../../../node_modules/sanity/src/structure/panes/list/ListPaneHeader.tsx", "../../../../../node_modules/sanity/src/structure/panes/list/ListPane.tsx"], "sourcesContent": ["import {Box, Text} from '@sanity/ui'\nimport {useCallback} from 'react'\nimport {\n  CommandList,\n  type CommandListItemContext,\n  type GeneralPreviewLayoutKey,\n  useGetI18nText,\n  useI18nText,\n} from 'sanity'\nimport {styled} from 'styled-components'\n\nimport {PaneContent, PaneItem, usePaneLayout} from '../../components'\nimport {type PaneListItem, type PaneListItemDivider} from '../../types'\n\ninterface ListPaneContentProps {\n  childItemId?: string\n  isActive?: boolean\n  items: (PaneListItem<unknown> | PaneListItemDivider)[] | undefined\n  layout?: GeneralPreviewLayoutKey\n  showIcons: boolean\n  title: string\n}\n\nconst DividerContainer = styled(Box)`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin: 0.75rem 0 0.25rem 0;\n`\n\nconst Divider = styled.hr`\n  flex: 1;\n  background-color: var(--card-border-color);\n  height: 1px;\n  margin: 0;\n  border: none;\n`\n\nconst DividerTitle = styled(Text)`\n  padding-bottom: 0.75rem;\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n`\n\ninterface DividerItemProps {\n  item: PaneListItemDivider\n}\n\nfunction DividerItem({item}: DividerItemProps) {\n  const {title: dividerTitle} = useI18nText(item)\n  return (\n    <DividerContainer>\n      <DividerTitle weight=\"semibold\" muted size={1}>\n        {dividerTitle}\n      </DividerTitle>\n\n      <Divider />\n    </DividerContainer>\n  )\n}\n\n/**\n * @internal\n */\nexport function ListPaneContent(props: ListPaneContentProps) {\n  const {childItemId, items, isActive, layout, showIcons, title} = props\n  const {collapsed: layoutCollapsed} = usePaneLayout()\n  const getI18nText = useGetI18nText(\n    items?.filter(\n      (item): item is Exclude<typeof item, {type: 'divider'}> => item.type !== 'divider',\n    ),\n  )\n\n  const getItemDisabled = useCallback(\n    (itemIndex: number) => {\n      return items?.find((_, i) => i === itemIndex)?.type === 'divider'\n    },\n    [items],\n  )\n\n  const shouldShowIconForItem = useCallback(\n    (item: PaneListItem): boolean => {\n      const itemShowIcon = item.displayOptions?.showIcon\n\n      // Specific true/false on item should have precedence over list setting\n      if (typeof itemShowIcon !== 'undefined') {\n        return itemShowIcon !== false // Boolean(item.icon)\n      }\n\n      // If no item setting is defined, defer to the pane settings\n      return showIcons !== false // Boolean(item.icon)\n    },\n    [showIcons],\n  )\n\n  const renderItem = useCallback(\n    (item: PaneListItem<unknown> | PaneListItemDivider, ctx: CommandListItemContext) => {\n      const {virtualIndex: itemIndex} = ctx\n\n      if (item.type === 'divider') {\n        return (\n          <Box key={`divider-${itemIndex}`} marginBottom={1}>\n            {item.title ? <DividerItem item={item} /> : <Divider />}\n          </Box>\n        )\n      }\n\n      const pressed = !isActive && childItemId === item.id\n      const selected = isActive && childItemId === item.id\n      // If this is a document list item, pass on the ID and type,\n      // otherwise leave it undefined to use the passed title and gang\n      const value =\n        item._id && item.schemaType\n          ? {_id: item._id, _type: item.schemaType.name, title: item.title}\n          : undefined\n\n      return (\n        <PaneItem\n          icon={shouldShowIconForItem(item) ? item.icon : false}\n          id={item.id}\n          key={item.id}\n          layout={layout}\n          marginBottom={1}\n          pressed={pressed}\n          schemaType={item.schemaType}\n          selected={selected}\n          title={getI18nText(item).title}\n          value={value}\n        />\n      )\n    },\n    [childItemId, getI18nText, isActive, layout, shouldShowIconForItem],\n  )\n\n  return (\n    <PaneContent overflow={layoutCollapsed ? 'hidden' : 'auto'}>\n      {items && items.length > 0 && (\n        <CommandList\n          activeItemDataAttr=\"data-hovered\"\n          ariaLabel={title}\n          canReceiveFocus\n          getItemDisabled={getItemDisabled}\n          itemHeight={51}\n          items={items}\n          onlyShowSelectionWhenActive\n          paddingBottom={1}\n          paddingX={3}\n          renderItem={renderItem}\n          wrapAround={false}\n        />\n      )}\n    </PaneContent>\n  )\n}\n", "import {ArrowLeftIcon} from '@sanity/icons'\n\nimport {Button} from '../../../ui-components'\nimport {BackLink, PaneHeader, PaneHeaderActions, usePane} from '../../components'\nimport {type PaneMenuItem, type PaneMenuItemGroup} from '../../types'\nimport {useStructureTool} from '../../useStructureTool'\n\ninterface ListPaneHeaderProps {\n  index: number\n  menuItems?: PaneMenuItem[]\n  menuItemGroups?: PaneMenuItemGroup[]\n  title: string\n}\n\nexport const ListPaneHeader = ({index, menuItems, menuItemGroups, title}: ListPaneHeaderProps) => {\n  const {features} = useStructureTool()\n  const {collapsed, isLast} = usePane()\n  // Prevent focus if this is the last (non-collapsed) pane.\n  const tabIndex = isLast && !collapsed ? -1 : 0\n\n  return (\n    <PaneHeader\n      actions={<PaneHeaderActions menuItems={menuItems} menuItemGroups={menuItemGroups} />}\n      backButton={\n        features.backButton &&\n        index > 0 && (\n          <Button\n            as={BackLink}\n            data-as=\"a\"\n            icon={ArrowLeftIcon}\n            mode=\"bleed\"\n            tooltipProps={{content: 'Back'}}\n          />\n        )\n      }\n      tabIndex={tabIndex}\n      title={title}\n    />\n  )\n}\n", "import {Card, Code} from '@sanity/ui'\nimport {useI18nText} from 'sanity'\n\nimport {Pane} from '../../components'\nimport {_DEBUG} from '../../constants'\nimport {type BaseStructureToolPaneProps} from '../types'\nimport {ListPaneContent} from './ListPaneContent'\nimport {ListPaneHeader} from './ListPaneHeader'\n\ntype ListPaneProps = BaseStructureToolPaneProps<'list'>\n\n/**\n * @internal\n */\nexport function ListPane(props: ListPaneProps) {\n  const {childItemId, index, isActive, isSelected, pane, paneKey} = props\n\n  const {defaultLayout, displayOptions, items, menuItems, menuItemGroups} = pane\n  const showIcons = displayOptions?.showIcons !== false\n  const {title} = useI18nText(pane)\n\n  return (\n    <Pane\n      currentMaxWidth={350}\n      data-testid=\"structure-tool-list-pane\"\n      data-ui=\"ListPane\"\n      id={paneKey}\n      maxWidth={640}\n      minWidth={320}\n      selected={isSelected}\n    >\n      {_DEBUG && (\n        <Card padding={4} tone=\"transparent\">\n          <Code>{pane.source || '(none)'}</Code>\n        </Card>\n      )}\n\n      <ListPaneHeader\n        index={index}\n        menuItems={menuItems}\n        menuItemGroups={menuItemGroups}\n        title={title}\n      />\n\n      <ListPaneContent\n        childItemId={childItemId}\n        isActive={isActive}\n        items={items}\n        layout={defaultLayout}\n        key={paneKey}\n        showIcons={showIcons}\n        title={title}\n      />\n    </Pane>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAMA,mBAAmBC,GAAOC,GAAG;;;;;;AAAnC,IAOMC,UAAUF,GAAOG;;;;;;;AAPvB,IAeMC,eAAeJ,GAAOK,IAAI;;;;;AAUhC,SAAAC,YAAAC,IAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GAAqB;IAAAC;EAAAA,IAAAH,IACnB;IAAAI,OAAAC;EAAAA,IAA8BC,YAAYH,IAAI;AAACI,MAAAA;AAAAN,IAAAA,CAAAA,MAAAI,gBAG3CE,SAAAA,wBAAC,cAAoB,EAAA,QAAA,YAAW,OAAA,MAAY,MAAC,GAAA,UAE7C,aAAA,CAAA,GAAeN,EAAAA,CAAAA,IAAAI,cAAAJ,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAO,MAAAA;AAAAP,IAAA,CAAA,MAAAQ,OAAAC,IAAA,2BAAA,KAEfF,SAAC,wBAAA,SAAA,CAAU,CAAA,GAAAP,EAAAA,CAAAA,IAAAO,MAAAA,KAAAP,EAAA,CAAA;AAAAU,MAAAA;AAAA,SAAAV,EAAAA,CAAAA,MAAAM,MALbI,SAAAA,yBAAC,kBACCJ,EAAAA,UAAAA;IAAAA;IAIAC;EAAAA,EAAAA,CACF,GAAmBP,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAU,MAAAA,KAAAV,EAAA,CAAA,GANnBU;AAMmB;AAOhB,SAAAC,gBAAAC,OAAA;AAAAZ,QAAAA,QAAAC,iCAAA,EAAA,GACL;IAAAY;IAAAC;IAAAC;IAAAC;IAAAC;IAAAd;EAAAA,IAAiES,OACjE;IAAAM,WAAAC;EAAAA,IAAqCC,cAAc;AAACrB,MAAAA;AAAAC,IAAAA,CAAAA,MAAAc,SAElDf,KAAAe,+BAAKO,OAAAC,QAEJtB,EAAAA,CAAAA,IAAAc,OAAAd,EAAAA,CAAAA,IAAAD,MAAAA,KAAAC,EAAA,CAAA;AAHHuB,QAAAA,cAAoBC,eAClBzB,EAGF;AAACO,MAAAA;AAAAN,IAAAA,CAAAA,MAAAc,SAGCR,KAAAmB,CACSX,cAAAA;;AAAAA,iDAAKY,KAAAC,CAAAA,GAAAC,MAAiBA,MAAMH,eAA5BX,mBAAqCe,UAAY;KACzD7B,EAAAA,CAAAA,IAAAc,OAAAd,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAHH,QAAA8B,kBAAwBxB;AAKvBC,MAAAA;AAAAP,IAAAA,CAAAA,MAAAiB,aAGCV,KAAAwB,CAAA,WAAA;;AACEC,UAAAA,gBAAqB9B,YAAI+B,mBAAJ/B,mBAAIgC;AAAyB,WAG9C,OAAOF,eAAiB,MACnBA,iBAAsB,QAIxBf,cAAmB;EAAA,GAC3BjB,EAAAA,CAAAA,IAAAiB,WAAAjB,EAAAA,CAAAA,IAAAO,MAAAA,KAAAP,EAAA,CAAA;AAXH,QAAAmC,wBAA8B5B;AAa7BG,MAAAA;AAAAV,IAAAa,CAAAA,MAAAA,eAAAb,EAAA,CAAA,MAAAuB,eAAAvB,EAAAe,CAAAA,MAAAA,YAAAf,EAAA,CAAA,MAAAgB,UAAAhB,EAAAA,EAAAA,MAAAmC,yBAGCzB,KAAAA,CAAA0B,QAAAC,QAAA;AACE,UAAA;MAAAC,cAAAC;IAAAA,IAAkCF;AAAG,QAEjCnC,OAAI2B,SAAU;AAAS,iBAAA,wBAEtB,KAA+C,EAAA,cAAC,GAC9C3B,UAAAA,OAAIC,YAAU,wBAAA,aAAA,EAAkBD,MAAG,OAAA,CAAK,QAAI,wBAAA,SAAA,CAAA,CAAO,EADjCuB,GAAAA,WAAAA,WAAS,EAE9B;AAIJ,UAAAe,UAAgB,CAACzB,YAAYF,gBAAgBX,OAAIuC,IACjDC,WAAiB3B,YAAYF,gBAAgBX,OAAIuC,IAGjDE,QACEzC,OAAI0C,OAAQ1C,OAAI2C,aAAW;MAAAD,KACjB1C,OAAI0C;MAAAE,OAAa5C,OAAI2C,WAAAE;MAAA5C,OAAyBD,OAAIC;IAAAA,IAAA6C;AAI5D,eAAA,wBAAC,UACO,EAAA,MAAAb,sBAAsBjC,MAAI,IAAIA,OAAI+C,OAAAA,OACpC,IAAA/C,OAAIuC,IAEAzB,QACM,cAAC,GACNwB,SACG,YAAAtC,OAAI2C,YACNH,UACH,OAAAnB,YAAYrB,MAAI,EAACC,OACjBwC,MAPFzC,GAAAA,OAAIuC,EAQT;EAAA,GAELzC,EAAAA,CAAAA,IAAAa,aAAAb,EAAAA,CAAAA,IAAAuB,aAAAvB,EAAAA,CAAAA,IAAAe,UAAAf,EAAAA,CAAAA,IAAAgB,QAAAhB,EAAAA,EAAAA,IAAAmC,uBAAAnC,EAAAA,EAAAA,IAAAU,MAAAA,KAAAV,EAAA,EAAA;AAnCH,QAAAkD,aAAmBxC,IAwCMyC,KAAAhC,kBAAkB,WAAW;AAAMiC,MAAAA;AAAApD,IAAA,EAAA,MAAA8B,mBAAA9B,EAAAc,EAAAA,MAAAA,SAAAd,EAAAkD,EAAAA,MAAAA,cAAAlD,EAAAA,EAAAA,MAAAG,SACvDiD,KAAAtC,SAASA,MAAKuC,SAAW,SACvB,wBAAA,aAAA,EACoB,oBAAA,gBACRlD,WAAAA,OACX,iBAAc,MACG2B,iBACL,YAAA,IACLhB,OACP,6BAAA,MACe,eAAC,GACN,UAAC,GACCoC,YACA,YAAA,MAAA,CAEf,GAAAlD,EAAAA,EAAAA,IAAA8B,iBAAA9B,EAAAA,EAAAA,IAAAc,OAAAd,EAAAA,EAAAA,IAAAkD,YAAAlD,EAAAA,EAAAA,IAAAG,OAAAH,EAAAA,EAAAA,IAAAoD,MAAAA,KAAApD,EAAA,EAAA;AAAAsD,MAAAA;AAAA,SAAAtD,EAAAmD,EAAAA,MAAAA,MAAAnD,EAAAA,EAAAA,MAAAoD,MAfHE,SAAC,wBAAA,aAAsB,EAAA,UAAAH,IACpBC,UAeH,GAAA,CAAA,GAAcpD,EAAAA,EAAAA,IAAAmD,IAAAnD,EAAAA,EAAAA,IAAAoD,IAAApD,EAAAA,EAAAA,IAAAsD,MAAAA,KAAAtD,EAAA,EAAA,GAhBdsD;AAgBc;AAvFX,SAAAhC,MAAApB,MAAA;AAAA,SAK0DA,KAAI2B,SAAU;AAAS;ACvDjF,IAAM0B,iBAAiBxD,CAAA,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GAAC;IAAAuD;IAAAC;IAAAC;IAAAvD;EAAAA,IAAAJ,IAC7B;IAAA4D;EAAA,IAAmBC,iBAAAA,GACnB;IAAA1C;IAAA2C;EAAAA,IAA4BC,QAAQ,GAEpCC,WAAiBF,UAAM,CAAK3C,YAAkB,KAAA;AAAAZ,MAAAA;AAAAN,IAAA0D,CAAAA,MAAAA,kBAAA1D,EAAAA,CAAAA,MAAAyD,aAIjCnD,SAAC,wBAAA,mBAA6BmD,EAAAA,WAA2BC,eAAkB,CAAA,GAAA1D,EAAAA,CAAAA,IAAA0D,gBAAA1D,EAAAA,CAAAA,IAAAyD,WAAAzD,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAO,MAAAA;AAAAP,IAAAA,CAAAA,MAAA2D,SAAAK,cAAAhE,EAAAA,CAAAA,MAAAwD,SAElFjD,KAAAoD,SAAQK,cACRR,QAAS,SACP,wBAAC,QACKS,EAAAA,IAAOA,UACH,WAAA,KACFC,MAAAA,eACD,MAAA,SACS,cAAA;IAAAC,SAAU;EAAA,EAE3B,CAAA,GAAAnE,EAAA,CAAA,IAAA2D,SAAAK,YAAAhE,EAAAA,CAAAA,IAAAwD,OAAAxD,EAAAA,CAAAA,IAAAO,MAAAA,KAAAP,EAAA,CAAA;AAAAU,MAAAA;AAAAV,SAAAA,EAAA,CAAA,MAAAM,MAAAN,EAAAO,CAAAA,MAAAA,MAAAP,EAAA+D,CAAAA,MAAAA,YAAA/D,EAAAA,CAAAA,MAAAG,SAZLO,SAAAA,wBAAC,cACU,EAAA,SAAAJ,IAEP,YAAAC,IAWQwD,UACH5D,MACP,CAAA,GAAAH,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAO,IAAAP,EAAAA,CAAAA,IAAA+D,UAAA/D,EAAAA,CAAAA,IAAAG,OAAAH,EAAAA,EAAAA,IAAAU,MAAAA,KAAAV,EAAA,EAAA,GAhBFU;AAgBE;ACvBC,SAAA0D,SAAAxD,OAAA;AAAAZ,QAAAA,QAAAC,iCAAA,EAAA,GACL;IAAAY;IAAA2C;IAAAzC;IAAAsD;IAAAC;IAAAC;EAAAA,IAAkE3D,OAElE;IAAA4D;IAAAvC;IAAAnB;IAAA2C;IAAAC;EAA0EY,IAAAA,MAC1ErD,aAAkBgB,iDAAchB,eAAqB,OACrD;IAAAd;EAAAA,IAAgBE,YAAYiE,IAAI;AAACvE,MAAAA;AAAAC,IAAA,CAAA,MAAAsE,KAAAG,UAY5B1E,KAAA2E,QAIA1E,EAAA,CAAA,IAAAsE,KAAAG,QAAAzE,EAAAA,CAAAA,IAAAD,MAAAA,KAAAC,EAAA,CAAA;AAAAM,MAAAA;AAAAN,IAAA,CAAA,MAAAwD,SAAAxD,EAAA0D,CAAAA,MAAAA,kBAAA1D,EAAAyD,CAAAA,MAAAA,aAAAzD,EAAAA,CAAAA,MAAAG,SAEDG,SAAC,wBAAA,gBAAA,EACQkD,OACIC,WACKC,gBACTvD,MACP,CAAA,GAAAH,EAAAA,CAAAA,IAAAwD,OAAAxD,EAAAA,CAAAA,IAAA0D,gBAAA1D,EAAAA,CAAAA,IAAAyD,WAAAzD,EAAAA,CAAAA,IAAAG,OAAAH,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAO,MAAAA;AAAAP,IAAAa,CAAAA,MAAAA,eAAAb,EAAAwE,CAAAA,MAAAA,iBAAAxE,EAAAe,CAAAA,MAAAA,YAAAf,EAAAA,EAAAA,MAAAc,SAAAd,EAAA,EAAA,MAAAuE,WAAAvE,EAAA,EAAA,MAAAiB,aAAAjB,EAAA,EAAA,MAAAG,SAEFI,SAAAA,wBAAC,iBACcM,EAAAA,aACHE,UACHD,OACC0D,QAAAA,eAEGvD,WACJd,MAAAA,GAAAA,OACP,GAAAH,EAAAA,CAAAA,IAAAa,aAAAb,EAAAA,CAAAA,IAAAwE,eAAAxE,EAAAA,CAAAA,IAAAe,UAAAf,EAAAA,EAAAA,IAAAc,OAAAd,EAAAA,EAAAA,IAAAuE,SAAAvE,EAAAA,EAAAA,IAAAiB,WAAAjB,EAAAA,EAAAA,IAAAG,OAAAH,EAAAA,EAAAA,IAAAO,MAAAA,KAAAP,EAAA,EAAA;AAAAU,MAAAA;AAAA,SAAAV,EAAAqE,EAAAA,MAAAA,cAAArE,EAAA,EAAA,MAAAuE,WAAAvE,EAAAD,EAAAA,MAAAA,MAAAC,EAAA,EAAA,MAAAM,MAAAN,EAAAA,EAAAA,MAAAO,MA9BJG,SAAC,yBAAA,MAAA,EACkB,iBAAE,KACP,eAAA,4BACJ,WAAA,YACJ6D,IAAAA,SACM,UAAA,KACA,UAAA,KACAF,UAAAA,YAETtE,UAAAA;IAAAA;IAMDO;IAOAC;EAAAA,EAAAA,CASF,GAAOP,EAAAA,EAAAA,IAAAqE,YAAArE,EAAAA,EAAAA,IAAAuE,SAAAvE,EAAAA,EAAAA,IAAAD,IAAAC,EAAAA,EAAAA,IAAAM,IAAAN,EAAAA,EAAAA,IAAAO,IAAAP,EAAAA,EAAAA,IAAAU,MAAAA,KAAAV,EAAA,EAAA,GA/BPU;AA+BO;", "names": ["Divider<PERSON><PERSON><PERSON>", "styled", "Box", "Divider", "hr", "DividerTitle", "Text", "DividerItem", "t0", "$", "_c", "item", "title", "dividerTitle", "useI18nText", "t1", "t2", "Symbol", "for", "t3", "ListPaneContent", "props", "childItemId", "items", "isActive", "layout", "showIcons", "collapsed", "layoutCollapsed", "usePaneLayout", "filter", "_temp", "getI18nText", "useGetI18nText", "itemIndex", "find", "_", "i", "type", "getItemDisabled", "item_0", "itemShowIcon", "displayOptions", "showIcon", "shouldShowIconForItem", "item_1", "ctx", "virtualIndex", "itemIndex_0", "pressed", "id", "selected", "value", "_id", "schemaType", "_type", "name", "undefined", "icon", "renderItem", "t4", "t5", "length", "t6", "ListPaneHeader", "index", "menuItems", "menuItemGroups", "features", "useStructureTool", "isLast", "usePane", "tabIndex", "backButton", "BackLink", "ArrowLeftIcon", "content", "ListPane", "isSelected", "pane", "paneKey", "defaultLayout", "source", "_DEBUG"]}
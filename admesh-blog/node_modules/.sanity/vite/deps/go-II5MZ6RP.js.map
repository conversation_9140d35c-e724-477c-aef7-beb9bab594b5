{"version": 3, "sources": ["../../../../../node_modules/@codemirror/legacy-modes/mode/go.js"], "sourcesContent": ["var keywords = {\n  \"break\":true, \"case\":true, \"chan\":true, \"const\":true, \"continue\":true,\n  \"default\":true, \"defer\":true, \"else\":true, \"fallthrough\":true, \"for\":true,\n  \"func\":true, \"go\":true, \"goto\":true, \"if\":true, \"import\":true,\n  \"interface\":true, \"map\":true, \"package\":true, \"range\":true, \"return\":true,\n  \"select\":true, \"struct\":true, \"switch\":true, \"type\":true, \"var\":true,\n  \"bool\":true, \"byte\":true, \"complex64\":true, \"complex128\":true,\n  \"float32\":true, \"float64\":true, \"int8\":true, \"int16\":true, \"int32\":true,\n  \"int64\":true, \"string\":true, \"uint8\":true, \"uint16\":true, \"uint32\":true,\n  \"uint64\":true, \"int\":true, \"uint\":true, \"uintptr\":true, \"error\": true,\n  \"rune\":true, \"any\":true, \"comparable\":true\n};\n\nvar atoms = {\n  \"true\":true, \"false\":true, \"iota\":true, \"nil\":true, \"append\":true,\n  \"cap\":true, \"close\":true, \"complex\":true, \"copy\":true, \"delete\":true, \"imag\":true,\n  \"len\":true, \"make\":true, \"new\":true, \"panic\":true, \"print\":true,\n  \"println\":true, \"real\":true, \"recover\":true\n};\n\nvar isOperatorChar = /[+\\-*&^%:=<>!|\\/]/;\n\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\" || ch == \"`\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\d\\.]/.test(ch)) {\n    if (ch == \".\") {\n      stream.match(/^[0-9]+([eE][\\-+]?[0-9]+)?/);\n    } else if (ch == \"0\") {\n      stream.match(/^[xX][0-9a-fA-F]+/) || stream.match(/^0[0-7]+/);\n    } else {\n      stream.match(/^[0-9]*\\.?[0-9]*([eE][\\-+]?[0-9]+)?/);\n    }\n    return \"number\";\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) {\n    if (cur == \"case\" || cur == \"default\") curPunc = \"case\";\n    return \"keyword\";\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && quote != \"`\" && next == \"\\\\\";\n    }\n    if (end || !(escaped || quote == \"`\"))\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\nfunction popContext(state) {\n  if (!state.context.prev) return;\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const go = {\n  name: \"go\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n      if (ctx.type == \"case\") ctx.type = \"}\";\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"case\") ctx.type = \"case\";\n    else if (curPunc == \"}\" && ctx.type == \"}\") popContext(state);\n    else if (curPunc == ctx.type) popContext(state);\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return null;\n    var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n    if (ctx.type == \"case\" && /^(?:case|default)\\b/.test(textAfter)) return ctx.indented;\n    var closing = firstChar == ctx.type;\n    if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s([{}]|case |default\\s*:)$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI,WAAW;AAAA,EACb,SAAQ;AAAA,EAAM,QAAO;AAAA,EAAM,QAAO;AAAA,EAAM,SAAQ;AAAA,EAAM,YAAW;AAAA,EACjE,WAAU;AAAA,EAAM,SAAQ;AAAA,EAAM,QAAO;AAAA,EAAM,eAAc;AAAA,EAAM,OAAM;AAAA,EACrE,QAAO;AAAA,EAAM,MAAK;AAAA,EAAM,QAAO;AAAA,EAAM,MAAK;AAAA,EAAM,UAAS;AAAA,EACzD,aAAY;AAAA,EAAM,OAAM;AAAA,EAAM,WAAU;AAAA,EAAM,SAAQ;AAAA,EAAM,UAAS;AAAA,EACrE,UAAS;AAAA,EAAM,UAAS;AAAA,EAAM,UAAS;AAAA,EAAM,QAAO;AAAA,EAAM,OAAM;AAAA,EAChE,QAAO;AAAA,EAAM,QAAO;AAAA,EAAM,aAAY;AAAA,EAAM,cAAa;AAAA,EACzD,WAAU;AAAA,EAAM,WAAU;AAAA,EAAM,QAAO;AAAA,EAAM,SAAQ;AAAA,EAAM,SAAQ;AAAA,EACnE,SAAQ;AAAA,EAAM,UAAS;AAAA,EAAM,SAAQ;AAAA,EAAM,UAAS;AAAA,EAAM,UAAS;AAAA,EACnE,UAAS;AAAA,EAAM,OAAM;AAAA,EAAM,QAAO;AAAA,EAAM,WAAU;AAAA,EAAM,SAAS;AAAA,EACjE,QAAO;AAAA,EAAM,OAAM;AAAA,EAAM,cAAa;AACxC;AAEA,IAAI,QAAQ;AAAA,EACV,QAAO;AAAA,EAAM,SAAQ;AAAA,EAAM,QAAO;AAAA,EAAM,OAAM;AAAA,EAAM,UAAS;AAAA,EAC7D,OAAM;AAAA,EAAM,SAAQ;AAAA,EAAM,WAAU;AAAA,EAAM,QAAO;AAAA,EAAM,UAAS;AAAA,EAAM,QAAO;AAAA,EAC7E,OAAM;AAAA,EAAM,QAAO;AAAA,EAAM,OAAM;AAAA,EAAM,SAAQ;AAAA,EAAM,SAAQ;AAAA,EAC3D,WAAU;AAAA,EAAM,QAAO;AAAA,EAAM,WAAU;AACzC;AAEA,IAAI,iBAAiB;AAErB,IAAI;AAEJ,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACA,MAAI,SAAS,KAAK,EAAE,GAAG;AACrB,QAAI,MAAM,KAAK;AACb,aAAO,MAAM,4BAA4B;AAAA,IAC3C,WAAW,MAAM,KAAK;AACpB,aAAO,MAAM,mBAAmB,KAAK,OAAO,MAAM,UAAU;AAAA,IAC9D,OAAO;AACL,aAAO,MAAM,qCAAqC;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AACA,MAAI,qBAAqB,KAAK,EAAE,GAAG;AACjC,cAAU;AACV,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,oBAAoB;AACpC,MAAI,MAAM,OAAO,QAAQ;AACzB,MAAI,SAAS,qBAAqB,GAAG,GAAG;AACtC,QAAI,OAAO,UAAU,OAAO,UAAW,WAAU;AACjD,WAAO;AAAA,EACT;AACA,MAAI,MAAM,qBAAqB,GAAG,EAAG,QAAO;AAC5C,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAAC,cAAM;AAAM;AAAA,MAAM;AAClD,gBAAU,CAAC,WAAW,SAAS,OAAO,QAAQ;AAAA,IAChD;AACA,QAAI,OAAO,EAAE,WAAW,SAAS;AAC/B,YAAM,WAAW;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,OAAO;AACd;AACA,SAAS,YAAY,OAAO,KAAK,MAAM;AACrC,SAAO,MAAM,UAAU,IAAI,QAAQ,MAAM,UAAU,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,CAAC,MAAM,QAAQ,KAAM;AACzB,MAAI,IAAI,MAAM,QAAQ;AACtB,MAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AAC/B,UAAM,WAAW,MAAM,QAAQ;AACjC,SAAO,MAAM,UAAU,MAAM,QAAQ;AACvC;AAIO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO,KAAK;AAAA,MACjD,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,MAAM;AAChB,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,IAAI,SAAS,KAAM,KAAI,QAAQ;AACnC,YAAM,WAAW,OAAO,YAAY;AACpC,YAAM,cAAc;AACpB,UAAI,IAAI,QAAQ,OAAQ,KAAI,OAAO;AAAA,IACrC;AACA,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,cAAU;AACV,QAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,QAAI,SAAS,UAAW,QAAO;AAC/B,QAAI,IAAI,SAAS,KAAM,KAAI,QAAQ;AAEnC,QAAI,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aAClD,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,OAAQ,KAAI,OAAO;AAAA,aAC9B,WAAW,OAAO,IAAI,QAAQ,IAAK,YAAW,KAAK;AAAA,aACnD,WAAW,IAAI,KAAM,YAAW,KAAK;AAC9C,UAAM,cAAc;AACpB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,YAAY,aAAa,MAAM,YAAY,KAAM,QAAO;AAClE,QAAI,MAAM,MAAM,SAAS,YAAY,aAAa,UAAU,OAAO,CAAC;AACpE,QAAI,IAAI,QAAQ,UAAU,sBAAsB,KAAK,SAAS,EAAG,QAAO,IAAI;AAC5E,QAAI,UAAU,aAAa,IAAI;AAC/B,QAAI,IAAI,MAAO,QAAO,IAAI,UAAU,UAAU,IAAI;AAAA,QAC7C,QAAO,IAAI,YAAY,UAAU,IAAI,GAAG;AAAA,EAC/C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": []}
{"version": 3, "sources": ["../../../../../node_modules/@codemirror/legacy-modes/mode/xml.js"], "sourcesContent": ["var htmlConfig = {\n  autoSelfClosers: {'area': true, 'base': true, 'br': true, 'col': true, 'command': true,\n                    'embed': true, 'frame': true, 'hr': true, 'img': true, 'input': true,\n                    'keygen': true, 'link': true, 'meta': true, 'param': true, 'source': true,\n                    'track': true, 'wbr': true, 'menuitem': true},\n  implicitlyClosed: {'dd': true, 'li': true, 'optgroup': true, 'option': true, 'p': true,\n                     'rp': true, 'rt': true, 'tbody': true, 'td': true, 'tfoot': true,\n                     'th': true, 'tr': true},\n  contextGrabbers: {\n    'dd': {'dd': true, 'dt': true},\n    'dt': {'dd': true, 'dt': true},\n    'li': {'li': true},\n    'option': {'option': true, 'optgroup': true},\n    'optgroup': {'optgroup': true},\n    'p': {'address': true, 'article': true, 'aside': true, 'blockquote': true, 'dir': true,\n          'div': true, 'dl': true, 'fieldset': true, 'footer': true, 'form': true,\n          'h1': true, 'h2': true, 'h3': true, 'h4': true, 'h5': true, 'h6': true,\n          'header': true, 'hgroup': true, 'hr': true, 'menu': true, 'nav': true, 'ol': true,\n          'p': true, 'pre': true, 'section': true, 'table': true, 'ul': true},\n    'rp': {'rp': true, 'rt': true},\n    'rt': {'rp': true, 'rt': true},\n    'tbody': {'tbody': true, 'tfoot': true},\n    'td': {'td': true, 'th': true},\n    'tfoot': {'tbody': true},\n    'th': {'td': true, 'th': true},\n    'thead': {'tbody': true, 'tfoot': true},\n    'tr': {'tr': true}\n  },\n  doNotIndent: {\"pre\": true},\n  allowUnquoted: true,\n  allowMissing: true,\n  caseFold: true\n}\n\nvar xmlConfig = {\n  autoSelfClosers: {},\n  implicitlyClosed: {},\n  contextGrabbers: {},\n  doNotIndent: {},\n  allowUnquoted: false,\n  allowMissing: false,\n  allowMissingTagName: false,\n  caseFold: false\n}\n\nexport function mkXML(parserConfig) {\n  var config = {}\n  var defaults = parserConfig.htmlMode ? htmlConfig : xmlConfig\n  for (var prop in defaults) config[prop] = defaults[prop]\n  for (var prop in parserConfig) config[prop] = parserConfig[prop]\n\n  // Return variables for tokenizers\n  var type, setStyle;\n\n  function inText(stream, state) {\n    function chain(parser) {\n      state.tokenize = parser;\n      return parser(stream, state);\n    }\n\n    var ch = stream.next();\n    if (ch == \"<\") {\n      if (stream.eat(\"!\")) {\n        if (stream.eat(\"[\")) {\n          if (stream.match(\"CDATA[\")) return chain(inBlock(\"atom\", \"]]>\"));\n          else return null;\n        } else if (stream.match(\"--\")) {\n          return chain(inBlock(\"comment\", \"-->\"));\n        } else if (stream.match(\"DOCTYPE\", true, true)) {\n          stream.eatWhile(/[\\w\\._\\-]/);\n          return chain(doctype(1));\n        } else {\n          return null;\n        }\n      } else if (stream.eat(\"?\")) {\n        stream.eatWhile(/[\\w\\._\\-]/);\n        state.tokenize = inBlock(\"meta\", \"?>\");\n        return \"meta\";\n      } else {\n        type = stream.eat(\"/\") ? \"closeTag\" : \"openTag\";\n        state.tokenize = inTag;\n        return \"angleBracket\";\n      }\n    } else if (ch == \"&\") {\n      var ok;\n      if (stream.eat(\"#\")) {\n        if (stream.eat(\"x\")) {\n          ok = stream.eatWhile(/[a-fA-F\\d]/) && stream.eat(\";\");\n        } else {\n          ok = stream.eatWhile(/[\\d]/) && stream.eat(\";\");\n        }\n      } else {\n        ok = stream.eatWhile(/[\\w\\.\\-:]/) && stream.eat(\";\");\n      }\n      return ok ? \"atom\" : \"error\";\n    } else {\n      stream.eatWhile(/[^&<]/);\n      return null;\n    }\n  }\n  inText.isInText = true;\n\n  function inTag(stream, state) {\n    var ch = stream.next();\n    if (ch == \">\" || (ch == \"/\" && stream.eat(\">\"))) {\n      state.tokenize = inText;\n      type = ch == \">\" ? \"endTag\" : \"selfcloseTag\";\n      return \"angleBracket\";\n    } else if (ch == \"=\") {\n      type = \"equals\";\n      return null;\n    } else if (ch == \"<\") {\n      state.tokenize = inText;\n      state.state = baseState;\n      state.tagName = state.tagStart = null;\n      state.tokenize(stream, state);\n      return \"invalid\"\n    } else if (/[\\'\\\"]/.test(ch)) {\n      state.tokenize = inAttribute(ch);\n      state.stringStartCol = stream.column();\n      return state.tokenize(stream, state);\n    } else {\n      stream.match(/^[^\\s\\u00a0=<>\\\"\\']*[^\\s\\u00a0=<>\\\"\\'\\/]/);\n      return \"word\";\n    }\n  }\n\n  function inAttribute(quote) {\n    var closure = function(stream, state) {\n      while (!stream.eol()) {\n        if (stream.next() == quote) {\n          state.tokenize = inTag;\n          break;\n        }\n      }\n      return \"string\";\n    };\n    closure.isInAttribute = true;\n    return closure;\n  }\n\n  function inBlock(style, terminator) {\n    return function(stream, state) {\n      while (!stream.eol()) {\n        if (stream.match(terminator)) {\n          state.tokenize = inText;\n          break;\n        }\n        stream.next();\n      }\n      return style;\n    }\n  }\n\n  function doctype(depth) {\n    return function(stream, state) {\n      var ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == \"<\") {\n          state.tokenize = doctype(depth + 1);\n          return state.tokenize(stream, state);\n        } else if (ch == \">\") {\n          if (depth == 1) {\n            state.tokenize = inText;\n            break;\n          } else {\n            state.tokenize = doctype(depth - 1);\n            return state.tokenize(stream, state);\n          }\n        }\n      }\n      return \"meta\";\n    };\n  }\n\n  function lower(tagName) {\n    return tagName && tagName.toLowerCase();\n  }\n\n  function Context(state, tagName, startOfLine) {\n    this.prev = state.context;\n    this.tagName = tagName || \"\";\n    this.indent = state.indented;\n    this.startOfLine = startOfLine;\n    if (config.doNotIndent.hasOwnProperty(tagName) || (state.context && state.context.noIndent))\n      this.noIndent = true;\n  }\n  function popContext(state) {\n    if (state.context) state.context = state.context.prev;\n  }\n  function maybePopContext(state, nextTagName) {\n    var parentTagName;\n    while (true) {\n      if (!state.context) {\n        return;\n      }\n      parentTagName = state.context.tagName;\n      if (!config.contextGrabbers.hasOwnProperty(lower(parentTagName)) ||\n          !config.contextGrabbers[lower(parentTagName)].hasOwnProperty(lower(nextTagName))) {\n        return;\n      }\n      popContext(state);\n    }\n  }\n\n  function baseState(type, stream, state) {\n    if (type == \"openTag\") {\n      state.tagStart = stream.column();\n      return tagNameState;\n    } else if (type == \"closeTag\") {\n      return closeTagNameState;\n    } else {\n      return baseState;\n    }\n  }\n  function tagNameState(type, stream, state) {\n    if (type == \"word\") {\n      state.tagName = stream.current();\n      setStyle = \"tag\";\n      return attrState;\n    } else if (config.allowMissingTagName && type == \"endTag\") {\n      setStyle = \"angleBracket\";\n      return attrState(type, stream, state);\n    } else {\n      setStyle = \"error\";\n      return tagNameState;\n    }\n  }\n  function closeTagNameState(type, stream, state) {\n    if (type == \"word\") {\n      var tagName = stream.current();\n      if (state.context && state.context.tagName != tagName &&\n          config.implicitlyClosed.hasOwnProperty(lower(state.context.tagName)))\n        popContext(state);\n      if ((state.context && state.context.tagName == tagName) || config.matchClosing === false) {\n        setStyle = \"tag\";\n        return closeState;\n      } else {\n        setStyle = \"error\";\n        return closeStateErr;\n      }\n    } else if (config.allowMissingTagName && type == \"endTag\") {\n      setStyle = \"angleBracket\";\n      return closeState(type, stream, state);\n    } else {\n      setStyle = \"error\";\n      return closeStateErr;\n    }\n  }\n\n  function closeState(type, _stream, state) {\n    if (type != \"endTag\") {\n      setStyle = \"error\";\n      return closeState;\n    }\n    popContext(state);\n    return baseState;\n  }\n  function closeStateErr(type, stream, state) {\n    setStyle = \"error\";\n    return closeState(type, stream, state);\n  }\n\n  function attrState(type, _stream, state) {\n    if (type == \"word\") {\n      setStyle = \"attribute\";\n      return attrEqState;\n    } else if (type == \"endTag\" || type == \"selfcloseTag\") {\n      var tagName = state.tagName, tagStart = state.tagStart;\n      state.tagName = state.tagStart = null;\n      if (type == \"selfcloseTag\" ||\n          config.autoSelfClosers.hasOwnProperty(lower(tagName))) {\n        maybePopContext(state, tagName);\n      } else {\n        maybePopContext(state, tagName);\n        state.context = new Context(state, tagName, tagStart == state.indented);\n      }\n      return baseState;\n    }\n    setStyle = \"error\";\n    return attrState;\n  }\n  function attrEqState(type, stream, state) {\n    if (type == \"equals\") return attrValueState;\n    if (!config.allowMissing) setStyle = \"error\";\n    return attrState(type, stream, state);\n  }\n  function attrValueState(type, stream, state) {\n    if (type == \"string\") return attrContinuedState;\n    if (type == \"word\" && config.allowUnquoted) {setStyle = \"string\"; return attrState;}\n    setStyle = \"error\";\n    return attrState(type, stream, state);\n  }\n  function attrContinuedState(type, stream, state) {\n    if (type == \"string\") return attrContinuedState;\n    return attrState(type, stream, state);\n  }\n\n  return {\n    name: \"xml\",\n\n    startState: function() {\n      var state = {tokenize: inText,\n                   state: baseState,\n                   indented: 0,\n                   tagName: null, tagStart: null,\n                   context: null}\n      return state\n    },\n\n    token: function(stream, state) {\n      if (!state.tagName && stream.sol())\n        state.indented = stream.indentation();\n\n      if (stream.eatSpace()) return null;\n      type = null;\n      var style = state.tokenize(stream, state);\n      if ((style || type) && style != \"comment\") {\n        setStyle = null;\n        state.state = state.state(type || style, stream, state);\n        if (setStyle) style = setStyle\n      }\n      return style;\n    },\n\n    indent: function(state, textAfter, cx) {\n      var context = state.context;\n      // Indent multi-line strings (e.g. css).\n      if (state.tokenize.isInAttribute) {\n        if (state.tagStart == state.indented)\n          return state.stringStartCol + 1;\n        else\n          return state.indented + cx.unit;\n      }\n      if (context && context.noIndent) return null;\n      if (state.tokenize != inTag && state.tokenize != inText) return null\n      // Indent the starts of attribute names.\n      if (state.tagName) {\n        if (config.multilineTagIndentPastTag !== false)\n          return state.tagStart + state.tagName.length + 2;\n        else\n          return state.tagStart + cx.unit * (config.multilineTagIndentFactor || 1);\n      }\n      if (config.alignCDATA && /<!\\[CDATA\\[/.test(textAfter)) return 0;\n      var tagAfter = textAfter && /^<(\\/)?([\\w_:\\.-]*)/.exec(textAfter);\n      if (tagAfter && tagAfter[1]) { // Closing tag spotted\n        while (context) {\n          if (context.tagName == tagAfter[2]) {\n            context = context.prev;\n            break;\n          } else if (config.implicitlyClosed.hasOwnProperty(lower(context.tagName))) {\n            context = context.prev;\n          } else {\n            break;\n          }\n        }\n      } else if (tagAfter) { // Opening tag spotted\n        while (context) {\n          var grabbers = config.contextGrabbers[lower(context.tagName)];\n          if (grabbers && grabbers.hasOwnProperty(lower(tagAfter[2])))\n            context = context.prev;\n          else\n            break;\n        }\n      }\n      while (context && context.prev && !context.startOfLine)\n        context = context.prev;\n      if (context) return context.indent + cx.unit;\n      else return state.baseIndent || 0;\n    },\n\n    languageData: {\n      indentOnInput: /<\\/[\\s\\w:]+>$/,\n      commentTokens: {block: {open: \"<!--\", close: \"-->\"}}\n    },\n\n    configuration: config.htmlMode ? \"html\" : \"xml\",\n    skipAttribute: function(state) {\n      if (state.state == attrValueState)\n        state.state = attrState\n    },\n\n    xmlCurrentTag: function(state) {\n      return state.tagName ? {name: state.tagName, close: state.type == \"closeTag\"} : null\n    },\n\n    xmlCurrentContext: function(state) {\n      var context = []\n      for (var cx = state.context; cx; cx = cx.prev)\n        context.push(cx.tagName)\n      return context.reverse()\n    }\n  };\n};\n\nexport const xml = mkXML({})\nexport const html = mkXML({htmlMode: true})\n"], "mappings": ";;;AAAA,IAAI,aAAa;AAAA,EACf,iBAAiB;AAAA,IAAC,QAAQ;AAAA,IAAM,QAAQ;AAAA,IAAM,MAAM;AAAA,IAAM,OAAO;AAAA,IAAM,WAAW;AAAA,IAChE,SAAS;AAAA,IAAM,SAAS;AAAA,IAAM,MAAM;AAAA,IAAM,OAAO;AAAA,IAAM,SAAS;AAAA,IAChE,UAAU;AAAA,IAAM,QAAQ;AAAA,IAAM,QAAQ;AAAA,IAAM,SAAS;AAAA,IAAM,UAAU;AAAA,IACrE,SAAS;AAAA,IAAM,OAAO;AAAA,IAAM,YAAY;AAAA,EAAI;AAAA,EAC9D,kBAAkB;AAAA,IAAC,MAAM;AAAA,IAAM,MAAM;AAAA,IAAM,YAAY;AAAA,IAAM,UAAU;AAAA,IAAM,KAAK;AAAA,IAC/D,MAAM;AAAA,IAAM,MAAM;AAAA,IAAM,SAAS;AAAA,IAAM,MAAM;AAAA,IAAM,SAAS;AAAA,IAC5D,MAAM;AAAA,IAAM,MAAM;AAAA,EAAI;AAAA,EACzC,iBAAiB;AAAA,IACf,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,IAC7B,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,IAC7B,MAAM,EAAC,MAAM,KAAI;AAAA,IACjB,UAAU,EAAC,UAAU,MAAM,YAAY,KAAI;AAAA,IAC3C,YAAY,EAAC,YAAY,KAAI;AAAA,IAC7B,KAAK;AAAA,MAAC,WAAW;AAAA,MAAM,WAAW;AAAA,MAAM,SAAS;AAAA,MAAM,cAAc;AAAA,MAAM,OAAO;AAAA,MAC5E,OAAO;AAAA,MAAM,MAAM;AAAA,MAAM,YAAY;AAAA,MAAM,UAAU;AAAA,MAAM,QAAQ;AAAA,MACnE,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAAM,MAAM;AAAA,MAClE,UAAU;AAAA,MAAM,UAAU;AAAA,MAAM,MAAM;AAAA,MAAM,QAAQ;AAAA,MAAM,OAAO;AAAA,MAAM,MAAM;AAAA,MAC7E,KAAK;AAAA,MAAM,OAAO;AAAA,MAAM,WAAW;AAAA,MAAM,SAAS;AAAA,MAAM,MAAM;AAAA,IAAI;AAAA,IACxE,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,IAC7B,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,IAC7B,SAAS,EAAC,SAAS,MAAM,SAAS,KAAI;AAAA,IACtC,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,IAC7B,SAAS,EAAC,SAAS,KAAI;AAAA,IACvB,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,IAC7B,SAAS,EAAC,SAAS,MAAM,SAAS,KAAI;AAAA,IACtC,MAAM,EAAC,MAAM,KAAI;AAAA,EACnB;AAAA,EACA,aAAa,EAAC,OAAO,KAAI;AAAA,EACzB,eAAe;AAAA,EACf,cAAc;AAAA,EACd,UAAU;AACZ;AAEA,IAAI,YAAY;AAAA,EACd,iBAAiB,CAAC;AAAA,EAClB,kBAAkB,CAAC;AAAA,EACnB,iBAAiB,CAAC;AAAA,EAClB,aAAa,CAAC;AAAA,EACd,eAAe;AAAA,EACf,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,UAAU;AACZ;AAEO,SAAS,MAAM,cAAc;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,aAAa,WAAW,aAAa;AACpD,WAAS,QAAQ,SAAU,QAAO,IAAI,IAAI,SAAS,IAAI;AACvD,WAAS,QAAQ,aAAc,QAAO,IAAI,IAAI,aAAa,IAAI;AAG/D,MAAI,MAAM;AAEV,WAAS,OAAO,QAAQ,OAAO;AAC7B,aAAS,MAAM,QAAQ;AACrB,YAAM,WAAW;AACjB,aAAO,OAAO,QAAQ,KAAK;AAAA,IAC7B;AAEA,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,KAAK;AACb,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,cAAI,OAAO,MAAM,QAAQ,EAAG,QAAO,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAAA,cAC1D,QAAO;AAAA,QACd,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,iBAAO,MAAM,QAAQ,WAAW,KAAK,CAAC;AAAA,QACxC,WAAW,OAAO,MAAM,WAAW,MAAM,IAAI,GAAG;AAC9C,iBAAO,SAAS,WAAW;AAC3B,iBAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,QACzB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,eAAO,SAAS,WAAW;AAC3B,cAAM,WAAW,QAAQ,QAAQ,IAAI;AACrC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,OAAO,IAAI,GAAG,IAAI,aAAa;AACtC,cAAM,WAAW;AACjB,eAAO;AAAA,MACT;AAAA,IACF,WAAW,MAAM,KAAK;AACpB,UAAI;AACJ,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,eAAK,OAAO,SAAS,YAAY,KAAK,OAAO,IAAI,GAAG;AAAA,QACtD,OAAO;AACL,eAAK,OAAO,SAAS,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,QAChD;AAAA,MACF,OAAO;AACL,aAAK,OAAO,SAAS,WAAW,KAAK,OAAO,IAAI,GAAG;AAAA,MACrD;AACA,aAAO,KAAK,SAAS;AAAA,IACvB,OAAO;AACL,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,WAAW;AAElB,WAAS,MAAM,QAAQ,OAAO;AAC5B,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,OAAQ,MAAM,OAAO,OAAO,IAAI,GAAG,GAAI;AAC/C,YAAM,WAAW;AACjB,aAAO,MAAM,MAAM,WAAW;AAC9B,aAAO;AAAA,IACT,WAAW,MAAM,KAAK;AACpB,aAAO;AACP,aAAO;AAAA,IACT,WAAW,MAAM,KAAK;AACpB,YAAM,WAAW;AACjB,YAAM,QAAQ;AACd,YAAM,UAAU,MAAM,WAAW;AACjC,YAAM,SAAS,QAAQ,KAAK;AAC5B,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,EAAE,GAAG;AAC5B,YAAM,WAAW,YAAY,EAAE;AAC/B,YAAM,iBAAiB,OAAO,OAAO;AACrC,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC,OAAO;AACL,aAAO,MAAM,0CAA0C;AACvD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,YAAY,OAAO;AAC1B,QAAI,UAAU,SAAS,QAAQ,OAAO;AACpC,aAAO,CAAC,OAAO,IAAI,GAAG;AACpB,YAAI,OAAO,KAAK,KAAK,OAAO;AAC1B,gBAAM,WAAW;AACjB;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,YAAQ,gBAAgB;AACxB,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,OAAO,YAAY;AAClC,WAAO,SAAS,QAAQ,OAAO;AAC7B,aAAO,CAAC,OAAO,IAAI,GAAG;AACpB,YAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,gBAAM,WAAW;AACjB;AAAA,QACF;AACA,eAAO,KAAK;AAAA,MACd;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,QAAQ,OAAO;AACtB,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI;AACJ,cAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,YAAI,MAAM,KAAK;AACb,gBAAM,WAAW,QAAQ,QAAQ,CAAC;AAClC,iBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,QACrC,WAAW,MAAM,KAAK;AACpB,cAAI,SAAS,GAAG;AACd,kBAAM,WAAW;AACjB;AAAA,UACF,OAAO;AACL,kBAAM,WAAW,QAAQ,QAAQ,CAAC;AAClC,mBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,MAAM,SAAS;AACtB,WAAO,WAAW,QAAQ,YAAY;AAAA,EACxC;AAEA,WAAS,QAAQ,OAAO,SAAS,aAAa;AAC5C,SAAK,OAAO,MAAM;AAClB,SAAK,UAAU,WAAW;AAC1B,SAAK,SAAS,MAAM;AACpB,SAAK,cAAc;AACnB,QAAI,OAAO,YAAY,eAAe,OAAO,KAAM,MAAM,WAAW,MAAM,QAAQ;AAChF,WAAK,WAAW;AAAA,EACpB;AACA,WAAS,WAAW,OAAO;AACzB,QAAI,MAAM,QAAS,OAAM,UAAU,MAAM,QAAQ;AAAA,EACnD;AACA,WAAS,gBAAgB,OAAO,aAAa;AAC3C,QAAI;AACJ,WAAO,MAAM;AACX,UAAI,CAAC,MAAM,SAAS;AAClB;AAAA,MACF;AACA,sBAAgB,MAAM,QAAQ;AAC9B,UAAI,CAAC,OAAO,gBAAgB,eAAe,MAAM,aAAa,CAAC,KAC3D,CAAC,OAAO,gBAAgB,MAAM,aAAa,CAAC,EAAE,eAAe,MAAM,WAAW,CAAC,GAAG;AACpF;AAAA,MACF;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,UAAUA,OAAM,QAAQ,OAAO;AACtC,QAAIA,SAAQ,WAAW;AACrB,YAAM,WAAW,OAAO,OAAO;AAC/B,aAAO;AAAA,IACT,WAAWA,SAAQ,YAAY;AAC7B,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,aAAaA,OAAM,QAAQ,OAAO;AACzC,QAAIA,SAAQ,QAAQ;AAClB,YAAM,UAAU,OAAO,QAAQ;AAC/B,iBAAW;AACX,aAAO;AAAA,IACT,WAAW,OAAO,uBAAuBA,SAAQ,UAAU;AACzD,iBAAW;AACX,aAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,IACtC,OAAO;AACL,iBAAW;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,kBAAkBA,OAAM,QAAQ,OAAO;AAC9C,QAAIA,SAAQ,QAAQ;AAClB,UAAI,UAAU,OAAO,QAAQ;AAC7B,UAAI,MAAM,WAAW,MAAM,QAAQ,WAAW,WAC1C,OAAO,iBAAiB,eAAe,MAAM,MAAM,QAAQ,OAAO,CAAC;AACrE,mBAAW,KAAK;AAClB,UAAK,MAAM,WAAW,MAAM,QAAQ,WAAW,WAAY,OAAO,iBAAiB,OAAO;AACxF,mBAAW;AACX,eAAO;AAAA,MACT,OAAO;AACL,mBAAW;AACX,eAAO;AAAA,MACT;AAAA,IACF,WAAW,OAAO,uBAAuBA,SAAQ,UAAU;AACzD,iBAAW;AACX,aAAO,WAAWA,OAAM,QAAQ,KAAK;AAAA,IACvC,OAAO;AACL,iBAAW;AACX,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,WAAWA,OAAM,SAAS,OAAO;AACxC,QAAIA,SAAQ,UAAU;AACpB,iBAAW;AACX,aAAO;AAAA,IACT;AACA,eAAW,KAAK;AAChB,WAAO;AAAA,EACT;AACA,WAAS,cAAcA,OAAM,QAAQ,OAAO;AAC1C,eAAW;AACX,WAAO,WAAWA,OAAM,QAAQ,KAAK;AAAA,EACvC;AAEA,WAAS,UAAUA,OAAM,SAAS,OAAO;AACvC,QAAIA,SAAQ,QAAQ;AAClB,iBAAW;AACX,aAAO;AAAA,IACT,WAAWA,SAAQ,YAAYA,SAAQ,gBAAgB;AACrD,UAAI,UAAU,MAAM,SAAS,WAAW,MAAM;AAC9C,YAAM,UAAU,MAAM,WAAW;AACjC,UAAIA,SAAQ,kBACR,OAAO,gBAAgB,eAAe,MAAM,OAAO,CAAC,GAAG;AACzD,wBAAgB,OAAO,OAAO;AAAA,MAChC,OAAO;AACL,wBAAgB,OAAO,OAAO;AAC9B,cAAM,UAAU,IAAI,QAAQ,OAAO,SAAS,YAAY,MAAM,QAAQ;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AACA,eAAW;AACX,WAAO;AAAA,EACT;AACA,WAAS,YAAYA,OAAM,QAAQ,OAAO;AACxC,QAAIA,SAAQ,SAAU,QAAO;AAC7B,QAAI,CAAC,OAAO,aAAc,YAAW;AACrC,WAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,EACtC;AACA,WAAS,eAAeA,OAAM,QAAQ,OAAO;AAC3C,QAAIA,SAAQ,SAAU,QAAO;AAC7B,QAAIA,SAAQ,UAAU,OAAO,eAAe;AAAC,iBAAW;AAAU,aAAO;AAAA,IAAU;AACnF,eAAW;AACX,WAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,EACtC;AACA,WAAS,mBAAmBA,OAAM,QAAQ,OAAO;AAC/C,QAAIA,SAAQ,SAAU,QAAO;AAC7B,WAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,EACtC;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IAEN,YAAY,WAAW;AACrB,UAAI,QAAQ;AAAA,QAAC,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,QACV,SAAS;AAAA,QAAM,UAAU;AAAA,QACzB,SAAS;AAAA,MAAI;AAC1B,aAAO;AAAA,IACT;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,CAAC,MAAM,WAAW,OAAO,IAAI;AAC/B,cAAM,WAAW,OAAO,YAAY;AAEtC,UAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,aAAO;AACP,UAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,WAAK,SAAS,SAAS,SAAS,WAAW;AACzC,mBAAW;AACX,cAAM,QAAQ,MAAM,MAAM,QAAQ,OAAO,QAAQ,KAAK;AACtD,YAAI,SAAU,SAAQ;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,UAAI,UAAU,MAAM;AAEpB,UAAI,MAAM,SAAS,eAAe;AAChC,YAAI,MAAM,YAAY,MAAM;AAC1B,iBAAO,MAAM,iBAAiB;AAAA;AAE9B,iBAAO,MAAM,WAAW,GAAG;AAAA,MAC/B;AACA,UAAI,WAAW,QAAQ,SAAU,QAAO;AACxC,UAAI,MAAM,YAAY,SAAS,MAAM,YAAY,OAAQ,QAAO;AAEhE,UAAI,MAAM,SAAS;AACjB,YAAI,OAAO,8BAA8B;AACvC,iBAAO,MAAM,WAAW,MAAM,QAAQ,SAAS;AAAA;AAE/C,iBAAO,MAAM,WAAW,GAAG,QAAQ,OAAO,4BAA4B;AAAA,MAC1E;AACA,UAAI,OAAO,cAAc,cAAc,KAAK,SAAS,EAAG,QAAO;AAC/D,UAAI,WAAW,aAAa,sBAAsB,KAAK,SAAS;AAChE,UAAI,YAAY,SAAS,CAAC,GAAG;AAC3B,eAAO,SAAS;AACd,cAAI,QAAQ,WAAW,SAAS,CAAC,GAAG;AAClC,sBAAU,QAAQ;AAClB;AAAA,UACF,WAAW,OAAO,iBAAiB,eAAe,MAAM,QAAQ,OAAO,CAAC,GAAG;AACzE,sBAAU,QAAQ;AAAA,UACpB,OAAO;AACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,UAAU;AACnB,eAAO,SAAS;AACd,cAAI,WAAW,OAAO,gBAAgB,MAAM,QAAQ,OAAO,CAAC;AAC5D,cAAI,YAAY,SAAS,eAAe,MAAM,SAAS,CAAC,CAAC,CAAC;AACxD,sBAAU,QAAQ;AAAA;AAElB;AAAA,QACJ;AAAA,MACF;AACA,aAAO,WAAW,QAAQ,QAAQ,CAAC,QAAQ;AACzC,kBAAU,QAAQ;AACpB,UAAI,QAAS,QAAO,QAAQ,SAAS,GAAG;AAAA,UACnC,QAAO,MAAM,cAAc;AAAA,IAClC;AAAA,IAEA,cAAc;AAAA,MACZ,eAAe;AAAA,MACf,eAAe,EAAC,OAAO,EAAC,MAAM,QAAQ,OAAO,MAAK,EAAC;AAAA,IACrD;AAAA,IAEA,eAAe,OAAO,WAAW,SAAS;AAAA,IAC1C,eAAe,SAAS,OAAO;AAC7B,UAAI,MAAM,SAAS;AACjB,cAAM,QAAQ;AAAA,IAClB;AAAA,IAEA,eAAe,SAAS,OAAO;AAC7B,aAAO,MAAM,UAAU,EAAC,MAAM,MAAM,SAAS,OAAO,MAAM,QAAQ,WAAU,IAAI;AAAA,IAClF;AAAA,IAEA,mBAAmB,SAAS,OAAO;AACjC,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,MAAM,SAAS,IAAI,KAAK,GAAG;AACvC,gBAAQ,KAAK,GAAG,OAAO;AACzB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AACF;AAEO,IAAM,MAAM,MAAM,CAAC,CAAC;AACpB,IAAM,OAAO,MAAM,EAAC,UAAU,KAAI,CAAC;", "names": ["type"]}
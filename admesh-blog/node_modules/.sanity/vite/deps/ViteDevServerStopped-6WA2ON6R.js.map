{"version": 3, "sources": ["../../../../../node_modules/sanity/src/core/studio/ViteDevServerStopped.tsx"], "sourcesContent": ["/* eslint-disable i18next/no-literal-string -- will not support i18n in error boundaries */\nimport {Card, Container, Heading, Stack, Text} from '@sanity/ui'\nimport {type ReactNode, useCallback, useEffect, useState} from 'react'\nimport {type ViteHotContext} from 'vite/types/hot.js'\n\nconst ERROR_TITLE = 'Dev server stopped'\nconst ERROR_DESCRIPTION =\n  'The development server has stopped. You may need to restart it to continue working.'\n\nexport class ViteDevServerStoppedError extends Error {\n  ViteDevServerStoppedError: boolean\n\n  constructor() {\n    super(ERROR_TITLE)\n    this.name = 'ViteDevServerStoppedError'\n    this.ViteDevServerStoppedError = true\n  }\n}\nconst serverHot = import.meta.hot\nconst isViteServer = (hot: unknown): hot is ViteHotContext => Boolean(hot)\n\nconst useDetectViteDevServerStopped = () => {\n  const [devServerStopped, setDevServerStopped] = useState(false)\n\n  const markDevServerStopped = useCallback(() => setDevServerStopped(true), [])\n\n  useEffect(() => {\n    // no early return to optimize tree-shaking\n    if (isViteServer(serverHot)) {\n      serverHot.on('vite:ws:disconnect', markDevServerStopped)\n    }\n  }, [markDevServerStopped])\n\n  return {devServerStopped}\n}\n\nconst ThrowViteServerStopped = () => {\n  const {devServerStopped} = useDetectViteDevServerStopped()\n\n  if (devServerStopped) throw new ViteDevServerStoppedError()\n\n  return null\n}\n\nexport const DetectViteDevServerStopped = (): ReactNode =>\n  isViteServer(serverHot) ? <ThrowViteServerStopped /> : null\n\nexport const DevServerStoppedErrorScreen = (): ReactNode => (\n  <Card\n    height=\"fill\"\n    overflow=\"auto\"\n    paddingY={[4, 5, 6, 7]}\n    paddingX={4}\n    sizing=\"border\"\n    tone=\"critical\"\n  >\n    <Container width={3}>\n      <Stack space={4}>\n        <Heading>{ERROR_TITLE}</Heading>\n\n        <Card border radius={2} overflow=\"auto\" padding={4} tone=\"inherit\">\n          <Stack space={4}>\n            <Text size={2}>{ERROR_DESCRIPTION}</Text>\n          </Stack>\n        </Card>\n      </Stack>\n    </Container>\n  </Card>\n)\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAMA,cAAc;AAApB,IACMC,oBACJ;AAEK,IAAMC,4BAAN,cAAwCC,MAAM;EAGnDC,cAAc;AACZ,UAAMJ,WAAW,GACjB,KAAKK,OAAO,6BACZ,KAAKH,4BAA4B;EAAA;AAErC;AACA,IAAMI,YAAYC,YAAYC;AAA9B,IACMC,eAAgBD,CAAAA,QAAwCE,CAAQF,CAAAA;AADtE,IAGMG,gCAAgCA,MAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GACpC,CAAAC,kBAAAC,mBAAA,QAAgDC,uBAAAA,KAAc;AAACC,MAAAA;AAAAL,IAAA,CAAA,MAAAM,OAAAC,IAAA,2BAAA,KAEtBF,KAAAA,MAAMF,oBAAAA,IAAwB,GAACH,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAxE,QAAAQ,uBAA6BH;AAAgD,MAAAI,IAAAC;AAAAV,IAAA,CAAA,MAAAM,OAAAC,IAAA,2BAAA,KAEnEE,KAAAA,MAAA;AAEJZ,iBAAAH,SAAsB,KACxBA,UAAAiB,GAAa,sBAAsBH,oBAAoB;EAAA,GAExDE,KAAAA,CAACF,oBAAoB,GAACR,EAAAA,CAAAA,IAAAS,IAAAT,EAAAA,CAAAA,IAAAU,OAAAD,KAAAT,EAAA,CAAA,GAAAU,KAAAV,EAAA,CAAA,QALzBY,wBAAUH,IAKPC,EAAsB;AAACG,MAAAA;AAAA,SAAAb,EAAAA,CAAAA,MAAAE,oBAEnBW,KAAA;IAAAX;EAAkBF,GAAAA,EAAAA,CAAAA,IAAAE,kBAAAF,EAAAA,CAAAA,IAAAa,MAAAA,KAAAb,EAAA,CAAA,GAAlBa;AAAkB;AAf3B,IAkBMC,yBAAyBA,MAAA;AAC7B,QAAA;IAAAZ;EAAAA,IAA2BH,8BAA8B;AAErDG,MAAAA;AAAgB,UAAA,IAAAZ,0BAAA;AAAA,SAAA;AAAA;AArBtB,IA0BayB,6BAA6BA,MAAA;AAAAf,QAAAA,QAAAC,iCAAA,CAAA;AAAAI,MAAAA;AAAAL,SAAAA,EAAA,CAAA,MAAAM,OAAAC,IAAA,2BAAA,KACxCF,KAAAR,aAAAH,SAAsB,QAAA,wBAAK,wBAAsB,CAAA,CAAA,IAAU,MAAAM,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA,GAA3DK;AAA2D;AA3B7D,IA6BaW,8BAA8BA,MAAA;AAAAhB,QAAAA,QAAAC,iCAAA,CAAA;AAAAI,MAAAA;AAAAL,IAAA,CAAA,MAAAM,OAAAC,IAAA,2BAAA,KAI7BF,KAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAYL,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAAS,MAAAA;AAAAT,IAAA,CAAA,MAAAM,OAAAC,IAAA,2BAAA,KAOlBE,SAAAA,wBAAC,SAAA,EAAA,UAAA,YAAA,CAAqB,GAAUT,EAAAA,CAAAA,IAAAS,MAAAA,KAAAT,EAAA,CAAA;AAAAU,MAAAA;AAAA,SAAAV,EAAA,CAAA,MAAAM,OAAAC,IAAA,2BAAA,KAVtCG,SAAC,wBAAA,MAAA,EACQ,QAAA,QACE,UAAA,QACC,UAAAL,IACA,UAAA,GACH,QAAA,UACF,MAAA,YAEL,cAAC,wBAAA,WAAA,EAAiB,OAAA,GAChB,cAAC,yBAAA,OAAA,EAAa,OAAA,GACZI,UAAAA;IAAAA;QAEA,wBAAC,MAAA,EAAK,QAAA,MAAe,QAAA,GAAY,UAAA,QAAgB,SAAA,GAAQ,MAAA,WACvD,cAAC,wBAAA,OAAA,EAAa,OAAA,GACZ,cAAA,wBAAC,MAAA,EAAW,MAAC,GAAA,UAAqB,kBAAA,CAAA,EACpC,CAAA,EACF,CAAA;EACF,EAAA,CAAA,EACF,CAAA,EAAA,CACF,GAAOT,EAAAA,CAAAA,IAAAU,MAAAA,KAAAV,EAAA,CAAA,GAnBPU;AAmBO;", "names": ["ERROR_TITLE", "ERROR_DESCRIPTION", "ViteDevServerStoppedError", "Error", "constructor", "name", "serverHot", "import", "hot", "isViteServer", "Boolean", "useDetectViteDevServerStopped", "$", "_c", "devServerStopped", "setDevServerStopped", "useState", "t0", "Symbol", "for", "markDevServerStopped", "t1", "t2", "on", "useEffect", "t3", "ThrowViteServerStopped", "DetectViteDevServerStopped", "DevServerStoppedErrorScreen"]}
import {
  defineLocaleResourceBundle,
  definePlugin,
  route
} from "./chunk-HKF2AZUV.js";
import {
  EyeOpenIcon
} from "./chunk-HOFXQ7MC.js";
import {
  require_react
} from "./chunk-BD3T7BTJ.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// node_modules/@sanity/vision/lib/_chunks-es/index.mjs
var import_react = __toESM(require_react(), 1);
var visionLocaleNamespace = "vision";
var visionUsEnglishLocaleBundle = defineLocaleResourceBundle({
  locale: "en-US",
  namespace: visionLocaleNamespace,
  resources: () => import("./resources-B4M2NODF.js")
});
var visionTool = definePlugin((options) => {
  const {
    name,
    title,
    icon,
    ...config
  } = options || {};
  return {
    name: "@sanity/vision",
    tools: [{
      name: name || "vision",
      title: title || "Vision",
      icon: icon || EyeOpenIcon,
      component: (0, import_react.lazy)(() => import("./SanityVision-ILWWXJXH.js")),
      options: config,
      router: route.create("/*"),
      __internalApplicationType: "sanity/vision"
    }],
    i18n: {
      bundles: [visionUsEnglishLocaleBundle]
    }
  };
});

export {
  visionLocaleNamespace,
  visionTool
};
//# sourceMappingURL=chunk-QZ57E3RI.js.map

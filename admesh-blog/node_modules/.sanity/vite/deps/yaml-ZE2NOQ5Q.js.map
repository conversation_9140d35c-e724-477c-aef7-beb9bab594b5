{"version": 3, "sources": ["../../../../../node_modules/@codemirror/legacy-modes/mode/yaml.js"], "sourcesContent": ["var cons = ['true', 'false', 'on', 'off', 'yes', 'no'];\nvar keywordRegex = new RegExp(\"\\\\b((\"+cons.join(\")|(\")+\"))$\", 'i');\n\nexport const yaml = {\n  name: \"yaml\",\n  token: function(stream, state) {\n    var ch = stream.peek();\n    var esc = state.escaped;\n    state.escaped = false;\n    /* comments */\n    if (ch == \"#\" && (stream.pos == 0 || /\\s/.test(stream.string.charAt(stream.pos - 1)))) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n\n    if (stream.match(/^('([^']|\\\\.)*'?|\"([^\"]|\\\\.)*\"?)/))\n      return \"string\";\n\n    if (state.literal && stream.indentation() > state.keyCol) {\n      stream.skipToEnd(); return \"string\";\n    } else if (state.literal) { state.literal = false; }\n    if (stream.sol()) {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      /* document start */\n      if(stream.match('---')) { return \"def\"; }\n      /* document end */\n      if (stream.match('...')) { return \"def\"; }\n      /* array list item */\n      if (stream.match(/^\\s*-\\s+/)) { return 'meta'; }\n    }\n    /* inline pairs/lists */\n    if (stream.match(/^(\\{|\\}|\\[|\\])/)) {\n      if (ch == '{')\n        state.inlinePairs++;\n      else if (ch == '}')\n        state.inlinePairs--;\n      else if (ch == '[')\n        state.inlineList++;\n      else\n        state.inlineList--;\n      return 'meta';\n    }\n\n    /* list separator */\n    if (state.inlineList > 0 && !esc && ch == ',') {\n      stream.next();\n      return 'meta';\n    }\n    /* pairs separator */\n    if (state.inlinePairs > 0 && !esc && ch == ',') {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      stream.next();\n      return 'meta';\n    }\n\n    /* start of value of a pair */\n    if (state.pairStart) {\n      /* block literals */\n      if (stream.match(/^\\s*(\\||\\>)\\s*/)) { state.literal = true; return 'meta'; };\n      /* references */\n      if (stream.match(/^\\s*(\\&|\\*)[a-z0-9\\._-]+\\b/i)) { return 'variable'; }\n      /* numbers */\n      if (state.inlinePairs == 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?$/)) { return 'number'; }\n      if (state.inlinePairs > 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?(?=(,|}))/)) { return 'number'; }\n      /* keywords */\n      if (stream.match(keywordRegex)) { return 'keyword'; }\n    }\n\n    /* pairs (associative arrays) -> key */\n    if (!state.pair && stream.match(/^\\s*(?:[,\\[\\]{}&*!|>'\"%@`][^\\s'\":]|[^,\\[\\]{}#&*!|>'\"%@`])[^#]*?(?=\\s*:($|\\s))/)) {\n      state.pair = true;\n      state.keyCol = stream.indentation();\n      return \"atom\";\n    }\n    if (state.pair && stream.match(/^:\\s*/)) { state.pairStart = true; return 'meta'; }\n\n    /* nothing found, continue */\n    state.pairStart = false;\n    state.escaped = (ch == '\\\\');\n    stream.next();\n    return null;\n  },\n  startState: function() {\n    return {\n      pair: false,\n      pairStart: false,\n      keyCol: 0,\n      inlinePairs: 0,\n      inlineList: 0,\n      literal: false,\n      escaped: false\n    };\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,OAAO,CAAC,QAAQ,SAAS,MAAM,OAAO,OAAO,IAAI;AACrD,IAAI,eAAe,IAAI,OAAO,UAAQ,KAAK,KAAK,KAAK,IAAE,OAAO,GAAG;AAE1D,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,MAAM;AAChB,UAAM,UAAU;AAEhB,QAAI,MAAM,QAAQ,OAAO,OAAO,KAAK,KAAK,KAAK,OAAO,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC,IAAI;AACrF,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,kCAAkC;AACjD,aAAO;AAET,QAAI,MAAM,WAAW,OAAO,YAAY,IAAI,MAAM,QAAQ;AACxD,aAAO,UAAU;AAAG,aAAO;AAAA,IAC7B,WAAW,MAAM,SAAS;AAAE,YAAM,UAAU;AAAA,IAAO;AACnD,QAAI,OAAO,IAAI,GAAG;AAChB,YAAM,SAAS;AACf,YAAM,OAAO;AACb,YAAM,YAAY;AAElB,UAAG,OAAO,MAAM,KAAK,GAAG;AAAE,eAAO;AAAA,MAAO;AAExC,UAAI,OAAO,MAAM,KAAK,GAAG;AAAE,eAAO;AAAA,MAAO;AAEzC,UAAI,OAAO,MAAM,UAAU,GAAG;AAAE,eAAO;AAAA,MAAQ;AAAA,IACjD;AAEA,QAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,UAAI,MAAM;AACR,cAAM;AAAA,eACC,MAAM;AACb,cAAM;AAAA,eACC,MAAM;AACb,cAAM;AAAA;AAEN,cAAM;AACR,aAAO;AAAA,IACT;AAGA,QAAI,MAAM,aAAa,KAAK,CAAC,OAAO,MAAM,KAAK;AAC7C,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,cAAc,KAAK,CAAC,OAAO,MAAM,KAAK;AAC9C,YAAM,SAAS;AACf,YAAM,OAAO;AACb,YAAM,YAAY;AAClB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AAGA,QAAI,MAAM,WAAW;AAEnB,UAAI,OAAO,MAAM,gBAAgB,GAAG;AAAE,cAAM,UAAU;AAAM,eAAO;AAAA,MAAQ;AAAC;AAE5E,UAAI,OAAO,MAAM,6BAA6B,GAAG;AAAE,eAAO;AAAA,MAAY;AAEtE,UAAI,MAAM,eAAe,KAAK,OAAO,MAAM,sBAAsB,GAAG;AAAE,eAAO;AAAA,MAAU;AACvF,UAAI,MAAM,cAAc,KAAK,OAAO,MAAM,8BAA8B,GAAG;AAAE,eAAO;AAAA,MAAU;AAE9F,UAAI,OAAO,MAAM,YAAY,GAAG;AAAE,eAAO;AAAA,MAAW;AAAA,IACtD;AAGA,QAAI,CAAC,MAAM,QAAQ,OAAO,MAAM,+EAA+E,GAAG;AAChH,YAAM,OAAO;AACb,YAAM,SAAS,OAAO,YAAY;AAClC,aAAO;AAAA,IACT;AACA,QAAI,MAAM,QAAQ,OAAO,MAAM,OAAO,GAAG;AAAE,YAAM,YAAY;AAAM,aAAO;AAAA,IAAQ;AAGlF,UAAM,YAAY;AAClB,UAAM,UAAW,MAAM;AACvB,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAAA,EACA,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}
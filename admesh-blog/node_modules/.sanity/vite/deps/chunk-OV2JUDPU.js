import {
  require_jsx_runtime
} from "./chunk-ZQZQC55M.js";
import {
  require_react
} from "./chunk-BD3T7BTJ.js";
import {
  __commonJS,
  __publicField,
  __toESM
} from "./chunk-OCBYBPSH.js";

// node_modules/debounce/index.js
var require_debounce = __commonJS({
  "node_modules/debounce/index.js"(exports, module) {
    function debounce2(func, wait, immediate) {
      var timeout, args, context, timestamp, result;
      if (null == wait) wait = 100;
      function later() {
        var last = Date.now() - timestamp;
        if (last < wait && last >= 0) {
          timeout = setTimeout(later, wait - last);
        } else {
          timeout = null;
          if (!immediate) {
            result = func.apply(context, args);
            context = args = null;
          }
        }
      }
      ;
      var debounced = function() {
        context = this;
        args = arguments;
        timestamp = Date.now();
        var callNow = immediate && !timeout;
        if (!timeout) timeout = setTimeout(later, wait);
        if (callNow) {
          result = func.apply(context, args);
          context = args = null;
        }
        return result;
      };
      debounced.clear = function() {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
      };
      debounced.flush = function() {
        if (timeout) {
          result = func.apply(context, args);
          context = args = null;
          clearTimeout(timeout);
          timeout = null;
        }
      };
      return debounced;
    }
    debounce2.debounce = debounce2;
    module.exports = debounce2;
  }
});

// node_modules/md5-o-matic/lib/md5omatic.js
var require_md5omatic = __commonJS({
  "node_modules/md5-o-matic/lib/md5omatic.js"(exports, module) {
    "use strict";
    module.exports = md5omatic;
    function md5omatic(str) {
      var x = str2blks_MD5(str);
      var a = 1732584193;
      var b = -271733879;
      var c = -1732584194;
      var d = 271733878;
      for (var i = 0; i < x.length; i += 16) {
        var olda = a;
        var oldb = b;
        var oldc = c;
        var oldd = d;
        a = ff(a, b, c, d, x[i + 0], 7, -680876936);
        d = ff(d, a, b, c, x[i + 1], 12, -389564586);
        c = ff(c, d, a, b, x[i + 2], 17, 606105819);
        b = ff(b, c, d, a, x[i + 3], 22, -1044525330);
        a = ff(a, b, c, d, x[i + 4], 7, -176418897);
        d = ff(d, a, b, c, x[i + 5], 12, 1200080426);
        c = ff(c, d, a, b, x[i + 6], 17, -1473231341);
        b = ff(b, c, d, a, x[i + 7], 22, -45705983);
        a = ff(a, b, c, d, x[i + 8], 7, 1770035416);
        d = ff(d, a, b, c, x[i + 9], 12, -1958414417);
        c = ff(c, d, a, b, x[i + 10], 17, -42063);
        b = ff(b, c, d, a, x[i + 11], 22, -1990404162);
        a = ff(a, b, c, d, x[i + 12], 7, 1804603682);
        d = ff(d, a, b, c, x[i + 13], 12, -40341101);
        c = ff(c, d, a, b, x[i + 14], 17, -1502002290);
        b = ff(b, c, d, a, x[i + 15], 22, 1236535329);
        a = gg(a, b, c, d, x[i + 1], 5, -165796510);
        d = gg(d, a, b, c, x[i + 6], 9, -1069501632);
        c = gg(c, d, a, b, x[i + 11], 14, 643717713);
        b = gg(b, c, d, a, x[i + 0], 20, -373897302);
        a = gg(a, b, c, d, x[i + 5], 5, -701558691);
        d = gg(d, a, b, c, x[i + 10], 9, 38016083);
        c = gg(c, d, a, b, x[i + 15], 14, -660478335);
        b = gg(b, c, d, a, x[i + 4], 20, -405537848);
        a = gg(a, b, c, d, x[i + 9], 5, 568446438);
        d = gg(d, a, b, c, x[i + 14], 9, -1019803690);
        c = gg(c, d, a, b, x[i + 3], 14, -187363961);
        b = gg(b, c, d, a, x[i + 8], 20, 1163531501);
        a = gg(a, b, c, d, x[i + 13], 5, -1444681467);
        d = gg(d, a, b, c, x[i + 2], 9, -51403784);
        c = gg(c, d, a, b, x[i + 7], 14, 1735328473);
        b = gg(b, c, d, a, x[i + 12], 20, -1926607734);
        a = hh(a, b, c, d, x[i + 5], 4, -378558);
        d = hh(d, a, b, c, x[i + 8], 11, -2022574463);
        c = hh(c, d, a, b, x[i + 11], 16, 1839030562);
        b = hh(b, c, d, a, x[i + 14], 23, -35309556);
        a = hh(a, b, c, d, x[i + 1], 4, -1530992060);
        d = hh(d, a, b, c, x[i + 4], 11, 1272893353);
        c = hh(c, d, a, b, x[i + 7], 16, -155497632);
        b = hh(b, c, d, a, x[i + 10], 23, -1094730640);
        a = hh(a, b, c, d, x[i + 13], 4, 681279174);
        d = hh(d, a, b, c, x[i + 0], 11, -358537222);
        c = hh(c, d, a, b, x[i + 3], 16, -722521979);
        b = hh(b, c, d, a, x[i + 6], 23, 76029189);
        a = hh(a, b, c, d, x[i + 9], 4, -640364487);
        d = hh(d, a, b, c, x[i + 12], 11, -421815835);
        c = hh(c, d, a, b, x[i + 15], 16, 530742520);
        b = hh(b, c, d, a, x[i + 2], 23, -995338651);
        a = ii(a, b, c, d, x[i + 0], 6, -198630844);
        d = ii(d, a, b, c, x[i + 7], 10, 1126891415);
        c = ii(c, d, a, b, x[i + 14], 15, -1416354905);
        b = ii(b, c, d, a, x[i + 5], 21, -57434055);
        a = ii(a, b, c, d, x[i + 12], 6, 1700485571);
        d = ii(d, a, b, c, x[i + 3], 10, -1894986606);
        c = ii(c, d, a, b, x[i + 10], 15, -1051523);
        b = ii(b, c, d, a, x[i + 1], 21, -2054922799);
        a = ii(a, b, c, d, x[i + 8], 6, 1873313359);
        d = ii(d, a, b, c, x[i + 15], 10, -30611744);
        c = ii(c, d, a, b, x[i + 6], 15, -1560198380);
        b = ii(b, c, d, a, x[i + 13], 21, 1309151649);
        a = ii(a, b, c, d, x[i + 4], 6, -145523070);
        d = ii(d, a, b, c, x[i + 11], 10, -1120210379);
        c = ii(c, d, a, b, x[i + 2], 15, 718787259);
        b = ii(b, c, d, a, x[i + 9], 21, -343485551);
        a = addme(a, olda);
        b = addme(b, oldb);
        c = addme(c, oldc);
        d = addme(d, oldd);
      }
      return rhex(a) + rhex(b) + rhex(c) + rhex(d);
    }
    var hex_chr = "0123456789abcdef";
    function bitOR(a, b) {
      var lsb = a & 1 | b & 1;
      var msb31 = a >>> 1 | b >>> 1;
      return msb31 << 1 | lsb;
    }
    function bitXOR(a, b) {
      var lsb = a & 1 ^ b & 1;
      var msb31 = a >>> 1 ^ b >>> 1;
      return msb31 << 1 | lsb;
    }
    function bitAND(a, b) {
      var lsb = a & 1 & (b & 1);
      var msb31 = a >>> 1 & b >>> 1;
      return msb31 << 1 | lsb;
    }
    function addme(x, y) {
      var lsw = (x & 65535) + (y & 65535);
      var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
      return msw << 16 | lsw & 65535;
    }
    function rhex(num) {
      var str = "";
      var j;
      for (j = 0; j <= 3; j++)
        str += hex_chr.charAt(num >> j * 8 + 4 & 15) + hex_chr.charAt(num >> j * 8 & 15);
      return str;
    }
    function str2blks_MD5(str) {
      var nblk = (str.length + 8 >> 6) + 1;
      var blks = new Array(nblk * 16);
      var i;
      for (i = 0; i < nblk * 16; i++)
        blks[i] = 0;
      for (i = 0; i < str.length; i++)
        blks[i >> 2] |= str.charCodeAt(i) << (str.length * 8 + i) % 4 * 8;
      blks[i >> 2] |= 128 << (str.length * 8 + i) % 4 * 8;
      var l = str.length * 8;
      blks[nblk * 16 - 2] = l & 255;
      blks[nblk * 16 - 2] |= (l >>> 8 & 255) << 8;
      blks[nblk * 16 - 2] |= (l >>> 16 & 255) << 16;
      blks[nblk * 16 - 2] |= (l >>> 24 & 255) << 24;
      return blks;
    }
    function rol(num, cnt) {
      return num << cnt | num >>> 32 - cnt;
    }
    function cmn(q, a, b, x, s, t) {
      return addme(rol(addme(addme(a, q), addme(x, t)), s), b);
    }
    function ff(a, b, c, d, x, s, t) {
      return cmn(bitOR(bitAND(b, c), bitAND(~b, d)), a, b, x, s, t);
    }
    function gg(a, b, c, d, x, s, t) {
      return cmn(bitOR(bitAND(b, d), bitAND(c, ~d)), a, b, x, s, t);
    }
    function hh(a, b, c, d, x, s, t) {
      return cmn(bitXOR(bitXOR(b, c), d), a, b, x, s, t);
    }
    function ii(a, b, c, d, x, s, t) {
      return cmn(bitXOR(c, bitOR(b, ~d)), a, b, x, s, t);
    }
  }
});

// node_modules/@rexxars/react-json-inspector/dist/index.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var import_debounce = __toESM(require_debounce());
var import_md5_o_matic = __toESM(require_md5omatic());
var id = Math.ceil(Math.random() * 10);
var uid = () => ++id;
function type(value) {
  return Object.prototype.toString.call(value).slice(8, -1);
}
function isPrimitive(value) {
  const t = type(value);
  return t !== "Object" && t !== "Array";
}
var Highlighter = class extends import_react.Component {
  shouldComponentUpdate(p) {
    return p.highlight !== this.props.highlight;
  }
  render() {
    const str = this.props.string || "", highlight = this.props.highlight || "", highlightStart = str.search(highlight);
    if (!highlight || highlightStart === -1)
      return (0, import_jsx_runtime.jsx)("span", { children: str });
    const highlightLength = highlight.source.length, highlightString = str.slice(
      highlightStart,
      highlightStart + highlightLength
    );
    return (0, import_jsx_runtime.jsx)("span", { children: str.split(highlight).map(function(part, index) {
      return (0, import_jsx_runtime.jsxs)("span", { children: [
        index > 0 ? (0, import_jsx_runtime.jsx)("span", { className: "json-inspector__hl", children: highlightString }) : null,
        part
      ] }, index);
    }) });
  }
};
function isObject(value) {
  return typeof value == "object" && value !== null && !Array.isArray(value);
}
var PATH_PREFIX = ".root.";
var Leaf = class _Leaf extends import_react.Component {
  constructor(props) {
    super(props);
    __publicField(this, "_onShowOriginalClick", (e) => {
      var _a, _b;
      this.setState({
        original: (_b = (_a = this.props).getOriginal) == null ? void 0 : _b.call(_a, this.keypath())
      }), e.stopPropagation();
    });
    this.state = {
      expanded: this._isInitiallyExpanded(this.props)
    };
  }
  render() {
    const { label, data, root, id: inputId } = this.props, id2 = "id_" + uid(), d = {
      path: this.keypath(),
      key: label.toString(),
      value: data
    }, onLabelClick = this._onClick.bind(this, d);
    return (0, import_jsx_runtime.jsxs)(
      "div",
      {
        "data-testid": root ? "leaf-root" : "leaf-child",
        "aria-expanded": this.state.expanded,
        "data-root": root || void 0,
        className: this.getClassName(),
        id: "leaf-" + this._rootPath(),
        children: [
          (0, import_jsx_runtime.jsx)(
            "input",
            {
              className: "json-inspector__radio",
              type: "radio",
              name: id2,
              id: inputId,
              tabIndex: -1
            }
          ),
          (0, import_jsx_runtime.jsxs)(
            "label",
            {
              className: "json-inspector__line",
              htmlFor: id2,
              onClick: onLabelClick,
              children: [
                (0, import_jsx_runtime.jsx)("div", { className: "json-inspector__flatpath", children: d.path }),
                (0, import_jsx_runtime.jsxs)("span", { className: "json-inspector__key", children: [
                  this.format(d.key),
                  ":",
                  this.renderInteractiveLabel(d.key, true)
                ] }),
                this.renderTitle(),
                this.renderShowOriginalButton()
              ]
            }
          ),
          this.renderChildren()
        ]
      }
    );
  }
  renderTitle() {
    const data = this.data(), t = type(data);
    if (Array.isArray(data)) {
      const length = data.length;
      return (0, import_jsx_runtime.jsxs)("span", { className: "json-inspector__value json-inspector__value_helper", children: [
        length > 0 ? "[…] " : "[] ",
        items(length)
      ] });
    }
    if (typeof data == "object" && data !== null) {
      const keys = Object.keys(data).length;
      return (0, import_jsx_runtime.jsxs)("span", { className: "json-inspector__value json-inspector__value_helper", children: [
        keys > 0 ? "{…} " : "{} ",
        properties(keys)
      ] });
    }
    return (0, import_jsx_runtime.jsxs)(
      "span",
      {
        className: "json-inspector__value json-inspector__value_" + t.toLowerCase(),
        children: [
          this.format(String(data)),
          this.renderInteractiveLabel(data, false)
        ]
      }
    );
  }
  renderChildren() {
    const {
      verboseShowOriginal,
      query,
      id: id2,
      isExpanded,
      interactiveLabel,
      onClick,
      getOriginal
    } = this.props, childPrefix = this._rootPath(), data = this.data();
    return this.state.expanded && (isObject(data) || Array.isArray(data)) ? Object.keys(data).map((key) => {
      const value = data[key], shouldGetOriginal = !this.state.original || (verboseShowOriginal ? query : false);
      return (0, import_jsx_runtime.jsx)(
        _Leaf,
        {
          data: value,
          label: key,
          prefix: childPrefix,
          onClick,
          id: id2,
          query,
          getOriginal: shouldGetOriginal ? getOriginal : void 0,
          isExpanded,
          interactiveLabel,
          verboseShowOriginal
        },
        getLeafKey(key, value)
      );
    }) : null;
  }
  renderShowOriginalButton() {
    const { data, getOriginal, query } = this.props;
    return isPrimitive(data) || this.state.original || !getOriginal || !query || query.test(this.keypath()) ? null : (0, import_jsx_runtime.jsx)(
      "span",
      {
        className: "json-inspector__show-original",
        onClick: this._onShowOriginalClick
      }
    );
  }
  renderInteractiveLabel(originalValue, isKey) {
    const InteractiveLabel = this.props.interactiveLabel;
    return typeof InteractiveLabel == "function" ? (0, import_jsx_runtime.jsx)(
      InteractiveLabel,
      {
        value: String(originalValue),
        originalValue,
        isKey,
        keypath: this.keypath()
      }
    ) : null;
  }
  static getDerivedStateFromProps(props, state) {
    return props.query ? {
      expanded: !props.query.test(props.label)
    } : null;
  }
  componentDidUpdate(prevProps) {
    prevProps.query && !this.props.query && this.setState({
      expanded: this._isInitiallyExpanded(this.props)
    });
  }
  _rootPath() {
    return (this.props.prefix || "") + "." + this.props.label;
  }
  keypath() {
    return this._rootPath().slice(PATH_PREFIX.length);
  }
  data() {
    return this.state.original || this.props.data;
  }
  format(str) {
    return (0, import_jsx_runtime.jsx)(Highlighter, { string: str, highlight: this.props.query });
  }
  getClassName() {
    let cn = "json-inspector__leaf";
    return this.props.root && (cn += " json-inspector__leaf_root"), this.state.expanded && (cn += " json-inspector__leaf_expanded"), isPrimitive(this.props.data) || (cn += " json-inspector__leaf_composite"), cn;
  }
  toggle() {
    this.setState({
      expanded: !this.state.expanded
    });
  }
  _onClick(data, e) {
    this.toggle(), this.props.onClick && this.props.onClick(data), e.stopPropagation();
  }
  _isInitiallyExpanded(p) {
    if (p.root)
      return true;
    const keypath = this.keypath();
    return p.query ? !p.query.test(keypath) && typeof p.getOriginal == "function" : p.isExpanded ? p.isExpanded(keypath, p.data) : false;
  }
};
function items(count) {
  return count + (count === 1 ? " item" : " items");
}
function properties(count) {
  return count + (count === 1 ? " property" : " properties");
}
function getLeafKey(key, value) {
  if (isPrimitive(value)) {
    const hash = (0, import_md5_o_matic.default)(String(value));
    return key + ":" + hash;
  } else
    return key + "[" + type(value) + "]";
}
var noop = (...args) => {
};
var SearchBar = ({ onChange = noop }) => {
  const onSearchChange = (0, import_react.useCallback)(
    (evt) => onChange(evt.target.value),
    [onChange]
  );
  return (0, import_jsx_runtime.jsx)(
    "input",
    {
      className: "json-inspector__search",
      type: "search",
      placeholder: "Search",
      onChange: onSearchChange
    }
  );
};
function isEmpty(object) {
  return isObject(object) ? Object.keys(object).length === 0 : Array.isArray(object) ? object.length === 0 : object === null || typeof object != "string" || typeof object != "number" ? true : Object.keys(object).length === 0;
}
var getFilterer = memoize(
  (data, opts) => {
    const options = opts || { cacheResults: true }, cache = {};
    return function(query) {
      if (!options.cacheResults)
        return find(data, query, options);
      let subquery;
      if (!cache[query]) {
        for (var i = query.length - 1; i > 0; i -= 1)
          if (subquery = query.slice(0, i), cache[subquery]) {
            cache[query] = find(cache[subquery], query, options);
            break;
          }
      }
      return cache[query] || (cache[query] = find(data, query, options)), cache[query];
    };
  }
);
function find(data, query, options) {
  return !isObject(data) && !Array.isArray(data) ? {} : Object.keys(data).reduce(function(acc, key) {
    const value = data[key];
    let matches;
    return value ? typeof value != "object" ? ((contains(query, key, options) || contains(query, value, options)) && (acc[key] = value), acc) : contains(query, key, options) ? (acc[key] = value, acc) : (matches = find(value, query, options), isEmpty(matches) || Object.assign(acc, pair(key, matches)), acc) : acc;
  }, {});
}
function contains(query, value, options) {
  if (!value)
    return false;
  var haystack = String(value), needle = query;
  return (options == null ? void 0 : options.ignoreCase) && (haystack = haystack.toLowerCase(), needle = needle.toLowerCase()), haystack.indexOf(needle) !== -1;
}
function pair(key, value) {
  return { [key]: value };
}
function memoize(fn) {
  let lastData, lastOptions, lastResult;
  return (data, options) => ((!lastResult || data !== lastData || options !== lastOptions) && (lastData = data, lastOptions = options, lastResult = fn(data, options)), lastResult);
}
var PATH_DELIMITER = ".";
function integer(str) {
  return parseInt(str, 10);
}
function lens(data, path) {
  var p = path.split(PATH_DELIMITER), segment = p.shift();
  if (!segment)
    return data;
  if (Array.isArray(data) && data[integer(segment)])
    return lens(data[integer(segment)], p.join(PATH_DELIMITER));
  if (isObject(data) && segment in data)
    return lens(data[segment], p.join(PATH_DELIMITER));
}
var defaultValidateQuery = (query) => query.length >= 2;
var defaultFilterOptions = { cacheResults: true, ignoreCase: false };
var JsonInspector = class extends import_react.Component {
  constructor(props) {
    super(props);
    __publicField(this, "search", (query) => {
      this.setState({ query });
    });
    __publicField(this, "createFilterer", (data, options) => {
      this.setState({
        filterer: getFilterer(data, options)
      });
    });
    __publicField(this, "getOriginal", (path) => lens(this.props.data, path));
    this.state = {
      query: "",
      filterer: getFilterer(props.data, props.filterOptions)
    };
  }
  render() {
    const {
      data: rawData,
      className,
      onClick,
      id: id2,
      isExpanded,
      interactiveLabel,
      verboseShowOriginal,
      filterOptions = defaultFilterOptions,
      validateQuery = defaultValidateQuery
    } = this.props, isQueryValid = this.state.query !== "" && validateQuery(this.state.query), data = isQueryValid ? this.state.filterer(this.state.query) : rawData, isNotFound = isQueryValid && isEmpty(data);
    return (0, import_jsx_runtime.jsxs)(
      "div",
      {
        "data-testid": "json-inspector",
        className: "json-inspector " + className,
        children: [
          this.renderToolbar(),
          isNotFound ? (0, import_jsx_runtime.jsx)("div", { className: "json-inspector__not-found", children: "Nothing found" }) : (0, import_jsx_runtime.jsx)(
            Leaf,
            {
              data,
              onClick,
              id: id2,
              getOriginal: this.getOriginal,
              query: isQueryValid ? new RegExp(
                this.state.query,
                filterOptions.ignoreCase ? "i" : ""
              ) : null,
              label: "root",
              root: true,
              isExpanded,
              interactiveLabel,
              verboseShowOriginal
            }
          )
        ]
      }
    );
  }
  renderToolbar() {
    var _a;
    const Search = this.props.search;
    return Search ? (0, import_jsx_runtime.jsx)("div", { className: "json-inspector__toolbar", children: (0, import_jsx_runtime.jsx)(
      Search,
      {
        onChange: (0, import_debounce.default)(
          this.search,
          (_a = this.props.searchOptions) == null ? void 0 : _a.debounceTime
        ),
        data: this.props.data,
        query: this.state.query
      }
    ) }) : null;
  }
  static getDerivedStateFromProps(nextProps, prevState) {
    const filterer = getFilterer(nextProps.data, nextProps.filterOptions);
    return filterer === prevState.filterer ? null : { ...prevState, filterer };
  }
  shouldComponentUpdate(nextProps, prevState) {
    return prevState.query !== this.state.query || nextProps.data !== this.props.data || nextProps.onClick !== this.props.onClick;
  }
};
__publicField(JsonInspector, "defaultProps", {
  data: null,
  search: SearchBar,
  searchOptions: {
    debounceTime: 0
  },
  className: "",
  id: "json-" + Date.now(),
  onClick: noop,
  filterOptions: {
    cacheResults: true,
    ignoreCase: false
  },
  validateQuery: function(query) {
    return query.length >= 2;
  },
  /**
   * Decide whether the leaf node at given `keypath` should be expanded initially.
   *
   * @param keypath - Path to the node
   * @param value - Value of the node
   * @returns True if node should be expanded, false otherwise
   */
  isExpanded: function(keypath, value) {
    return false;
  },
  verboseShowOriginal: false
});

export {
  JsonInspector
};
//# sourceMappingURL=chunk-OV2JUDPU.js.map

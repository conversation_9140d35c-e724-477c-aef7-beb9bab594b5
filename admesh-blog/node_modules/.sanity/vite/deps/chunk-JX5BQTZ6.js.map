{"version": 3, "sources": ["../../../react-compiler-runtime/src/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as React from 'react';\n\nconst {useRef, useEffect, isValidElement} = React;\nconst ReactSecretInternals =\n  //@ts-ignore\n  React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE ??\n  //@ts-ignore\n  React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\ntype MemoCache = Array<number | typeof $empty>;\n\nconst $empty = Symbol.for('react.memo_cache_sentinel');\n\n// Re-export React.c if present, otherwise fallback to the userspace polyfill for versions of React\n// < 19.\nexport const c =\n  // @ts-expect-error\n  typeof React.__COMPILER_RUNTIME?.c === 'function'\n    ? // @ts-expect-error\n      React.__COMPILER_RUNTIME.c\n    : function c(size: number) {\n        return React.useMemo<Array<unknown>>(() => {\n          const $ = new Array(size);\n          for (let ii = 0; ii < size; ii++) {\n            $[ii] = $empty;\n          }\n          // This symbol is added to tell the react devtools that this array is from\n          // useMemoCache.\n          // @ts-ignore\n          $[$empty] = true;\n          return $;\n        }, []);\n      };\n\nconst LazyGuardDispatcher: {[key: string]: (...args: Array<any>) => any} = {};\n[\n  'readContext',\n  'useCallback',\n  'useContext',\n  'useEffect',\n  'useImperativeHandle',\n  'useInsertionEffect',\n  'useLayoutEffect',\n  'useMemo',\n  'useReducer',\n  'useRef',\n  'useState',\n  'useDebugValue',\n  'useDeferredValue',\n  'useTransition',\n  'useMutableSource',\n  'useSyncExternalStore',\n  'useId',\n  'unstable_isNewReconciler',\n  'getCacheSignal',\n  'getCacheForType',\n  'useCacheRefresh',\n].forEach(name => {\n  LazyGuardDispatcher[name] = () => {\n    throw new Error(\n      `[React] Unexpected React hook call (${name}) from a React compiled function. ` +\n        \"Check that all hooks are called directly and named according to convention ('use[A-Z]') \",\n    );\n  };\n});\n\nlet originalDispatcher: unknown = null;\n\n// Allow guards are not emitted for useMemoCache\nLazyGuardDispatcher['useMemoCache'] = (count: number) => {\n  if (originalDispatcher == null) {\n    throw new Error(\n      'React Compiler internal invariant violation: unexpected null dispatcher',\n    );\n  } else {\n    return (originalDispatcher as any).useMemoCache(count);\n  }\n};\n\nenum GuardKind {\n  PushGuardContext = 0,\n  PopGuardContext = 1,\n  PushExpectHook = 2,\n  PopExpectHook = 3,\n}\n\nfunction setCurrent(newDispatcher: any) {\n  ReactSecretInternals.ReactCurrentDispatcher.current = newDispatcher;\n  return ReactSecretInternals.ReactCurrentDispatcher.current;\n}\n\nconst guardFrames: Array<unknown> = [];\n\n/**\n * When `enableEmitHookGuards` is set, this does runtime validation\n * of the no-conditional-hook-calls rule.\n * As React Compiler needs to statically understand which calls to move out of\n * conditional branches (i.e. React Compiler cannot memoize the results of hook\n * calls), its understanding of \"the rules of React\" are more restrictive.\n * This validation throws on unsound inputs at runtime.\n *\n * Components should only be invoked through React as React Compiler could memoize\n * the call to AnotherComponent, introducing conditional hook calls in its\n * compiled output.\n * ```js\n * function Invalid(props) {\n *  const myJsx = AnotherComponent(props);\n *  return <div> { myJsx } </div>;\n * }\n *\n * Hooks must be named as hooks.\n * ```js\n * const renamedHook = useState;\n * function Invalid() {\n *   const [state, setState] = renamedHook(0);\n * }\n * ```\n *\n * Hooks must be directly called.\n * ```\n * function call(fn) {\n *  return fn();\n * }\n * function Invalid() {\n *   const result = call(useMyHook);\n * }\n * ```\n */\nexport function $dispatcherGuard(kind: GuardKind) {\n  const curr = ReactSecretInternals.ReactCurrentDispatcher.current;\n  if (kind === GuardKind.PushGuardContext) {\n    // Push before checking invariant or errors\n    guardFrames.push(curr);\n\n    if (guardFrames.length === 1) {\n      // save if we're the first guard on the stack\n      originalDispatcher = curr;\n    }\n\n    if (curr === LazyGuardDispatcher) {\n      throw new Error(\n        `[React] Unexpected call to custom hook or component from a React compiled function. ` +\n          \"Check that (1) all hooks are called directly and named according to convention ('use[A-Z]') \" +\n          'and (2) components are returned as JSX instead of being directly invoked.',\n      );\n    }\n    setCurrent(LazyGuardDispatcher);\n  } else if (kind === GuardKind.PopGuardContext) {\n    // Pop before checking invariant or errors\n    const lastFrame = guardFrames.pop();\n\n    if (lastFrame == null) {\n      throw new Error(\n        'React Compiler internal error: unexpected null in guard stack',\n      );\n    }\n    if (guardFrames.length === 0) {\n      originalDispatcher = null;\n    }\n    setCurrent(lastFrame);\n  } else if (kind === GuardKind.PushExpectHook) {\n    // ExpectHooks could be nested, so we save the current dispatcher\n    // for the matching PopExpectHook to restore.\n    guardFrames.push(curr);\n    setCurrent(originalDispatcher);\n  } else if (kind === GuardKind.PopExpectHook) {\n    const lastFrame = guardFrames.pop();\n    if (lastFrame == null) {\n      throw new Error(\n        'React Compiler internal error: unexpected null in guard stack',\n      );\n    }\n    setCurrent(lastFrame);\n  } else {\n    throw new Error('React Compiler internal error: unreachable block' + kind);\n  }\n}\n\nexport function $reset($: MemoCache) {\n  for (let ii = 0; ii < $.length; ii++) {\n    $[ii] = $empty;\n  }\n}\n\nexport function $makeReadOnly() {\n  throw new Error('TODO: implement $makeReadOnly in react-compiler-runtime');\n}\n\n/**\n * Instrumentation to count rerenders in React components\n */\nexport const renderCounterRegistry: Map<\n  string,\n  Set<{count: number}>\n> = new Map();\nexport function clearRenderCounterRegistry() {\n  for (const counters of renderCounterRegistry.values()) {\n    counters.forEach(counter => {\n      counter.count = 0;\n    });\n  }\n}\n\nfunction registerRenderCounter(name: string, val: {count: number}) {\n  let counters = renderCounterRegistry.get(name);\n  if (counters == null) {\n    counters = new Set();\n    renderCounterRegistry.set(name, counters);\n  }\n  counters.add(val);\n}\n\nfunction removeRenderCounter(name: string, val: {count: number}): void {\n  const counters = renderCounterRegistry.get(name);\n  if (counters == null) {\n    return;\n  }\n  counters.delete(val);\n}\n\nexport function useRenderCounter(name: string): void {\n  const val = useRef<{count: number}>(null);\n\n  if (val.current != null) {\n    val.current.count += 1;\n  }\n  useEffect(() => {\n    // Not counting initial render shouldn't be a problem\n    if (val.current == null) {\n      const counter = {count: 0};\n      registerRenderCounter(name, counter);\n      // @ts-ignore\n      val.current = counter;\n    }\n    return () => {\n      if (val.current !== null) {\n        removeRenderCounter(name, val.current);\n      }\n    };\n  });\n}\n\nconst seenErrors = new Set();\n\nexport function $structuralCheck(\n  oldValue: any,\n  newValue: any,\n  variableName: string,\n  fnName: string,\n  kind: string,\n  loc: string,\n): void {\n  function error(l: string, r: string, path: string, depth: number) {\n    const str = `${fnName}:${loc} [${kind}] ${variableName}${path} changed from ${l} to ${r} at depth ${depth}`;\n    if (seenErrors.has(str)) {\n      return;\n    }\n    seenErrors.add(str);\n    console.error(str);\n  }\n  const depthLimit = 2;\n  function recur(oldValue: any, newValue: any, path: string, depth: number) {\n    if (depth > depthLimit) {\n      return;\n    } else if (oldValue === newValue) {\n      return;\n    } else if (typeof oldValue !== typeof newValue) {\n      error(`type ${typeof oldValue}`, `type ${typeof newValue}`, path, depth);\n    } else if (typeof oldValue === 'object') {\n      const oldArray = Array.isArray(oldValue);\n      const newArray = Array.isArray(newValue);\n      if (oldValue === null && newValue !== null) {\n        error('null', `type ${typeof newValue}`, path, depth);\n      } else if (newValue === null) {\n        error(`type ${typeof oldValue}`, 'null', path, depth);\n      } else if (oldValue instanceof Map) {\n        if (!(newValue instanceof Map)) {\n          error(`Map instance`, `other value`, path, depth);\n        } else if (oldValue.size !== newValue.size) {\n          error(\n            `Map instance with size ${oldValue.size}`,\n            `Map instance with size ${newValue.size}`,\n            path,\n            depth,\n          );\n        } else {\n          for (const [k, v] of oldValue) {\n            if (!newValue.has(k)) {\n              error(\n                `Map instance with key ${k}`,\n                `Map instance without key ${k}`,\n                path,\n                depth,\n              );\n            } else {\n              recur(v, newValue.get(k), `${path}.get(${k})`, depth + 1);\n            }\n          }\n        }\n      } else if (newValue instanceof Map) {\n        error('other value', `Map instance`, path, depth);\n      } else if (oldValue instanceof Set) {\n        if (!(newValue instanceof Set)) {\n          error(`Set instance`, `other value`, path, depth);\n        } else if (oldValue.size !== newValue.size) {\n          error(\n            `Set instance with size ${oldValue.size}`,\n            `Set instance with size ${newValue.size}`,\n            path,\n            depth,\n          );\n        } else {\n          for (const v of newValue) {\n            if (!oldValue.has(v)) {\n              error(\n                `Set instance without element ${v}`,\n                `Set instance with element ${v}`,\n                path,\n                depth,\n              );\n            }\n          }\n        }\n      } else if (newValue instanceof Set) {\n        error('other value', `Set instance`, path, depth);\n      } else if (oldArray || newArray) {\n        if (oldArray !== newArray) {\n          error(\n            `type ${oldArray ? 'array' : 'object'}`,\n            `type ${newArray ? 'array' : 'object'}`,\n            path,\n            depth,\n          );\n        } else if (oldValue.length !== newValue.length) {\n          error(\n            `array with length ${oldValue.length}`,\n            `array with length ${newValue.length}`,\n            path,\n            depth,\n          );\n        } else {\n          for (let ii = 0; ii < oldValue.length; ii++) {\n            recur(oldValue[ii], newValue[ii], `${path}[${ii}]`, depth + 1);\n          }\n        }\n      } else if (isValidElement(oldValue) || isValidElement(newValue)) {\n        if (isValidElement(oldValue) !== isValidElement(newValue)) {\n          error(\n            `type ${isValidElement(oldValue) ? 'React element' : 'object'}`,\n            `type ${isValidElement(newValue) ? 'React element' : 'object'}`,\n            path,\n            depth,\n          );\n        } else if (oldValue.type !== newValue.type) {\n          error(\n            `React element of type ${oldValue.type}`,\n            `React element of type ${newValue.type}`,\n            path,\n            depth,\n          );\n        } else {\n          recur(\n            oldValue.props,\n            newValue.props,\n            `[props of ${path}]`,\n            depth + 1,\n          );\n        }\n      } else {\n        for (const key in newValue) {\n          if (!(key in oldValue)) {\n            error(\n              `object without key ${key}`,\n              `object with key ${key}`,\n              path,\n              depth,\n            );\n          }\n        }\n        for (const key in oldValue) {\n          if (!(key in newValue)) {\n            error(\n              `object with key ${key}`,\n              `object without key ${key}`,\n              path,\n              depth,\n            );\n          } else {\n            recur(oldValue[key], newValue[key], `${path}.${key}`, depth + 1);\n          }\n        }\n      }\n    } else if (typeof oldValue === 'function') {\n      // Bail on functions for now\n      return;\n    } else if (isNaN(oldValue) || isNaN(newValue)) {\n      if (isNaN(oldValue) !== isNaN(newValue)) {\n        error(\n          `${isNaN(oldValue) ? 'NaN' : 'non-NaN value'}`,\n          `${isNaN(newValue) ? 'NaN' : 'non-NaN value'}`,\n          path,\n          depth,\n        );\n      }\n    } else if (oldValue !== newValue) {\n      error(oldValue, newValue, path, depth);\n    }\n  }\n  recur(oldValue, newValue, '', 0);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,QAAA,gBAAA,CAAA;AAAA,aAAA,eAAA;MAAA,kBAAA,MAAA;MAAA,eAAA,MAAA;MAAA,QAAA,MAAA;MAAA,kBAAA,MAAA;MAAA,GAAA,MAAA;MAAA,4BAAA,MAAA;MAAA,uBAAA,MAAA;MAAA,kBAAA,MAAA;IAAA,CAAA;AAAA,WAAA,UAAA,aAAA,aAAA;AAOA,QAAA,QAAuB,QAAA,eAAA;AAEvB,QAAM,EAAC,QAAQ,WAAW,eAAc,IAAI;AAT5C,QAAA;AAUA,QAAM;;OAEJ,KAAM,MAAA,oEAAN,OAAA,KAEM,MAAA;;AAIR,QAAM,SAAS,OAAO,IAAI,2BAA2B;AAlBrD,QAAAA;AAsBO,QAAM;;MAEX,SAAOA,MAAM,MAAA,uBAAN,OAAA,SAAAA,IAA0B,OAAM;;QAE7B,MAAA,mBAAmB;UACzB,SAASC,GAAE,MAAc;AACvB,eAAa,MAAA,QAAwB,MAAM;AACzC,gBAAM,IAAI,IAAI,MAAM,IAAI;AACxB,mBAAS,KAAK,GAAG,KAAK,MAAM,MAAM;AAChC,cAAE,EAAE,IAAI;UACV;AAIA,YAAE,MAAM,IAAI;AACZ,iBAAO;QACT,GAAG,CAAC,CAAC;MACP;;AAEN,QAAM,sBAAqE,CAAC;AAC5E;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,EAAE,QAAQ,CAAA,SAAQ;AAChB,0BAAoB,IAAI,IAAI,MAAM;AAChC,cAAM,IAAI;UACR,uCAAuC,IAAI;QAE7C;MACF;IACF,CAAC;AAED,QAAI,qBAA8B;AAGlC,wBAAoB,cAAc,IAAI,CAAC,UAAkB;AACvD,UAAI,sBAAsB,MAAM;AAC9B,cAAM,IAAI;UACR;QACF;MACF,OAAO;AACL,eAAQ,mBAA2B,aAAa,KAAK;MACvD;IACF;AASA,aAAS,WAAW,eAAoB;AACtC,2BAAqB,uBAAuB,UAAU;AACtD,aAAO,qBAAqB,uBAAuB;IACrD;AAEA,QAAM,cAA8B,CAAC;AAqC9B,aAAS,iBAAiB,MAAiB;AAChD,YAAM,OAAO,qBAAqB,uBAAuB;AACzD,UAAI,SAAS,GAA4B;AAEvC,oBAAY,KAAK,IAAI;AAErB,YAAI,YAAY,WAAW,GAAG;AAE5B,+BAAqB;QACvB;AAEA,YAAI,SAAS,qBAAqB;AAChC,gBAAM,IAAI;YACR;UAGF;QACF;AACA,mBAAW,mBAAmB;MAChC,WAAW,SAAS,GAA2B;AAE7C,cAAM,YAAY,YAAY,IAAI;AAElC,YAAI,aAAa,MAAM;AACrB,gBAAM,IAAI;YACR;UACF;QACF;AACA,YAAI,YAAY,WAAW,GAAG;AAC5B,+BAAqB;QACvB;AACA,mBAAW,SAAS;MACtB,WAAW,SAAS,GAA0B;AAG5C,oBAAY,KAAK,IAAI;AACrB,mBAAW,kBAAkB;MAC/B,WAAW,SAAS,GAAyB;AAC3C,cAAM,YAAY,YAAY,IAAI;AAClC,YAAI,aAAa,MAAM;AACrB,gBAAM,IAAI;YACR;UACF;QACF;AACA,mBAAW,SAAS;MACtB,OAAO;AACL,cAAM,IAAI,MAAM,qDAAqD,IAAI;MAC3E;IACF;AAEO,aAAS,OAAO,GAAc;AACnC,eAAS,KAAK,GAAG,KAAK,EAAE,QAAQ,MAAM;AACpC,UAAE,EAAE,IAAI;MACV;IACF;AAEO,aAAS,gBAAgB;AAC9B,YAAM,IAAI,MAAM,yDAAyD;IAC3E;AAKO,QAAM,wBAGT,oBAAI,IAAI;AACL,aAAS,6BAA6B;AAC3C,iBAAW,YAAY,sBAAsB,OAAO,GAAG;AACrD,iBAAS,QAAQ,CAAA,YAAW;AAC1B,kBAAQ,QAAQ;QAClB,CAAC;MACH;IACF;AAEA,aAAS,sBAAsB,MAAc,KAAsB;AACjE,UAAI,WAAW,sBAAsB,IAAI,IAAI;AAC7C,UAAI,YAAY,MAAM;AACpB,mBAAW,oBAAI,IAAI;AACnB,8BAAsB,IAAI,MAAM,QAAQ;MAC1C;AACA,eAAS,IAAI,GAAG;IAClB;AAEA,aAAS,oBAAoB,MAAc,KAA4B;AACrE,YAAM,WAAW,sBAAsB,IAAI,IAAI;AAC/C,UAAI,YAAY,MAAM;AACpB;MACF;AACA,eAAS,OAAO,GAAG;IACrB;AAEO,aAAS,iBAAiB,MAAoB;AACnD,YAAM,MAAM,OAAwB,IAAI;AAExC,UAAI,IAAI,WAAW,MAAM;AACvB,YAAI,QAAQ,SAAS;MACvB;AACA,gBAAU,MAAM;AAEd,YAAI,IAAI,WAAW,MAAM;AACvB,gBAAM,UAAU,EAAC,OAAO,EAAC;AACzB,gCAAsB,MAAM,OAAO;AAEnC,cAAI,UAAU;QAChB;AACA,eAAO,MAAM;AACX,cAAI,IAAI,YAAY,MAAM;AACxB,gCAAoB,MAAM,IAAI,OAAO;UACvC;QACF;MACF,CAAC;IACH;AAEA,QAAM,aAAa,oBAAI,IAAI;AAEpB,aAAS,iBACd,UACA,UACA,cACA,QACA,MACA,KACM;AACN,eAAS,MAAM,GAAW,GAAW,MAAc,OAAe;AAChE,cAAM,MAAM,GAAG,MAAM,IAAI,GAAG,KAAK,IAAI,KAAK,YAAY,GAAG,IAAI,iBAAiB,CAAC,OAAO,CAAC,aAAa,KAAK;AACzG,YAAI,WAAW,IAAI,GAAG,GAAG;AACvB;QACF;AACA,mBAAW,IAAI,GAAG;AAClB,gBAAQ,MAAM,GAAG;MACnB;AACA,YAAM,aAAa;AACnB,eAAS,MAAMC,WAAeC,WAAe,MAAc,OAAe;AACxE,YAAI,QAAQ,YAAY;AACtB;QACF,WAAWD,cAAaC,WAAU;AAChC;QACF,WAAW,OAAOD,cAAa,OAAOC,WAAU;AAC9C,gBAAM,QAAQ,OAAOD,SAAQ,IAAI,QAAQ,OAAOC,SAAQ,IAAI,MAAM,KAAK;QACzE,WAAW,OAAOD,cAAa,UAAU;AACvC,gBAAM,WAAW,MAAM,QAAQA,SAAQ;AACvC,gBAAM,WAAW,MAAM,QAAQC,SAAQ;AACvC,cAAID,cAAa,QAAQC,cAAa,MAAM;AAC1C,kBAAM,QAAQ,QAAQ,OAAOA,SAAQ,IAAI,MAAM,KAAK;UACtD,WAAWA,cAAa,MAAM;AAC5B,kBAAM,QAAQ,OAAOD,SAAQ,IAAI,QAAQ,MAAM,KAAK;UACtD,WAAWA,qBAAoB,KAAK;AAClC,gBAAI,EAAEC,qBAAoB,MAAM;AAC9B,oBAAM,gBAAgB,eAAe,MAAM,KAAK;YAClD,WAAWD,UAAS,SAASC,UAAS,MAAM;AAC1C;gBACE,0BAA0BD,UAAS,IAAI;gBACvC,0BAA0BC,UAAS,IAAI;gBACvC;gBACA;cACF;YACF,OAAO;AACL,yBAAW,CAAC,GAAG,CAAC,KAAKD,WAAU;AAC7B,oBAAI,CAACC,UAAS,IAAI,CAAC,GAAG;AACpB;oBACE,yBAAyB,CAAC;oBAC1B,4BAA4B,CAAC;oBAC7B;oBACA;kBACF;gBACF,OAAO;AACL,wBAAM,GAAGA,UAAS,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;gBAC1D;cACF;YACF;UACF,WAAWA,qBAAoB,KAAK;AAClC,kBAAM,eAAe,gBAAgB,MAAM,KAAK;UAClD,WAAWD,qBAAoB,KAAK;AAClC,gBAAI,EAAEC,qBAAoB,MAAM;AAC9B,oBAAM,gBAAgB,eAAe,MAAM,KAAK;YAClD,WAAWD,UAAS,SAASC,UAAS,MAAM;AAC1C;gBACE,0BAA0BD,UAAS,IAAI;gBACvC,0BAA0BC,UAAS,IAAI;gBACvC;gBACA;cACF;YACF,OAAO;AACL,yBAAW,KAAKA,WAAU;AACxB,oBAAI,CAACD,UAAS,IAAI,CAAC,GAAG;AACpB;oBACE,gCAAgC,CAAC;oBACjC,6BAA6B,CAAC;oBAC9B;oBACA;kBACF;gBACF;cACF;YACF;UACF,WAAWC,qBAAoB,KAAK;AAClC,kBAAM,eAAe,gBAAgB,MAAM,KAAK;UAClD,WAAW,YAAY,UAAU;AAC/B,gBAAI,aAAa,UAAU;AACzB;gBACE,QAAQ,WAAW,UAAU,QAAQ;gBACrC,QAAQ,WAAW,UAAU,QAAQ;gBACrC;gBACA;cACF;YACF,WAAWD,UAAS,WAAWC,UAAS,QAAQ;AAC9C;gBACE,qBAAqBD,UAAS,MAAM;gBACpC,qBAAqBC,UAAS,MAAM;gBACpC;gBACA;cACF;YACF,OAAO;AACL,uBAAS,KAAK,GAAG,KAAKD,UAAS,QAAQ,MAAM;AAC3C,sBAAMA,UAAS,EAAE,GAAGC,UAAS,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE,KAAK,QAAQ,CAAC;cAC/D;YACF;UACF,WAAW,eAAeD,SAAQ,KAAK,eAAeC,SAAQ,GAAG;AAC/D,gBAAI,eAAeD,SAAQ,MAAM,eAAeC,SAAQ,GAAG;AACzD;gBACE,QAAQ,eAAeD,SAAQ,IAAI,kBAAkB,QAAQ;gBAC7D,QAAQ,eAAeC,SAAQ,IAAI,kBAAkB,QAAQ;gBAC7D;gBACA;cACF;YACF,WAAWD,UAAS,SAASC,UAAS,MAAM;AAC1C;gBACE,yBAAyBD,UAAS,IAAI;gBACtC,yBAAyBC,UAAS,IAAI;gBACtC;gBACA;cACF;YACF,OAAO;AACL;gBACED,UAAS;gBACTC,UAAS;gBACT,aAAa,IAAI;gBACjB,QAAQ;cACV;YACF;UACF,OAAO;AACL,uBAAW,OAAOA,WAAU;AAC1B,kBAAI,EAAE,OAAOD,YAAW;AACtB;kBACE,sBAAsB,GAAG;kBACzB,mBAAmB,GAAG;kBACtB;kBACA;gBACF;cACF;YACF;AACA,uBAAW,OAAOA,WAAU;AAC1B,kBAAI,EAAE,OAAOC,YAAW;AACtB;kBACE,mBAAmB,GAAG;kBACtB,sBAAsB,GAAG;kBACzB;kBACA;gBACF;cACF,OAAO;AACL,sBAAMD,UAAS,GAAG,GAAGC,UAAS,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,QAAQ,CAAC;cACjE;YACF;UACF;QACF,WAAW,OAAOD,cAAa,YAAY;AAEzC;QACF,WAAW,MAAMA,SAAQ,KAAK,MAAMC,SAAQ,GAAG;AAC7C,cAAI,MAAMD,SAAQ,MAAM,MAAMC,SAAQ,GAAG;AACvC;cACE,GAAG,MAAMD,SAAQ,IAAI,QAAQ,eAAe;cAC5C,GAAG,MAAMC,SAAQ,IAAI,QAAQ,eAAe;cAC5C;cACA;YACF;UACF;QACF,WAAWD,cAAaC,WAAU;AAChC,gBAAMD,WAAUC,WAAU,MAAM,KAAK;QACvC;MACF;AACA,YAAM,UAAU,UAAU,IAAI,CAAC;IACjC;;;", "names": ["_a", "c", "oldValue", "newValue"]}
{"version": 3, "sources": ["../../../../../node_modules/@uiw/codemirror-themes/esm/index.js", "../../../../../node_modules/@sanity/code-input/src/codemirror/defaultCodeModes.ts", "../../../../../node_modules/@sanity/code-input/src/codemirror/extensions/backwardsCompatibleTone.ts", "../../../../../node_modules/@sanity/code-input/src/codemirror/extensions/highlightLineExtension.ts", "../../../../../node_modules/@sanity/code-input/src/codemirror/extensions/theme.ts", "../../../../../node_modules/@sanity/code-input/src/codemirror/extensions/useCodeMirrorTheme.ts", "../../../../../node_modules/@sanity/code-input/src/codemirror/extensions/useFontSize.ts", "../../../../../node_modules/@sanity/code-input/src/codemirror/CodeMirrorProxy.tsx"], "sourcesContent": ["import { EditorView } from '@codemirror/view';\nimport { HighlightStyle, syntaxHighlighting } from '@codemirror/language';\nexport var createTheme = _ref => {\n  var {\n    theme,\n    settings = {},\n    styles = []\n  } = _ref;\n  var themeOptions = {\n    '.cm-gutters': {}\n  };\n  var baseStyle = {};\n  if (settings.background) {\n    baseStyle.backgroundColor = settings.background;\n  }\n  if (settings.backgroundImage) {\n    baseStyle.backgroundImage = settings.backgroundImage;\n  }\n  if (settings.foreground) {\n    baseStyle.color = settings.foreground;\n  }\n  if (settings.fontSize) {\n    baseStyle.fontSize = settings.fontSize;\n  }\n  if (settings.background || settings.foreground) {\n    themeOptions['&'] = baseStyle;\n  }\n  if (settings.fontFamily) {\n    themeOptions['&.cm-editor .cm-scroller'] = {\n      fontFamily: settings.fontFamily\n    };\n  }\n  if (settings.gutterBackground) {\n    themeOptions['.cm-gutters'].backgroundColor = settings.gutterBackground;\n  }\n  if (settings.gutterForeground) {\n    themeOptions['.cm-gutters'].color = settings.gutterForeground;\n  }\n  if (settings.gutterBorder) {\n    themeOptions['.cm-gutters'].borderRightColor = settings.gutterBorder;\n  }\n  if (settings.caret) {\n    themeOptions['.cm-content'] = {\n      caretColor: settings.caret\n    };\n    themeOptions['.cm-cursor, .cm-dropCursor'] = {\n      borderLeftColor: settings.caret\n    };\n  }\n  var activeLineGutterStyle = {};\n  if (settings.gutterActiveForeground) {\n    activeLineGutterStyle.color = settings.gutterActiveForeground;\n  }\n  if (settings.lineHighlight) {\n    themeOptions['.cm-activeLine'] = {\n      backgroundColor: settings.lineHighlight\n    };\n    activeLineGutterStyle.backgroundColor = settings.lineHighlight;\n  }\n  themeOptions['.cm-activeLineGutter'] = activeLineGutterStyle;\n  if (settings.selection) {\n    themeOptions['&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection'] = {\n      background: settings.selection + ' !important'\n    };\n  }\n  if (settings.selectionMatch) {\n    themeOptions['& .cm-selectionMatch'] = {\n      backgroundColor: settings.selectionMatch\n    };\n  }\n  var themeExtension = EditorView.theme(themeOptions, {\n    dark: theme === 'dark'\n  });\n  var highlightStyle = HighlightStyle.define(styles);\n  var extension = [themeExtension, syntaxHighlighting(highlightStyle)];\n  return extension;\n};\nexport default createTheme;", "import {StreamLanguage} from '@codemirror/language'\nimport type {Extension} from '@codemirror/state'\n\nexport interface CodeMode {\n  name: string\n  loader: ModeLoader\n}\nexport type ModeLoader = () => Promise<Extension | undefined> | Extension | undefined\n\nexport const defaultCodeModes: CodeMode[] = [\n  {\n    name: 'groq',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascriptLanguage}) => javascriptLanguage),\n  },\n  {\n    name: 'javascript',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) => javascript({jsx: false})),\n  },\n  {\n    name: 'jsx',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) => javascript({jsx: true})),\n  },\n  {\n    name: 'typescript',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) =>\n        javascript({jsx: false, typescript: true}),\n      ),\n  },\n  {\n    name: 'tsx',\n    loader: () =>\n      import('@codemirror/lang-javascript').then(({javascript}) =>\n        javascript({jsx: true, typescript: true}),\n      ),\n  },\n  {name: 'php', loader: () => import('@codemirror/lang-php').then(({php}) => php())},\n  {name: 'sql', loader: () => import('@codemirror/lang-sql').then(({sql}) => sql())},\n  {\n    name: 'mysql',\n    loader: () => import('@codemirror/lang-sql').then(({sql, MySQL}) => sql({dialect: MySQL})),\n  },\n  {name: 'json', loader: () => import('@codemirror/lang-json').then(({json}) => json())},\n  {\n    name: 'markdown',\n    loader: () => import('@codemirror/lang-markdown').then(({markdown}) => markdown()),\n  },\n  {name: 'java', loader: () => import('@codemirror/lang-java').then(({java}) => java())},\n  {name: 'html', loader: () => import('@codemirror/lang-html').then(({html}) => html())},\n  {\n    name: 'csharp',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/clike').then(({csharp}) =>\n        StreamLanguage.define(csharp),\n      ),\n  },\n  {\n    name: 'sh',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/shell').then(({shell}) => StreamLanguage.define(shell)),\n  },\n  {\n    name: 'css',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/css').then(({css}) => StreamLanguage.define(css)),\n  },\n  {\n    name: 'scss',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/css').then(({css}) => StreamLanguage.define(css)),\n  },\n  {\n    name: 'sass',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/sass').then(({sass}) => StreamLanguage.define(sass)),\n  },\n  {\n    name: 'ruby',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/ruby').then(({ruby}) => StreamLanguage.define(ruby)),\n  },\n  {\n    name: 'python',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/python').then(({python}) =>\n        StreamLanguage.define(python),\n      ),\n  },\n  {\n    name: 'xml',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/xml').then(({xml}) => StreamLanguage.define(xml)),\n  },\n  {\n    name: 'yaml',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/yaml').then(({yaml}) => StreamLanguage.define(yaml)),\n  },\n  {\n    name: 'golang',\n    loader: () =>\n      import('@codemirror/legacy-modes/mode/go').then(({go}) => StreamLanguage.define(go)),\n  },\n  {name: 'text', loader: () => undefined},\n  {name: 'batch', loader: () => undefined},\n]\n", "import type {ThemeContextValue} from '@sanity/ui'\n\n/**\n * `@sanity/ui@v2.9` introduced two new tones; \"neutral\" and \"suggest\",\n * which maps to \"default\" and \"primary\" respectively in the old theme.\n * This function returns the \"backwards compatible\" tone value.\n *\n * @returns The tone value that is backwards compatible with the old theme.\n * @internal\n */\nexport function getBackwardsCompatibleTone(\n  themeCtx: ThemeContextValue,\n): Exclude<ThemeContextValue['tone'], 'neutral' | 'suggest'> {\n  if (themeCtx.tone !== 'neutral' && themeCtx.tone !== 'suggest') {\n    return themeCtx.tone\n  }\n\n  return themeCtx.tone === 'neutral' ? 'default' : 'primary'\n}\n", "/* eslint-disable no-param-reassign */\n\nimport {type Extension, StateEffect, StateField} from '@codemirror/state'\nimport {Decoration, type DecorationSet, EditorView, lineNumbers} from '@codemirror/view'\nimport type {ThemeContextValue} from '@sanity/ui'\nimport {rgba} from '@sanity/ui/theme'\n\nimport {getBackwardsCompatibleTone} from './backwardsCompatibleTone'\n\nconst highlightLineClass = 'cm-highlight-line'\n\nexport const addLineHighlight = StateEffect.define<number>()\nexport const removeLineHighlight = StateEffect.define<number>()\n\nexport const lineHighlightField = StateField.define({\n  create() {\n    return Decoration.none\n  },\n  update(lines, tr) {\n    lines = lines.map(tr.changes)\n    for (const e of tr.effects) {\n      if (e.is(addLineHighlight)) {\n        lines = lines.update({add: [lineHighlightMark.range(e.value)]})\n      }\n      if (e.is(removeLineHighlight)) {\n        lines = lines.update({\n          filter: (from) => {\n            // removeLineHighlight value is lineStart for the highlight, so keep other effects\n            return from !== e.value\n          },\n        })\n      }\n    }\n    return lines\n  },\n  toJSON(value, state) {\n    const highlightLines: number[] = []\n    const iter = value.iter()\n    while (iter.value) {\n      const lineNumber = state.doc.lineAt(iter.from).number\n      if (!highlightLines.includes(lineNumber)) {\n        highlightLines.push(lineNumber)\n      }\n      iter.next()\n    }\n    return highlightLines\n  },\n  fromJSON(value: number[], state) {\n    const lines = state.doc.lines\n    const highlights = value\n      .filter((line) => line <= lines) // one-indexed\n      .map((line) => lineHighlightMark.range(state.doc.line(line).from))\n    highlights.sort((a, b) => a.from - b.from)\n    try {\n      return Decoration.none.update({\n        add: highlights,\n      })\n    } catch (e) {\n      console.error(e)\n      return Decoration.none\n    }\n  },\n  provide: (f) => EditorView.decorations.from(f),\n})\n\nconst lineHighlightMark = Decoration.line({\n  class: highlightLineClass,\n})\n\nexport const highlightState: {\n  [prop: string]: StateField<DecorationSet>\n} = {\n  highlight: lineHighlightField,\n}\n\nexport interface HighlightLineConfig {\n  onHighlightChange?: (lines: number[]) => void\n  readOnly?: boolean\n  theme: ThemeContextValue\n}\n\nfunction createCodeMirrorTheme(options: {themeCtx: ThemeContextValue}) {\n  const {themeCtx} = options\n\n  const fallbackTone = getBackwardsCompatibleTone(themeCtx)\n\n  const dark = {color: themeCtx.theme.color.dark[fallbackTone]}\n  const light = {color: themeCtx.theme.color.light[fallbackTone]}\n\n  return EditorView.baseTheme({\n    '.cm-lineNumbers': {\n      cursor: 'default',\n    },\n    '.cm-line.cm-line': {\n      position: 'relative',\n    },\n\n    // need set background with pseudoelement so it does not render over selection color\n    [`.${highlightLineClass}::before`]: {\n      position: 'absolute',\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0,\n      zIndex: -3,\n      content: \"''\",\n      boxSizing: 'border-box',\n    },\n    [`&dark .${highlightLineClass}::before`]: {\n      background: rgba(dark.color.muted.caution.pressed.bg, 0.5),\n    },\n    [`&light .${highlightLineClass}::before`]: {\n      background: rgba(light.color.muted.caution.pressed.bg, 0.75),\n    },\n  })\n}\n\nexport const highlightLine = (config: HighlightLineConfig): Extension => {\n  const highlightTheme = createCodeMirrorTheme({themeCtx: config.theme})\n\n  return [\n    lineHighlightField,\n    config.readOnly\n      ? []\n      : lineNumbers({\n          domEventHandlers: {\n            mousedown: (editorView, lineInfo) => {\n              // Determine if the line for the clicked gutter line number has highlighted state or not\n              const line = editorView.state.doc.lineAt(lineInfo.from)\n              let isHighlighted = false\n              editorView.state\n                .field(lineHighlightField)\n                .between(line.from, line.to, (from, to, value) => {\n                  if (value) {\n                    isHighlighted = true\n                    return false // stop iteration\n                  }\n                  return undefined\n                })\n\n              if (isHighlighted) {\n                editorView.dispatch({effects: removeLineHighlight.of(line.from)})\n              } else {\n                editorView.dispatch({effects: addLineHighlight.of(line.from)})\n              }\n              if (config?.onHighlightChange) {\n                config.onHighlightChange(editorView.state.toJSON(highlightState).highlight)\n              }\n              return true\n            },\n          },\n        }),\n    highlightTheme,\n  ]\n}\n\n/**\n * Adds and removes highlights to the provided view using highlightLines\n * @param view\n * @param highlightLines\n */\nexport function setHighlightedLines(view: EditorView, highlightLines: number[]): void {\n  const doc = view.state.doc\n  const lines = doc.lines\n  //1-based line numbers\n  const allLineNumbers = Array.from({length: lines}, (x, i) => i + 1)\n  view.dispatch({\n    effects: allLineNumbers.map((lineNumber) => {\n      const line = doc.line(lineNumber)\n      if (highlightLines?.includes(lineNumber)) {\n        return addLineHighlight.of(line.from)\n      }\n      return removeLineHighlight.of(line.from)\n    }),\n  })\n}\n", "import type {Extension} from '@codemirror/state'\nimport {EditorView} from '@codemirror/view'\nimport {useRootTheme} from '@sanity/ui'\nimport {rgba} from '@sanity/ui/theme'\nimport {useMemo} from 'react'\n\nimport {getBackwardsCompatibleTone} from './backwardsCompatibleTone'\n\nexport function useThemeExtension(): Extension {\n  const themeCtx = useRootTheme()\n\n  return useMemo(() => {\n    const fallbackTone = getBackwardsCompatibleTone(themeCtx)\n    const dark = {color: themeCtx.theme.color.dark[fallbackTone]}\n    const light = {color: themeCtx.theme.color.light[fallbackTone]}\n\n    return EditorView.baseTheme({\n      '&.cm-editor': {\n        height: '100%',\n      },\n      '&.cm-editor.cm-focused': {\n        outline: 'none',\n      },\n\n      // Matching brackets\n      '&.cm-editor.cm-focused .cm-matchingBracket': {\n        backgroundColor: 'transparent',\n      },\n      '&.cm-editor.cm-focused .cm-nonmatchingBracket': {\n        backgroundColor: 'transparent',\n      },\n      '&dark.cm-editor.cm-focused .cm-matchingBracket': {\n        outline: `1px solid ${dark.color.base.border}`,\n      },\n      '&dark.cm-editor.cm-focused .cm-nonmatchingBracket': {\n        outline: `1px solid ${dark.color.base.border}`,\n      },\n      '&light.cm-editor.cm-focused .cm-matchingBracket': {\n        outline: `1px solid ${light.color.base.border}`,\n      },\n      '&light.cm-editor.cm-focused .cm-nonmatchingBracket': {\n        outline: `1px solid ${light.color.base.border}`,\n      },\n\n      // Size and padding of gutter\n      '& .cm-lineNumbers .cm-gutterElement': {\n        minWidth: `32px !important`,\n        padding: `0 8px !important`,\n      },\n      '& .cm-gutter.cm-foldGutter': {\n        width: `0px !important`,\n      },\n\n      // Color of gutter\n      '&dark .cm-gutters': {\n        color: `${rgba(dark.color.card.enabled.code.fg, 0.5)} !important`,\n        borderRight: `1px solid ${rgba(dark.color.base.border, 0.5)}`,\n      },\n      '&light .cm-gutters': {\n        color: `${rgba(light.color.card.enabled.code.fg, 0.5)} !important`,\n        borderRight: `1px solid ${rgba(light.color.base.border, 0.5)}`,\n      },\n    })\n  }, [themeCtx])\n}\n", "import type {Extension} from '@codemirror/state'\nimport {tags as t} from '@lezer/highlight'\nimport {useTheme} from '@sanity/ui'\nimport {rgba} from '@sanity/ui/theme'\nimport {createTheme} from '@uiw/codemirror-themes'\nimport {useMemo} from 'react'\n\nexport function useCodeMirrorTheme(): Extension {\n  const theme = useTheme()\n\n  return useMemo(() => {\n    const {code: codeFont} = theme.sanity.fonts\n    const {base, card, dark, syntax} = theme.sanity.color\n\n    return createTheme({\n      theme: dark ? 'dark' : 'light',\n      settings: {\n        background: card.enabled.bg,\n        foreground: card.enabled.code.fg,\n        lineHighlight: card.enabled.bg,\n        fontFamily: codeFont.family,\n        caret: base.focusRing,\n        selection: rgba(base.focusRing, 0.2),\n        selectionMatch: rgba(base.focusRing, 0.4),\n        gutterBackground: card.disabled.bg,\n        gutterForeground: card.disabled.code.fg,\n        gutterActiveForeground: card.enabled.fg,\n      },\n      styles: [\n        {\n          tag: [t.heading, t.heading2, t.heading3, t.heading4, t.heading5, t.heading6],\n          color: card.enabled.fg,\n        },\n        {tag: t.angleBracket, color: card.enabled.code.fg},\n        {tag: t.atom, color: syntax.keyword},\n        {tag: t.attributeName, color: syntax.attrName},\n        {tag: t.bool, color: syntax.boolean},\n        {tag: t.bracket, color: card.enabled.code.fg},\n        {tag: t.className, color: syntax.className},\n        {tag: t.comment, color: syntax.comment},\n        {tag: t.definition(t.typeName), color: syntax.function},\n        {\n          tag: [\n            t.definition(t.variableName),\n            t.function(t.variableName),\n            t.className,\n            t.attributeName,\n          ],\n          color: syntax.function,\n        },\n        {tag: [t.function(t.propertyName), t.propertyName], color: syntax.function},\n        {tag: t.keyword, color: syntax.keyword},\n        {tag: t.null, color: syntax.number},\n        {tag: t.number, color: syntax.number},\n        {tag: t.meta, color: card.enabled.code.fg},\n        {tag: t.operator, color: syntax.operator},\n        {tag: t.propertyName, color: syntax.property},\n        {tag: [t.string, t.special(t.brace)], color: syntax.string},\n        {tag: t.tagName, color: syntax.className},\n        {tag: t.typeName, color: syntax.keyword},\n      ],\n    })\n  }, [theme])\n}\n", "import type {Extension} from '@codemirror/state'\nimport {EditorView} from '@codemirror/view'\nimport {rem, useTheme} from '@sanity/ui'\nimport {useMemo} from 'react'\n\nexport function useFontSizeExtension(props: {fontSize: number}): Extension {\n  const {fontSize: fontSizeProp} = props\n  const theme = useTheme()\n\n  return useMemo(() => {\n    const {code: codeFont} = theme.sanity.fonts\n    const {fontSize, lineHeight} = codeFont.sizes[fontSizeProp] || codeFont.sizes[2]\n\n    return EditorView.baseTheme({\n      '&': {\n        fontSize: rem(fontSize),\n      },\n\n      '& .cm-scroller': {\n        lineHeight: `${lineHeight / fontSize} !important`,\n      },\n    })\n  }, [fontSizeProp, theme])\n}\n", "import type {Extension} from '@codemirror/state'\nimport {EditorView} from '@codemirror/view'\nimport {useRootTheme} from '@sanity/ui'\nimport CodeMirror, {type ReactCodeMirrorProps, type ReactCodeMirrorRef} from '@uiw/react-codemirror'\nimport {forwardRef, useCallback, useContext, useEffect, useMemo, useState} from 'react'\n\nimport {CodeInputConfigContext} from './CodeModeContext'\nimport {defaultCodeModes} from './defaultCodeModes'\nimport {\n  highlightLine,\n  highlightState,\n  setHighlightedLines,\n} from './extensions/highlightLineExtension'\nimport {useThemeExtension} from './extensions/theme'\nimport {useCodeMirrorTheme} from './extensions/useCodeMirrorTheme'\nimport {useFontSizeExtension} from './extensions/useFontSize'\n\nexport interface CodeMirrorProps extends ReactCodeMirrorProps {\n  highlightLines?: number[]\n  languageMode?: string\n  onHighlightChange?: (lines: number[]) => void\n}\n\n/**\n * CodeMirrorProxy is a wrapper component around CodeMirror that we lazy load to reduce initial bundle size.\n *\n * It is also responsible for integrating any CodeMirror extensions.\n */\nconst CodeMirrorProxy = forwardRef<ReactCodeMirrorRef, CodeMirrorProps>(\n  function CodeMirrorProxy(props, ref) {\n    const {\n      basicSetup: basicSetupProp,\n      highlightLines,\n      languageMode,\n      onHighlightChange,\n      readOnly,\n      value,\n      ...codeMirrorProps\n    } = props\n\n    const themeCtx = useRootTheme()\n    const codeMirrorTheme = useCodeMirrorTheme()\n    const [editorView, setEditorView] = useState<EditorView | undefined>(undefined)\n\n    // Resolve extensions\n    const themeExtension = useThemeExtension()\n    const fontSizeExtension = useFontSizeExtension({fontSize: 1})\n    const languageExtension = useLanguageExtension(languageMode)\n    const highlightLineExtension = useMemo(\n      () =>\n        highlightLine({\n          onHighlightChange,\n          readOnly,\n          theme: themeCtx,\n        }),\n      [onHighlightChange, readOnly, themeCtx],\n    )\n\n    const extensions = useMemo(() => {\n      const baseExtensions = [\n        themeExtension,\n        fontSizeExtension,\n        highlightLineExtension,\n        EditorView.lineWrapping,\n      ]\n      if (languageExtension) {\n        return [...baseExtensions, languageExtension]\n      }\n      return baseExtensions\n    }, [fontSizeExtension, highlightLineExtension, languageExtension, themeExtension])\n\n    useEffect(() => {\n      if (editorView) {\n        setHighlightedLines(editorView, highlightLines ?? [])\n      }\n    }, [editorView, highlightLines, value])\n\n    const initialState = useMemo(() => {\n      return {\n        json: {\n          doc: value ?? '',\n          selection: {\n            main: 0,\n            ranges: [{anchor: 0, head: 0}],\n          },\n          highlight: highlightLines ?? [],\n        },\n        fields: highlightState,\n      }\n      // only need to calculate this on initial render\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    const handleCreateEditor = useCallback((view: EditorView) => {\n      setEditorView(view)\n    }, [])\n\n    const basicSetup = useMemo(\n      () =>\n        basicSetupProp ?? {\n          highlightActiveLine: false,\n        },\n      [basicSetupProp],\n    )\n\n    return (\n      <CodeMirror\n        {...codeMirrorProps}\n        value={value}\n        ref={ref}\n        extensions={extensions}\n        theme={codeMirrorTheme}\n        onCreateEditor={handleCreateEditor}\n        initialState={initialState}\n        basicSetup={basicSetup}\n      />\n    )\n  },\n)\n\nfunction useLanguageExtension(mode?: string) {\n  const codeConfig = useContext(CodeInputConfigContext)\n\n  const [languageExtension, setLanguageExtension] = useState<Extension | undefined>()\n\n  useEffect(() => {\n    const customModes = codeConfig?.codeModes ?? []\n    const modes = [...customModes, ...defaultCodeModes]\n\n    const codeMode = modes.find((m) => m.name === mode)\n    if (!codeMode?.loader) {\n      console.warn(\n        `Found no codeMode for language mode ${mode}, syntax highlighting will be disabled.`,\n      )\n    }\n    let active = true\n    Promise.resolve(codeMode?.loader())\n      .then((extension) => {\n        if (active) {\n          setLanguageExtension(extension)\n        }\n      })\n      .catch((e) => {\n        console.error(`Failed to load language mode ${mode}`, e)\n        if (active) {\n          setLanguageExtension(undefined)\n        }\n      })\n    return () => {\n      active = false\n    }\n  }, [mode, codeConfig])\n\n  return languageExtension\n}\n\nexport default CodeMirrorProxy\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAI,cAAc,UAAQ;AAC/B,MAAI;AAAA,IACF;AAAA,IACA,WAAW,CAAC;AAAA,IACZ,SAAS,CAAC;AAAA,EACZ,IAAI;AACJ,MAAI,eAAe;AAAA,IACjB,eAAe,CAAC;AAAA,EAClB;AACA,MAAI,YAAY,CAAC;AACjB,MAAI,SAAS,YAAY;AACvB,cAAU,kBAAkB,SAAS;AAAA,EACvC;AACA,MAAI,SAAS,iBAAiB;AAC5B,cAAU,kBAAkB,SAAS;AAAA,EACvC;AACA,MAAI,SAAS,YAAY;AACvB,cAAU,QAAQ,SAAS;AAAA,EAC7B;AACA,MAAI,SAAS,UAAU;AACrB,cAAU,WAAW,SAAS;AAAA,EAChC;AACA,MAAI,SAAS,cAAc,SAAS,YAAY;AAC9C,iBAAa,GAAG,IAAI;AAAA,EACtB;AACA,MAAI,SAAS,YAAY;AACvB,iBAAa,0BAA0B,IAAI;AAAA,MACzC,YAAY,SAAS;AAAA,IACvB;AAAA,EACF;AACA,MAAI,SAAS,kBAAkB;AAC7B,iBAAa,aAAa,EAAE,kBAAkB,SAAS;AAAA,EACzD;AACA,MAAI,SAAS,kBAAkB;AAC7B,iBAAa,aAAa,EAAE,QAAQ,SAAS;AAAA,EAC/C;AACA,MAAI,SAAS,cAAc;AACzB,iBAAa,aAAa,EAAE,mBAAmB,SAAS;AAAA,EAC1D;AACA,MAAI,SAAS,OAAO;AAClB,iBAAa,aAAa,IAAI;AAAA,MAC5B,YAAY,SAAS;AAAA,IACvB;AACA,iBAAa,4BAA4B,IAAI;AAAA,MAC3C,iBAAiB,SAAS;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,wBAAwB,CAAC;AAC7B,MAAI,SAAS,wBAAwB;AACnC,0BAAsB,QAAQ,SAAS;AAAA,EACzC;AACA,MAAI,SAAS,eAAe;AAC1B,iBAAa,gBAAgB,IAAI;AAAA,MAC/B,iBAAiB,SAAS;AAAA,IAC5B;AACA,0BAAsB,kBAAkB,SAAS;AAAA,EACnD;AACA,eAAa,sBAAsB,IAAI;AACvC,MAAI,SAAS,WAAW;AACtB,iBAAa,oIAAoI,IAAI;AAAA,MACnJ,YAAY,SAAS,YAAY;AAAA,IACnC;AAAA,EACF;AACA,MAAI,SAAS,gBAAgB;AAC3B,iBAAa,sBAAsB,IAAI;AAAA,MACrC,iBAAiB,SAAS;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW,MAAM,cAAc;AAAA,IAClD,MAAM,UAAU;AAAA,EAClB,CAAC;AACD,MAAI,iBAAiB,eAAe,OAAO,MAAM;AACjD,MAAI,YAAY,CAAC,gBAAgB,mBAAmB,cAAc,CAAC;AACnE,SAAO;AACT;;;ACnEO,IAAM,mBAA+B;EAC1C;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAA6B,EAAE,KAAK,CAAC,EAAC,mBAAkB,MAAM,kBAAkB;EAC3F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAA6B,EAAE,KAAK,CAAC,EAAC,WAAA,MAAgB,WAAW,EAAC,KAAK,MAAA,CAAM,CAAC;EACzF;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAA6B,EAAE,KAAK,CAAC,EAAC,WAAA,MAAgB,WAAW,EAAC,KAAK,KAAA,CAAK,CAAC;EACxF;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAA6B,EAAE;MAAK,CAAC,EAAC,WAC3C,MAAA,WAAW,EAAC,KAAK,OAAO,YAAY,KAAK,CAAA;IAAA;EAE/C;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAA6B,EAAE;MAAK,CAAC,EAAC,WAC3C,MAAA,WAAW,EAAC,KAAK,MAAM,YAAY,KAAK,CAAA;IAAA;EAE9C;EACA,EAAC,MAAM,OAAO,QAAQ,MAAM,OAAO,oBAAsB,EAAE,KAAK,CAAC,EAAC,IAAA,MAAS,IAAK,CAAA,EAAC;EACjF,EAAC,MAAM,OAAO,QAAQ,MAAM,OAAO,oBAAsB,EAAE,KAAK,CAAC,EAAC,IAAA,MAAS,IAAK,CAAA,EAAC;EACjF;IACE,MAAM;IACN,QAAQ,MAAM,OAAO,oBAAsB,EAAE,KAAK,CAAC,EAAC,KAAK,MAAA,MAAW,IAAI,EAAC,SAAS,MAAA,CAAM,CAAC;EAC3F;EACA,EAAC,MAAM,QAAQ,QAAQ,MAAM,OAAO,oBAAuB,EAAE,KAAK,CAAC,EAAC,KAAA,MAAU,KAAM,CAAA,EAAC;EACrF;IACE,MAAM;IACN,QAAQ,MAAM,OAAO,oBAA2B,EAAE,KAAK,CAAC,EAAC,SAAc,MAAA,SAAU,CAAA;EACnF;EACA,EAAC,MAAM,QAAQ,QAAQ,MAAM,OAAO,oBAAuB,EAAE,KAAK,CAAC,EAAC,KAAA,MAAU,KAAM,CAAA,EAAC;EACrF,EAAC,MAAM,QAAQ,QAAQ,MAAM,OAAO,oBAAuB,EAAE,KAAK,CAAC,EAAC,KAAA,MAAU,KAAM,CAAA,EAAC;EACrF;IACE,MAAM;IACN,QAAQ,MACN,OAAO,qBAAqC,EAAE;MAAK,CAAC,EAAC,OAAA,MACnD,eAAe,OAAO,MAAM;IAAA;EAElC;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,qBAAqC,EAAE,KAAK,CAAC,EAAC,MAAA,MAAW,eAAe,OAAO,KAAK,CAAC;EAChG;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,mBAAmC,EAAE,KAAK,CAAC,EAAC,IAAA,MAAS,eAAe,OAAO,GAAG,CAAC;EAC1F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,mBAAmC,EAAE,KAAK,CAAC,EAAC,IAAA,MAAS,eAAe,OAAO,GAAG,CAAC;EAC1F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAAoC,EAAE,KAAK,CAAC,EAAC,KAAA,MAAU,eAAe,OAAO,IAAI,CAAC;EAC7F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAAoC,EAAE,KAAK,CAAC,EAAC,KAAA,MAAU,eAAe,OAAO,IAAI,CAAC;EAC7F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,sBAAsC,EAAE;MAAK,CAAC,EAAC,OAAA,MACpD,eAAe,OAAO,MAAM;IAAA;EAElC;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,mBAAmC,EAAE,KAAK,CAAC,EAAC,IAAA,MAAS,eAAe,OAAO,GAAG,CAAC;EAC1F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,oBAAoC,EAAE,KAAK,CAAC,EAAC,KAAA,MAAU,eAAe,OAAO,IAAI,CAAC;EAC7F;EACA;IACE,MAAM;IACN,QAAQ,MACN,OAAO,kBAAkC,EAAE,KAAK,CAAC,EAAC,GAAA,MAAQ,eAAe,OAAO,EAAE,CAAC;EACvF;EACA,EAAC,MAAM,QAAQ,QAAQ,MAAG;EAAA,EAAY;EACtC,EAAC,MAAM,SAAS,QAAQ,MAAG;EAAY,EAAA;AACzC;AClGO,SAAS,2BACd,UAC2D;AACvD,SAAA,SAAS,SAAS,aAAa,SAAS,SAAS,YAC5C,SAAS,OAGX,SAAS,SAAS,YAAY,YAAY;AACnD;ACTA,IAAM,qBAAqB;AAA3B,IAEa,mBAAmB,YAAY,OAAA;AAF5C,IAGa,sBAAsB,YAAY,OAAA;AAH/C,IAKa,qBAAqB,WAAW,OAAO;EAClD,SAAS;AACP,WAAO,WAAW;EACpB;EACA,OAAO,OAAO,IAAI;AACR,YAAA,MAAM,IAAI,GAAG,OAAO;AAC5B,eAAW,KAAK,GAAG;AACb,QAAE,GAAG,gBAAgB,MACvB,QAAQ,MAAM,OAAO,EAAC,KAAK,CAAC,kBAAkB,MAAM,EAAE,KAAK,CAAC,EAAA,CAAE,IAE5D,EAAE,GAAG,mBAAmB,MAC1B,QAAQ,MAAM,OAAO;QACnB,QAAQ,CAAC,SAEA,SAAS,EAAE;MAAA,CAErB;AAGE,WAAA;EACT;EACA,OAAO,OAAO,OAAO;AACnB,UAAM,iBAA2B,CAC3B,GAAA,OAAO,MAAM,KAAK;AACxB,WAAO,KAAK,SAAO;AACjB,YAAM,aAAa,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE;AAC1C,qBAAe,SAAS,UAAU,KACrC,eAAe,KAAK,UAAU,GAEhC,KAAK,KAAK;IAAA;AAEL,WAAA;EACT;EACA,SAAS,OAAiB,OAAO;AACzB,UAAA,QAAQ,MAAM,IAAI,OAClB,aAAa,MAChB,OAAO,CAAC,SAAS,QAAQ,KAAK,EAC9B,IAAI,CAAC,SAAS,kBAAkB,MAAM,MAAM,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC;AACnE,eAAW,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AACrC,QAAA;AACK,aAAA,WAAW,KAAK,OAAO;QAC5B,KAAK;MAAA,CACN;IAAA,SACM,GAAG;AACF,aAAA,QAAA,MAAM,CAAC,GACR,WAAW;IAAA;EAEtB;EACA,SAAS,CAAC,MAAM,WAAW,YAAY,KAAK,CAAC;AAC/C,CAAC;AAtDD,IAwDM,oBAAoB,WAAW,KAAK;EACxC,OAAO;AACT,CAAC;AA1DD,IA4Da,iBAET;EACF,WAAW;AACb;AAQA,SAAS,sBAAsB,SAAwC;AAC/D,QAAA,EAAC,SAAQ,IAAI,SAEb,eAAe,2BAA2B,QAAQ,GAElD,OAAO,EAAC,OAAO,SAAS,MAAM,MAAM,KAAK,YAAY,EAAA,GACrD,QAAQ,EAAC,OAAO,SAAS,MAAM,MAAM,MAAM,YAAY,EAAC;AAE9D,SAAO,WAAW,UAAU;IAC1B,mBAAmB;MACjB,QAAQ;IACV;IACA,oBAAoB;MAClB,UAAU;IACZ;;IAGA,CAAC,IAAI,kBAAkB,UAAU,GAAG;MAClC,UAAU;MACV,KAAK;MACL,QAAQ;MACR,MAAM;MACN,OAAO;MACP,QAAQ;MACR,SAAS;MACT,WAAW;IACb;IACA,CAAC,UAAU,kBAAkB,UAAU,GAAG;MACxC,YAAY,KAAK,KAAK,MAAM,MAAM,QAAQ,QAAQ,IAAI,GAAG;IAC3D;IACA,CAAC,WAAW,kBAAkB,UAAU,GAAG;MACzC,YAAY,KAAK,MAAM,MAAM,MAAM,QAAQ,QAAQ,IAAI,IAAI;IAAA;EAC7D,CACD;AACH;AAEa,IAAA,gBAAgB,CAAC,WAA2C;AACvE,QAAM,iBAAiB,sBAAsB,EAAC,UAAU,OAAO,MAAA,CAAM;AAE9D,SAAA;IACL;IACA,OAAO,WACH,CAAC,IACD,YAAY;MACV,kBAAkB;QAChB,WAAW,CAAC,YAAY,aAAa;AAEnC,gBAAM,OAAO,WAAW,MAAM,IAAI,OAAO,SAAS,IAAI;AACtD,cAAI,gBAAgB;AACpB,iBAAA,WAAW,MACR,MAAM,kBAAkB,EACxB,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,UAAU;AAC5C,gBAAA;AACF,qBAAA,gBAAgB,MACT;UAAA,CAGV,GAEC,gBACF,WAAW,SAAS,EAAC,SAAS,oBAAoB,GAAG,KAAK,IAAI,EAAE,CAAA,IAEhE,WAAW,SAAS,EAAC,SAAS,iBAAiB,GAAG,KAAK,IAAI,EAAC,CAAC,GAE3D,UAAQ,QAAA,OAAA,qBACV,OAAO,kBAAkB,WAAW,MAAM,OAAO,cAAc,EAAE,SAAS,GAErE;QAAA;MACT;IACF,CACD;IACL;EACF;AACF;AAOgB,SAAA,oBAAoB,MAAkB,gBAAgC;AACpF,QAAM,MAAM,KAAK,MAAM,KACjB,QAAQ,IAAI,OAEZ,iBAAiB,MAAM,KAAK,EAAC,QAAQ,MAAK,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC;AAClE,OAAK,SAAS;IACZ,SAAS,eAAe,IAAI,CAAC,eAAe;AACpC,YAAA,OAAO,IAAI,KAAK,UAAU;AAC5B,aAAA,kBAAA,QAAA,eAAgB,SAAS,UAAA,IACpB,iBAAiB,GAAG,KAAK,IAAI,IAE/B,oBAAoB,GAAG,KAAK,IAAI;IACxC,CAAA;EAAA,CACF;AACH;ACvKO,SAAS,oBAA+B;AAC7C,QAAM,WAAW,aAAa;AAE9B,aAAO,sBAAQ,MAAM;AACb,UAAA,eAAe,2BAA2B,QAAQ,GAClD,OAAO,EAAC,OAAO,SAAS,MAAM,MAAM,KAAK,YAAY,EAAC,GACtD,QAAQ,EAAC,OAAO,SAAS,MAAM,MAAM,MAAM,YAAY,EAAC;AAE9D,WAAO,WAAW,UAAU;MAC1B,eAAe;QACb,QAAQ;MACV;MACA,0BAA0B;QACxB,SAAS;MACX;;MAGA,8CAA8C;QAC5C,iBAAiB;MACnB;MACA,iDAAiD;QAC/C,iBAAiB;MACnB;MACA,kDAAkD;QAChD,SAAS,aAAa,KAAK,MAAM,KAAK,MAAM;MAC9C;MACA,qDAAqD;QACnD,SAAS,aAAa,KAAK,MAAM,KAAK,MAAM;MAC9C;MACA,mDAAmD;QACjD,SAAS,aAAa,MAAM,MAAM,KAAK,MAAM;MAC/C;MACA,sDAAsD;QACpD,SAAS,aAAa,MAAM,MAAM,KAAK,MAAM;MAC/C;;MAGA,uCAAuC;QACrC,UAAU;QACV,SAAS;MACX;MACA,8BAA8B;QAC5B,OAAO;MACT;;MAGA,qBAAqB;QACnB,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,CAAC;QACpD,aAAa,aAAa,KAAK,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;MAC7D;MACA,sBAAsB;QACpB,OAAO,GAAG,KAAK,MAAM,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,CAAC;QACrD,aAAa,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ,GAAG,CAAC;MAAA;IAC9D,CACD;EAAA,GACA,CAAC,QAAQ,CAAC;AACf;ACzDO,SAAS,qBAAgC;AAC9C,QAAM,QAAQ,SAAS;AAEvB,aAAO,sBAAQ,MAAM;AACnB,UAAM,EAAC,MAAM,SAAQ,IAAI,MAAM,OAAO,OAChC,EAAC,MAAM,MAAM,MAAM,OAAM,IAAI,MAAM,OAAO;AAEhD,WAAO,YAAY;MACjB,OAAO,OAAO,SAAS;MACvB,UAAU;QACR,YAAY,KAAK,QAAQ;QACzB,YAAY,KAAK,QAAQ,KAAK;QAC9B,eAAe,KAAK,QAAQ;QAC5B,YAAY,SAAS;QACrB,OAAO,KAAK;QACZ,WAAW,KAAK,KAAK,WAAW,GAAG;QACnC,gBAAgB,KAAK,KAAK,WAAW,GAAG;QACxC,kBAAkB,KAAK,SAAS;QAChC,kBAAkB,KAAK,SAAS,KAAK;QACrC,wBAAwB,KAAK,QAAQ;MACvC;MACA,QAAQ;QACN;UACE,KAAK,CAACA,KAAE,SAASA,KAAE,UAAUA,KAAE,UAAUA,KAAE,UAAUA,KAAE,UAAUA,KAAE,QAAQ;UAC3E,OAAO,KAAK,QAAQ;QACtB;QACA,EAAC,KAAKA,KAAE,cAAc,OAAO,KAAK,QAAQ,KAAK,GAAE;QACjD,EAAC,KAAKA,KAAE,MAAM,OAAO,OAAO,QAAO;QACnC,EAAC,KAAKA,KAAE,eAAe,OAAO,OAAO,SAAQ;QAC7C,EAAC,KAAKA,KAAE,MAAM,OAAO,OAAO,QAAO;QACnC,EAAC,KAAKA,KAAE,SAAS,OAAO,KAAK,QAAQ,KAAK,GAAE;QAC5C,EAAC,KAAKA,KAAE,WAAW,OAAO,OAAO,UAAS;QAC1C,EAAC,KAAKA,KAAE,SAAS,OAAO,OAAO,QAAO;QACtC,EAAC,KAAKA,KAAE,WAAWA,KAAE,QAAQ,GAAG,OAAO,OAAO,SAAQ;QACtD;UACE,KAAK;YACHA,KAAE,WAAWA,KAAE,YAAY;YAC3BA,KAAE,SAASA,KAAE,YAAY;YACzBA,KAAE;YACFA,KAAE;UACJ;UACA,OAAO,OAAO;QAChB;QACA,EAAC,KAAK,CAACA,KAAE,SAASA,KAAE,YAAY,GAAGA,KAAE,YAAY,GAAG,OAAO,OAAO,SAAQ;QAC1E,EAAC,KAAKA,KAAE,SAAS,OAAO,OAAO,QAAO;QACtC,EAAC,KAAKA,KAAE,MAAM,OAAO,OAAO,OAAM;QAClC,EAAC,KAAKA,KAAE,QAAQ,OAAO,OAAO,OAAM;QACpC,EAAC,KAAKA,KAAE,MAAM,OAAO,KAAK,QAAQ,KAAK,GAAE;QACzC,EAAC,KAAKA,KAAE,UAAU,OAAO,OAAO,SAAQ;QACxC,EAAC,KAAKA,KAAE,cAAc,OAAO,OAAO,SAAQ;QAC5C,EAAC,KAAK,CAACA,KAAE,QAAQA,KAAE,QAAQA,KAAE,KAAK,CAAC,GAAG,OAAO,OAAO,OAAM;QAC1D,EAAC,KAAKA,KAAE,SAAS,OAAO,OAAO,UAAS;QACxC,EAAC,KAAKA,KAAE,UAAU,OAAO,OAAO,QAAO;MAAA;IACzC,CACD;EAAA,GACA,CAAC,KAAK,CAAC;AACZ;AC1DO,SAAS,qBAAqB,OAAsC;AACzE,QAAM,EAAC,UAAU,aAAA,IAAgB,OAC3B,QAAQ,SAAS;AAEvB,aAAO,sBAAQ,MAAM;AACnB,UAAM,EAAC,MAAM,SAAA,IAAY,MAAM,OAAO,OAChC,EAAC,UAAU,WAAA,IAAc,SAAS,MAAM,YAAY,KAAK,SAAS,MAAM,CAAC;AAE/E,WAAO,WAAW,UAAU;MAC1B,KAAK;QACH,UAAU,IAAI,QAAQ;MACxB;MAEA,kBAAkB;QAChB,YAAY,GAAG,aAAa,QAAQ;MAAA;IACtC,CACD;EAAA,GACA,CAAC,cAAc,KAAK,CAAC;AAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;ACKA,IAAM,sBAAkB;EACtB,SAAyB,OAAO,KAAK;AACnC,UAQI,KAPF,OAAA;MAAY,YAAA;MACZ;MACA;MACA;MACA;MACA;IApCN,IAsCQ,IADC,kBAAA,UACD,IADC;MANH;MACA;MACA;MACA;MACA;MACA;IAII,CAAA,GAAA,WAAW,aAAa,GACxB,kBAAkB,mBAAA,GAClB,CAAC,YAAY,aAAa,QAAI,uBAAiC,MAAS,GAGxE,iBAAiB,kBAAA,GACjB,oBAAoB,qBAAqB,EAAC,UAAU,EAAA,CAAE,GACtD,oBAAoB,qBAAqB,YAAY,GACrD,6BAAyB;MAC7B,MACE,cAAc;QACZ;QACA;QACA,OAAO;MAAA,CACR;MACH,CAAC,mBAAmB,UAAU,QAAQ;IAAA,GAGlC,iBAAa,sBAAQ,MAAM;AAC/B,YAAM,iBAAiB;QACrB;QACA;QACA;QACA,WAAW;MACb;AACA,aAAI,oBACK,CAAC,GAAG,gBAAgB,iBAAiB,IAEvC;IAAA,GACN,CAAC,mBAAmB,wBAAwB,mBAAmB,cAAc,CAAC;AAEjF,gCAAU,MAAM;AACV,oBACF,oBAAoB,YAAY,kBAAA,OAAA,iBAAkB,CAAA,CAAE;IAErD,GAAA,CAAC,YAAY,gBAAgB,KAAK,CAAC;AAEhC,UAAA,mBAAe,sBAAQ,OACpB;MACL,MAAM;QACJ,KAAK,SAAS,OAAA,QAAA;QACd,WAAW;UACT,MAAM;UACN,QAAQ,CAAC,EAAC,QAAQ,GAAG,MAAM,EAAE,CAAA;QAC/B;QACA,WAAW,kBAAA,OAAA,iBAAkB,CAAA;MAC/B;MACA,QAAQ;IAAA,IAIT,CAAA,CAAE,GAEC,yBAAqB,0BAAY,CAAC,SAAqB;AAC3D,oBAAc,IAAI;IAAA,GACjB,CAAE,CAAA,GAEC,iBAAa;MACjB,MACE,kBAAkB,OAAA,iBAAA;QAChB,qBAAqB;MACvB;MACF,CAAC,cAAc;IACjB;AAGE,eAAA;MAAC;MAAA,cAAA,eAAA,CAAA,GACK,eADL,GAAA;QAEC;QACA;QACA;QACA,OAAO;QACP,gBAAgB;QAChB;QACA;MAAA,CAAA;IACF;EAAA;AAGN;AAEA,SAAS,qBAAqB,MAAe;AACrC,QAAA,iBAAa,yBAAW,sBAAsB,GAE9C,CAAC,mBAAmB,oBAAoB,QAAI,uBAAgC;AAElF,aAAA,wBAAU,MAAM;AA7HlB,QAAA;AAiII,UAAM,WAFQ,CAAC,IADK,KAAY,cAAA,OAAA,SAAA,WAAA,cAAZ,OAAA,KAAyB,CAAA,GACd,GAAG,gBAAgB,EAE3B,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI;AAC7C,gBAAA,QAAA,SAAU,UACb,QAAQ;MACN,uCAAuC,IAAI;IAC7C;AAEF,QAAI,SAAS;AACb,WAAA,QAAQ,QAAQ,YAAU,OAAA,SAAA,SAAA,OAAQ,CAAA,EAC/B,KAAK,CAAC,cAAc;AACf,gBACF,qBAAqB,SAAS;IAAA,CAEjC,EACA,MAAM,CAAC,MAAM;AACJ,cAAA,MAAM,gCAAgC,IAAI,IAAI,CAAC,GACnD,UACF,qBAAqB,MAAS;IAEjC,CAAA,GACI,MAAM;AACF,eAAA;IACX;EACC,GAAA,CAAC,MAAM,UAAU,CAAC,GAEd;AACT;", "names": ["t"]}
import {
  esm_default
} from "./chunk-J4YCMODO.js";
import "./chunk-SGSHY4V2.js";
import {
  Decoration,
  EditorView,
  HighlightStyle,
  StateEffect,
  StateField,
  StreamLanguage,
  lineNumbers,
  syntaxHighlighting,
  tags
} from "./chunk-EOZ53ZFO.js";
import {
  CodeInputConfigContext
} from "./chunk-7YEY24D2.js";
import "./chunk-6XMXOQIO.js";
import {
  rem,
  rgba,
  useRootTheme,
  useTheme
} from "./chunk-3HD5UKUW.js";
import "./chunk-4HFGZHGE.js";
import "./chunk-CJ3ODOED.js";
import "./chunk-DWYNPLUJ.js";
import "./chunk-NB2E7ZMB.js";
import "./chunk-V3YD7LXU.js";
import {
  require_jsx_runtime
} from "./chunk-EVVIBKQA.js";
import {
  require_react
} from "./chunk-3GN7GPJI.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// ../node_modules/@sanity/code-input/lib/_chunks-es/CodeMirrorProxy.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);

// ../node_modules/@uiw/codemirror-themes/esm/index.js
var createTheme = (_ref) => {
  var {
    theme,
    settings = {},
    styles = []
  } = _ref;
  var themeOptions = {
    ".cm-gutters": {}
  };
  var baseStyle = {};
  if (settings.background) {
    baseStyle.backgroundColor = settings.background;
  }
  if (settings.backgroundImage) {
    baseStyle.backgroundImage = settings.backgroundImage;
  }
  if (settings.foreground) {
    baseStyle.color = settings.foreground;
  }
  if (settings.fontSize) {
    baseStyle.fontSize = settings.fontSize;
  }
  if (settings.background || settings.foreground) {
    themeOptions["&"] = baseStyle;
  }
  if (settings.fontFamily) {
    themeOptions["&.cm-editor .cm-scroller"] = {
      fontFamily: settings.fontFamily
    };
  }
  if (settings.gutterBackground) {
    themeOptions[".cm-gutters"].backgroundColor = settings.gutterBackground;
  }
  if (settings.gutterForeground) {
    themeOptions[".cm-gutters"].color = settings.gutterForeground;
  }
  if (settings.gutterBorder) {
    themeOptions[".cm-gutters"].borderRightColor = settings.gutterBorder;
  }
  if (settings.caret) {
    themeOptions[".cm-content"] = {
      caretColor: settings.caret
    };
    themeOptions[".cm-cursor, .cm-dropCursor"] = {
      borderLeftColor: settings.caret
    };
  }
  var activeLineGutterStyle = {};
  if (settings.gutterActiveForeground) {
    activeLineGutterStyle.color = settings.gutterActiveForeground;
  }
  if (settings.lineHighlight) {
    themeOptions[".cm-activeLine"] = {
      backgroundColor: settings.lineHighlight
    };
    activeLineGutterStyle.backgroundColor = settings.lineHighlight;
  }
  themeOptions[".cm-activeLineGutter"] = activeLineGutterStyle;
  if (settings.selection) {
    themeOptions["&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection"] = {
      background: settings.selection + " !important"
    };
  }
  if (settings.selectionMatch) {
    themeOptions["& .cm-selectionMatch"] = {
      backgroundColor: settings.selectionMatch
    };
  }
  var themeExtension = EditorView.theme(themeOptions, {
    dark: theme === "dark"
  });
  var highlightStyle = HighlightStyle.define(styles);
  var extension = [themeExtension, syntaxHighlighting(highlightStyle)];
  return extension;
};

// ../node_modules/@sanity/code-input/lib/_chunks-es/CodeMirrorProxy.js
var defaultCodeModes = [
  {
    name: "groq",
    loader: () => import("./dist-UTWDTO53.js").then(({ javascriptLanguage }) => javascriptLanguage)
  },
  {
    name: "javascript",
    loader: () => import("./dist-UTWDTO53.js").then(({ javascript }) => javascript({ jsx: false }))
  },
  {
    name: "jsx",
    loader: () => import("./dist-UTWDTO53.js").then(({ javascript }) => javascript({ jsx: true }))
  },
  {
    name: "typescript",
    loader: () => import("./dist-UTWDTO53.js").then(
      ({ javascript }) => javascript({ jsx: false, typescript: true })
    )
  },
  {
    name: "tsx",
    loader: () => import("./dist-UTWDTO53.js").then(
      ({ javascript }) => javascript({ jsx: true, typescript: true })
    )
  },
  { name: "php", loader: () => import("./dist-T4RE255P.js").then(({ php }) => php()) },
  { name: "sql", loader: () => import("./dist-K3VBXPVZ.js").then(({ sql }) => sql()) },
  {
    name: "mysql",
    loader: () => import("./dist-K3VBXPVZ.js").then(({ sql, MySQL }) => sql({ dialect: MySQL }))
  },
  { name: "json", loader: () => import("./dist-5J6R2OI5.js").then(({ json }) => json()) },
  {
    name: "markdown",
    loader: () => import("./dist-Q5EN5Z66.js").then(({ markdown }) => markdown())
  },
  { name: "java", loader: () => import("./dist-APQMWKIV.js").then(({ java }) => java()) },
  { name: "html", loader: () => import("./dist-TQCWSVOS.js").then(({ html }) => html()) },
  {
    name: "csharp",
    loader: () => import("./clike-M3U2L6WF.js").then(
      ({ csharp }) => StreamLanguage.define(csharp)
    )
  },
  {
    name: "sh",
    loader: () => import("./shell-QWYJIED5.js").then(({ shell }) => StreamLanguage.define(shell))
  },
  {
    name: "css",
    loader: () => import("./css-LVXYFEZL.js").then(({ css }) => StreamLanguage.define(css))
  },
  {
    name: "scss",
    loader: () => import("./css-LVXYFEZL.js").then(({ css }) => StreamLanguage.define(css))
  },
  {
    name: "sass",
    loader: () => import("./sass-LTERBBYM.js").then(({ sass }) => StreamLanguage.define(sass))
  },
  {
    name: "ruby",
    loader: () => import("./ruby-3OVYAA3V.js").then(({ ruby }) => StreamLanguage.define(ruby))
  },
  {
    name: "python",
    loader: () => import("./python-N4YBU32H.js").then(
      ({ python }) => StreamLanguage.define(python)
    )
  },
  {
    name: "xml",
    loader: () => import("./xml-ODRAEG2A.js").then(({ xml }) => StreamLanguage.define(xml))
  },
  {
    name: "yaml",
    loader: () => import("./yaml-ZE2NOQ5Q.js").then(({ yaml }) => StreamLanguage.define(yaml))
  },
  {
    name: "golang",
    loader: () => import("./go-II5MZ6RP.js").then(({ go }) => StreamLanguage.define(go))
  },
  { name: "text", loader: () => {
  } },
  { name: "batch", loader: () => {
  } }
];
function getBackwardsCompatibleTone(themeCtx) {
  return themeCtx.tone !== "neutral" && themeCtx.tone !== "suggest" ? themeCtx.tone : themeCtx.tone === "neutral" ? "default" : "primary";
}
var highlightLineClass = "cm-highlight-line";
var addLineHighlight = StateEffect.define();
var removeLineHighlight = StateEffect.define();
var lineHighlightField = StateField.define({
  create() {
    return Decoration.none;
  },
  update(lines, tr) {
    lines = lines.map(tr.changes);
    for (const e of tr.effects)
      e.is(addLineHighlight) && (lines = lines.update({ add: [lineHighlightMark.range(e.value)] })), e.is(removeLineHighlight) && (lines = lines.update({
        filter: (from) => from !== e.value
      }));
    return lines;
  },
  toJSON(value, state) {
    const highlightLines = [], iter = value.iter();
    for (; iter.value; ) {
      const lineNumber = state.doc.lineAt(iter.from).number;
      highlightLines.includes(lineNumber) || highlightLines.push(lineNumber), iter.next();
    }
    return highlightLines;
  },
  fromJSON(value, state) {
    const lines = state.doc.lines, highlights = value.filter((line) => line <= lines).map((line) => lineHighlightMark.range(state.doc.line(line).from));
    highlights.sort((a, b) => a.from - b.from);
    try {
      return Decoration.none.update({
        add: highlights
      });
    } catch (e) {
      return console.error(e), Decoration.none;
    }
  },
  provide: (f) => EditorView.decorations.from(f)
});
var lineHighlightMark = Decoration.line({
  class: highlightLineClass
});
var highlightState = {
  highlight: lineHighlightField
};
function createCodeMirrorTheme(options) {
  const { themeCtx } = options, fallbackTone = getBackwardsCompatibleTone(themeCtx), dark = { color: themeCtx.theme.color.dark[fallbackTone] }, light = { color: themeCtx.theme.color.light[fallbackTone] };
  return EditorView.baseTheme({
    ".cm-lineNumbers": {
      cursor: "default"
    },
    ".cm-line.cm-line": {
      position: "relative"
    },
    // need set background with pseudoelement so it does not render over selection color
    [`.${highlightLineClass}::before`]: {
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: -3,
      content: "''",
      boxSizing: "border-box"
    },
    [`&dark .${highlightLineClass}::before`]: {
      background: rgba(dark.color.muted.caution.pressed.bg, 0.5)
    },
    [`&light .${highlightLineClass}::before`]: {
      background: rgba(light.color.muted.caution.pressed.bg, 0.75)
    }
  });
}
var highlightLine = (config) => {
  const highlightTheme = createCodeMirrorTheme({ themeCtx: config.theme });
  return [
    lineHighlightField,
    config.readOnly ? [] : lineNumbers({
      domEventHandlers: {
        mousedown: (editorView, lineInfo) => {
          const line = editorView.state.doc.lineAt(lineInfo.from);
          let isHighlighted = false;
          return editorView.state.field(lineHighlightField).between(line.from, line.to, (from, to, value) => {
            if (value)
              return isHighlighted = true, false;
          }), isHighlighted ? editorView.dispatch({ effects: removeLineHighlight.of(line.from) }) : editorView.dispatch({ effects: addLineHighlight.of(line.from) }), config != null && config.onHighlightChange && config.onHighlightChange(editorView.state.toJSON(highlightState).highlight), true;
        }
      }
    }),
    highlightTheme
  ];
};
function setHighlightedLines(view, highlightLines) {
  const doc = view.state.doc, lines = doc.lines, allLineNumbers = Array.from({ length: lines }, (x, i) => i + 1);
  view.dispatch({
    effects: allLineNumbers.map((lineNumber) => {
      const line = doc.line(lineNumber);
      return highlightLines != null && highlightLines.includes(lineNumber) ? addLineHighlight.of(line.from) : removeLineHighlight.of(line.from);
    })
  });
}
function useThemeExtension() {
  const themeCtx = useRootTheme();
  return (0, import_react.useMemo)(() => {
    const fallbackTone = getBackwardsCompatibleTone(themeCtx), dark = { color: themeCtx.theme.color.dark[fallbackTone] }, light = { color: themeCtx.theme.color.light[fallbackTone] };
    return EditorView.baseTheme({
      "&.cm-editor": {
        height: "100%"
      },
      "&.cm-editor.cm-focused": {
        outline: "none"
      },
      // Matching brackets
      "&.cm-editor.cm-focused .cm-matchingBracket": {
        backgroundColor: "transparent"
      },
      "&.cm-editor.cm-focused .cm-nonmatchingBracket": {
        backgroundColor: "transparent"
      },
      "&dark.cm-editor.cm-focused .cm-matchingBracket": {
        outline: `1px solid ${dark.color.base.border}`
      },
      "&dark.cm-editor.cm-focused .cm-nonmatchingBracket": {
        outline: `1px solid ${dark.color.base.border}`
      },
      "&light.cm-editor.cm-focused .cm-matchingBracket": {
        outline: `1px solid ${light.color.base.border}`
      },
      "&light.cm-editor.cm-focused .cm-nonmatchingBracket": {
        outline: `1px solid ${light.color.base.border}`
      },
      // Size and padding of gutter
      "& .cm-lineNumbers .cm-gutterElement": {
        minWidth: "32px !important",
        padding: "0 8px !important"
      },
      "& .cm-gutter.cm-foldGutter": {
        width: "0px !important"
      },
      // Color of gutter
      "&dark .cm-gutters": {
        color: `${rgba(dark.color.card.enabled.code.fg, 0.5)} !important`,
        borderRight: `1px solid ${rgba(dark.color.base.border, 0.5)}`
      },
      "&light .cm-gutters": {
        color: `${rgba(light.color.card.enabled.code.fg, 0.5)} !important`,
        borderRight: `1px solid ${rgba(light.color.base.border, 0.5)}`
      }
    });
  }, [themeCtx]);
}
function useCodeMirrorTheme() {
  const theme = useTheme();
  return (0, import_react.useMemo)(() => {
    const { code: codeFont } = theme.sanity.fonts, { base, card, dark, syntax } = theme.sanity.color;
    return createTheme({
      theme: dark ? "dark" : "light",
      settings: {
        background: card.enabled.bg,
        foreground: card.enabled.code.fg,
        lineHighlight: card.enabled.bg,
        fontFamily: codeFont.family,
        caret: base.focusRing,
        selection: rgba(base.focusRing, 0.2),
        selectionMatch: rgba(base.focusRing, 0.4),
        gutterBackground: card.disabled.bg,
        gutterForeground: card.disabled.code.fg,
        gutterActiveForeground: card.enabled.fg
      },
      styles: [
        {
          tag: [tags.heading, tags.heading2, tags.heading3, tags.heading4, tags.heading5, tags.heading6],
          color: card.enabled.fg
        },
        { tag: tags.angleBracket, color: card.enabled.code.fg },
        { tag: tags.atom, color: syntax.keyword },
        { tag: tags.attributeName, color: syntax.attrName },
        { tag: tags.bool, color: syntax.boolean },
        { tag: tags.bracket, color: card.enabled.code.fg },
        { tag: tags.className, color: syntax.className },
        { tag: tags.comment, color: syntax.comment },
        { tag: tags.definition(tags.typeName), color: syntax.function },
        {
          tag: [
            tags.definition(tags.variableName),
            tags.function(tags.variableName),
            tags.className,
            tags.attributeName
          ],
          color: syntax.function
        },
        { tag: [tags.function(tags.propertyName), tags.propertyName], color: syntax.function },
        { tag: tags.keyword, color: syntax.keyword },
        { tag: tags.null, color: syntax.number },
        { tag: tags.number, color: syntax.number },
        { tag: tags.meta, color: card.enabled.code.fg },
        { tag: tags.operator, color: syntax.operator },
        { tag: tags.propertyName, color: syntax.property },
        { tag: [tags.string, tags.special(tags.brace)], color: syntax.string },
        { tag: tags.tagName, color: syntax.className },
        { tag: tags.typeName, color: syntax.keyword }
      ]
    });
  }, [theme]);
}
function useFontSizeExtension(props) {
  const { fontSize: fontSizeProp } = props, theme = useTheme();
  return (0, import_react.useMemo)(() => {
    const { code: codeFont } = theme.sanity.fonts, { fontSize, lineHeight } = codeFont.sizes[fontSizeProp] || codeFont.sizes[2];
    return EditorView.baseTheme({
      "&": {
        fontSize: rem(fontSize)
      },
      "& .cm-scroller": {
        lineHeight: `${lineHeight / fontSize} !important`
      }
    });
  }, [fontSizeProp, theme]);
}
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    __hasOwnProp.call(b, prop) && __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b))
      __propIsEnum.call(b, prop) && __defNormalProp(a, prop, b[prop]);
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    __hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0 && (target[prop] = source[prop]);
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source))
      exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop) && (target[prop] = source[prop]);
  return target;
};
var CodeMirrorProxy = (0, import_react.forwardRef)(
  function(props, ref) {
    const _a = props, {
      basicSetup: basicSetupProp,
      highlightLines,
      languageMode,
      onHighlightChange,
      readOnly,
      value
    } = _a, codeMirrorProps = __objRest(_a, [
      "basicSetup",
      "highlightLines",
      "languageMode",
      "onHighlightChange",
      "readOnly",
      "value"
    ]), themeCtx = useRootTheme(), codeMirrorTheme = useCodeMirrorTheme(), [editorView, setEditorView] = (0, import_react.useState)(void 0), themeExtension = useThemeExtension(), fontSizeExtension = useFontSizeExtension({ fontSize: 1 }), languageExtension = useLanguageExtension(languageMode), highlightLineExtension = (0, import_react.useMemo)(
      () => highlightLine({
        onHighlightChange,
        readOnly,
        theme: themeCtx
      }),
      [onHighlightChange, readOnly, themeCtx]
    ), extensions = (0, import_react.useMemo)(() => {
      const baseExtensions = [
        themeExtension,
        fontSizeExtension,
        highlightLineExtension,
        EditorView.lineWrapping
      ];
      return languageExtension ? [...baseExtensions, languageExtension] : baseExtensions;
    }, [fontSizeExtension, highlightLineExtension, languageExtension, themeExtension]);
    (0, import_react.useEffect)(() => {
      editorView && setHighlightedLines(editorView, highlightLines != null ? highlightLines : []);
    }, [editorView, highlightLines, value]);
    const initialState = (0, import_react.useMemo)(() => ({
      json: {
        doc: value != null ? value : "",
        selection: {
          main: 0,
          ranges: [{ anchor: 0, head: 0 }]
        },
        highlight: highlightLines != null ? highlightLines : []
      },
      fields: highlightState
    }), []), handleCreateEditor = (0, import_react.useCallback)((view) => {
      setEditorView(view);
    }, []), basicSetup = (0, import_react.useMemo)(
      () => basicSetupProp != null ? basicSetupProp : {
        highlightActiveLine: false
      },
      [basicSetupProp]
    );
    return (0, import_jsx_runtime.jsx)(
      esm_default,
      __spreadProps(__spreadValues({}, codeMirrorProps), {
        value,
        ref,
        extensions,
        theme: codeMirrorTheme,
        onCreateEditor: handleCreateEditor,
        initialState,
        basicSetup
      })
    );
  }
);
function useLanguageExtension(mode) {
  const codeConfig = (0, import_react.useContext)(CodeInputConfigContext), [languageExtension, setLanguageExtension] = (0, import_react.useState)();
  return (0, import_react.useEffect)(() => {
    var _a;
    const codeMode = [...(_a = codeConfig == null ? void 0 : codeConfig.codeModes) != null ? _a : [], ...defaultCodeModes].find((m) => m.name === mode);
    codeMode != null && codeMode.loader || console.warn(
      `Found no codeMode for language mode ${mode}, syntax highlighting will be disabled.`
    );
    let active = true;
    return Promise.resolve(codeMode == null ? void 0 : codeMode.loader()).then((extension) => {
      active && setLanguageExtension(extension);
    }).catch((e) => {
      console.error(`Failed to load language mode ${mode}`, e), active && setLanguageExtension(void 0);
    }), () => {
      active = false;
    };
  }, [mode, codeConfig]), languageExtension;
}
export {
  CodeMirrorProxy as default
};
//# sourceMappingURL=CodeMirrorProxy-JMCWFYBW.js.map

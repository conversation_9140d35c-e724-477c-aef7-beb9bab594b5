{"version": 3, "sources": ["../../../../../node_modules/@codemirror/legacy-modes/mode/css.js"], "sourcesContent": ["export function mkCSS(parserConfig) {\n  parserConfig = {...defaults, ...parserConfig}\n  var inline = parserConfig.inline\n\n  var tokenHooks = parserConfig.tokenHooks,\n      documentTypes = parserConfig.documentTypes || {},\n      mediaTypes = parserConfig.mediaTypes || {},\n      mediaFeatures = parserConfig.mediaFeatures || {},\n      mediaValueKeywords = parserConfig.mediaValueKeywords || {},\n      propertyKeywords = parserConfig.propertyKeywords || {},\n      nonStandardPropertyKeywords = parserConfig.nonStandardPropertyKeywords || {},\n      fontProperties = parserConfig.fontProperties || {},\n      counterDescriptors = parserConfig.counterDescriptors || {},\n      colorKeywords = parserConfig.colorKeywords || {},\n      valueKeywords = parserConfig.valueKeywords || {},\n      allowNested = parserConfig.allowNested,\n      lineComment = parserConfig.lineComment,\n      supportsAtComponent = parserConfig.supportsAtComponent === true,\n      highlightNonStandardPropertyKeywords = parserConfig.highlightNonStandardPropertyKeywords !== false;\n\n  var type, override;\n  function ret(style, tp) { type = tp; return style; }\n\n  // Tokenizers\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (tokenHooks[ch]) {\n      var result = tokenHooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == \"@\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"def\", stream.current());\n    } else if (ch == \"=\" || (ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) {\n      return ret(null, \"compare\");\n    } else if (ch == \"\\\"\" || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \"#\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"atom\", \"hash\");\n    } else if (ch == \"!\") {\n      stream.match(/^\\s*\\w*/);\n      return ret(\"keyword\", \"important\");\n    } else if (/\\d/.test(ch) || ch == \".\" && stream.eat(/\\d/)) {\n      stream.eatWhile(/[\\w.%]/);\n      return ret(\"number\", \"unit\");\n    } else if (ch === \"-\") {\n      if (/[\\d.]/.test(stream.peek())) {\n        stream.eatWhile(/[\\w.%]/);\n        return ret(\"number\", \"unit\");\n      } else if (stream.match(/^-[\\w\\\\\\-]*/)) {\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return ret(\"def\", \"variable-definition\");\n        return ret(\"variableName\", \"variable\");\n      } else if (stream.match(/^\\w+-/)) {\n        return ret(\"meta\", \"meta\");\n      }\n    } else if (/[,+>*\\/]/.test(ch)) {\n      return ret(null, \"select-op\");\n    } else if (ch == \".\" && stream.match(/^-?[_a-z][_a-z0-9-]*/i)) {\n      return ret(\"qualifier\", \"qualifier\");\n    } else if (/[:;{}\\[\\]\\(\\)]/.test(ch)) {\n      return ret(null, ch);\n    } else if (stream.match(/^[\\w-.]+(?=\\()/)) {\n      if (/^(url(-prefix)?|domain|regexp)$/i.test(stream.current())) {\n        state.tokenize = tokenParenthesized;\n      }\n      return ret(\"variableName.function\", \"variable\");\n    } else if (/[\\w\\\\\\-]/.test(ch)) {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"property\", \"word\");\n    } else {\n      return ret(null, null);\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          if (quote == \")\") stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && ch == \"\\\\\";\n      }\n      if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenParenthesized(stream, state) {\n    stream.next(); // Must be '('\n    if (!stream.match(/^\\s*[\\\"\\')]/, false))\n      state.tokenize = tokenString(\")\");\n    else\n      state.tokenize = null;\n    return ret(null, \"(\");\n  }\n\n  // Context management\n\n  function Context(type, indent, prev) {\n    this.type = type;\n    this.indent = indent;\n    this.prev = prev;\n  }\n\n  function pushContext(state, stream, type, indent) {\n    state.context = new Context(type, stream.indentation() + (indent === false ? 0 : stream.indentUnit), state.context);\n    return type;\n  }\n\n  function popContext(state) {\n    if (state.context.prev)\n      state.context = state.context.prev;\n    return state.context.type;\n  }\n\n  function pass(type, stream, state) {\n    return states[state.context.type](type, stream, state);\n  }\n  function popAndPass(type, stream, state, n) {\n    for (var i = n || 1; i > 0; i--)\n      state.context = state.context.prev;\n    return pass(type, stream, state);\n  }\n\n  // Parser\n\n  function wordAsValue(stream) {\n    var word = stream.current().toLowerCase();\n    if (valueKeywords.hasOwnProperty(word))\n      override = \"atom\";\n    else if (colorKeywords.hasOwnProperty(word))\n      override = \"keyword\";\n    else\n      override = \"variable\";\n  }\n\n  var states = {};\n\n  states.top = function(type, stream, state) {\n    if (type == \"{\") {\n      return pushContext(state, stream, \"block\");\n    } else if (type == \"}\" && state.context.prev) {\n      return popContext(state);\n    } else if (supportsAtComponent && /@component/i.test(type)) {\n      return pushContext(state, stream, \"atComponentBlock\");\n    } else if (/^@(-moz-)?document$/i.test(type)) {\n      return pushContext(state, stream, \"documentTypes\");\n    } else if (/^@(media|supports|(-moz-)?document|import)$/i.test(type)) {\n      return pushContext(state, stream, \"atBlock\");\n    } else if (/^@(font-face|counter-style)/i.test(type)) {\n      state.stateArg = type;\n      return \"restricted_atBlock_before\";\n    } else if (/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(type)) {\n      return \"keyframes\";\n    } else if (type && type.charAt(0) == \"@\") {\n      return pushContext(state, stream, \"at\");\n    } else if (type == \"hash\") {\n      override = \"builtin\";\n    } else if (type == \"word\") {\n      override = \"tag\";\n    } else if (type == \"variable-definition\") {\n      return \"maybeprop\";\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    } else if (type == \":\") {\n      return \"pseudo\";\n    } else if (allowNested && type == \"(\") {\n      return pushContext(state, stream, \"parens\");\n    }\n    return state.context.type;\n  };\n\n  states.block = function(type, stream, state) {\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (propertyKeywords.hasOwnProperty(word)) {\n        override = \"property\";\n        return \"maybeprop\";\n      } else if (nonStandardPropertyKeywords.hasOwnProperty(word)) {\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n        return \"maybeprop\";\n      } else if (allowNested) {\n        override = stream.match(/^\\s*:(?:\\s|$)/, false) ? \"property\" : \"tag\";\n        return \"block\";\n      } else {\n        override = \"error\";\n        return \"maybeprop\";\n      }\n    } else if (type == \"meta\") {\n      return \"block\";\n    } else if (!allowNested && (type == \"hash\" || type == \"qualifier\")) {\n      override = \"error\";\n      return \"block\";\n    } else {\n      return states.top(type, stream, state);\n    }\n  };\n\n  states.maybeprop = function(type, stream, state) {\n    if (type == \":\") return pushContext(state, stream, \"prop\");\n    return pass(type, stream, state);\n  };\n\n  states.prop = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" && allowNested) return pushContext(state, stream, \"propBlock\");\n    if (type == \"}\" || type == \"{\") return popAndPass(type, stream, state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n\n    if (type == \"hash\" && !/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(stream.current())) {\n      override = \"error\";\n    } else if (type == \"word\") {\n      wordAsValue(stream);\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    }\n    return \"prop\";\n  };\n\n  states.propBlock = function(type, _stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"word\") { override = \"property\"; return \"maybeprop\"; }\n    return state.context.type;\n  };\n\n  states.parens = function(type, stream, state) {\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \")\") return popContext(state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n    if (type == \"word\") wordAsValue(stream);\n    return \"parens\";\n  };\n\n  states.pseudo = function(type, stream, state) {\n    if (type == \"meta\") return \"pseudo\";\n\n    if (type == \"word\") {\n      override = \"variableName.constant\";\n      return state.context.type;\n    }\n    return pass(type, stream, state);\n  };\n\n  states.documentTypes = function(type, stream, state) {\n    if (type == \"word\" && documentTypes.hasOwnProperty(stream.current())) {\n      override = \"tag\";\n      return state.context.type;\n    } else {\n      return states.atBlock(type, stream, state);\n    }\n  };\n\n  states.atBlock = function(type, stream, state) {\n    if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n    if (type == \"}\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"{\") return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\");\n\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (word == \"only\" || word == \"not\" || word == \"and\" || word == \"or\")\n        override = \"keyword\";\n      else if (mediaTypes.hasOwnProperty(word))\n        override = \"attribute\";\n      else if (mediaFeatures.hasOwnProperty(word))\n        override = \"property\";\n      else if (mediaValueKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else if (propertyKeywords.hasOwnProperty(word))\n        override = \"property\";\n      else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n      else if (valueKeywords.hasOwnProperty(word))\n        override = \"atom\";\n      else if (colorKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else\n        override = \"error\";\n    }\n    return state.context.type;\n  };\n\n  states.atComponentBlock = function(type, stream, state) {\n    if (type == \"}\")\n      return popAndPass(type, stream, state);\n    if (type == \"{\")\n      return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\", false);\n    if (type == \"word\")\n      override = \"error\";\n    return state.context.type;\n  };\n\n  states.atBlock_parens = function(type, stream, state) {\n    if (type == \")\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state, 2);\n    return states.atBlock(type, stream, state);\n  };\n\n  states.restricted_atBlock_before = function(type, stream, state) {\n    if (type == \"{\")\n      return pushContext(state, stream, \"restricted_atBlock\");\n    if (type == \"word\" && state.stateArg == \"@counter-style\") {\n      override = \"variable\";\n      return \"restricted_atBlock_before\";\n    }\n    return pass(type, stream, state);\n  };\n\n  states.restricted_atBlock = function(type, stream, state) {\n    if (type == \"}\") {\n      state.stateArg = null;\n      return popContext(state);\n    }\n    if (type == \"word\") {\n      if ((state.stateArg == \"@font-face\" && !fontProperties.hasOwnProperty(stream.current().toLowerCase())) ||\n          (state.stateArg == \"@counter-style\" && !counterDescriptors.hasOwnProperty(stream.current().toLowerCase())))\n        override = \"error\";\n      else\n        override = \"property\";\n      return \"maybeprop\";\n    }\n    return \"restricted_atBlock\";\n  };\n\n  states.keyframes = function(type, stream, state) {\n    if (type == \"word\") { override = \"variable\"; return \"keyframes\"; }\n    if (type == \"{\") return pushContext(state, stream, \"top\");\n    return pass(type, stream, state);\n  };\n\n  states.at = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"tag\";\n    else if (type == \"hash\") override = \"builtin\";\n    return \"at\";\n  };\n\n  states.interpolation = function(type, stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"{\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"variable\";\n    else if (type != \"variable\" && type != \"(\" && type != \")\") override = \"error\";\n    return \"interpolation\";\n  };\n\n  return {\n    name: parserConfig.name,\n    startState: function() {\n      return {tokenize: null,\n              state: inline ? \"block\" : \"top\",\n              stateArg: null,\n              context: new Context(inline ? \"block\" : \"top\", 0, null)};\n    },\n\n    token: function(stream, state) {\n      if (!state.tokenize && stream.eatSpace()) return null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style && typeof style == \"object\") {\n        type = style[1];\n        style = style[0];\n      }\n      override = style;\n      if (type != \"comment\")\n        state.state = states[state.state](type, stream, state);\n      return override;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context, ch = textAfter && textAfter.charAt(0);\n      var indent = cx.indent;\n      if (cx.type == \"prop\" && (ch == \"}\" || ch == \")\")) cx = cx.prev;\n      if (cx.prev) {\n        if (ch == \"}\" && (cx.type == \"block\" || cx.type == \"top\" ||\n                          cx.type == \"interpolation\" || cx.type == \"restricted_atBlock\")) {\n          // Resume indentation from parent context.\n          cx = cx.prev;\n          indent = cx.indent;\n        } else if (ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n                   ch == \"{\" && (cx.type == \"at\" || cx.type == \"atBlock\")) {\n          // Dedent relative to current context.\n          indent = Math.max(0, cx.indent - iCx.unit);\n        }\n      }\n      return indent;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*\\}$/,\n      commentTokens: {line: lineComment, block: {open: \"/*\", close: \"*/\"}},\n      autocomplete: allWords\n    }\n  };\n};\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) {\n    keys[array[i].toLowerCase()] = true;\n  }\n  return keys;\n}\n\nvar documentTypes_ = [\n  \"domain\", \"regexp\", \"url\", \"url-prefix\"\n], documentTypes = keySet(documentTypes_);\n\nvar mediaTypes_ = [\n  \"all\", \"aural\", \"braille\", \"handheld\", \"print\", \"projection\", \"screen\",\n  \"tty\", \"tv\", \"embossed\"\n], mediaTypes = keySet(mediaTypes_);\n\nvar mediaFeatures_ = [\n  \"width\", \"min-width\", \"max-width\", \"height\", \"min-height\", \"max-height\",\n  \"device-width\", \"min-device-width\", \"max-device-width\", \"device-height\",\n  \"min-device-height\", \"max-device-height\", \"aspect-ratio\",\n  \"min-aspect-ratio\", \"max-aspect-ratio\", \"device-aspect-ratio\",\n  \"min-device-aspect-ratio\", \"max-device-aspect-ratio\", \"color\", \"min-color\",\n  \"max-color\", \"color-index\", \"min-color-index\", \"max-color-index\",\n  \"monochrome\", \"min-monochrome\", \"max-monochrome\", \"resolution\",\n  \"min-resolution\", \"max-resolution\", \"scan\", \"grid\", \"orientation\",\n  \"device-pixel-ratio\", \"min-device-pixel-ratio\", \"max-device-pixel-ratio\",\n  \"pointer\", \"any-pointer\", \"hover\", \"any-hover\", \"prefers-color-scheme\",\n  \"dynamic-range\", \"video-dynamic-range\"\n], mediaFeatures = keySet(mediaFeatures_);\n\nvar mediaValueKeywords_ = [\n  \"landscape\", \"portrait\", \"none\", \"coarse\", \"fine\", \"on-demand\", \"hover\",\n  \"interlace\", \"progressive\",\n  \"dark\", \"light\",\n  \"standard\", \"high\"\n], mediaValueKeywords = keySet(mediaValueKeywords_);\n\nvar propertyKeywords_ = [\n  \"align-content\", \"align-items\", \"align-self\", \"alignment-adjust\",\n  \"alignment-baseline\", \"all\", \"anchor-point\", \"animation\", \"animation-delay\",\n  \"animation-direction\", \"animation-duration\", \"animation-fill-mode\",\n  \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\n  \"animation-timing-function\", \"appearance\", \"azimuth\", \"backdrop-filter\",\n  \"backface-visibility\", \"background\", \"background-attachment\",\n  \"background-blend-mode\", \"background-clip\", \"background-color\",\n  \"background-image\", \"background-origin\", \"background-position\",\n  \"background-position-x\", \"background-position-y\", \"background-repeat\",\n  \"background-size\", \"baseline-shift\", \"binding\", \"bleed\", \"block-size\",\n  \"bookmark-label\", \"bookmark-level\", \"bookmark-state\", \"bookmark-target\",\n  \"border\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\n  \"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\",\n  \"border-collapse\", \"border-color\", \"border-image\", \"border-image-outset\",\n  \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\n  \"border-image-width\", \"border-left\", \"border-left-color\", \"border-left-style\",\n  \"border-left-width\", \"border-radius\", \"border-right\", \"border-right-color\",\n  \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\",\n  \"border-top\", \"border-top-color\", \"border-top-left-radius\",\n  \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\n  \"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\",\n  \"break-after\", \"break-before\", \"break-inside\", \"caption-side\", \"caret-color\",\n  \"clear\", \"clip\", \"color\", \"color-profile\", \"column-count\", \"column-fill\",\n  \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\",\n  \"column-rule-width\", \"column-span\", \"column-width\", \"columns\", \"contain\",\n  \"content\", \"counter-increment\", \"counter-reset\", \"crop\", \"cue\", \"cue-after\",\n  \"cue-before\", \"cursor\", \"direction\", \"display\", \"dominant-baseline\",\n  \"drop-initial-after-adjust\", \"drop-initial-after-align\",\n  \"drop-initial-before-adjust\", \"drop-initial-before-align\", \"drop-initial-size\",\n  \"drop-initial-value\", \"elevation\", \"empty-cells\", \"fit\", \"fit-content\", \"fit-position\",\n  \"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\",\n  \"flex-shrink\", \"flex-wrap\", \"float\", \"float-offset\", \"flow-from\", \"flow-into\",\n  \"font\", \"font-family\", \"font-feature-settings\", \"font-kerning\",\n  \"font-language-override\", \"font-optical-sizing\", \"font-size\",\n  \"font-size-adjust\", \"font-stretch\", \"font-style\", \"font-synthesis\",\n  \"font-variant\", \"font-variant-alternates\", \"font-variant-caps\",\n  \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\n  \"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\",\n  \"grid\", \"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\",\n  \"grid-column\", \"grid-column-end\", \"grid-column-gap\", \"grid-column-start\",\n  \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-gap\", \"grid-row-start\",\n  \"grid-template\", \"grid-template-areas\", \"grid-template-columns\",\n  \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\", \"icon\",\n  \"image-orientation\", \"image-rendering\", \"image-resolution\", \"inline-box-align\",\n  \"inset\", \"inset-block\", \"inset-block-end\", \"inset-block-start\", \"inset-inline\",\n  \"inset-inline-end\", \"inset-inline-start\", \"isolation\", \"justify-content\",\n  \"justify-items\", \"justify-self\", \"left\", \"letter-spacing\", \"line-break\",\n  \"line-height\", \"line-height-step\", \"line-stacking\", \"line-stacking-ruby\",\n  \"line-stacking-shift\", \"line-stacking-strategy\", \"list-style\",\n  \"list-style-image\", \"list-style-position\", \"list-style-type\", \"margin\",\n  \"margin-bottom\", \"margin-left\", \"margin-right\", \"margin-top\", \"marks\",\n  \"marquee-direction\", \"marquee-loop\", \"marquee-play-count\", \"marquee-speed\",\n  \"marquee-style\", \"mask-clip\", \"mask-composite\", \"mask-image\", \"mask-mode\",\n  \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\",\"mask-type\",\n  \"max-block-size\", \"max-height\", \"max-inline-size\",\n  \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\n  \"mix-blend-mode\", \"move-to\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\",\n  \"nav-up\", \"object-fit\", \"object-position\", \"offset\", \"offset-anchor\",\n  \"offset-distance\", \"offset-path\", \"offset-position\", \"offset-rotate\",\n  \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\",\n  \"outline-style\", \"outline-width\", \"overflow\", \"overflow-style\",\n  \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-bottom\",\n  \"padding-left\", \"padding-right\", \"padding-top\", \"page\", \"page-break-after\",\n  \"page-break-before\", \"page-break-inside\", \"page-policy\", \"pause\",\n  \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pitch\",\n  \"pitch-range\", \"place-content\", \"place-items\", \"place-self\", \"play-during\",\n  \"position\", \"presentation-level\", \"punctuation-trim\", \"quotes\",\n  \"region-break-after\", \"region-break-before\", \"region-break-inside\",\n  \"region-fragment\", \"rendering-intent\", \"resize\", \"rest\", \"rest-after\",\n  \"rest-before\", \"richness\", \"right\", \"rotate\", \"rotation\", \"rotation-point\",\n  \"row-gap\", \"ruby-align\", \"ruby-overhang\", \"ruby-position\", \"ruby-span\",\n  \"scale\", \"scroll-behavior\", \"scroll-margin\", \"scroll-margin-block\",\n  \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\n  \"scroll-margin-inline\", \"scroll-margin-inline-end\",\n  \"scroll-margin-inline-start\", \"scroll-margin-left\", \"scroll-margin-right\",\n  \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\",\n  \"scroll-padding-block-end\", \"scroll-padding-block-start\",\n  \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\n  \"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\",\n  \"scroll-padding-top\", \"scroll-snap-align\", \"scroll-snap-type\",\n  \"shape-image-threshold\", \"shape-inside\", \"shape-margin\", \"shape-outside\",\n  \"size\", \"speak\", \"speak-as\", \"speak-header\", \"speak-numeral\",\n  \"speak-punctuation\", \"speech-rate\", \"stress\", \"string-set\", \"tab-size\",\n  \"table-layout\", \"target\", \"target-name\", \"target-new\", \"target-position\",\n  \"text-align\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\n  \"text-decoration-color\", \"text-decoration-line\", \"text-decoration-skip\",\n  \"text-decoration-skip-ink\", \"text-decoration-style\", \"text-emphasis\",\n  \"text-emphasis-color\", \"text-emphasis-position\", \"text-emphasis-style\",\n  \"text-height\", \"text-indent\", \"text-justify\", \"text-orientation\",\n  \"text-outline\", \"text-overflow\", \"text-rendering\", \"text-shadow\",\n  \"text-size-adjust\", \"text-space-collapse\", \"text-transform\",\n  \"text-underline-position\", \"text-wrap\", \"top\", \"touch-action\", \"transform\", \"transform-origin\",\n  \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\n  \"transition-property\", \"transition-timing-function\", \"translate\",\n  \"unicode-bidi\", \"user-select\", \"vertical-align\", \"visibility\", \"voice-balance\",\n  \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\",\n  \"voice-stress\", \"voice-volume\", \"volume\", \"white-space\", \"widows\", \"width\",\n  \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\", \"writing-mode\", \"z-index\",\n  // SVG-specific\n  \"clip-path\", \"clip-rule\", \"mask\", \"enable-background\", \"filter\", \"flood-color\",\n  \"flood-opacity\", \"lighting-color\", \"stop-color\", \"stop-opacity\", \"pointer-events\",\n  \"color-interpolation\", \"color-interpolation-filters\",\n  \"color-rendering\", \"fill\", \"fill-opacity\", \"fill-rule\", \"image-rendering\",\n  \"marker\", \"marker-end\", \"marker-mid\", \"marker-start\", \"paint-order\", \"shape-rendering\", \"stroke\",\n  \"stroke-dasharray\", \"stroke-dashoffset\", \"stroke-linecap\", \"stroke-linejoin\",\n  \"stroke-miterlimit\", \"stroke-opacity\", \"stroke-width\", \"text-rendering\",\n  \"baseline-shift\", \"dominant-baseline\", \"glyph-orientation-horizontal\",\n  \"glyph-orientation-vertical\", \"text-anchor\", \"writing-mode\",\n], propertyKeywords = keySet(propertyKeywords_);\n\nvar nonStandardPropertyKeywords_ = [\n  \"accent-color\", \"aspect-ratio\", \"border-block\", \"border-block-color\", \"border-block-end\",\n  \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\n  \"border-block-start\", \"border-block-start-color\", \"border-block-start-style\",\n  \"border-block-start-width\", \"border-block-style\", \"border-block-width\",\n  \"border-inline\", \"border-inline-color\", \"border-inline-end\",\n  \"border-inline-end-color\", \"border-inline-end-style\",\n  \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\n  \"border-inline-start-style\", \"border-inline-start-width\",\n  \"border-inline-style\", \"border-inline-width\", \"content-visibility\", \"margin-block\",\n  \"margin-block-end\", \"margin-block-start\", \"margin-inline\", \"margin-inline-end\",\n  \"margin-inline-start\", \"overflow-anchor\", \"overscroll-behavior\", \"padding-block\", \"padding-block-end\",\n  \"padding-block-start\", \"padding-inline\", \"padding-inline-end\",\n  \"padding-inline-start\", \"scroll-snap-stop\", \"scrollbar-3d-light-color\",\n  \"scrollbar-arrow-color\", \"scrollbar-base-color\", \"scrollbar-dark-shadow-color\",\n  \"scrollbar-face-color\", \"scrollbar-highlight-color\", \"scrollbar-shadow-color\",\n  \"scrollbar-track-color\", \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"shape-inside\", \"zoom\"\n], nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_);\n\nvar fontProperties_ = [\n  \"font-display\", \"font-family\", \"src\", \"unicode-range\", \"font-variant\",\n  \"font-feature-settings\", \"font-stretch\", \"font-weight\", \"font-style\"\n], fontProperties = keySet(fontProperties_);\n\nvar counterDescriptors_ = [\n  \"additive-symbols\", \"fallback\", \"negative\", \"pad\", \"prefix\", \"range\",\n  \"speak-as\", \"suffix\", \"symbols\", \"system\"\n], counterDescriptors = keySet(counterDescriptors_);\n\nvar colorKeywords_ = [\n  \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n  \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n  \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n  \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n  \"darkgray\", \"darkgreen\", \"darkgrey\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n  \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n  \"darkslateblue\", \"darkslategray\", \"darkslategrey\", \"darkturquoise\", \"darkviolet\",\n  \"deeppink\", \"deepskyblue\", \"dimgray\", \"dimgrey\", \"dodgerblue\", \"firebrick\",\n  \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n  \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n  \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n  \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n  \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightgrey\", \"lightpink\",\n  \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\", \"lightslategrey\",\n  \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n  \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n  \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n  \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n  \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n  \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n  \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n  \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n  \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n  \"slateblue\", \"slategray\", \"slategrey\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n  \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n  \"whitesmoke\", \"yellow\", \"yellowgreen\"\n], colorKeywords = keySet(colorKeywords_);\n\nvar valueKeywords_ = [\n  \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"afar\",\n  \"after-white-space\", \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\",\n  \"always\", \"amharic\", \"amharic-abegede\", \"antialiased\", \"appworkspace\",\n  \"arabic-indic\", \"armenian\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\", \"avoid-page\",\n  \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\", \"bidi-override\", \"binary\",\n  \"bengali\", \"blink\", \"block\", \"block-axis\", \"blur\", \"bold\", \"bolder\", \"border\", \"border-box\",\n  \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"brightness\", \"bullets\", \"button\",\n  \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"cambodian\",\n  \"capitalize\", \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\",\n  \"cell\", \"center\", \"checkbox\", \"circle\", \"cjk-decimal\", \"cjk-earthly-branch\",\n  \"cjk-heavenly-stem\", \"cjk-ideographic\", \"clear\", \"clip\", \"close-quote\",\n  \"col-resize\", \"collapse\", \"color\", \"color-burn\", \"color-dodge\", \"column\", \"column-reverse\",\n  \"compact\", \"condensed\", \"conic-gradient\", \"contain\", \"content\", \"contents\",\n  \"content-box\", \"context-menu\", \"continuous\", \"contrast\", \"copy\", \"counter\", \"counters\", \"cover\", \"crop\",\n  \"cross\", \"crosshair\", \"cubic-bezier\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n  \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\",\n  \"destination-in\", \"destination-out\", \"destination-over\", \"devanagari\", \"difference\",\n  \"disc\", \"discard\", \"disclosure-closed\", \"disclosure-open\", \"document\",\n  \"dot-dash\", \"dot-dot-dash\",\n  \"dotted\", \"double\", \"down\", \"drop-shadow\", \"e-resize\", \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\",\n  \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\", \"ethiopic\", \"ethiopic-abegede\",\n  \"ethiopic-abegede-am-et\", \"ethiopic-abegede-gez\", \"ethiopic-abegede-ti-er\",\n  \"ethiopic-abegede-ti-et\", \"ethiopic-halehame-aa-er\",\n  \"ethiopic-halehame-aa-et\", \"ethiopic-halehame-am-et\",\n  \"ethiopic-halehame-gez\", \"ethiopic-halehame-om-et\",\n  \"ethiopic-halehame-sid-et\", \"ethiopic-halehame-so-et\",\n  \"ethiopic-halehame-ti-er\", \"ethiopic-halehame-ti-et\", \"ethiopic-halehame-tig\",\n  \"ethiopic-numeric\", \"ew-resize\", \"exclusion\", \"expanded\", \"extends\", \"extra-condensed\",\n  \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\", \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\",\n  \"forwards\", \"from\", \"geometricPrecision\", \"georgian\", \"grayscale\", \"graytext\", \"grid\", \"groove\",\n  \"gujarati\", \"gurmukhi\", \"hand\", \"hangul\", \"hangul-consonant\", \"hard-light\", \"hebrew\",\n  \"help\", \"hidden\", \"hide\", \"higher\", \"highlight\", \"highlighttext\",\n  \"hiragana\", \"hiragana-iroha\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"hue-rotate\", \"icon\", \"ignore\",\n  \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\",\n  \"infobackground\", \"infotext\", \"inherit\", \"initial\", \"inline\", \"inline-axis\",\n  \"inline-block\", \"inline-flex\", \"inline-grid\", \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\",\n  \"italic\", \"japanese-formal\", \"japanese-informal\", \"justify\", \"kannada\",\n  \"katakana\", \"katakana-iroha\", \"keep-all\", \"khmer\",\n  \"korean-hangul-formal\", \"korean-hanja-formal\", \"korean-hanja-informal\",\n  \"landscape\", \"lao\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\",\n  \"line-through\", \"linear\", \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\",\n  \"local\", \"logical\", \"loud\", \"lower\", \"lower-alpha\", \"lower-armenian\",\n  \"lower-greek\", \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\",\n  \"lower-roman\", \"lowercase\", \"ltr\", \"luminosity\", \"malayalam\", \"manipulation\", \"match\", \"matrix\", \"matrix3d\",\n  \"media-play-button\", \"media-slider\", \"media-sliderthumb\",\n  \"media-volume-slider\", \"media-volume-sliderthumb\", \"medium\",\n  \"menu\", \"menulist\", \"menulist-button\",\n  \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n  \"mix\", \"mongolian\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"myanmar\", \"n-resize\",\n  \"narrower\", \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\",\n  \"no-open-quote\", \"no-repeat\", \"none\", \"normal\", \"not-allowed\", \"nowrap\",\n  \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\", \"oblique\", \"octal\", \"opacity\", \"open-quote\",\n  \"optimizeLegibility\", \"optimizeSpeed\", \"oriya\", \"oromo\", \"outset\",\n  \"outside\", \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\",\n  \"painted\", \"page\", \"paused\", \"persian\", \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\",\n  \"pointer\", \"polygon\", \"portrait\", \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\",\n  \"progress\", \"push-button\", \"radial-gradient\", \"radio\", \"read-only\",\n  \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\",\n  \"relative\", \"repeat\", \"repeating-linear-gradient\", \"repeating-radial-gradient\",\n  \"repeating-conic-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n  \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\",\n  \"rotateZ\", \"round\", \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\",\n  \"s-resize\", \"sans-serif\", \"saturate\", \"saturation\", \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\",\n  \"scroll\", \"scrollbar\", \"scroll-position\", \"se-resize\", \"searchfield\",\n  \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"self-start\", \"self-end\",\n  \"semi-condensed\", \"semi-expanded\", \"separate\", \"sepia\", \"serif\", \"show\", \"sidama\",\n  \"simp-chinese-formal\", \"simp-chinese-informal\", \"single\",\n  \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n  \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\",\n  \"small\", \"small-caps\", \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"somali\",\n  \"source-atop\", \"source-in\", \"source-out\", \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\",\n  \"square-button\", \"start\", \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\",\n  \"subpixel-antialiased\", \"svg_masks\", \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\",\n  \"table-caption\", \"table-cell\", \"table-column\", \"table-column-group\",\n  \"table-footer-group\", \"table-header-group\", \"table-row\", \"table-row-group\",\n  \"tamil\",\n  \"telugu\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thai\",\n  \"thick\", \"thin\", \"threeddarkshadow\", \"threedface\", \"threedhighlight\",\n  \"threedlightshadow\", \"threedshadow\", \"tibetan\", \"tigre\", \"tigrinya-er\",\n  \"tigrinya-er-abegede\", \"tigrinya-et\", \"tigrinya-et-abegede\", \"to\", \"top\",\n  \"trad-chinese-formal\", \"trad-chinese-informal\", \"transform\",\n  \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\",\n  \"transparent\", \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\",\n  \"upper-alpha\", \"upper-armenian\", \"upper-greek\", \"upper-hexadecimal\",\n  \"upper-latin\", \"upper-norwegian\", \"upper-roman\", \"uppercase\", \"urdu\", \"url\",\n  \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\", \"visiblePainted\",\n  \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\",\n  \"window\", \"windowframe\", \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\",\n  \"xx-large\", \"xx-small\"\n], valueKeywords = keySet(valueKeywords_);\n\nvar allWords = documentTypes_.concat(mediaTypes_).concat(mediaFeatures_).concat(mediaValueKeywords_)\n    .concat(propertyKeywords_).concat(nonStandardPropertyKeywords_).concat(colorKeywords_)\n    .concat(valueKeywords_);\n\nexport const keywords = {properties: propertyKeywords_, colors: colorKeywords_,\n                         fonts: fontProperties_, values: valueKeywords_, all: allWords}\n\nconst defaults = {\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n}\n\nexport const css = mkCSS({name: \"css\"})\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\nexport const sCSS = mkCSS({\n  name: \"scss\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \":\": function(stream) {\n      if (stream.match(/^\\s*\\{/, false))\n        return [null, null]\n      return false;\n    },\n    \"$\": function(stream) {\n      stream.match(/^[\\w-]+/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName.special\", \"variable\"];\n    },\n    \"#\": function(stream) {\n      if (!stream.eat(\"{\")) return false;\n      return [null, \"interpolation\"];\n    }\n  }\n})\n\nexport const less = mkCSS({\n  name: \"less\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \"@\": function(stream) {\n      if (stream.eat(\"{\")) return [null, \"interpolation\"];\n      if (stream.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\\b/i, false)) return false;\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName\", \"variable\"];\n    },\n    \"&\": function() {\n      return [\"atom\", \"atom\"];\n    }\n  }\n})\n\nexport const gss = mkCSS({\n  name: \"gss\",\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  supportsAtComponent: true,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n})\n"], "mappings": ";AAAO,SAAS,MAAM,cAAc;AAClC,iBAAe,EAAC,GAAG,UAAU,GAAG,aAAY;AAC5C,MAAI,SAAS,aAAa;AAE1B,MAAI,aAAa,aAAa,YAC1BA,iBAAgB,aAAa,iBAAiB,CAAC,GAC/CC,cAAa,aAAa,cAAc,CAAC,GACzCC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/CC,sBAAqB,aAAa,sBAAsB,CAAC,GACzDC,oBAAmB,aAAa,oBAAoB,CAAC,GACrDC,+BAA8B,aAAa,+BAA+B,CAAC,GAC3EC,kBAAiB,aAAa,kBAAkB,CAAC,GACjDC,sBAAqB,aAAa,sBAAsB,CAAC,GACzDC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/CC,iBAAgB,aAAa,iBAAiB,CAAC,GAC/C,cAAc,aAAa,aAC3B,cAAc,aAAa,aAC3B,sBAAsB,aAAa,wBAAwB,MAC3D,uCAAuC,aAAa,yCAAyC;AAEjG,MAAI,MAAM;AACV,WAAS,IAAI,OAAO,IAAI;AAAE,WAAO;AAAI,WAAO;AAAA,EAAO;AAInD,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,WAAW,EAAE,GAAG;AAClB,UAAI,SAAS,WAAW,EAAE,EAAE,QAAQ,KAAK;AACzC,UAAI,WAAW,MAAO,QAAO;AAAA,IAC/B;AACA,QAAI,MAAM,KAAK;AACb,aAAO,SAAS,UAAU;AAC1B,aAAO,IAAI,OAAO,OAAO,QAAQ,CAAC;AAAA,IACpC,WAAW,MAAM,QAAQ,MAAM,OAAO,MAAM,QAAQ,OAAO,IAAI,GAAG,GAAG;AACnE,aAAO,IAAI,MAAM,SAAS;AAAA,IAC5B,WAAW,MAAM,OAAQ,MAAM,KAAK;AAClC,YAAM,WAAW,YAAY,EAAE;AAC/B,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC,WAAW,MAAM,KAAK;AACpB,aAAO,SAAS,UAAU;AAC1B,aAAO,IAAI,QAAQ,MAAM;AAAA,IAC3B,WAAW,MAAM,KAAK;AACpB,aAAO,MAAM,SAAS;AACtB,aAAO,IAAI,WAAW,WAAW;AAAA,IACnC,WAAW,KAAK,KAAK,EAAE,KAAK,MAAM,OAAO,OAAO,IAAI,IAAI,GAAG;AACzD,aAAO,SAAS,QAAQ;AACxB,aAAO,IAAI,UAAU,MAAM;AAAA,IAC7B,WAAW,OAAO,KAAK;AACrB,UAAI,QAAQ,KAAK,OAAO,KAAK,CAAC,GAAG;AAC/B,eAAO,SAAS,QAAQ;AACxB,eAAO,IAAI,UAAU,MAAM;AAAA,MAC7B,WAAW,OAAO,MAAM,aAAa,GAAG;AACtC,eAAO,SAAS,UAAU;AAC1B,YAAI,OAAO,MAAM,SAAS,KAAK;AAC7B,iBAAO,IAAI,OAAO,qBAAqB;AACzC,eAAO,IAAI,gBAAgB,UAAU;AAAA,MACvC,WAAW,OAAO,MAAM,OAAO,GAAG;AAChC,eAAO,IAAI,QAAQ,MAAM;AAAA,MAC3B;AAAA,IACF,WAAW,WAAW,KAAK,EAAE,GAAG;AAC9B,aAAO,IAAI,MAAM,WAAW;AAAA,IAC9B,WAAW,MAAM,OAAO,OAAO,MAAM,uBAAuB,GAAG;AAC7D,aAAO,IAAI,aAAa,WAAW;AAAA,IACrC,WAAW,iBAAiB,KAAK,EAAE,GAAG;AACpC,aAAO,IAAI,MAAM,EAAE;AAAA,IACrB,WAAW,OAAO,MAAM,gBAAgB,GAAG;AACzC,UAAI,mCAAmC,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC7D,cAAM,WAAW;AAAA,MACnB;AACA,aAAO,IAAI,yBAAyB,UAAU;AAAA,IAChD,WAAW,WAAW,KAAK,EAAE,GAAG;AAC9B,aAAO,SAAS,UAAU;AAC1B,aAAO,IAAI,YAAY,MAAM;AAAA,IAC/B,OAAO;AACL,aAAO,IAAI,MAAM,IAAI;AAAA,IACvB;AAAA,EACF;AAEA,WAAS,YAAY,OAAO;AAC1B,WAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,UAAU,OAAO;AACrB,cAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,YAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,cAAI,SAAS,IAAK,QAAO,OAAO,CAAC;AACjC;AAAA,QACF;AACA,kBAAU,CAAC,WAAW,MAAM;AAAA,MAC9B;AACA,UAAI,MAAM,SAAS,CAAC,WAAW,SAAS,IAAK,OAAM,WAAW;AAC9D,aAAO,IAAI,UAAU,QAAQ;AAAA,IAC/B;AAAA,EACF;AAEA,WAAS,mBAAmB,QAAQ,OAAO;AACzC,WAAO,KAAK;AACZ,QAAI,CAAC,OAAO,MAAM,eAAe,KAAK;AACpC,YAAM,WAAW,YAAY,GAAG;AAAA;AAEhC,YAAM,WAAW;AACnB,WAAO,IAAI,MAAM,GAAG;AAAA,EACtB;AAIA,WAAS,QAAQC,OAAM,QAAQ,MAAM;AACnC,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAEA,WAAS,YAAY,OAAO,QAAQA,OAAM,QAAQ;AAChD,UAAM,UAAU,IAAI,QAAQA,OAAM,OAAO,YAAY,KAAK,WAAW,QAAQ,IAAI,OAAO,aAAa,MAAM,OAAO;AAClH,WAAOA;AAAA,EACT;AAEA,WAAS,WAAW,OAAO;AACzB,QAAI,MAAM,QAAQ;AAChB,YAAM,UAAU,MAAM,QAAQ;AAChC,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,WAAS,KAAKA,OAAM,QAAQ,OAAO;AACjC,WAAO,OAAO,MAAM,QAAQ,IAAI,EAAEA,OAAM,QAAQ,KAAK;AAAA,EACvD;AACA,WAAS,WAAWA,OAAM,QAAQ,OAAO,GAAG;AAC1C,aAAS,IAAI,KAAK,GAAG,IAAI,GAAG;AAC1B,YAAM,UAAU,MAAM,QAAQ;AAChC,WAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,EACjC;AAIA,WAAS,YAAY,QAAQ;AAC3B,QAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,QAAID,eAAc,eAAe,IAAI;AACnC,iBAAW;AAAA,aACJD,eAAc,eAAe,IAAI;AACxC,iBAAW;AAAA;AAEX,iBAAW;AAAA,EACf;AAEA,MAAI,SAAS,CAAC;AAEd,SAAO,MAAM,SAASE,OAAM,QAAQ,OAAO;AACzC,QAAIA,SAAQ,KAAK;AACf,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C,WAAWA,SAAQ,OAAO,MAAM,QAAQ,MAAM;AAC5C,aAAO,WAAW,KAAK;AAAA,IACzB,WAAW,uBAAuB,cAAc,KAAKA,KAAI,GAAG;AAC1D,aAAO,YAAY,OAAO,QAAQ,kBAAkB;AAAA,IACtD,WAAW,uBAAuB,KAAKA,KAAI,GAAG;AAC5C,aAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,IACnD,WAAW,+CAA+C,KAAKA,KAAI,GAAG;AACpE,aAAO,YAAY,OAAO,QAAQ,SAAS;AAAA,IAC7C,WAAW,+BAA+B,KAAKA,KAAI,GAAG;AACpD,YAAM,WAAWA;AACjB,aAAO;AAAA,IACT,WAAW,sCAAsC,KAAKA,KAAI,GAAG;AAC3D,aAAO;AAAA,IACT,WAAWA,SAAQA,MAAK,OAAO,CAAC,KAAK,KAAK;AACxC,aAAO,YAAY,OAAO,QAAQ,IAAI;AAAA,IACxC,WAAWA,SAAQ,QAAQ;AACzB,iBAAW;AAAA,IACb,WAAWA,SAAQ,QAAQ;AACzB,iBAAW;AAAA,IACb,WAAWA,SAAQ,uBAAuB;AACxC,aAAO;AAAA,IACT,WAAWA,SAAQ,iBAAiB;AAClC,aAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,IACnD,WAAWA,SAAQ,KAAK;AACtB,aAAO;AAAA,IACT,WAAW,eAAeA,SAAQ,KAAK;AACrC,aAAO,YAAY,OAAO,QAAQ,QAAQ;AAAA,IAC5C;AACA,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,SAAO,QAAQ,SAASA,OAAM,QAAQ,OAAO;AAC3C,QAAIA,SAAQ,QAAQ;AAClB,UAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,UAAIN,kBAAiB,eAAe,IAAI,GAAG;AACzC,mBAAW;AACX,eAAO;AAAA,MACT,WAAWC,6BAA4B,eAAe,IAAI,GAAG;AAC3D,mBAAW,uCAAuC,mBAAmB;AACrE,eAAO;AAAA,MACT,WAAW,aAAa;AACtB,mBAAW,OAAO,MAAM,iBAAiB,KAAK,IAAI,aAAa;AAC/D,eAAO;AAAA,MACT,OAAO;AACL,mBAAW;AACX,eAAO;AAAA,MACT;AAAA,IACF,WAAWK,SAAQ,QAAQ;AACzB,aAAO;AAAA,IACT,WAAW,CAAC,gBAAgBA,SAAQ,UAAUA,SAAQ,cAAc;AAClE,iBAAW;AACX,aAAO;AAAA,IACT,OAAO;AACL,aAAO,OAAO,IAAIA,OAAM,QAAQ,KAAK;AAAA,IACvC;AAAA,EACF;AAEA,SAAO,YAAY,SAASA,OAAM,QAAQ,OAAO;AAC/C,QAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,MAAM;AACzD,WAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,EACjC;AAEA,SAAO,OAAO,SAASA,OAAM,QAAQ,OAAO;AAC1C,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,QAAIA,SAAQ,OAAO,YAAa,QAAO,YAAY,OAAO,QAAQ,WAAW;AAC7E,QAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,QAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,QAAQ;AAE3D,QAAIA,SAAQ,UAAU,CAAC,sDAAsD,KAAK,OAAO,QAAQ,CAAC,GAAG;AACnG,iBAAW;AAAA,IACb,WAAWA,SAAQ,QAAQ;AACzB,kBAAY,MAAM;AAAA,IACpB,WAAWA,SAAQ,iBAAiB;AAClC,aAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAEA,SAAO,YAAY,SAASA,OAAM,SAAS,OAAO;AAChD,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,QAAIA,SAAQ,QAAQ;AAAE,iBAAW;AAAY,aAAO;AAAA,IAAa;AACjE,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,SAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,QAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,QAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,QAAQ;AAC3D,QAAIA,SAAQ,gBAAiB,QAAO,YAAY,OAAO,QAAQ,eAAe;AAC9E,QAAIA,SAAQ,OAAQ,aAAY,MAAM;AACtC,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,QAAIA,SAAQ,OAAQ,QAAO;AAE3B,QAAIA,SAAQ,QAAQ;AAClB,iBAAW;AACX,aAAO,MAAM,QAAQ;AAAA,IACvB;AACA,WAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,EACjC;AAEA,SAAO,gBAAgB,SAASA,OAAM,QAAQ,OAAO;AACnD,QAAIA,SAAQ,UAAUV,eAAc,eAAe,OAAO,QAAQ,CAAC,GAAG;AACpE,iBAAW;AACX,aAAO,MAAM,QAAQ;AAAA,IACvB,OAAO;AACL,aAAO,OAAO,QAAQU,OAAM,QAAQ,KAAK;AAAA,IAC3C;AAAA,EACF;AAEA,SAAO,UAAU,SAASA,OAAM,QAAQ,OAAO;AAC7C,QAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,gBAAgB;AACnE,QAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK,KAAK,YAAY,OAAO,QAAQ,cAAc,UAAU,KAAK;AAErG,QAAIA,SAAQ,gBAAiB,QAAO,YAAY,OAAO,QAAQ,eAAe;AAE9E,QAAIA,SAAQ,QAAQ;AAClB,UAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,UAAI,QAAQ,UAAU,QAAQ,SAAS,QAAQ,SAAS,QAAQ;AAC9D,mBAAW;AAAA,eACJT,YAAW,eAAe,IAAI;AACrC,mBAAW;AAAA,eACJC,eAAc,eAAe,IAAI;AACxC,mBAAW;AAAA,eACJC,oBAAmB,eAAe,IAAI;AAC7C,mBAAW;AAAA,eACJC,kBAAiB,eAAe,IAAI;AAC3C,mBAAW;AAAA,eACJC,6BAA4B,eAAe,IAAI;AACtD,mBAAW,uCAAuC,mBAAmB;AAAA,eAC9DI,eAAc,eAAe,IAAI;AACxC,mBAAW;AAAA,eACJD,eAAc,eAAe,IAAI;AACxC,mBAAW;AAAA;AAEX,mBAAW;AAAA,IACf;AACA,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,SAAO,mBAAmB,SAASE,OAAM,QAAQ,OAAO;AACtD,QAAIA,SAAQ;AACV,aAAO,WAAWA,OAAM,QAAQ,KAAK;AACvC,QAAIA,SAAQ;AACV,aAAO,WAAW,KAAK,KAAK,YAAY,OAAO,QAAQ,cAAc,UAAU,OAAO,KAAK;AAC7F,QAAIA,SAAQ;AACV,iBAAW;AACb,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,SAAO,iBAAiB,SAASA,OAAM,QAAQ,OAAO;AACpD,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,QAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,OAAO,CAAC;AACxE,WAAO,OAAO,QAAQA,OAAM,QAAQ,KAAK;AAAA,EAC3C;AAEA,SAAO,4BAA4B,SAASA,OAAM,QAAQ,OAAO;AAC/D,QAAIA,SAAQ;AACV,aAAO,YAAY,OAAO,QAAQ,oBAAoB;AACxD,QAAIA,SAAQ,UAAU,MAAM,YAAY,kBAAkB;AACxD,iBAAW;AACX,aAAO;AAAA,IACT;AACA,WAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,EACjC;AAEA,SAAO,qBAAqB,SAASA,OAAM,QAAQ,OAAO;AACxD,QAAIA,SAAQ,KAAK;AACf,YAAM,WAAW;AACjB,aAAO,WAAW,KAAK;AAAA,IACzB;AACA,QAAIA,SAAQ,QAAQ;AAClB,UAAK,MAAM,YAAY,gBAAgB,CAACJ,gBAAe,eAAe,OAAO,QAAQ,EAAE,YAAY,CAAC,KAC/F,MAAM,YAAY,oBAAoB,CAACC,oBAAmB,eAAe,OAAO,QAAQ,EAAE,YAAY,CAAC;AAC1G,mBAAW;AAAA;AAEX,mBAAW;AACb,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,SAAO,YAAY,SAASG,OAAM,QAAQ,OAAO;AAC/C,QAAIA,SAAQ,QAAQ;AAAE,iBAAW;AAAY,aAAO;AAAA,IAAa;AACjE,QAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,KAAK;AACxD,WAAO,KAAKA,OAAM,QAAQ,KAAK;AAAA,EACjC;AAEA,SAAO,KAAK,SAASA,OAAM,QAAQ,OAAO;AACxC,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,QAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,QAAIA,SAAQ,OAAQ,YAAW;AAAA,aACtBA,SAAQ,OAAQ,YAAW;AACpC,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,SAASA,OAAM,QAAQ,OAAO;AACnD,QAAIA,SAAQ,IAAK,QAAO,WAAW,KAAK;AACxC,QAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,WAAWA,OAAM,QAAQ,KAAK;AACrE,QAAIA,SAAQ,OAAQ,YAAW;AAAA,aACtBA,SAAQ,cAAcA,SAAQ,OAAOA,SAAQ,IAAK,YAAW;AACtE,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,MAAM,aAAa;AAAA,IACnB,YAAY,WAAW;AACrB,aAAO;AAAA,QAAC,UAAU;AAAA,QACV,OAAO,SAAS,UAAU;AAAA,QAC1B,UAAU;AAAA,QACV,SAAS,IAAI,QAAQ,SAAS,UAAU,OAAO,GAAG,IAAI;AAAA,MAAC;AAAA,IACjE;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,CAAC,MAAM,YAAY,OAAO,SAAS,EAAG,QAAO;AACjD,UAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,UAAI,SAAS,OAAO,SAAS,UAAU;AACrC,eAAO,MAAM,CAAC;AACd,gBAAQ,MAAM,CAAC;AAAA,MACjB;AACA,iBAAW;AACX,UAAI,QAAQ;AACV,cAAM,QAAQ,OAAO,MAAM,KAAK,EAAE,MAAM,QAAQ,KAAK;AACvD,aAAO;AAAA,IACT;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAW,KAAK;AACtC,UAAI,KAAK,MAAM,SAAS,KAAK,aAAa,UAAU,OAAO,CAAC;AAC5D,UAAI,SAAS,GAAG;AAChB,UAAI,GAAG,QAAQ,WAAW,MAAM,OAAO,MAAM,KAAM,MAAK,GAAG;AAC3D,UAAI,GAAG,MAAM;AACX,YAAI,MAAM,QAAQ,GAAG,QAAQ,WAAW,GAAG,QAAQ,SACjC,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,uBAAuB;AAEhF,eAAK,GAAG;AACR,mBAAS,GAAG;AAAA,QACd,WAAW,MAAM,QAAQ,GAAG,QAAQ,YAAY,GAAG,QAAQ,qBAChD,MAAM,QAAQ,GAAG,QAAQ,QAAQ,GAAG,QAAQ,YAAY;AAEjE,mBAAS,KAAK,IAAI,GAAG,GAAG,SAAS,IAAI,IAAI;AAAA,QAC3C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IAEA,cAAc;AAAA,MACZ,eAAe;AAAA,MACf,eAAe,EAAC,MAAM,aAAa,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,MACnE,cAAc;AAAA,IAChB;AAAA,EACF;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,OAAO,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,SAAK,MAAM,CAAC,EAAE,YAAY,CAAC,IAAI;AAAA,EACjC;AACA,SAAO;AACT;AAEA,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAC7B;AAFA,IAEG,gBAAgB,OAAO,cAAc;AAExC,IAAI,cAAc;AAAA,EAChB;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAc;AAAA,EAC9D;AAAA,EAAO;AAAA,EAAM;AACf;AAHA,IAGG,aAAa,OAAO,WAAW;AAElC,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAa;AAAA,EAAU;AAAA,EAAc;AAAA,EAC3D;AAAA,EAAgB;AAAA,EAAoB;AAAA,EAAoB;AAAA,EACxD;AAAA,EAAqB;AAAA,EAAqB;AAAA,EAC1C;AAAA,EAAoB;AAAA,EAAoB;AAAA,EACxC;AAAA,EAA2B;AAAA,EAA2B;AAAA,EAAS;AAAA,EAC/D;AAAA,EAAa;AAAA,EAAe;AAAA,EAAmB;AAAA,EAC/C;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAClD;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACpD;AAAA,EAAsB;AAAA,EAA0B;AAAA,EAChD;AAAA,EAAW;AAAA,EAAe;AAAA,EAAS;AAAA,EAAa;AAAA,EAChD;AAAA,EAAiB;AACnB;AAZA,IAYG,gBAAgB,OAAO,cAAc;AAExC,IAAI,sBAAsB;AAAA,EACxB;AAAA,EAAa;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAa;AAAA,EAChE;AAAA,EAAa;AAAA,EACb;AAAA,EAAQ;AAAA,EACR;AAAA,EAAY;AACd;AALA,IAKG,qBAAqB,OAAO,mBAAmB;AAElD,IAAI,oBAAoB;AAAA,EACtB;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAc;AAAA,EAC9C;AAAA,EAAsB;AAAA,EAAO;AAAA,EAAgB;AAAA,EAAa;AAAA,EAC1D;AAAA,EAAuB;AAAA,EAAsB;AAAA,EAC7C;AAAA,EAA6B;AAAA,EAAkB;AAAA,EAC/C;AAAA,EAA6B;AAAA,EAAc;AAAA,EAAW;AAAA,EACtD;AAAA,EAAuB;AAAA,EAAc;AAAA,EACrC;AAAA,EAAyB;AAAA,EAAmB;AAAA,EAC5C;AAAA,EAAoB;AAAA,EAAqB;AAAA,EACzC;AAAA,EAAyB;AAAA,EAAyB;AAAA,EAClD;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAS;AAAA,EACzD;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAAkB;AAAA,EACtD;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAuB;AAAA,EAClD;AAAA,EAA8B;AAAA,EAAuB;AAAA,EACrD;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EACnD;AAAA,EAAuB;AAAA,EAAsB;AAAA,EAC7C;AAAA,EAAsB;AAAA,EAAe;AAAA,EAAqB;AAAA,EAC1D;AAAA,EAAqB;AAAA,EAAiB;AAAA,EAAgB;AAAA,EACtD;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAkB;AAAA,EAC9D;AAAA,EAAc;AAAA,EAAoB;AAAA,EAClC;AAAA,EAA2B;AAAA,EAAoB;AAAA,EAC/C;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAwB;AAAA,EAAc;AAAA,EAChE;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAC/D;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAC3D;AAAA,EAAc;AAAA,EAAe;AAAA,EAAqB;AAAA,EAClD;AAAA,EAAqB;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAW;AAAA,EAC/D;AAAA,EAAW;AAAA,EAAqB;AAAA,EAAiB;AAAA,EAAQ;AAAA,EAAO;AAAA,EAChE;AAAA,EAAc;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAChD;AAAA,EAA6B;AAAA,EAC7B;AAAA,EAA8B;AAAA,EAA6B;AAAA,EAC3D;AAAA,EAAsB;AAAA,EAAa;AAAA,EAAe;AAAA,EAAO;AAAA,EAAe;AAAA,EACxE;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAa;AAAA,EACrD;AAAA,EAAe;AAAA,EAAa;AAAA,EAAS;AAAA,EAAgB;AAAA,EAAa;AAAA,EAClE;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAyB;AAAA,EAChD;AAAA,EAA0B;AAAA,EAAuB;AAAA,EACjD;AAAA,EAAoB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAClD;AAAA,EAAgB;AAAA,EAA2B;AAAA,EAC3C;AAAA,EAA2B;AAAA,EAA0B;AAAA,EACrD;AAAA,EAAyB;AAAA,EAA2B;AAAA,EAAe;AAAA,EACnE;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAqB;AAAA,EAAkB;AAAA,EAC5D;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAmB;AAAA,EACrD;AAAA,EAAY;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAgB;AAAA,EACxD;AAAA,EAAiB;AAAA,EAAuB;AAAA,EACxC;AAAA,EAAsB;AAAA,EAAuB;AAAA,EAAU;AAAA,EAAW;AAAA,EAClE;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAC5D;AAAA,EAAS;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAChE;AAAA,EAAoB;AAAA,EAAsB;AAAA,EAAa;AAAA,EACvD;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAC3D;AAAA,EAAe;AAAA,EAAoB;AAAA,EAAiB;AAAA,EACpD;AAAA,EAAuB;AAAA,EAA0B;AAAA,EACjD;AAAA,EAAoB;AAAA,EAAuB;AAAA,EAAmB;AAAA,EAC9D;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAc;AAAA,EAC9D;AAAA,EAAqB;AAAA,EAAgB;AAAA,EAAsB;AAAA,EAC3D;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAkB;AAAA,EAAc;AAAA,EAC9D;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAY;AAAA,EAC3D;AAAA,EAAkB;AAAA,EAAc;AAAA,EAChC;AAAA,EAAa;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAmB;AAAA,EAChE;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAClE;AAAA,EAAU;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAU;AAAA,EACrD;AAAA,EAAmB;AAAA,EAAe;AAAA,EAAmB;AAAA,EACrD;AAAA,EAAW;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAAiB;AAAA,EAC3D;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAY;AAAA,EAC9C;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EACxD;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAQ;AAAA,EACxD;AAAA,EAAqB;AAAA,EAAqB;AAAA,EAAe;AAAA,EACzD;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAsB;AAAA,EACpE;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAc;AAAA,EAC7D;AAAA,EAAY;AAAA,EAAsB;AAAA,EAAoB;AAAA,EACtD;AAAA,EAAsB;AAAA,EAAuB;AAAA,EAC7C;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAU;AAAA,EAAQ;AAAA,EACzD;AAAA,EAAe;AAAA,EAAY;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAC1D;AAAA,EAAW;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAC3D;AAAA,EAAS;AAAA,EAAmB;AAAA,EAAiB;AAAA,EAC7C;AAAA,EAA2B;AAAA,EAA6B;AAAA,EACxD;AAAA,EAAwB;AAAA,EACxB;AAAA,EAA8B;AAAA,EAAsB;AAAA,EACpD;AAAA,EAAqB;AAAA,EAAkB;AAAA,EACvC;AAAA,EAA4B;AAAA,EAC5B;AAAA,EAAyB;AAAA,EAAyB;AAAA,EAClD;AAAA,EAA+B;AAAA,EAAuB;AAAA,EACtD;AAAA,EAAsB;AAAA,EAAqB;AAAA,EAC3C;AAAA,EAAyB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EACzD;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAY;AAAA,EAAgB;AAAA,EAC7C;AAAA,EAAqB;AAAA,EAAe;AAAA,EAAU;AAAA,EAAc;AAAA,EAC5D;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAe;AAAA,EAAc;AAAA,EACvD;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAwB;AAAA,EACzD;AAAA,EAAyB;AAAA,EAAwB;AAAA,EACjD;AAAA,EAA4B;AAAA,EAAyB;AAAA,EACrD;AAAA,EAAuB;AAAA,EAA0B;AAAA,EACjD;AAAA,EAAe;AAAA,EAAe;AAAA,EAAgB;AAAA,EAC9C;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAkB;AAAA,EACnD;AAAA,EAAoB;AAAA,EAAuB;AAAA,EAC3C;AAAA,EAA2B;AAAA,EAAa;AAAA,EAAO;AAAA,EAAgB;AAAA,EAAa;AAAA,EAC5E;AAAA,EAAmB;AAAA,EAAc;AAAA,EAAoB;AAAA,EACrD;AAAA,EAAuB;AAAA,EAA8B;AAAA,EACrD;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAkB;AAAA,EAAc;AAAA,EAC/D;AAAA,EAAkB;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAe;AAAA,EAChE;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAe;AAAA,EAAU;AAAA,EACnE;AAAA,EAAe;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAgB;AAAA;AAAA,EAE1E;AAAA,EAAa;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAqB;AAAA,EAAU;AAAA,EACjE;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAgB;AAAA,EACjE;AAAA,EAAuB;AAAA,EACvB;AAAA,EAAmB;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAa;AAAA,EACxD;AAAA,EAAU;AAAA,EAAc;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAmB;AAAA,EACxF;AAAA,EAAoB;AAAA,EAAqB;AAAA,EAAkB;AAAA,EAC3D;AAAA,EAAqB;AAAA,EAAkB;AAAA,EAAgB;AAAA,EACvD;AAAA,EAAkB;AAAA,EAAqB;AAAA,EACvC;AAAA,EAA8B;AAAA,EAAe;AAC/C;AA7GA,IA6GG,mBAAmB,OAAO,iBAAiB;AAE9C,IAAI,+BAA+B;AAAA,EACjC;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAsB;AAAA,EACtE;AAAA,EAA0B;AAAA,EAA0B;AAAA,EACpD;AAAA,EAAsB;AAAA,EAA4B;AAAA,EAClD;AAAA,EAA4B;AAAA,EAAsB;AAAA,EAClD;AAAA,EAAiB;AAAA,EAAuB;AAAA,EACxC;AAAA,EAA2B;AAAA,EAC3B;AAAA,EAA2B;AAAA,EAAuB;AAAA,EAClD;AAAA,EAA6B;AAAA,EAC7B;AAAA,EAAuB;AAAA,EAAuB;AAAA,EAAsB;AAAA,EACpE;AAAA,EAAoB;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAC3D;AAAA,EAAuB;AAAA,EAAmB;AAAA,EAAuB;AAAA,EAAiB;AAAA,EAClF;AAAA,EAAuB;AAAA,EAAkB;AAAA,EACzC;AAAA,EAAwB;AAAA,EAAoB;AAAA,EAC5C;AAAA,EAAyB;AAAA,EAAwB;AAAA,EACjD;AAAA,EAAwB;AAAA,EAA6B;AAAA,EACrD;AAAA,EAAyB;AAAA,EAA6B;AAAA,EACtD;AAAA,EAA8B;AAAA,EAAkC;AAAA,EAAgB;AAClF;AAlBA,IAkBG,8BAA8B,OAAO,4BAA4B;AAEpE,IAAI,kBAAkB;AAAA,EACpB;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAO;AAAA,EAAiB;AAAA,EACvD;AAAA,EAAyB;AAAA,EAAgB;AAAA,EAAe;AAC1D;AAHA,IAGG,iBAAiB,OAAO,eAAe;AAE1C,IAAI,sBAAsB;AAAA,EACxB;AAAA,EAAoB;AAAA,EAAY;AAAA,EAAY;AAAA,EAAO;AAAA,EAAU;AAAA,EAC7D;AAAA,EAAY;AAAA,EAAU;AAAA,EAAW;AACnC;AAHA,IAGG,qBAAqB,OAAO,mBAAmB;AAElD,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAS;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAc;AAAA,EAC3D;AAAA,EAAa;AAAA,EAAa;AAAA,EAAc;AAAA,EAAa;AAAA,EAAS;AAAA,EAC9D;AAAA,EAAY;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAY;AAAA,EACvD;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAa;AAAA,EAAe;AAAA,EACjE;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EAAc;AAAA,EACrD;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAiB;AAAA,EACpE;AAAA,EAAY;AAAA,EAAe;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAC/D;AAAA,EAAe;AAAA,EAAe;AAAA,EAAW;AAAA,EAAa;AAAA,EACtD;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAe;AAAA,EAC7D;AAAA,EAAW;AAAA,EAAa;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EACpD;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAa;AAAA,EAC3D;AAAA,EAAa;AAAA,EAAwB;AAAA,EAAa;AAAA,EAAc;AAAA,EAAa;AAAA,EAC7E;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAkB;AAAA,EAClE;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAC/D;AAAA,EAAU;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAgB;AAAA,EAC5D;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAC1D;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAa;AAAA,EAC7D;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAa;AAAA,EAAU;AAAA,EAClE;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAiB;AAAA,EACzD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACnD;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAa;AAAA,EAAa;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAc;AAAA,EAAY;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EACpE;AAAA,EAAa;AAAA,EAAa;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAa;AAAA,EAC3E;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAa;AAAA,EAAU;AAAA,EAAS;AAAA,EAC7D;AAAA,EAAc;AAAA,EAAU;AAC1B;AA3BA,IA2BG,gBAAgB,OAAO,cAAc;AAExC,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAiB;AAAA,EAClE;AAAA,EAAqB;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAc;AAAA,EAC1E;AAAA,EAAU;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAe;AAAA,EACvD;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAAgB;AAAA,EAC/F;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAc;AAAA,EAAa;AAAA,EAAY;AAAA,EAAS;AAAA,EAAiB;AAAA,EAC7F;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAC/E;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAa;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EAC/E;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAQ;AAAA,EACvE;AAAA,EAAc;AAAA,EAAuB;AAAA,EAAW;AAAA,EAAe;AAAA,EAC/D;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAe;AAAA,EACvD;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAS;AAAA,EAAQ;AAAA,EACzD;AAAA,EAAc;AAAA,EAAY;AAAA,EAAS;AAAA,EAAc;AAAA,EAAe;AAAA,EAAU;AAAA,EAC1E;AAAA,EAAW;AAAA,EAAa;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAW;AAAA,EAChE;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EACjG;AAAA,EAAS;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAC/F;AAAA,EAAwB;AAAA,EAAW;AAAA,EAAkB;AAAA,EAAS;AAAA,EAC9D;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAc;AAAA,EACvE;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAC3D;AAAA,EAAY;AAAA,EACZ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAe;AAAA,EACzF;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAO;AAAA,EAAY;AAAA,EAC9D;AAAA,EAA0B;AAAA,EAAwB;AAAA,EAClD;AAAA,EAA0B;AAAA,EAC1B;AAAA,EAA2B;AAAA,EAC3B;AAAA,EAAyB;AAAA,EACzB;AAAA,EAA4B;AAAA,EAC5B;AAAA,EAA2B;AAAA,EAA2B;AAAA,EACtD;AAAA,EAAoB;AAAA,EAAa;AAAA,EAAa;AAAA,EAAY;AAAA,EAAW;AAAA,EACrE;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAc;AAAA,EAC5G;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAsB;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAQ;AAAA,EACvF;AAAA,EAAY;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAoB;AAAA,EAAc;AAAA,EAC5E;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAa;AAAA,EACjD;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAc;AAAA,EAAQ;AAAA,EACxF;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAuB;AAAA,EAC5D;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAC9D;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAa;AAAA,EAC9F;AAAA,EAAU;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAAW;AAAA,EAC7D;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAY;AAAA,EAC1C;AAAA,EAAwB;AAAA,EAAuB;AAAA,EAC/C;AAAA,EAAa;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAW;AAAA,EACnE;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAmB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAW;AAAA,EAC9E;AAAA,EAAS;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAe;AAAA,EACpD;AAAA,EAAe;AAAA,EAAqB;AAAA,EAAe;AAAA,EACnD;AAAA,EAAe;AAAA,EAAa;AAAA,EAAO;AAAA,EAAc;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAU;AAAA,EACjG;AAAA,EAAqB;AAAA,EAAgB;AAAA,EACrC;AAAA,EAAuB;AAAA,EAA4B;AAAA,EACnD;AAAA,EAAQ;AAAA,EAAY;AAAA,EACpB;AAAA,EAAY;AAAA,EAAe;AAAA,EAAU;AAAA,EACrC;AAAA,EAAO;AAAA,EAAa;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAwB;AAAA,EAAY;AAAA,EAAW;AAAA,EACpG;AAAA,EAAY;AAAA,EAAa;AAAA,EAAe;AAAA,EAAkB;AAAA,EAC1D;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAe;AAAA,EAC/D;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAa;AAAA,EAAe;AAAA,EAAW;AAAA,EAAS;AAAA,EAAW;AAAA,EAC9F;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAS;AAAA,EACzD;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAC9D;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAW;AAAA,EAAe;AAAA,EAAc;AAAA,EAAe;AAAA,EACpF;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAO;AAAA,EAAY;AAAA,EAAY;AAAA,EACjE;AAAA,EAAY;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAS;AAAA,EACvD;AAAA,EAAc;AAAA,EAA6B;AAAA,EAAa;AAAA,EACxD;AAAA,EAAY;AAAA,EAAU;AAAA,EAA6B;AAAA,EACnD;AAAA,EAA4B;AAAA,EAAY;AAAA,EAAY;AAAA,EAAS;AAAA,EAC7D;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAClE;AAAA,EAAW;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAe;AAAA,EAAO;AAAA,EAAU;AAAA,EACzE;AAAA,EAAY;AAAA,EAAc;AAAA,EAAY;AAAA,EAAc;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EACtG;AAAA,EAAU;AAAA,EAAa;AAAA,EAAmB;AAAA,EAAa;AAAA,EACvD;AAAA,EAA6B;AAAA,EAC7B;AAAA,EAA8B;AAAA,EAAkC;AAAA,EAAc;AAAA,EAC9E;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EACzE;AAAA,EAAuB;AAAA,EAAyB;AAAA,EAChD;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAoB;AAAA,EAAS;AAAA,EACvD;AAAA,EAAmB;AAAA,EAA0B;AAAA,EAAwB;AAAA,EACrE;AAAA,EAAS;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAc;AAAA,EAAS;AAAA,EAC1E;AAAA,EAAe;AAAA,EAAa;AAAA,EAAc;AAAA,EAAe;AAAA,EAAS;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAChI;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAc;AAAA,EAAW;AAAA,EAAU;AAAA,EAAc;AAAA,EACrF;AAAA,EAAwB;AAAA,EAAa;AAAA,EAAS;AAAA,EAAa;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EAC/F;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAgB;AAAA,EAC/C;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAa;AAAA,EACzD;AAAA,EACA;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAY;AAAA,EAAY;AAAA,EAAa;AAAA,EACtE;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAoB;AAAA,EAAc;AAAA,EACnD;AAAA,EAAqB;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAS;AAAA,EACzD;AAAA,EAAuB;AAAA,EAAe;AAAA,EAAuB;AAAA,EAAM;AAAA,EACnE;AAAA,EAAuB;AAAA,EAAyB;AAAA,EAChD;AAAA,EAAa;AAAA,EAAe;AAAA,EAAc;AAAA,EAAc;AAAA,EACxD;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAS;AAAA,EAChG;AAAA,EAAe;AAAA,EAAkB;AAAA,EAAe;AAAA,EAChD;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAe;AAAA,EAAa;AAAA,EAAQ;AAAA,EACtE;AAAA,EAAO;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAW;AAAA,EAAe;AAAA,EAC1E;AAAA,EAAiB;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACvD;AAAA,EAAU;AAAA,EAAe;AAAA,EAAc;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAC9F;AAAA,EAAY;AACd;AA3FA,IA2FG,gBAAgB,OAAO,cAAc;AAExC,IAAI,WAAW,eAAe,OAAO,WAAW,EAAE,OAAO,cAAc,EAAE,OAAO,mBAAmB,EAC9F,OAAO,iBAAiB,EAAE,OAAO,4BAA4B,EAAE,OAAO,cAAc,EACpF,OAAO,cAAc;AAEnB,IAAM,WAAW;AAAA,EAAC,YAAY;AAAA,EAAmB,QAAQ;AAAA,EACvC,OAAO;AAAA,EAAiB,QAAQ;AAAA,EAAgB,KAAK;AAAQ;AAEtF,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,IACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,UAAI,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAC7B,YAAM,WAAW;AACjB,aAAO,cAAc,QAAQ,KAAK;AAAA,IACpC;AAAA,EACF;AACF;AAEO,IAAM,MAAM,MAAM,EAAC,MAAM,MAAK,CAAC;AAEtC,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,OAAO;AACtB,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,YAAY,MAAM,KAAK;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO,CAAC,WAAW,SAAS;AAC9B;AAEO,IAAM,OAAO,MAAM;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,IACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,eAAO,UAAU;AACjB,eAAO,CAAC,WAAW,SAAS;AAAA,MAC9B,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,cAAM,WAAW;AACjB,eAAO,cAAc,QAAQ,KAAK;AAAA,MACpC,OAAO;AACL,eAAO,CAAC,YAAY,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,IACA,KAAK,SAAS,QAAQ;AACpB,UAAI,OAAO,MAAM,UAAU,KAAK;AAC9B,eAAO,CAAC,MAAM,IAAI;AACpB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,SAAS,QAAQ;AACpB,aAAO,MAAM,SAAS;AACtB,UAAI,OAAO,MAAM,SAAS,KAAK;AAC7B,eAAO,CAAC,OAAO,qBAAqB;AACtC,aAAO,CAAC,wBAAwB,UAAU;AAAA,IAC5C;AAAA,IACA,KAAK,SAAS,QAAQ;AACpB,UAAI,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAC7B,aAAO,CAAC,MAAM,eAAe;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AAEM,IAAM,OAAO,MAAM;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,IACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,UAAI,OAAO,IAAI,GAAG,GAAG;AACnB,eAAO,UAAU;AACjB,eAAO,CAAC,WAAW,SAAS;AAAA,MAC9B,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,cAAM,WAAW;AACjB,eAAO,cAAc,QAAQ,KAAK;AAAA,MACpC,OAAO;AACL,eAAO,CAAC,YAAY,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,IACA,KAAK,SAAS,QAAQ;AACpB,UAAI,OAAO,IAAI,GAAG,EAAG,QAAO,CAAC,MAAM,eAAe;AAClD,UAAI,OAAO,MAAM,yGAAyG,KAAK,EAAG,QAAO;AACzI,aAAO,SAAS,UAAU;AAC1B,UAAI,OAAO,MAAM,SAAS,KAAK;AAC7B,eAAO,CAAC,OAAO,qBAAqB;AACtC,aAAO,CAAC,gBAAgB,UAAU;AAAA,IACpC;AAAA,IACA,KAAK,WAAW;AACd,aAAO,CAAC,QAAQ,MAAM;AAAA,IACxB;AAAA,EACF;AACF,CAAC;AAEM,IAAM,MAAM,MAAM;AAAA,EACvB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AAAA,EACrB,YAAY;AAAA,IACV,KAAK,SAAS,QAAQ,OAAO;AAC3B,UAAI,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAC7B,YAAM,WAAW;AACjB,aAAO,cAAc,QAAQ,KAAK;AAAA,IACpC;AAAA,EACF;AACF,CAAC;", "names": ["documentTypes", "mediaTypes", "mediaFeatures", "mediaValueKeywords", "propertyKeywords", "nonStandardPropertyKeywords", "fontProperties", "counterDescriptors", "colorKeywords", "valueKeywords", "type"]}
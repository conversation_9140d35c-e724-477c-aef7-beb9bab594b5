import {
  defineLocalesResources
} from "./chunk-HKF2AZUV.js";
import "./chunk-AWGNGER7.js";
import "./chunk-KSAIUMCL.js";
import "./chunk-JX5BQTZ6.js";
import "./chunk-QCUVG3AT.js";
import "./chunk-N7YGFUAT.js";
import "./chunk-HOFXQ7MC.js";
import "./chunk-ZQZQC55M.js";
import "./chunk-BD3T7BTJ.js";
import "./chunk-OCBYBPSH.js";

// node_modules/sanity/lib/_chunks-es/resources5.mjs
var createLocaleStrings = defineLocalesResources("create", {
  /** CTA in "Start writing in Create" dialog: Learn more */
  "start-in-create-dialog.cta.learn-more": "Learn more.",
  /** Text for the document pane banner informing users that the document is linked to Sanity Create */
  "studio-create-link-banner.text": "This document is linked to Sanity Create",
  /** Tooltip for Create Link button */
  "create-link-info.tooltip": "Learn more",
  /** Text above header in Create Link info popover */
  "create-link-info-popover.eyebrow-title": "Sanity Create",
  /** Text in badge above header in Create Link info popover */
  "create-link-info-popover.eyebrow-badge": "Early access",
  /** Header in Create Link info popover */
  "create-link-info-popover.header": "Idea-first authoring",
  /** Informational text in Create Link info popover */
  "create-link-info-popover.text": "Write naturally in an AI-powered editor. Your content automatically maps to Studio fields as you type.",
  /** Edit in Create button text */
  "edit-in-create-button.text": "Edit with Sanity Create",
  /** Unlink document from Sanity Create button text */
  "unlink-from-create-button.text": "Unlink",
  /** Unlink from Create dialog header */
  "unlink-from-create-dialog.header": "Switch editing to Studio?",
  /** Unlink from Create dialog – first informational paragraph */
  "unlink-from-create-dialog.first-paragraph": "You’re unlinking “<strong>{{title}}</strong>” from Sanity Create so it can be edited here.",
  /** Unlink from Create dialog – second informational paragraph */
  "unlink-from-create-dialog.second-paragraph": "You’ll keep your content in both places. Any new changes in Sanity Create will stop syncing to this Studio.",
  /** Unlink from Create dialog: Cancel button text */
  "unlink-from-create-dialog.cancel.text": "Cancel",
  /** Unlink from Create dialog: Document title used if no other title can be determined */
  "unlink-from-create-dialog.document.untitled.text": "Untitled",
  /** Unlink from Create dialog: Unlink button text */
  "unlink-from-create-dialog.unlink.text": "Unlink now"
});
export {
  createLocaleStrings as default
};
//# sourceMappingURL=resources5-HTKYRL77.js.map

{"version": 3, "sources": ["../../../../../node_modules/@sanity/vision/src/i18n/index.ts", "../../../../../node_modules/@sanity/vision/src/visionTool.ts"], "sourcesContent": ["import {defineLocaleResourceBundle} from 'sanity'\n\n/**\n * The locale namespace for the vision tool\n *\n * @internal\n */\nexport const visionLocaleNamespace = 'vision' as const\n\n/**\n * The default locale bundle for the vision tool, which is US English.\n *\n * @internal\n */\nexport const visionUsEnglishLocaleBundle = defineLocaleResourceBundle({\n  locale: 'en-US',\n  namespace: visionLocaleNamespace,\n  resources: () => import('./resources'),\n})\n", "import {EyeOpenIcon} from '@sanity/icons'\nimport {lazy} from 'react'\nimport {definePlugin} from 'sanity'\nimport {route} from 'sanity/router'\n\nimport {visionUsEnglishLocaleBundle} from './i18n'\nimport {type VisionToolConfig} from './types'\n\nexport const visionTool = definePlugin<VisionToolConfig | void>((options) => {\n  const {name, title, icon, ...config} = options || {}\n  return {\n    name: '@sanity/vision',\n    tools: [\n      {\n        name: name || 'vision',\n        title: title || 'Vision',\n        icon: icon || EyeOpenIcon,\n        component: lazy(() => import('./SanityVision')),\n        options: config,\n        router: route.create('/*'),\n        __internalApplicationType: 'sanity/vision',\n      },\n    ],\n    i18n: {\n      bundles: [visionUsEnglishLocaleBundle],\n    },\n  }\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;AAOaA,IAAAA,wBAAwB;AAAxBA,IAOAC,8BAA8BC,2BAA2B;EACpEC,QAAQ;EACRC,WAAWJ;EACXK,WAAWA,MAAM,OAAO,yBAAa;AACvC,CAAC;AAXYL,ICCAM,aAAaC,aAAuCC,CAAY,YAAA;AACrE,QAAA;IAACC;IAAMC;IAAOC;IAAM,GAAGC;EAAM,IAAIJ,WAAW,CAAC;AAC5C,SAAA;IACLC,MAAM;IACNI,OAAO,CACL;MACEJ,MAAMA,QAAQ;MACdC,OAAOA,SAAS;MAChBC,MAAMA,QAAQG;MACdC,eAAWC,mBAAK,MAAM,OAAO,4BAAgB,CAAC;MAC9CR,SAASI;MACTK,QAAQC,MAAMC,OAAO,IAAI;MACzBC,2BAA2B;IAAA,CAC5B;IAEHC,MAAM;MACJC,SAAS,CAACrB,2BAA2B;IAAA;EAEzC;AACF,CAAC;", "names": ["visionLocaleNamespace", "visionUsEnglishLocaleBundle", "defineLocaleResourceBundle", "locale", "namespace", "resources", "visionTool", "definePlugin", "options", "name", "title", "icon", "config", "tools", "EyeOpenIcon", "component", "lazy", "router", "route", "create", "__internalApplicationType", "i18n", "bundles"]}
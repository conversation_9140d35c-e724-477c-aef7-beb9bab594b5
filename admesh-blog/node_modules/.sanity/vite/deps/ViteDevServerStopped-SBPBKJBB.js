import {
  Card,
  Container,
  Heading,
  Stack,
  Text
} from "./chunk-AWGNGER7.js";
import {
  require_dist
} from "./chunk-JX5BQTZ6.js";
import "./chunk-N7YGFUAT.js";
import "./chunk-HOFXQ7MC.js";
import {
  require_jsx_runtime
} from "./chunk-ZQZQC55M.js";
import {
  require_react
} from "./chunk-BD3T7BTJ.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// node_modules/sanity/lib/_chunks-es/ViteDevServerStopped.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react_compiler_runtime = __toESM(require_dist(), 1);
var import_react = __toESM(require_react(), 1);
var ERROR_TITLE = "Dev server stopped";
var ERROR_DESCRIPTION = "The development server has stopped. You may need to restart it to continue working.";
var ViteDevServerStoppedError = class extends Error {
  constructor() {
    super(ERROR_TITLE), this.name = "ViteDevServerStoppedError", this.ViteDevServerStoppedError = true;
  }
};
var serverHot = import.meta.hot;
var isViteServer = (hot) => !!hot;
var useDetectViteDevServerStopped = () => {
  const $ = (0, import_react_compiler_runtime.c)(5), [devServerStopped, setDevServerStopped] = (0, import_react.useState)(false);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = () => setDevServerStopped(true), $[0] = t0) : t0 = $[0];
  const markDevServerStopped = t0;
  let t1, t2;
  $[1] === Symbol.for("react.memo_cache_sentinel") ? (t1 = () => {
    isViteServer(serverHot) && serverHot.on("vite:ws:disconnect", markDevServerStopped);
  }, t2 = [markDevServerStopped], $[1] = t1, $[2] = t2) : (t1 = $[1], t2 = $[2]), (0, import_react.useEffect)(t1, t2);
  let t3;
  return $[3] !== devServerStopped ? (t3 = {
    devServerStopped
  }, $[3] = devServerStopped, $[4] = t3) : t3 = $[4], t3;
};
var ThrowViteServerStopped = () => {
  const {
    devServerStopped
  } = useDetectViteDevServerStopped();
  if (devServerStopped)
    throw new ViteDevServerStoppedError();
  return null;
};
var DetectViteDevServerStopped = () => {
  const $ = (0, import_react_compiler_runtime.c)(1);
  let t0;
  return $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = isViteServer(serverHot) ? (0, import_jsx_runtime.jsx)(ThrowViteServerStopped, {}) : null, $[0] = t0) : t0 = $[0], t0;
};
var DevServerStoppedErrorScreen = () => {
  const $ = (0, import_react_compiler_runtime.c)(3);
  let t0;
  $[0] === Symbol.for("react.memo_cache_sentinel") ? (t0 = [4, 5, 6, 7], $[0] = t0) : t0 = $[0];
  let t1;
  $[1] === Symbol.for("react.memo_cache_sentinel") ? (t1 = (0, import_jsx_runtime.jsx)(Heading, { children: ERROR_TITLE }), $[1] = t1) : t1 = $[1];
  let t2;
  return $[2] === Symbol.for("react.memo_cache_sentinel") ? (t2 = (0, import_jsx_runtime.jsx)(Card, { height: "fill", overflow: "auto", paddingY: t0, paddingX: 4, sizing: "border", tone: "critical", children: (0, import_jsx_runtime.jsx)(Container, { width: 3, children: (0, import_jsx_runtime.jsxs)(Stack, { space: 4, children: [
    t1,
    (0, import_jsx_runtime.jsx)(Card, { border: true, radius: 2, overflow: "auto", padding: 4, tone: "inherit", children: (0, import_jsx_runtime.jsx)(Stack, { space: 4, children: (0, import_jsx_runtime.jsx)(Text, { size: 2, children: ERROR_DESCRIPTION }) }) })
  ] }) }) }), $[2] = t2) : t2 = $[2], t2;
};
export {
  DetectViteDevServerStopped,
  DevServerStoppedErrorScreen,
  ViteDevServerStoppedError
};
//# sourceMappingURL=ViteDevServerStopped-SBPBKJBB.js.map

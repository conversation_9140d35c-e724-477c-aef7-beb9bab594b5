{"version": 3, "sources": ["../../../sanity/src/core/tasks/i18n/resources.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\nimport {defineLocalesResources} from '../../i18n'\n\n/**\n * Defined locale strings for the task tool, in US English.\n *\n * @internal\n */\nconst tasksLocaleStrings = defineLocalesResources('tasks', {\n  /** The label for the create task action */\n  'actions.create.text': 'Create new task',\n  /** The label for the open tasks panel action */\n  'actions.open.text': 'Tasks',\n  /** The label for the button to create a new task */\n  'buttons.create.text': 'Create Task',\n  /** The label for the button to discard changes */\n  'buttons.discard.text': 'Discard',\n  /** The label for the button to open the draft */\n  'buttons.draft.text': 'Draft',\n  /** The label for the button to create a new task */\n  'buttons.new.text': 'New task',\n  /** The text for tooltip in the create a new task button when mode is upsell */\n  'buttons.new.upsell-tooltip': 'Upgrade to create tasks',\n  /** The label for the button that will navigate to the next task */\n  'buttons.next.tooltip': 'Go to next task',\n  /** The label for the button that will previous to the next task */\n  'buttons.previous.tooltip': 'Go to previous task',\n  /** Text for the remove task dialog asking for confirmation of deletion */\n  'dialog.remove-task.body': 'Once deleted, a task cannot be recovered.',\n  /** The label for the cancel button on the remove task dialog */\n  'dialog.remove-task.buttons.cancel.text': 'Cancel',\n  /** The label for the confirmation button on the remove task dialog */\n  'dialog.remove-task.buttons.confirm.text': 'Delete',\n  /** The title for the remove task dialog */\n  'dialog.remove-task.title': 'Delete this task?',\n  /** The text used as a placeholder for the footer action in a document with a single task */\n  'document.footer.open-tasks.placeholder_one': 'Open task',\n  /** The text used as a placeholder for the footer action in a document with multiple tasks */\n  'document.footer.open-tasks.placeholder_other': 'Open tasks',\n  /** The label used in the button in the footer action in a document with a single task */\n  'document.footer.open-tasks.text_one': '{{count}} open task',\n  /** The label used in the button in the footer action in a document with multiple tasks */\n  'document.footer.open-tasks.text_other': '{{count}} open tasks',\n\n  /** The heading in the tasks sidebar, in the assigned tab, when the user hasn't been assigned to any task*/\n  'empty-state.list.assigned.heading': \"You haven't been assigned any tasks\",\n  /** The text in the tasks sidebar, in the assigned tab, when the user hasn't been assigned to any task*/\n  'empty-state.list.assigned.text': \"Once you're assigned tasks they'll show up here\",\n  /** The text in the tasks sidebar button any of the empty states is rendered*/\n  'empty-state.list.create-new': 'Create new task',\n  /** The heading in the tasks sidebar, in the document tab, when the document doesn't have any task*/\n  'empty-state.list.document.heading': \"This document doesn't have any tasks yet\",\n  /** The text in the tasks sidebar, in the document tab, when the document doesn't have any task*/\n  'empty-state.list.document.text': 'Once a document has connected tasks, they will be shown here.',\n  /** The heading in the tasks sidebar, when viewing the document tab, but there is not an active document*/\n  'empty-state.list.no-active-document.heading': 'Open a document to see its task',\n  /** The text in the tasks sidebar, when viewing the document tab, but there is not an active document*/\n  'empty-state.list.no-active-document.text': 'Tasks on your active document will be shown here.',\n  /** The heading in the tasks sidebar, in the subscriber tab, when the user is not subscribed to any task*/\n  'empty-state.list.subscribed.heading': \"You haven't subscribed to any tasks\",\n  /** The text in the tasks sidebar, in the subscriber tab, when the user is not subscribed to any task*/\n  'empty-state.list.subscribed.text':\n    'When you create, modify, or comment on a task you will be subscribed automatically',\n\n  /** The heading in the tasks sidebar, in the assigned tab, under the closed details, when it's empty.*/\n  'empty-state.status.list.closed.assigned.heading': 'No completed tasks',\n  /** The text in the tasks sidebar, in the assigned tab, under the closed details, when it's empty.*/\n  'empty-state.status.list.closed.assigned.text': 'Your tasks marked done will show up here',\n  /** The heading in the tasks sidebar, in the document tab, under the closed details, when it's empty.*/\n  'empty-state.status.list.closed.document.heading': 'No completed tasks',\n  /** The heading in the tasks sidebar, in the subscribed tab, under the closed details, when it's empty.*/\n  'empty-state.status.list.closed.subscribed.heading': 'No completed tasks',\n  /** The text in the tasks sidebar, in the subscribed tab, under the closed details, when it's empty.*/\n  'empty-state.status.list.closed.subscribed.text':\n    'Tasks you subscribe to marked done will show up here',\n\n  /** The heading in the tasks sidebar, in the assigned tab, under the open details, when it's empty.*/\n  'empty-state.status.list.open.assigned.heading': \"You're all caught up\",\n  /** The text in the tasks sidebar, in the assigned tab, under the open details, when it's empty.*/\n  'empty-state.status.list.open.assigned.text': 'New tasks assigned to you will show up here',\n  /** The heading in the tasks sidebar, in the document tab, under the open details, when it's empty.*/\n  'empty-state.status.list.open.document.heading': 'No tasks on this document',\n  /** The heading in the tasks sidebar, in the subscribed tab, under the open details, when it's empty.*/\n  'empty-state.status.list.open.subscribed.heading': 'No subscribed tasks',\n  /** The text in the tasks sidebar, in the subscribed tab, under the open details, when it's empty.*/\n  'empty-state.status.list.open.subscribed.text': 'Tasks you subscribe to will show up here',\n\n  /** Text used in the assignee input when there is no user assigned */\n  'form.input.assignee.no-user-assigned.text': 'Unassigned',\n  /** Text used in the assignee input tooltip when there is no user assigned */\n  'form.input.assignee.no-user-assigned.tooltip': 'Set assignee',\n  /** Text used in the assignee input when searching and no users are found */\n  'form.input.assignee.search.no-users.text': 'No users found',\n  /** Placeholder text used in the search box in the assignee input */\n  'form.input.assignee.search.placeholder': 'Select assignee',\n  /** Text used in the assignee input when user is not authorized */\n  'form.input.assignee.unauthorized.text': 'Unauthorized',\n  /** Text used in the assignee input tooltip when there is no user assigned */\n  'form.input.assignee.user-assigned.tooltip': 'Change assignee',\n  /** Text used in the assignee input when user is not found */\n  'form.input.assignee.user-not-found.text': 'User not found',\n  /** The label used in the create more toggle */\n  'form.input.create-more.text': 'Create more',\n  /** The label used in the date input button tooltip when it's empty */\n  'form.input.date.buttons.empty.tooltip': 'Set due date',\n  /** The label used in the date input to remove the current value */\n  'form.input.date.buttons.remove.text': 'Remove',\n  /** The label used in the date input button tooltip when it has value */\n  'form.input.date.buttons.tooltip': 'Change due date',\n  /** Placeholder text used in the description input */\n  'form.input.description.placeholder': 'Add description',\n  /**  Text used in the tooltip in the status change button  */\n  'form.input.status.button.tooltip': 'Change status',\n  /** The label used in the target input to remove the current value */\n  'form.input.target.buttons.remove.text': 'Remove target content',\n  /** The text used in the target input when encountering a schema error */\n  'form.input.target.error.schema-not-found': 'Schema not found',\n  /** The placeholder text used in the target input for the search component */\n  'form.input.target.search.placeholder': 'Select target document',\n  /** The placeholder text for the title input */\n  'form.input.title.placeholder': 'Task title',\n  /** The status error message presented when the user does not supply a title */\n  'form.status.error.title-required': 'Title is required',\n  /** The status message upon successful creation of a task */\n  'form.status.success': 'Task created',\n  /** The text displayed when no tasks are found */\n  'list.empty.text': 'No tasks',\n  /** The text displayed at the bottom of the tasks list inviting users provide feedback */\n  'list.feedback.text': 'Help us improve, <Link>share feedback on Tasks</Link> ',\n  /** The label for the copy link menu item */\n  'menuitem.copylink.text': 'Copy link to task',\n  /** The label for the delete task menu item */\n  'menuitem.delete.text': 'Delete task',\n  /** The label for the duplicate task menu item */\n  'menuitem.duplicate.text': 'Duplicate task',\n  /** The text for the duplicate task menu item tooltip when mode is upsell */\n  'menuitem.duplicate.upsell-tooltip': 'Upgrade to duplicate tasks',\n  /** Fragment used to construct the first entry in the activity log */\n  'panel.activity.created-fragment': 'created this task',\n  /** The title of the activity section of the task */\n  'panel.activity.title': 'Activity',\n  /** The text used in the activity log when unable to find the user */\n  'panel.activity.unknown-user': 'Unknown user',\n  /** The tooltip for the close button for the task panel */\n  'panel.close.tooltip': 'Close sidebar',\n  /** The placeholder text for the comment text box */\n  'panel.comment.placeholder': 'Add a comment...',\n  /** The placeholder text for the comment text box when mode is upsell */\n  'panel.comment.placeholder.upsell': 'Upgrade to comment on tasks',\n  /** The title used in the task panel when showing the create task form */\n  'panel.create.title': 'Create',\n  /** The title used in the drafts pulldown */\n  'panel.drafts.title': 'Drafts',\n  /** The tooltip for the task navigation component */\n  'panel.navigation.tooltip': 'Open tasks',\n  /** Title of the Tasks panel   */\n  'panel.title': 'Tasks',\n\n  /** Label for the Assigned Tab */\n  'tab.assigned.label': 'Assigned',\n  /** Label for the Active Document Tab */\n  'tab.document.label': 'Active Document',\n  /** Label for the Subscribed Tab */\n  'tab.subscribed.label': 'Subscribed',\n  /** Tooltip for the tasks navbar icon */\n  'toolbar.tooltip': 'Tasks',\n})\n\n/**\n * @alpha\n */\nexport type TasksLocaleResourceKeys = keyof typeof tasksLocaleStrings\n\nexport default tasksLocaleStrings\n"], "mappings": ";;;;;;;;;;;;;;AAQMA,IAAAA,qBAAqBC,uBAAuB,SAAS;;EAEzD,uBAAuB;;EAEvB,qBAAqB;;EAErB,uBAAuB;;EAEvB,wBAAwB;;EAExB,sBAAsB;;EAEtB,oBAAoB;;EAEpB,8BAA8B;;EAE9B,wBAAwB;;EAExB,4BAA4B;;EAE5B,2BAA2B;;EAE3B,0CAA0C;;EAE1C,2CAA2C;;EAE3C,4BAA4B;;EAE5B,8CAA8C;;EAE9C,gDAAgD;;EAEhD,uCAAuC;;EAEvC,yCAAyC;;EAGzC,qCAAqC;;EAErC,kCAAkC;;EAElC,+BAA+B;;EAE/B,qCAAqC;;EAErC,kCAAkC;;EAElC,+CAA+C;;EAE/C,4CAA4C;;EAE5C,uCAAuC;;EAEvC,oCACE;;EAGF,mDAAmD;;EAEnD,gDAAgD;;EAEhD,mDAAmD;;EAEnD,qDAAqD;;EAErD,kDACE;;EAGF,iDAAiD;;EAEjD,8CAA8C;;EAE9C,iDAAiD;;EAEjD,mDAAmD;;EAEnD,gDAAgD;;EAGhD,6CAA6C;;EAE7C,gDAAgD;;EAEhD,4CAA4C;;EAE5C,0CAA0C;;EAE1C,yCAAyC;;EAEzC,6CAA6C;;EAE7C,2CAA2C;;EAE3C,+BAA+B;;EAE/B,yCAAyC;;EAEzC,uCAAuC;;EAEvC,mCAAmC;;EAEnC,sCAAsC;;EAEtC,oCAAoC;;EAEpC,yCAAyC;;EAEzC,4CAA4C;;EAE5C,wCAAwC;;EAExC,gCAAgC;;EAEhC,oCAAoC;;EAEpC,uBAAuB;;EAEvB,mBAAmB;;EAEnB,sBAAsB;;EAEtB,0BAA0B;;EAE1B,wBAAwB;;EAExB,2BAA2B;;EAE3B,qCAAqC;;EAErC,mCAAmC;;EAEnC,wBAAwB;;EAExB,+BAA+B;;EAE/B,uBAAuB;;EAEvB,6BAA6B;;EAE7B,oCAAoC;;EAEpC,sBAAsB;;EAEtB,sBAAsB;;EAEtB,4BAA4B;;EAE5B,eAAe;;EAGf,sBAAsB;;EAEtB,sBAAsB;;EAEtB,wBAAwB;;EAExB,mBAAmB;AACrB,CAAC;", "names": ["tasksLocaleStrings", "defineLocalesResources"]}
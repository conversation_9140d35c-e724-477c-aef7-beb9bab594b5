import {
  ActiveWorkspaceMatcher,
  AddonDatasetProvider,
  ArrayOfObjectOptionsInput,
  ArrayOfObjectsFunctions,
  ArrayOfObjectsInput,
  ArrayOfObjectsInputMember,
  ArrayOfObjectsInputMembers,
  ArrayOfObjectsItem,
  ArrayOfOptionsInput,
  ArrayOfPrimitiveOptionsInput,
  ArrayOfPrimitivesFunctions,
  ArrayOfPrimitivesInput,
  ArrayOfPrimitivesItem,
  AutoCollapseMenu,
  AvatarSkeleton$1,
  BetaBadge,
  BlockImagePreview,
  BlockPreview,
  BooleanInput,
  COMMENTS_INSPECTOR_NAME,
  CONNECTING,
  CapabilityGate,
  ChangeBreadcrumb,
  ChangeConnectorRoot,
  ChangeFieldWrapper,
  ChangeIndicator,
  ChangeIndicatorsTracker,
  ChangeList,
  ChangeResolver,
  ChangeTitleSegment,
  ChangesError,
  CircularProgress,
  CollapseMenu,
  CollapseMenuButton,
  ColorSchemeCustomProvider,
  ColorSchemeLocalStorageProvider,
  ColorSchemeProvider,
  CommandList,
  CommentDeleteDialog,
  CommentDisabledIcon,
  CommentInlineHighlightSpan,
  CommentInput,
  CommentsAuthoringPathProvider,
  CommentsEnabledProvider,
  CommentsIntentProvider,
  CommentsList,
  CommentsProvider,
  CommentsSelectedPathProvider,
  CompactPreview,
  ConfigPropertyError,
  ConfigResolutionError,
  ContextMenuButton,
  CopyPasteProvider,
  CorsOriginError,
  DEFAULT_MAX_RECURSION_DEPTH,
  DEFAULT_STUDIO_CLIENT_OPTIONS,
  DRAFTS_FOLDER,
  DateInput,
  DateTimeInput$2,
  DefaultPreview,
  DetailPreview,
  DiffCard,
  DiffErrorBoundary,
  DiffFromTo,
  DiffInspectWrapper,
  DiffString,
  DiffStringSegment,
  DiffTooltip,
  DocumentPreviewPresence,
  DocumentStatus,
  DocumentStatusIndicator,
  EMPTY_ARRAY$B,
  EMPTY_OBJECT,
  EditPortal,
  EditScheduleForm,
  EmailInput,
  ErrorActions,
  ErrorMessage,
  Event$1,
  EventsProvider,
  FallbackDiff,
  FieldActionMenu,
  FieldActionsProvider,
  FieldActionsResolver,
  FieldChange,
  FieldPresence,
  FieldPresenceInner,
  FieldPresenceWithOverlay,
  Filters,
  FormBuilder,
  FormCallbacksProvider,
  FormField,
  FormFieldHeaderText,
  FormFieldSet,
  FormFieldStatus,
  FormFieldValidationStatus,
  FormInput,
  FormProvider,
  FormValueProvider,
  FromTo,
  FromToArrow,
  GetFormValueProvider,
  GetHookCollectionState,
  GroupChange,
  Hotkeys,
  HoveredFieldProvider,
  ImperativeToast,
  InlinePreview,
  InsufficientPermissionsMessage,
  IntentButton,
  IsLastPaneProvider,
  LATEST,
  LegacyLayerProvider,
  LinearProgress,
  LoadingBlock,
  LocaleProvider,
  LocaleProviderBase,
  MEDIA_LIBRARY_ASSET_ASPECT_TYPE_NAME,
  MediaPreview,
  MemberField,
  MemberFieldError,
  MemberFieldSet,
  MemberItemError,
  MetaInfo,
  NoChanges,
  NumberInput,
  ObjectInput,
  ObjectInputMember,
  ObjectInputMembers,
  ObjectMembers,
  PatchEvent,
  PerspectiveProvider,
  PopoverDialog,
  PortableTextInput,
  PresenceOverlay,
  PresenceScope,
  Preview$1,
  PreviewCard,
  PreviewLoader,
  RELEASES_INTENT,
  RELEASES_STUDIO_CLIENT_OPTIONS,
  ReferenceInputOptionsProvider,
  ReferenceInputPreviewCard,
  RelativeTime,
  ReleaseAvatar,
  ReleasesNav,
  Resizable,
  ResourceCacheProvider,
  RevertChangesButton,
  Rule,
  SANITY_PATCH_TYPE,
  SANITY_VERSION,
  SESSION_ID,
  SanityCreateConfigProvider,
  SanityDefaultPreview,
  ScheduleAction,
  ScheduledBadge,
  SchemaError,
  ScrollContainer,
  SearchButton,
  SearchDialog,
  SearchHeader,
  SearchPopover,
  SearchProvider,
  SearchResultItemPreview,
  SelectInput,
  SlugInput,
  SourceProvider,
  StatusButton,
  StringInput,
  Studio,
  StudioAnnouncementsCard,
  StudioAnnouncementsDialog,
  StudioCrossDatasetReferenceInput,
  StudioFileInput,
  StudioImageInput,
  StudioLayout,
  StudioLayoutComponent,
  StudioLogo,
  StudioNavbar,
  StudioProvider,
  StudioReferenceInput,
  StudioToolMenu,
  TIMELINE_ITEM_I18N_KEY_MAPPING,
  TagsArrayInput,
  TelephoneInput,
  TemplatePreview,
  TextInput,
  TextWithTone,
  Timeline,
  TimelineController,
  ToolLink,
  TooltipOfDisabled,
  TransformPatches,
  Translate,
  UniversalArrayInput,
  UpsellDescriptionSerializer,
  UpsellDialogDismissed,
  UpsellDialogLearnMoreCtaClicked,
  UpsellDialogUpgradeCtaClicked,
  UpsellDialogViewed,
  UrlInput,
  UserAvatar,
  UserColorManagerProvider,
  VERSION_FOLDER,
  ValueError,
  VersionChip$1,
  VersionInlineBadge,
  VirtualizerScrollInstanceProvider,
  WithReferringDocuments,
  WorkspaceLoaderBoundary,
  WorkspaceProvider,
  WorkspacesProvider,
  ZIndexProvider,
  _createAuthStore,
  _isCustomDocumentTypeDefinition,
  _isSanityDocumentTypeDefinition,
  asLoadable,
  buildCommentRangeDecorations,
  buildLegacyTheme,
  buildRangeDecorationSelectionsFromComments,
  buildTextSelectionFromFragment,
  catchWithCount,
  checkoutPair,
  collate,
  createAuthStore,
  createBufferedDocument,
  createConfig,
  createConnectionStatusStore,
  createDefaultIcon,
  createDocumentPreviewStore,
  createDocumentStore,
  createDraftFrom,
  createGrantsStore,
  createHistoryStore,
  createHookFromObservableFactory,
  createKeyValueStore,
  createMockAuthStore,
  createObservableBufferedDocument,
  createPatchChannel,
  createPlugin,
  createPresenceStore,
  createProjectStore,
  createPublishedFrom,
  createSWR,
  createSanityMediaLibraryFileSource,
  createSanityMediaLibraryImageSource,
  createSchema,
  createSearch,
  createSharedResizeObserver,
  createSourceFromConfig,
  createUserColorManager,
  createUserStore,
  createWorkspaceFromConfig,
  dec,
  decodePath,
  defaultLocale,
  defaultRenderAnnotation,
  defaultRenderBlock,
  defaultRenderField,
  defaultRenderInlineBlock,
  defaultRenderInput,
  defaultRenderItem,
  defaultRenderPreview,
  defaultTemplateForType,
  defaultTemplatesForSchema,
  defaultTheme,
  defineArrayMember,
  defineAssetAspect,
  defineConfig,
  defineDocumentFieldAction,
  defineDocumentInspector,
  defineField,
  defineLocale,
  defineLocaleResourceBundle,
  defineLocalesResources,
  definePlugin,
  defineSearchFilter,
  defineSearchFilterOperators,
  defineSearchOperator,
  defineType,
  diffMatchPatch,
  diffResolver,
  documentFieldActionsReducer,
  documentIdEquals,
  editState,
  emitOperation,
  encodePath,
  escapeField,
  fieldNeedsEscape,
  findIndex,
  flattenConfig,
  formatRelativeLocale,
  formatRelativeLocalePublishDate,
  fromMutationPatches,
  getAnnotationAtPath,
  getAnnotationColor,
  getCalendarLabels,
  getConfigContextFromSource,
  getDiffAtPath,
  getDocumentPairPermissions,
  getDocumentValuePermissions,
  getDocumentVariantType,
  getDraftId,
  getExpandOperations,
  getIdPair,
  getInitialValueStream,
  getItemKey$1,
  getItemKeySegment,
  getNamelessWorkspaceIdentifier,
  getPairListener,
  getPreviewPaths,
  getPreviewStateObservable$1,
  getPreviewValueWithFallback,
  getProviderTitle,
  getPublishedId,
  getReleaseIdFromReleaseDocumentId,
  getReleaseTone,
  getSanityCreateLinkMetadata,
  getSchemaTypeTitle,
  getSearchableTypes,
  getTemplatePermissions,
  getValueAtPath,
  getValueError,
  getVersionFromId,
  getVersionId,
  getVersionInlineBadge,
  getWorkspaceIdentifier,
  globalScope,
  grantsPermissionOn,
  hasCommentMessageValue,
  idMatchesPerspective,
  inc,
  initialDocumentFieldActions,
  insert$1,
  isAddedItemDiff,
  isArray,
  isArrayOfBlocksInputProps,
  isArrayOfBlocksSchemaType,
  isArrayOfObjectsInputProps,
  isArrayOfObjectsSchemaType,
  isArrayOfPrimitivesInputProps,
  isArrayOfPrimitivesSchemaType,
  isArraySchemaType,
  isAssetAspect,
  isAuthStore,
  isBlockChildrenObjectField,
  isBlockListObjectField,
  isBlockSchemaType,
  isBlockStyleObjectField,
  isBooleanInputProps,
  isBooleanSchemaType,
  isBuilder,
  isCookielessCompatibleLoginMethod,
  isCreateDocumentVersionEvent,
  isCreateIfNotExistsMutation,
  isCreateLiveDocumentEvent,
  isCreateMutation,
  isCreateOrReplaceMutation,
  isCreateSquashedMutation,
  isCrossDatasetReference,
  isCrossDatasetReferenceSchemaType,
  isDateTimeSchemaType,
  isDeleteDocumentGroupEvent,
  isDeleteDocumentVersionEvent,
  isDeleteMutation,
  isDeprecatedSchemaType,
  isDeprecationConfiguration,
  isDev,
  isDocumentSchemaType,
  isDraft,
  isDraftId,
  isDraftPerspective,
  isEditDocumentVersionEvent,
  isEmptyObject$1,
  isFieldChange,
  isFileSchemaType,
  isGlobalDocumentReference,
  isGoingToUnpublish,
  isGroupChange,
  isImage,
  isImageSchemaType,
  isIndexSegment,
  isIndexTuple,
  isKeySegment,
  isKeyedObject,
  isNonNullable$3,
  isNumberInputProps,
  isNumberSchemaType,
  isObjectInputProps,
  isObjectItemProps,
  isObjectSchemaType,
  isPatchMutation,
  isPerspectiveRaw,
  isPortableTextListBlock,
  isPortableTextSpan,
  isPortableTextTextBlock,
  isPrimitiveSchemaType,
  isProd,
  isPublishDocumentVersionEvent,
  isPublishedId,
  isPublishedPerspective,
  isRecord$4,
  isReference,
  isReferenceSchemaType,
  isReleaseDocument,
  isReleasePerspective,
  isReleaseScheduledOrScheduling,
  isRemovedItemDiff,
  isSanityCreateExcludedType,
  isSanityCreateLinked,
  isSanityCreateLinkedDocument,
  isSanityCreateStartCompatibleDoc,
  isSanityDefinedAction,
  isSanityDocument,
  isScheduleDocumentVersionEvent,
  isSearchStrategy,
  isSlug,
  isSpanSchemaType,
  isString,
  isStringInputProps,
  isStringSchemaType,
  isSystemBundle,
  isSystemBundleName,
  isTextSelectionComment,
  isTitledListValue,
  isTruthy,
  isTypedObject,
  isUnchangedDiff,
  isUnpublishDocumentEvent,
  isUnscheduleDocumentVersionEvent,
  isUpdateLiveDocumentEvent,
  isValidAnnouncementAudience,
  isValidAnnouncementRole,
  isValidationError,
  isValidationErrorMarker,
  isValidationInfo,
  isValidationInfoMarker,
  isValidationWarning,
  isValidationWarningMarker,
  isVersionId,
  joinPath,
  listenQuery,
  matchWorkspace,
  newDraftFrom,
  noop$1,
  normalizeIndexSegment,
  normalizeIndexTupleSegment,
  normalizeKeySegment,
  normalizePathSegment,
  onRetry,
  operationEvents,
  operatorDefinitions,
  pathToString$1,
  pathsAreEqual,
  prefixPath,
  prepareConfig,
  prepareForPreview,
  prepareTemplates,
  remoteSnapshots,
  removeDupes$3,
  removeMissingReferences,
  removeUndefinedLocaleResources,
  renderStudio,
  resizeObserver,
  resolveConditionalProperty,
  resolveConfig,
  resolveDiffComponent,
  resolveInitialObjectValue,
  resolveInitialValue,
  resolveInitialValueForType,
  resolveSchemaTypes,
  searchStrategies,
  serializeError,
  set,
  setAtPath,
  setIfMissing,
  sliceString,
  snapshotPair,
  stringToPath,
  supportsTouch,
  systemBundles,
  toMutationPatches,
  truncateString,
  typed,
  uncaughtErrorHandler,
  unset,
  usEnglishLocale,
  useActiveReleases,
  useActiveWorkspace,
  useAddonDataset,
  useAnnotationColor,
  useArchivedReleases,
  useCanvasCompanionDoc,
  useChangeIndicatorsReportedValues,
  useChangeIndicatorsReporter,
  useClient,
  useColorScheme,
  useColorSchemeInternalValue,
  useColorSchemeOptions,
  useColorSchemeSetValue,
  useColorSchemeValue,
  useComlinkStore,
  useComments,
  useCommentsEnabled,
  useCommentsSelectedPath,
  useCommentsTelemetry,
  useConditionalToast,
  useConfigContextFromSource,
  useConnectionState,
  useConnectionStatusStore,
  useCopyErrorDetails,
  useCopyPaste,
  useCurrentLocale,
  useCurrentUser,
  useDataset,
  useDateTimeFormat,
  useDidUpdate,
  useDiffAnnotationColor,
  useDocumentChange,
  useDocumentForm,
  useDocumentOperation,
  useDocumentOperationEvent,
  useDocumentPairPermissions,
  useDocumentPairPermissionsFromHookFactory,
  useDocumentPresence,
  useDocumentPreview,
  useDocumentPreviewStore,
  useDocumentStore,
  useDocumentType,
  useDocumentValuePermissions,
  useDocumentValues,
  useDocumentVersionInfo,
  useDocumentVersionTypeSortedList,
  useDocumentVersions,
  useEditState,
  useEvents,
  useEventsStore,
  useExcludedPerspective,
  useFeatureEnabled,
  useFieldActions,
  useFormBuilder,
  useFormCallbacks,
  useFormState,
  useFormValue,
  useFormattedDuration,
  useGetFormValue,
  useGetI18nText,
  useGlobalCopyPasteElementHandler,
  useGlobalPresence,
  useGrantsStore,
  useHistoryStore,
  useHoveredField,
  useI18nText,
  useInitialValue,
  useInitialValueResolverContext,
  useIsReleaseActive,
  useKeyValueStore,
  useListFormat,
  useLoadable,
  useLocale,
  useManageFavorite,
  useMiddlewareComponents,
  useNavigateToCanvasDoc,
  useNumberFormat,
  useObserveDocument,
  useOnScroll,
  useOnlyHasVersions,
  usePerspective,
  usePresenceStore,
  usePreviewCard,
  useProject,
  useProjectDatasets,
  useProjectId$1,
  useProjectStore,
  useReconnectingToast,
  useReferenceInputOptions,
  useReferringDocuments,
  useRelativeTime,
  useReleasesIds,
  useRenderingContextStore,
  useResolveInitialValueForType,
  useResourceCache,
  useReviewChanges,
  useRovingFocus,
  useSanityCreateConfig,
  useSchema,
  useSearchMaxFieldDepth,
  useSearchState,
  useSetPerspective,
  useSource,
  useStudioUrl,
  useSyncState,
  useTemplatePermissions,
  useTemplatePermissionsFromHookFactory,
  useTemplates,
  useThrottledCallback,
  useTimeAgo,
  useTimelineSelector,
  useTimelineStore,
  useTools,
  useTrackerStore,
  useTrackerStoreReporter,
  useTranslation,
  useTreeEditingEnabled,
  useUnique,
  useUnitFormatter,
  useUser,
  useUserColor,
  useUserColorManager,
  useUserListWithPermissions,
  useUserStore,
  useValidationStatus,
  useVersionOperations,
  useVirtualizerScrollInstance,
  useWorkspace,
  useWorkspaceLoader,
  useWorkspaceSchemaId,
  useWorkspaces,
  useZIndex,
  userHasRole,
  validateBasePaths,
  validateDocument,
  validateNames,
  validateWorkspaces,
  validation,
  visitDiff
} from "./chunk-6XMXOQIO.js";
import "./chunk-3HD5UKUW.js";
import "./chunk-4HFGZHGE.js";
import "./chunk-CJ3ODOED.js";
import "./chunk-DWYNPLUJ.js";
import "./chunk-NB2E7ZMB.js";
import "./chunk-V3YD7LXU.js";
import "./chunk-EVVIBKQA.js";
import "./chunk-3GN7GPJI.js";
import "./chunk-OCBYBPSH.js";
export {
  ActiveWorkspaceMatcher,
  AddonDatasetProvider,
  ArrayOfObjectOptionsInput,
  ArrayOfObjectsFunctions,
  ArrayOfObjectsInput,
  ArrayOfObjectsInputMember,
  ArrayOfObjectsInputMembers,
  ArrayOfObjectsItem,
  ArrayOfOptionsInput,
  ArrayOfPrimitiveOptionsInput,
  ArrayOfPrimitivesFunctions,
  ArrayOfPrimitivesInput,
  ArrayOfPrimitivesItem,
  AutoCollapseMenu,
  AvatarSkeleton$1 as AvatarSkeleton,
  BetaBadge,
  PortableTextInput as BlockEditor,
  BlockImagePreview,
  BlockPreview,
  BooleanInput,
  COMMENTS_INSPECTOR_NAME,
  CONNECTING,
  CapabilityGate,
  ChangeBreadcrumb,
  ChangeConnectorRoot,
  ChangeFieldWrapper,
  ChangeIndicator,
  ChangeIndicatorsTracker,
  ChangeList,
  ChangeResolver,
  ChangeTitleSegment,
  ChangesError,
  CircularProgress,
  CollapseMenu,
  CollapseMenuButton,
  ColorSchemeCustomProvider,
  ColorSchemeLocalStorageProvider,
  ColorSchemeProvider,
  CommandList,
  CommentDeleteDialog,
  CommentDisabledIcon,
  CommentInlineHighlightSpan,
  CommentInput,
  CommentsAuthoringPathProvider,
  CommentsEnabledProvider,
  CommentsIntentProvider,
  CommentsList,
  CommentsProvider,
  CommentsSelectedPathProvider,
  CompactPreview,
  Rule as ConcreteRuleClass,
  ConfigPropertyError,
  ConfigResolutionError,
  ContextMenuButton,
  CopyPasteProvider,
  CorsOriginError,
  StudioCrossDatasetReferenceInput as CrossDatasetReferenceInput,
  DEFAULT_MAX_RECURSION_DEPTH,
  DEFAULT_STUDIO_CLIENT_OPTIONS,
  DRAFTS_FOLDER,
  DateInput,
  DateTimeInput$2 as DateTimeInput,
  DefaultPreview,
  DetailPreview,
  DiffCard,
  DiffErrorBoundary,
  DiffFromTo,
  DiffInspectWrapper,
  DiffString,
  DiffStringSegment,
  DiffTooltip,
  DocumentPreviewPresence,
  DocumentStatus,
  DocumentStatusIndicator,
  EMPTY_ARRAY$B as EMPTY_ARRAY,
  EMPTY_OBJECT,
  EditPortal,
  EditScheduleForm,
  EmailInput,
  ErrorActions,
  ErrorMessage,
  Event$1 as Event,
  EventsProvider,
  FallbackDiff,
  FieldActionMenu,
  FieldActionsProvider,
  FieldActionsResolver,
  FieldChange,
  FieldPresence,
  FieldPresenceInner,
  FieldPresenceWithOverlay,
  StudioFileInput as FileInput,
  Filters,
  FormBuilder,
  FormCallbacksProvider,
  FormField,
  FormFieldHeaderText,
  FormFieldSet,
  FormFieldStatus,
  FormFieldValidationStatus,
  FormInput,
  FormProvider,
  FormValueProvider,
  FromTo,
  FromToArrow,
  GetFormValueProvider,
  GetHookCollectionState,
  GroupChange,
  Hotkeys,
  HoveredFieldProvider,
  StudioImageInput as ImageInput,
  ImperativeToast,
  InlinePreview,
  InsufficientPermissionsMessage,
  IntentButton,
  IsLastPaneProvider,
  LATEST,
  LegacyLayerProvider,
  LinearProgress,
  LoadingBlock,
  LocaleProvider,
  LocaleProviderBase,
  MEDIA_LIBRARY_ASSET_ASPECT_TYPE_NAME,
  MediaPreview,
  MemberField,
  MemberFieldError,
  MemberFieldSet,
  MemberItemError,
  MetaInfo,
  NoChanges,
  NumberInput,
  ObjectInput,
  ObjectInputMember,
  ObjectInputMembers,
  ObjectMembers,
  PatchEvent,
  PerspectiveProvider,
  PopoverDialog,
  PortableTextInput,
  PresenceOverlay,
  PresenceScope,
  Preview$1 as Preview,
  PreviewCard,
  PreviewLoader,
  RELEASES_INTENT,
  RELEASES_STUDIO_CLIENT_OPTIONS,
  StudioReferenceInput as ReferenceInput,
  ReferenceInputOptionsProvider,
  ReferenceInputPreviewCard,
  RelativeTime,
  ReleaseAvatar,
  ReleasesNav,
  Resizable,
  ResourceCacheProvider,
  RevertChangesButton,
  SANITY_PATCH_TYPE,
  SANITY_VERSION,
  SESSION_ID,
  SanityCreateConfigProvider,
  SanityDefaultPreview,
  ScheduleAction,
  ScheduledBadge,
  SchemaError,
  ScrollContainer,
  SearchButton,
  SearchDialog,
  SearchHeader,
  SearchPopover,
  SearchProvider,
  SearchResultItemPreview,
  SelectInput,
  SlugInput,
  SourceProvider,
  StatusButton,
  StringInput,
  Studio,
  StudioAnnouncementsCard,
  StudioAnnouncementsDialog,
  StudioLayout,
  StudioLayoutComponent,
  StudioLogo,
  StudioNavbar,
  StudioProvider,
  StudioToolMenu,
  TIMELINE_ITEM_I18N_KEY_MAPPING,
  TagsArrayInput,
  TelephoneInput,
  TemplatePreview,
  TextInput,
  TextWithTone,
  Timeline,
  TimelineController,
  ToolLink,
  TooltipOfDisabled,
  TransformPatches,
  Translate,
  UniversalArrayInput,
  UpsellDescriptionSerializer,
  UpsellDialogDismissed,
  UpsellDialogLearnMoreCtaClicked,
  UpsellDialogUpgradeCtaClicked,
  UpsellDialogViewed,
  UrlInput,
  UserAvatar,
  UserColorManagerProvider,
  VERSION_FOLDER,
  ValueError,
  VersionChip$1 as VersionChip,
  VersionInlineBadge,
  VirtualizerScrollInstanceProvider,
  WithReferringDocuments,
  WorkspaceLoaderBoundary as WorkspaceLoader,
  WorkspaceProvider,
  WorkspacesProvider,
  ZIndexProvider,
  _createAuthStore,
  _isCustomDocumentTypeDefinition,
  _isSanityDocumentTypeDefinition,
  asLoadable,
  buildCommentRangeDecorations,
  buildLegacyTheme,
  buildRangeDecorationSelectionsFromComments,
  buildTextSelectionFromFragment,
  catchWithCount,
  checkoutPair,
  collate,
  createAuthStore,
  createBufferedDocument,
  createConfig,
  createConnectionStatusStore,
  createDefaultIcon,
  createDocumentPreviewStore,
  createDocumentStore,
  createDraftFrom,
  createGrantsStore,
  createHistoryStore,
  createHookFromObservableFactory,
  createKeyValueStore,
  createMockAuthStore,
  createObservableBufferedDocument,
  createPatchChannel,
  createPlugin,
  createPresenceStore,
  createProjectStore,
  createPublishedFrom,
  createSWR,
  createSanityMediaLibraryFileSource,
  createSanityMediaLibraryImageSource,
  createSchema,
  createSearch,
  createSharedResizeObserver,
  createSourceFromConfig,
  createUserColorManager,
  createUserStore,
  createWorkspaceFromConfig,
  dec,
  decodePath,
  defaultLocale,
  defaultRenderAnnotation,
  defaultRenderBlock,
  defaultRenderField,
  defaultRenderInlineBlock,
  defaultRenderInput,
  defaultRenderItem,
  defaultRenderPreview,
  defaultTemplateForType,
  defaultTemplatesForSchema,
  defaultTheme,
  defineArrayMember,
  defineAssetAspect,
  defineConfig,
  defineDocumentFieldAction,
  defineDocumentInspector,
  defineField,
  defineLocale,
  defineLocaleResourceBundle,
  defineLocalesResources,
  definePlugin,
  defineSearchFilter,
  defineSearchFilterOperators,
  defineSearchOperator,
  defineType,
  diffMatchPatch,
  diffResolver,
  documentFieldActionsReducer,
  documentIdEquals,
  editState,
  emitOperation,
  encodePath,
  escapeField,
  fieldNeedsEscape,
  findIndex,
  flattenConfig,
  formatRelativeLocale,
  formatRelativeLocalePublishDate,
  fromMutationPatches,
  getAnnotationAtPath,
  getAnnotationColor,
  getCalendarLabels,
  getConfigContextFromSource,
  getDiffAtPath,
  getDocumentPairPermissions,
  getDocumentValuePermissions,
  getDocumentVariantType,
  getDraftId,
  getExpandOperations,
  getIdPair,
  getInitialValueStream,
  getItemKey$1 as getItemKey,
  getItemKeySegment,
  getNamelessWorkspaceIdentifier,
  getPairListener,
  getPreviewPaths,
  getPreviewStateObservable$1 as getPreviewStateObservable,
  getPreviewValueWithFallback,
  getProviderTitle,
  getPublishedId,
  getReleaseIdFromReleaseDocumentId,
  getReleaseTone,
  getSanityCreateLinkMetadata,
  getSchemaTypeTitle,
  getSearchableTypes,
  getTemplatePermissions,
  getValueAtPath,
  getValueError,
  getVersionFromId,
  getVersionId,
  getVersionInlineBadge,
  getWorkspaceIdentifier,
  globalScope,
  grantsPermissionOn,
  hasCommentMessageValue,
  idMatchesPerspective,
  inc,
  initialDocumentFieldActions,
  insert$1 as insert,
  isAddedItemDiff,
  isArray,
  isArrayOfBlocksInputProps,
  isArrayOfBlocksSchemaType,
  isArrayOfObjectsInputProps,
  isArrayOfObjectsSchemaType,
  isArrayOfPrimitivesInputProps,
  isArrayOfPrimitivesSchemaType,
  isArraySchemaType,
  isAssetAspect,
  isAuthStore,
  isBlockChildrenObjectField,
  isBlockListObjectField,
  isBlockSchemaType,
  isBlockStyleObjectField,
  isBooleanInputProps,
  isBooleanSchemaType,
  isBuilder,
  isCookielessCompatibleLoginMethod,
  isCreateDocumentVersionEvent,
  isCreateIfNotExistsMutation,
  isCreateLiveDocumentEvent,
  isCreateMutation,
  isCreateOrReplaceMutation,
  isCreateSquashedMutation,
  isCrossDatasetReference,
  isCrossDatasetReferenceSchemaType,
  isDateTimeSchemaType,
  isDeleteDocumentGroupEvent,
  isDeleteDocumentVersionEvent,
  isDeleteMutation,
  isDeprecatedSchemaType,
  isDeprecationConfiguration,
  isDev,
  isDocumentSchemaType,
  isDraft,
  isDraftId,
  isDraftPerspective,
  isEditDocumentVersionEvent,
  isEmptyObject$1 as isEmptyObject,
  isFieldChange,
  isFileSchemaType,
  isGlobalDocumentReference,
  isGoingToUnpublish,
  isGroupChange,
  isImage,
  isImageSchemaType,
  isIndexSegment,
  isIndexTuple,
  isKeySegment,
  isKeyedObject,
  isNonNullable$3 as isNonNullable,
  isNumberInputProps,
  isNumberSchemaType,
  isObjectInputProps,
  isObjectItemProps,
  isObjectSchemaType,
  isPatchMutation,
  isPerspectiveRaw,
  isPortableTextListBlock,
  isPortableTextSpan,
  isPortableTextTextBlock,
  isPrimitiveSchemaType,
  isProd,
  isPublishDocumentVersionEvent,
  isPublishedId,
  isPublishedPerspective,
  isRecord$4 as isRecord,
  isReference,
  isReferenceSchemaType,
  isReleaseDocument,
  isReleasePerspective,
  isReleaseScheduledOrScheduling,
  isRemovedItemDiff,
  isSanityCreateExcludedType,
  isSanityCreateLinked,
  isSanityCreateLinkedDocument,
  isSanityCreateStartCompatibleDoc,
  isSanityDefinedAction,
  isSanityDocument,
  isScheduleDocumentVersionEvent,
  isSearchStrategy,
  isSlug,
  isSpanSchemaType,
  isString,
  isStringInputProps,
  isStringSchemaType,
  isSystemBundle,
  isSystemBundleName,
  isTextSelectionComment,
  isTitledListValue,
  isTruthy,
  isTypedObject,
  isUnchangedDiff,
  isUnpublishDocumentEvent,
  isUnscheduleDocumentVersionEvent,
  isUpdateLiveDocumentEvent,
  isValidAnnouncementAudience,
  isValidAnnouncementRole,
  isValidationError,
  isValidationErrorMarker,
  isValidationInfo,
  isValidationInfoMarker,
  isValidationWarning,
  isValidationWarningMarker,
  isVersionId,
  joinPath,
  listenQuery,
  matchWorkspace,
  newDraftFrom,
  noop$1 as noop,
  normalizeIndexSegment,
  normalizeIndexTupleSegment,
  normalizeKeySegment,
  normalizePathSegment,
  onRetry,
  operationEvents,
  operatorDefinitions,
  pathToString$1 as pathToString,
  pathsAreEqual,
  prefixPath,
  prepareConfig,
  prepareForPreview,
  prepareTemplates,
  remoteSnapshots,
  removeDupes$3 as removeDupes,
  removeMissingReferences,
  removeUndefinedLocaleResources,
  renderStudio,
  resizeObserver,
  resolveConditionalProperty,
  resolveConfig,
  resolveDiffComponent,
  resolveInitialObjectValue,
  resolveInitialValue,
  resolveInitialValueForType,
  resolveSchemaTypes,
  searchStrategies,
  serializeError,
  set,
  setAtPath,
  setIfMissing,
  sliceString,
  snapshotPair,
  stringToPath,
  supportsTouch,
  systemBundles,
  toMutationPatches,
  truncateString,
  typed,
  uncaughtErrorHandler,
  unset,
  useObserveDocument as unstable_useObserveDocument,
  useDocumentPreview as unstable_useValuePreview,
  usEnglishLocale,
  useActiveReleases,
  useActiveWorkspace,
  useAddonDataset,
  useAnnotationColor,
  useArchivedReleases,
  useCanvasCompanionDoc,
  useChangeIndicatorsReportedValues,
  useChangeIndicatorsReporter,
  useClient,
  useColorScheme,
  useColorSchemeInternalValue,
  useColorSchemeOptions,
  useColorSchemeSetValue,
  useColorSchemeValue,
  useComlinkStore,
  useComments,
  useCommentsEnabled,
  useCommentsSelectedPath,
  useCommentsTelemetry,
  useConditionalToast,
  useConfigContextFromSource,
  useConnectionState,
  useConnectionStatusStore,
  useCopyErrorDetails,
  useCopyPaste,
  useCurrentLocale,
  useCurrentUser,
  useDataset,
  useDateTimeFormat,
  useDidUpdate,
  useDiffAnnotationColor,
  useDocumentChange,
  useDocumentForm,
  useDocumentOperation,
  useDocumentOperationEvent,
  useDocumentPairPermissions,
  useDocumentPairPermissionsFromHookFactory,
  useDocumentPresence,
  useDocumentPreviewStore,
  useDocumentStore,
  useDocumentType,
  useDocumentValuePermissions,
  useDocumentValues,
  useDocumentVersionInfo,
  useDocumentVersionTypeSortedList,
  useDocumentVersions,
  useEditState,
  useEvents,
  useEventsStore,
  useExcludedPerspective,
  useFeatureEnabled,
  useFieldActions,
  useFormBuilder,
  useFormCallbacks,
  useFormState,
  useFormValue,
  useFormattedDuration,
  useGetFormValue,
  useGetI18nText,
  useGlobalCopyPasteElementHandler,
  useGlobalPresence,
  useGrantsStore,
  useHistoryStore,
  useHoveredField,
  useI18nText,
  useInitialValue,
  useInitialValueResolverContext,
  useIsReleaseActive,
  useKeyValueStore,
  useListFormat,
  useLoadable,
  useLocale,
  useManageFavorite,
  useMiddlewareComponents,
  useNavigateToCanvasDoc,
  useNumberFormat,
  useOnScroll,
  useOnlyHasVersions,
  usePerspective,
  usePresenceStore,
  usePreviewCard,
  useProject,
  useProjectDatasets,
  useProjectId$1 as useProjectId,
  useProjectStore,
  useReconnectingToast,
  useReferenceInputOptions,
  useReferringDocuments,
  useRelativeTime,
  useReleasesIds,
  useRenderingContextStore,
  useResolveInitialValueForType,
  useResourceCache,
  useReviewChanges,
  useRovingFocus,
  useSanityCreateConfig,
  useSchema,
  useSearchMaxFieldDepth,
  useSearchState,
  useSetPerspective,
  useSource,
  useStudioUrl,
  useSyncState,
  useTemplatePermissions,
  useTemplatePermissionsFromHookFactory,
  useTemplates,
  useThrottledCallback,
  useTimeAgo,
  useTimelineSelector,
  useTimelineStore,
  useTools,
  useTrackerStore,
  useTrackerStoreReporter,
  useTranslation,
  useTreeEditingEnabled,
  useUnique,
  useUnitFormatter,
  useUser,
  useUserColor,
  useUserColorManager,
  useUserListWithPermissions,
  useUserStore,
  useValidationStatus,
  useVersionOperations,
  useVirtualizerScrollInstance,
  useWorkspace,
  useWorkspaceLoader,
  useWorkspaceSchemaId,
  useWorkspaces,
  useZIndex,
  userHasRole,
  validateBasePaths,
  validateDocument,
  validateNames,
  validateWorkspaces,
  validation,
  visitDiff
};

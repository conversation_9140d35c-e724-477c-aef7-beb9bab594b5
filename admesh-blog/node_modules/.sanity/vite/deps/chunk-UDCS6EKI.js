import {
  defineLocaleResourceBundle,
  definePlugin,
  route
} from "./chunk-6XMXOQIO.js";
import {
  EyeOpenIcon
} from "./chunk-V3YD7LXU.js";
import {
  require_react
} from "./chunk-3GN7GPJI.js";
import {
  __toESM
} from "./chunk-OCBYBPSH.js";

// ../node_modules/@sanity/vision/lib/_chunks-es/index.mjs
var import_react = __toESM(require_react(), 1);
var visionLocaleNamespace = "vision";
var visionUsEnglishLocaleBundle = defineLocaleResourceBundle({
  locale: "en-US",
  namespace: visionLocaleNamespace,
  resources: () => import("./resources-UDHIWQRH.js")
});
var visionTool = definePlugin((options) => {
  const {
    name,
    title,
    icon,
    ...config
  } = options || {};
  return {
    name: "@sanity/vision",
    tools: [{
      name: name || "vision",
      title: title || "Vision",
      icon: icon || EyeOpenIcon,
      component: (0, import_react.lazy)(() => import("./SanityVision-CTI2F4HH.js")),
      options: config,
      router: route.create("/*"),
      __internalApplicationType: "sanity/vision"
    }],
    i18n: {
      bundles: [visionUsEnglishLocaleBundle]
    }
  };
});

export {
  visionLocaleNamespace,
  visionTool
};
//# sourceMappingURL=chunk-UDCS6EKI.js.map

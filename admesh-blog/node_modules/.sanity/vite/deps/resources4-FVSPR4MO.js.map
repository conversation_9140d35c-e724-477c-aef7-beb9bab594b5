{"version": 3, "sources": ["../../../sanity/src/core/canvas/i18n/resources.ts"], "sourcesContent": ["import {defineLocalesResources} from '../../i18n'\n\n/**\n * Defined locale strings for the Canvas integration feature, in US English.\n *\n * @internal\n */\nconst canvasLocaleStrings = defineLocalesResources('canvas', {\n  /** The text for the \"Link to Canvas\" action. */\n  'action.link-document': 'Link to Canvas',\n\n  /** The text for the \"Link to Canvas\" action when the document is not in the dashboard. */\n  'action.link-document-disabled.not-in-dashboard':\n    'Open this document in Dashboard to link to Canvas',\n  /** The text for the \"Link to Canvas\" action when the user doesn't have permissions to link the document to Canvas. */\n  'action.link-document-disabled.missing-permissions':\n    \"You don't have permissions to link this document to Canvas\",\n  /** The text for the \"Link to Canvas\" action when the document is a version document. */\n  'action.link-document-disabled.version-document':\n    'Version documents are not yet supported in Canvas',\n  /** The text for the \"Link to Canvas\" action when the document is not yet resolved. */\n  'action.link-document-disabled.initial-value-not-resolved':\n    'Please wait until the document initial values are resolved',\n  /** The text for the \"Unlink from Canvas\" action. */\n  'action.unlink-document': 'Unlink from Canvas',\n  /** The text for the \"Edit in Canvas\" action. */\n  'action.edit-document': 'Edit in Canvas',\n  /** The text for the banner when the document is linked to Canvas. */\n  'banner.linked-to-canvas': 'This document is linked to Canvas',\n  /** The text for the action button in the banner when the document is linked to Canvas. */\n  'banner.edit-document-in-canvas': 'Edit in Canvas',\n\n  /** The title for the \"Confirm document changes\" dialog. */\n  'dialog.confirm-document-changes.title': 'Confirm document changes',\n  /** The description for the \"Confirm document changes\" dialog. */\n  'dialog.confirm-document-changes.description':\n    'This document needs to be updated to be compatible with Canvas.\\n Existing content may be edited or removed as part of this process.',\n  /** The text for the \"Confirm document changes\" dialog confirm button. */\n  'dialog.confirm-document-changes.confirm': 'Accept and continue',\n  /** The text for the \"Confirm document changes\" dialog cancel button. */\n  'dialog.confirm-document-changes.cancel': 'Cancel',\n  /** The description for the \"Confirm document changes\" dialog footer. */\n  'dialog.confirm-document-changes.footer-description': 'You can unlink from Canvas at any time',\n\n  /** The title for the \"Link to Canvas\" dialog. */\n  'dialog.link-to-canvas.title': 'Link to Canvas',\n  /** The text for the \"Link to Canvas\" dialog when the document is being validated. */\n  'dialog.link-to-canvas.validating': 'Validating',\n  /** The text for the \"Link to Canvas\" dialog when the document is being redirected. */\n  'dialog.link-to-canvas.redirecting': 'Taking you to Canvas to complete linking...',\n  /** The text for the Link to Canvas dialog when there is a error. */\n  'dialog.link-to-canvas.error': 'Failed to link to Canvas',\n\n  /** The title for the \"Unlink from Canvas\" dialog. */\n  'dialog.unlink-from-canvas.title': 'Unlink from Canvas',\n  /** The text for the \"Unlink from Canvas\" dialog when the document is being unlinked. */\n  'dialog.unlink-from-canvas.unlinking':\n    \"You're unlinking  <strong>“{{documentTitle}}”</strong> from Canvas.\",\n  /** The text for the \"Unlink from Canvas\" dialog cancel button. */\n  'dialog.unlink-from-canvas.cancel': 'Cancel',\n  /** The text for the \"Unlink from Canvas\" dialog unlink button. */\n  'dialog.unlink-from-canvas.unlink-action': 'Unlink now',\n  /** The text for the \"Unlink from Canvas\" dialog description. */\n  'dialog.unlink-from-canvas.description':\n    'Once unlinked, it will be editable here and future edits in Canvas will no longer automatically map to this document.',\n  /** The text for the \"Unlink from Canvas\" dialog success message. */\n  'dialog.unlink-from-canvas.success': 'Unlinked from Canvas',\n  /** The text for the \"Unlink from Canvas\" dialog error message. */\n  'dialog.unlink-from-canvas.error': 'Failed to unlink from Canvas',\n  /** The text for the \"Navigate to Canvas\" dialog error message. */\n  'navigate-to-canvas-doc.error.missing-permissions': 'Missing permissions to navigate to Canvas',\n})\n\n/**\n * @alpha\n */\nexport type CanvasLocaleResourceKeys = keyof typeof canvasLocaleStrings\n\nexport default canvasLocaleStrings\n"], "mappings": ";;;;;;;;;;;;;;AAOMA,IAAAA,sBAAsBC,uBAAuB,UAAU;;EAE3D,wBAAwB;;EAGxB,kDACE;;EAEF,qDACE;;EAEF,kDACE;;EAEF,4DACE;;EAEF,0BAA0B;;EAE1B,wBAAwB;;EAExB,2BAA2B;;EAE3B,kCAAkC;;EAGlC,yCAAyC;;EAEzC,+CACE;;;EAEF,2CAA2C;;EAE3C,0CAA0C;;EAE1C,sDAAsD;;EAGtD,+BAA+B;;EAE/B,oCAAoC;;EAEpC,qCAAqC;;EAErC,+BAA+B;;EAG/B,mCAAmC;;EAEnC,uCACE;;EAEF,oCAAoC;;EAEpC,2CAA2C;;EAE3C,yCACE;;EAEF,qCAAqC;;EAErC,mCAAmC;;EAEnC,oDAAoD;AACtD,CAAC;", "names": ["canvasLocaleStrings", "defineLocalesResources"]}
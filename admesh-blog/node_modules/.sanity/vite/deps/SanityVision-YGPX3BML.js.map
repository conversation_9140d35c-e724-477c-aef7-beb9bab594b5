{"version": 3, "sources": ["../../../../../node_modules/json-2-csv/lib/constants.js", "../../../../../node_modules/doc-path/lib/path.js", "../../../../../node_modules/deeks/lib/utils.js", "../../../../../node_modules/deeks/lib/types.js", "../../../../../node_modules/deeks/lib/deeks.js", "../../../../../node_modules/json-2-csv/lib/utils.js", "../../../../../node_modules/json-2-csv/lib/json2csv.js", "../../../../../node_modules/json-2-csv/lib/csv2json.js", "../../../../../node_modules/json-2-csv/lib/converter.js", "../../../../../node_modules/@rexxars/react-split-pane/src/Pane.tsx", "../../../../../node_modules/@rexxars/react-split-pane/src/Resizer.tsx", "../../../../../node_modules/@rexxars/react-split-pane/src/SplitPane.tsx", "../../../../../node_modules/json5/dist/index.mjs", "../../../../../node_modules/@sanity/vision/src/apiVersions.ts", "../../../../../node_modules/@sanity/vision/src/components/DelayedSpinner.tsx", "../../../../../node_modules/@sanity/vision/src/codemirror/extensions.ts", "../../../../../node_modules/@sanity/vision/src/codemirror/useCodemirrorTheme.ts", "../../../../../node_modules/@sanity/vision/src/codemirror/VisionCodeMirror.styled.tsx", "../../../../../node_modules/@sanity/vision/src/codemirror/VisionCodeMirror.tsx", "../../../../../node_modules/@sanity/vision/src/perspectives.ts", "../../../../../node_modules/@sanity/vision/src/util/encodeQueryString.ts", "../../../../../node_modules/@sanity/vision/src/util/isPlainObject.ts", "../../../../../node_modules/@sanity/vision/src/util/localStorage.ts", "../../../../../node_modules/@sanity/vision/src/util/parseApiQueryString.ts", "../../../../../node_modules/@sanity/vision/src/util/prefixApiVersion.ts", "../../../../../node_modules/@sanity/vision/src/util/validateApiVersion.ts", "../../../../../node_modules/@sanity/vision/src/util/tryParseParams.ts", "../../../../../node_modules/@sanity/vision/src/components/VisionGui.styled.tsx", "../../../../../node_modules/@sanity/vision/src/components/ParamsEditor.tsx", "../../../../../node_modules/@sanity/vision/src/hooks/useSavedQueries.ts", "../../../../../node_modules/@sanity/vision/src/components/QueryRecall.styled.tsx", "../../../../../node_modules/@sanity/vision/src/components/QueryRecall.tsx", "../../../../../node_modules/@sanity/vision/src/components/usePaneSize.ts", "../../../../../node_modules/@sanity/vision/src/components/VisionGuiControls.tsx", "../../../../../node_modules/@sanity/vision/src/components/PerspectivePopover.styled.tsx", "../../../../../node_modules/@sanity/vision/src/components/PerspectivePopover.tsx", "../../../../../node_modules/@sanity/vision/src/components/VisionGuiHeader.tsx", "../../../../../node_modules/@sanity/vision/src/util/getBlobUrl.ts", "../../../../../node_modules/@sanity/vision/src/components/QueryErrorDialog.styled.tsx", "../../../../../node_modules/@sanity/vision/src/components/QueryErrorDetails.tsx", "../../../../../node_modules/@sanity/vision/src/components/QueryErrorDialog.tsx", "../../../../../node_modules/@sanity/vision/src/components/ResultView.styled.tsx", "../../../../../node_modules/@sanity/vision/src/components/ResultView.tsx", "../../../../../node_modules/@sanity/vision/src/components/SaveResultButtons.tsx", "../../../../../node_modules/@sanity/vision/src/components/VisionGuiResult.tsx", "../../../../../node_modules/@sanity/vision/src/components/VisionGui.tsx", "../../../../../node_modules/@sanity/vision/src/hooks/useDatasets.ts", "../../../../../node_modules/@sanity/vision/src/containers/VisionContainer.tsx", "../../../../../node_modules/@sanity/vision/src/containers/VisionErrorBoundary.tsx", "../../../../../node_modules/@sanity/vision/src/SanityVision.tsx"], "sourcesContent": ["'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.excelBOM = exports.defaultCsv2JsonOptions = exports.defaultJson2CsvOptions = exports.errors = void 0;\nexports.errors = {\n    optionsRequired: 'Options were not passed and are required.',\n    json2csv: {\n        cannotCallOn: 'Cannot call json2csv on',\n        dataCheckFailure: 'Data provided was not an array of documents.',\n        notSameSchema: 'Not all documents have the same schema.'\n    },\n    csv2json: {\n        cannotCallOn: 'Cannot call csv2json on',\n        dataCheckFailure: 'CSV is not a string.'\n    }\n};\nexports.defaultJson2CsvOptions = {\n    arrayIndexesAsKeys: false,\n    checkSchemaDifferences: false,\n    delimiter: {\n        field: ',',\n        wrap: '\"',\n        eol: '\\n'\n    },\n    emptyFieldValue: undefined,\n    escapeHeaderNestedDots: true,\n    excelBOM: false,\n    excludeKeys: [],\n    expandNestedObjects: true,\n    expandArrayObjects: false,\n    prependHeader: true,\n    preventCsvInjection: false,\n    sortHeader: false,\n    trimFieldValues: false,\n    trimHeaderFields: false,\n    unwindArrays: false,\n    useDateIso8601Format: false,\n    useLocaleFormat: false,\n    wrapBooleans: false,\n};\nexports.defaultCsv2JsonOptions = {\n    delimiter: {\n        field: ',',\n        wrap: '\"',\n        eol: '\\n'\n    },\n    excelBOM: false,\n    preventCsvInjection: false,\n    trimFieldValues: false,\n    trimHeaderFields: false,\n};\nexports.excelBOM = '\\ufeff';\n", "/**\n * @license MIT\n * doc-path <https://github.com/mrodrig/doc-path>\n * Copyright (c) 2015-present, <PERSON>.\n */\n'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setPath = exports.evaluatePath = void 0;\n/**\n * Main function that evaluates the path in a particular object\n * @throws {Error} possible error if call stack size is exceeded\n */\nfunction evaluatePath(obj, kp) {\n    if (!obj) {\n        return null;\n    }\n    const { dotIndex, key, remaining } = state(kp);\n    const kpVal = typeof obj === 'object' && kp in obj ? obj[kp] : undefined;\n    const keyVal = typeof obj === 'object' && key in obj ? obj[key] : undefined;\n    if (dotIndex >= 0 && typeof obj === 'object' && !(kp in obj)) {\n        const { key: nextKey } = state(remaining);\n        const nextKeyAsInt = parseInt(nextKey);\n        // If there's an array at the current key in the object, then iterate over those items evaluating the remaining path\n        if (Array.isArray(keyVal) && isNaN(nextKeyAsInt)) {\n            return keyVal.map((doc) => evaluatePath(doc, remaining));\n        }\n        // Otherwise, we can just recur\n        return evaluatePath(keyVal, remaining);\n    }\n    else if (Array.isArray(obj)) {\n        const keyAsInt = parseInt(key);\n        if (kp === key && dotIndex === -1 && !isNaN(keyAsInt)) {\n            return keyVal;\n        }\n        // If this object is actually an array, then iterate over those items evaluating the path\n        return obj.map((doc) => evaluatePath(doc, kp));\n    }\n    else if (dotIndex >= 0 && kp !== key && typeof obj === 'object' && key in obj) {\n        // If there's a field with a non-nested dot, then recur into that sub-value\n        return evaluatePath(keyVal, remaining);\n    }\n    else if (dotIndex === -1 && typeof obj === 'object' && key in obj && !(kp in obj)) {\n        // If the field is here, but the key was escaped\n        return keyVal;\n    }\n    // Otherwise, we can just return value directly\n    return kpVal;\n}\nexports.evaluatePath = evaluatePath;\n/**\n * Main function that performs validation before passing off to _sp\n * @throws {Error} possible error if call stack size is exceeded\n */\nfunction setPath(obj, kp, v) {\n    if (!obj) {\n        throw new Error('No object was provided.');\n    }\n    else if (!kp) {\n        throw new Error('No keyPath was provided.');\n    }\n    return _sp(obj, kp, v);\n}\nexports.setPath = setPath;\n// Helper function that will set the value in the provided object/array.\nfunction _sp(obj, kp, v) {\n    const { dotIndex, key, remaining } = state(kp);\n    // If this is clearly a prototype pollution attempt, then refuse to modify the path\n    if (kp.startsWith('__proto__') || kp.startsWith('constructor') || kp.startsWith('prototype')) {\n        return obj;\n    }\n    if (dotIndex >= 0) {\n        const keyAsInt = parseInt(key);\n        // If there is a '.' in the key path, recur on the subdoc and ...\n        if (typeof obj === 'object' && obj !== null && !(key in obj) && Array.isArray(obj) && !isNaN(keyAsInt)) {\n            // If there's no value at obj[key] then populate an empty object\n            obj[key] = obj[key] ?? {};\n            // Continue iterating on the rest of the key path to set the appropriate value where intended and then return\n            _sp(obj[key], remaining, v);\n            return obj;\n        }\n        else if (typeof obj === 'object' && obj !== null && !(key in obj) && Array.isArray(obj)) {\n            // If this is an array and there are multiple levels of keys to iterate over, recur.\n            obj.forEach((doc) => _sp(doc, kp, v));\n            return obj;\n        }\n        else if (typeof obj === 'object' && obj !== null && !(key in obj) && !Array.isArray(obj)) {\n            const { key: nextKey } = state(remaining);\n            const nextKeyAsInt = parseInt(nextKey);\n            if (!isNaN(nextKeyAsInt)) {\n                // If the current key doesn't exist yet and the next key is a number (likely array index), populate an empty array\n                obj[key] = [];\n            }\n            else if (remaining === '') {\n                // If the remaining key is empty, then a `.` character appeared right at the end of the path and wasn't actually indicating a separate level\n                obj[kp] = v;\n                return obj;\n            }\n            else {\n                // If the current key doesn't exist yet, populate it\n                obj[key] = {};\n            }\n        }\n        _sp(obj[key], remaining, v);\n    }\n    else if (Array.isArray(obj)) {\n        const keyAsInt = parseInt(key);\n        // If the object is an array and this key is an int (likely array index), then set the value directly and return\n        if (kp === key && dotIndex === -1 && !isNaN(keyAsInt)) {\n            obj[key] = v;\n            return obj;\n        }\n        // If this \"obj\" is actually an array, then we can loop over each of the values and set the path\n        obj.forEach((doc) => _sp(doc, remaining, v));\n        return obj;\n    }\n    else {\n        // Otherwise, we can set the path directly\n        obj[key] = v;\n    }\n    return obj;\n}\n// Helper function that returns some information necessary to evaluate or set a path  based on the provided keyPath value\nfunction state(kp) {\n    const dotIndex = findFirstNonEscapedDotIndex(kp);\n    return {\n        dotIndex,\n        key: kp.slice(0, dotIndex >= 0 ? dotIndex : undefined).replace(/\\\\./g, '.'),\n        remaining: kp.slice(dotIndex + 1)\n    };\n}\nfunction findFirstNonEscapedDotIndex(kp) {\n    for (let i = 0; i < kp.length; i++) {\n        const previousChar = i > 0 ? kp[i - 1] : '', currentChar = kp[i];\n        if (currentChar === '.' && previousChar !== '\\\\')\n            return i;\n    }\n    return -1;\n}\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isDocumentToRecurOn = exports.flatten = exports.unique = void 0;\nfunction unique(array) {\n    return [...new Set(array)];\n}\nexports.unique = unique;\nfunction flatten(array) {\n    return [].concat(...array);\n}\nexports.flatten = flatten;\n/**\n * Returns whether this value is a document to recur on or not\n * @param val Any item whose type will be evaluated\n * @returns {boolean}\n */\nfunction isDocumentToRecurOn(val) {\n    return typeof val === 'object' && val !== null && !Array.isArray(val) && Object.keys(val).length;\n}\nexports.isDocumentToRecurOn = isDocumentToRecurOn;\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\n", "'use strict';\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deepKeysFromList = exports.deepKeys = void 0;\nconst utils = __importStar(require(\"./utils\"));\n__exportStar(require(\"./types\"), exports);\n/**\n * Return the deep keys list for a single document\n * @param object\n * @param options\n * @returns {Array}\n */\nfunction deepKeys(object, options) {\n    const parsedOptions = mergeOptions(options);\n    if (typeof object === 'object' && object !== null) {\n        return generateDeepKeysList('', object, parsedOptions);\n    }\n    return [];\n}\nexports.deepKeys = deepKeys;\n/**\n * Return the deep keys list for all documents in the provided list\n * @param list\n * @param options\n * @returns Array[Array[String]]\n */\nfunction deepKeysFromList(list, options) {\n    const parsedOptions = mergeOptions(options);\n    return list.map((document) => {\n        if (typeof document === 'object' && document !== null) {\n            // if the data at the key is a document, then we retrieve the subHeading starting with an empty string heading and the doc\n            return deepKeys(document, parsedOptions);\n        }\n        return [];\n    });\n}\nexports.deepKeysFromList = deepKeysFromList;\nfunction generateDeepKeysList(heading, data, options) {\n    const keys = Object.keys(data).map((currentKey) => {\n        // If the given heading is empty, then we set the heading to be the subKey, otherwise set it as a nested heading w/ a dot\n        const keyName = buildKeyName(heading, escapeNestedDotsIfSpecified(currentKey, options));\n        // If we have another nested document, recur on the sub-document to retrieve the full key name\n        if (options.expandNestedObjects && utils.isDocumentToRecurOn(data[currentKey]) || (options.arrayIndexesAsKeys && Array.isArray(data[currentKey]) && data[currentKey].length)) {\n            return generateDeepKeysList(keyName, data[currentKey], options);\n        }\n        else if (options.expandArrayObjects && Array.isArray(data[currentKey])) {\n            // If we have a nested array that we need to recur on\n            return processArrayKeys(data[currentKey], keyName, options);\n        }\n        else if (options.ignoreEmptyArrays && Array.isArray(data[currentKey]) && !data[currentKey].length) {\n            return [];\n        }\n        // Otherwise return this key name since we don't have a sub document\n        return keyName;\n    });\n    return utils.flatten(keys);\n}\n/**\n * Helper function to handle the processing of arrays when the expandArrayObjects\n * option is specified.\n * @param subArray\n * @param currentKeyPath\n * @param options\n * @returns {*}\n */\nfunction processArrayKeys(subArray, currentKeyPath, options) {\n    let subArrayKeys = deepKeysFromList(subArray, options);\n    if (!subArray.length) {\n        return options.ignoreEmptyArraysWhenExpanding ? [] : [currentKeyPath];\n    }\n    else if (subArray.length && utils.flatten(subArrayKeys).length === 0) {\n        // Has items in the array, but no objects\n        return [currentKeyPath];\n    }\n    else {\n        subArrayKeys = subArrayKeys.map((schemaKeys) => {\n            if (Array.isArray(schemaKeys) && schemaKeys.length === 0) {\n                return [currentKeyPath];\n            }\n            return schemaKeys.map((subKey) => buildKeyName(currentKeyPath, escapeNestedDotsIfSpecified(subKey, options)));\n        });\n        return utils.unique(utils.flatten(subArrayKeys));\n    }\n}\nfunction escapeNestedDotsIfSpecified(key, options) {\n    if (options.escapeNestedDots) {\n        return key.replace(/\\./g, '\\\\.');\n    }\n    return key;\n}\n/**\n * Function used to generate the key path\n * @param upperKeyName String accumulated key path\n * @param currentKeyName String current key name\n * @returns String\n */\nfunction buildKeyName(upperKeyName, currentKeyName) {\n    if (upperKeyName) {\n        return upperKeyName + '.' + currentKeyName;\n    }\n    return currentKeyName;\n}\nfunction mergeOptions(options) {\n    return {\n        arrayIndexesAsKeys: false,\n        expandNestedObjects: true,\n        expandArrayObjects: false,\n        ignoreEmptyArraysWhenExpanding: false,\n        escapeNestedDots: false,\n        ignoreEmptyArrays: false,\n        ...(options ?? {})\n    };\n}\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isInvalid = exports.flatten = exports.unique = exports.arrayDifference = exports.isError = exports.isUndefined = exports.isNull = exports.isObject = exports.isString = exports.isNumber = exports.unwind = exports.getNCharacters = exports.removeEmptyFields = exports.isEmptyField = exports.computeSchemaDifferences = exports.isDateRepresentation = exports.isStringRepresentation = exports.deepCopy = exports.validate = exports.buildC2JOptions = exports.buildJ2COptions = void 0;\nconst doc_path_1 = require(\"doc-path\");\nconst constants_1 = require(\"./constants\");\nconst dateStringRegex = /\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.\\d{3}Z/, MAX_ARRAY_LENGTH = 100000;\n/**\n * Build the options to be passed to the appropriate function\n * If a user does not provide custom options, then we use our default\n * If options are provided, then we set each valid key that was passed\n */\nfunction buildJ2COptions(opts) {\n    return {\n        ...constants_1.defaultJson2CsvOptions,\n        ...opts,\n        delimiter: {\n            field: opts?.delimiter?.field ?? constants_1.defaultJson2CsvOptions.delimiter.field,\n            wrap: opts?.delimiter?.wrap || constants_1.defaultJson2CsvOptions.delimiter.wrap,\n            eol: opts?.delimiter?.eol || constants_1.defaultJson2CsvOptions.delimiter.eol,\n        },\n        fieldTitleMap: Object.create({}),\n    };\n}\nexports.buildJ2COptions = buildJ2COptions;\n/**\n * Build the options to be passed to the appropriate function\n * If a user does not provide custom options, then we use our default\n * If options are provided, then we set each valid key that was passed\n */\nfunction buildC2JOptions(opts) {\n    return {\n        ...constants_1.defaultCsv2JsonOptions,\n        ...opts,\n        delimiter: {\n            field: opts?.delimiter?.field ?? constants_1.defaultCsv2JsonOptions.delimiter.field,\n            wrap: opts?.delimiter?.wrap || constants_1.defaultCsv2JsonOptions.delimiter.wrap,\n            eol: opts?.delimiter?.eol || constants_1.defaultCsv2JsonOptions.delimiter.eol,\n        },\n    };\n}\nexports.buildC2JOptions = buildC2JOptions;\nfunction validate(data, validationFn, errorMessages) {\n    if (!data)\n        throw new Error(`${errorMessages.cannotCallOn} ${data}.`);\n    if (!validationFn(data))\n        throw new Error(errorMessages.dataCheckFailure);\n    return true;\n}\nexports.validate = validate;\n/**\n * Utility function to deep copy an object, used by the module tests\n */\nfunction deepCopy(obj) {\n    return JSON.parse(JSON.stringify(obj));\n}\nexports.deepCopy = deepCopy;\n/**\n * Helper function that determines whether the provided value is a representation\n *   of a string. Given the RFC4180 requirements, that means that the value is\n *   wrapped in value wrap delimiters (usually a quotation mark on each side).\n */\nfunction isStringRepresentation(fieldValue, options) {\n    const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];\n    // If the field starts and ends with a wrap delimiter\n    return firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap;\n}\nexports.isStringRepresentation = isStringRepresentation;\n/**\n * Helper function that determines whether the provided value is a representation\n *   of a date.\n */\nfunction isDateRepresentation(fieldValue) {\n    return dateStringRegex.test(fieldValue);\n}\nexports.isDateRepresentation = isDateRepresentation;\n/**\n * Helper function that determines the schema differences between two objects.\n */\nfunction computeSchemaDifferences(schemaA, schemaB) {\n    return arrayDifference(schemaA, schemaB)\n        .concat(arrayDifference(schemaB, schemaA));\n}\nexports.computeSchemaDifferences = computeSchemaDifferences;\n/**\n * Utility function to check if a field is considered empty so that the emptyFieldValue can be used instead\n */\nfunction isEmptyField(fieldValue) {\n    return isUndefined(fieldValue) || isNull(fieldValue) || fieldValue === '';\n}\nexports.isEmptyField = isEmptyField;\n/**\n * Helper function that removes empty field values from an array.\n */\nfunction removeEmptyFields(fields) {\n    return fields.filter((field) => !isEmptyField(field));\n}\nexports.removeEmptyFields = removeEmptyFields;\n/**\n * Helper function that retrieves the next n characters from the start index in\n *   the string including the character at the start index. This is used to\n *   check if are currently at an EOL value, since it could be multiple\n *   characters in length (eg. '\\r\\n')\n */\nfunction getNCharacters(str, start, n) {\n    return str.substring(start, start + n);\n}\nexports.getNCharacters = getNCharacters;\n/**\n * The following unwind functionality is a heavily modified version of @edwincen's\n * unwind extension for lodash. Since lodash is a large package to require in,\n * and all of the required functionality was already being imported, either\n * natively or with doc-path, I decided to rewrite the majority of the logic\n * so that an additional dependency would not be required. The original code\n * with the lodash dependency can be found here:\n *\n * https://github.com/edwincen/unwind/blob/master/index.js\n */\n/**\n * Core function that unwinds an item at the provided path\n */\nfunction unwindItem(accumulator, item, fieldPath) {\n    const valueToUnwind = (0, doc_path_1.evaluatePath)(item, fieldPath);\n    let cloned = deepCopy(item);\n    if (Array.isArray(valueToUnwind) && valueToUnwind.length) {\n        valueToUnwind.forEach((val) => {\n            cloned = deepCopy(item);\n            accumulator.push((0, doc_path_1.setPath)(cloned, fieldPath, val));\n        });\n    }\n    else if (Array.isArray(valueToUnwind) && valueToUnwind.length === 0) {\n        // Push an empty string so the value is empty since there are no values\n        (0, doc_path_1.setPath)(cloned, fieldPath, '');\n        accumulator.push(cloned);\n    }\n    else {\n        accumulator.push(cloned);\n    }\n}\n/**\n * Main unwind function which takes an array and a field to unwind.\n */\nfunction unwind(array, field) {\n    const result = [];\n    array.forEach((item) => {\n        unwindItem(result, item, field);\n    });\n    return result;\n}\nexports.unwind = unwind;\n/**\n * Checks whether value can be converted to a number\n */\nfunction isNumber(value) {\n    return !isNaN(Number(value));\n}\nexports.isNumber = isNumber;\n/*\n * Helper functions which were created to remove underscorejs from this package.\n */\nfunction isString(value) {\n    return typeof value === 'string';\n}\nexports.isString = isString;\nfunction isObject(value) {\n    return typeof value === 'object';\n}\nexports.isObject = isObject;\nfunction isNull(value) {\n    return value === null;\n}\nexports.isNull = isNull;\nfunction isUndefined(value) {\n    return typeof value === 'undefined';\n}\nexports.isUndefined = isUndefined;\nfunction isError(value) {\n    // TODO(mrodrig): test this possible change\n    // return value instanceof Error;\n    return Object.prototype.toString.call(value) === '[object Error]';\n}\nexports.isError = isError;\nfunction arrayDifference(a, b) {\n    return a.filter((x) => !b.includes(x));\n}\nexports.arrayDifference = arrayDifference;\nfunction unique(array) {\n    return [...new Set(array)];\n}\nexports.unique = unique;\nfunction flatten(array) {\n    // Node 11+ - use the native array flattening function\n    if (array.flat) {\n        return array.flat();\n    }\n    // #167 - allow browsers to flatten very long 200k+ element arrays\n    if (array.length > MAX_ARRAY_LENGTH) {\n        let safeArray = [];\n        for (let a = 0; a < array.length; a += MAX_ARRAY_LENGTH) {\n            safeArray = safeArray.concat(...array.slice(a, a + MAX_ARRAY_LENGTH));\n        }\n        return safeArray;\n    }\n    return array.reduce((accumulator, value) => accumulator.concat(value), []);\n}\nexports.flatten = flatten;\n/**\n * Used to help avoid incorrect values returned by JSON.parse when converting\n * CSV back to JSON, such as '39e1804' which JSON.parse converts to Infinity\n */\nfunction isInvalid(parsedJson) {\n    return parsedJson === Infinity ||\n        parsedJson === -Infinity;\n}\nexports.isInvalid = isInvalid;\n", "'use strict';\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Json2Csv = void 0;\nconst doc_path_1 = require(\"doc-path\");\nconst deeks_1 = require(\"deeks\");\nconst constants_1 = require(\"./constants\");\nconst utils = __importStar(require(\"./utils\"));\nconst Json2Csv = function (options) {\n    const wrapDelimiterCheckRegex = new RegExp(options.delimiter.wrap, 'g'), crlfSearchRegex = /\\r?\\n|\\r/, customValueParser = options.parseValue && typeof options.parseValue === 'function' ? options.parseValue : null, expandingWithoutUnwinding = options.expandArrayObjects && !options.unwindArrays, deeksOptions = {\n        arrayIndexesAsKeys: options.arrayIndexesAsKeys,\n        expandNestedObjects: options.expandNestedObjects,\n        expandArrayObjects: expandingWithoutUnwinding,\n        ignoreEmptyArraysWhenExpanding: expandingWithoutUnwinding,\n        escapeNestedDots: true,\n    };\n    /** HEADER FIELD FUNCTIONS **/\n    /**\n     * Returns the list of data field names of all documents in the provided list\n     */\n    function getFieldNameList(data) {\n        // If keys weren't specified, then we'll use the list of keys generated by the deeks module\n        return (0, deeks_1.deepKeysFromList)(data, deeksOptions);\n    }\n    /**\n     * Processes the schemas by checking for schema differences, if so desired.\n     * If schema differences are not to be checked, then it resolves the unique\n     * list of field names.\n     */\n    function processSchemas(documentSchemas) {\n        // If there are no document schemas then there is nothing to diff and no unique fields to get\n        if (documentSchemas.length === 0) {\n            return [];\n        }\n        // If the user wants to check for the same schema (regardless of schema ordering)\n        if (options.checkSchemaDifferences) {\n            return checkSchemaDifferences(documentSchemas);\n        }\n        else {\n            // Otherwise, we do not care if the schemas are different, so we should get the unique list of keys\n            const uniqueFieldNames = utils.unique(utils.flatten(documentSchemas));\n            return uniqueFieldNames;\n        }\n    }\n    /**\n     * This function performs the schema difference check, if the user specifies that it should be checked.\n     * If there are no field names, then there are no differences.\n     * Otherwise, we get the first schema and the remaining list of schemas\n     */\n    function checkSchemaDifferences(documentSchemas) {\n        // have multiple documents - ensure only one schema (regardless of field ordering)\n        const firstDocSchema = documentSchemas[0], restOfDocumentSchemas = documentSchemas.slice(1), schemaDifferences = computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas);\n        // If there are schema inconsistencies, throw a schema not the same error\n        if (schemaDifferences) {\n            throw new Error(constants_1.errors.json2csv.notSameSchema);\n        }\n        return firstDocSchema;\n    }\n    /**\n     * Computes the number of schema differences\n     */\n    function computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas) {\n        return restOfDocumentSchemas.reduce((schemaDifferences, documentSchema) => {\n            // If there is a difference between the schemas, increment the counter of schema inconsistencies\n            const numberOfDifferences = utils.computeSchemaDifferences(firstDocSchema, documentSchema).length;\n            return numberOfDifferences > 0\n                ? schemaDifferences + 1\n                : schemaDifferences;\n        }, 0);\n    }\n    /**\n     * If so specified, this filters the detected key paths to exclude any keys that have been specified\n     */\n    function filterExcludedKeys(keyPaths) {\n        if (options.excludeKeys) {\n            return keyPaths.filter((keyPath) => {\n                for (const excludedKey of options.excludeKeys) {\n                    // Only match if the excludedKey appears at the beginning of the string so we don't accidentally match a key farther down in a key path\n                    const regex = excludedKey instanceof RegExp ? excludedKey : new RegExp(`^${excludedKey}`);\n                    if (excludedKey === keyPath || keyPath.match(regex)) {\n                        return false; // Exclude the key\n                    }\n                }\n                return true; // Otherwise, include the key\n            });\n        }\n        return keyPaths;\n    }\n    /**\n     * If so specified, this sorts the header field names alphabetically\n     */\n    function sortHeaderFields(fieldNames) {\n        if (options.sortHeader && typeof options.sortHeader === 'function') {\n            return fieldNames.sort(options.sortHeader);\n        }\n        else if (options.sortHeader) {\n            return fieldNames.sort();\n        }\n        return fieldNames;\n    }\n    /**\n     * Trims the header fields, if the user desires them to be trimmed.\n     */\n    function trimHeaderFields(params) {\n        if (options.trimHeaderFields) {\n            params.headerFields = params.headerFields.map((field) => field.split('.')\n                .map((component) => component.trim())\n                .join('.'));\n        }\n        return params;\n    }\n    /**\n     * Wrap the headings, if desired by the user.\n     */\n    function wrapHeaderFields(params) {\n        // only perform this if we are actually prepending the header\n        if (options.prependHeader) {\n            params.headerFields = params.headerFields.map(function (headingKey) {\n                return wrapFieldValueIfNecessary(headingKey);\n            });\n        }\n        return params;\n    }\n    /**\n     * Generates the CSV header string by joining the headerFields by the field delimiter\n     */\n    function generateCsvHeader(params) {\n        // #185 - generate a keys list to avoid finding native Map() methods\n        const fieldTitleMapKeys = Object.keys(options.fieldTitleMap);\n        params.header = params.headerFields\n            .map(function (field) {\n            let headerKey = field;\n            // If a custom field title was provided for this field, use that\n            if (fieldTitleMapKeys.includes(field)) {\n                headerKey = options.fieldTitleMap[field];\n            }\n            else if (!options.escapeHeaderNestedDots) {\n                // Otherwise, if the user doesn't want nested dots in keys to be escaped, then unescape them\n                headerKey = headerKey.replace(/\\\\\\./g, '.');\n            }\n            return headerKey;\n        })\n            .join(options.delimiter.field);\n        return params;\n    }\n    function convertKeysToHeaderFields() {\n        if (!options.keys)\n            return [];\n        return options.keys.map((key) => {\n            if (typeof key === 'object' && 'field' in key) {\n                options.fieldTitleMap[key.field] = key.title ?? key.field;\n                return key.field;\n            }\n            return key;\n        });\n    }\n    function extractWildcardMatchKeys() {\n        if (!options.keys)\n            return [];\n        return options.keys.flatMap(item => {\n            if (typeof item === 'string') {\n                // Exclude plain strings that were passed in options.keys\n                return [];\n            }\n            else if (item?.wildcardMatch) {\n                // Return \"field\" value for objects with wildcardMatch: true\n                return item.field;\n            }\n            // Exclude other objects\n            return [];\n        });\n    }\n    /**\n     * Retrieve the headings for all documents and return it.\n     * This checks that all documents have the same schema.\n     */\n    function retrieveHeaderFields(data) {\n        const wildcardMatchKeys = extractWildcardMatchKeys();\n        const keyStrings = convertKeysToHeaderFields();\n        const fieldNames = getFieldNameList(data);\n        const processed = processSchemas(fieldNames);\n        if (options.keys) {\n            options.keys = keyStrings;\n            const matchedKeys = keyStrings.flatMap((userProvidedKey) => {\n                // If this is not a wildcard matched key, then just return and include it in the resulting key list\n                if (!wildcardMatchKeys.includes(userProvidedKey)) {\n                    return userProvidedKey;\n                }\n                // Otherwise, identify all detected keys that match with the provided wildcard key:\n                const matches = [];\n                const regex = new RegExp(`^${userProvidedKey}`);\n                for (const detectedKey of processed) {\n                    if (userProvidedKey === detectedKey || detectedKey.match(regex)) {\n                        matches.push(detectedKey);\n                    }\n                }\n                return matches;\n            });\n            if (!options.unwindArrays) {\n                const filtered = filterExcludedKeys(matchedKeys);\n                return sortHeaderFields(filtered);\n            }\n        }\n        const filtered = filterExcludedKeys(processed);\n        return sortHeaderFields(filtered);\n    }\n    /** RECORD FIELD FUNCTIONS **/\n    function stillNeedsUnwind(params) {\n        for (const record of params.records) {\n            for (const field of params.headerFields) {\n                const value = (0, doc_path_1.evaluatePath)(record, field);\n                if (Array.isArray(value)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    /**\n     * Unwinds objects in arrays within record objects if the user specifies the\n     * expandArrayObjects option. If not specified, this passes the params\n     * argument through to the next function in the promise chain.\n     *\n     * The `finalPass` parameter is used to trigger one last pass to ensure no more\n     * arrays need to be expanded\n     */\n    function unwindRecordsIfNecessary(params, finalPass = false) {\n        if (options.unwindArrays) {\n            // Unwind each document at each header field\n            params.headerFields.forEach((headerField) => {\n                params.records = utils.unwind(params.records, headerField);\n            });\n            params.headerFields = retrieveHeaderFields(params.records);\n            // Continue unwinding if any nested arrays remain\n            if (stillNeedsUnwind(params)) {\n                return unwindRecordsIfNecessary(params, finalPass);\n            }\n            // Run a final time in case the earlier unwinding exposed additional\n            // arrays to unwind...\n            if (!finalPass) {\n                return unwindRecordsIfNecessary(params, true);\n            }\n            // If keys were provided, set the headerFields back to the provided keys after unwinding:\n            if (options.keys) {\n                const userSelectedFields = convertKeysToHeaderFields();\n                params.headerFields = filterExcludedKeys(userSelectedFields);\n            }\n            return params;\n        }\n        return params;\n    }\n    /**\n     * Main function which handles the processing of a record, or document to be converted to CSV format\n     * This function specifies and performs the necessary operations in the necessary order\n     * in order to obtain the data and convert it to CSV form while maintaining RFC 4180 compliance.\n     * * Order of operations:\n     * - Get fields from provided key list (as array of actual values)\n     * - Convert the values to csv/string representation [possible option here for custom converters?]\n     * - Trim fields\n     * - Determine if they need to be wrapped (& wrap if necessary)\n     * - Combine values for each line (by joining by field delimiter)\n     */\n    function processRecords(params) {\n        params.recordString = params.records.map((record) => {\n            // Retrieve data for each of the headerFields from this record\n            const recordFieldData = retrieveRecordFieldData(record, params.headerFields), \n            // Process the data in this record and return the\n            processedRecordData = recordFieldData.map((fieldValue) => {\n                fieldValue = trimRecordFieldValue(fieldValue);\n                fieldValue = preventCsvInjection(fieldValue);\n                let stringified = customValueParser ? customValueParser(fieldValue, recordFieldValueToString) : recordFieldValueToString(fieldValue);\n                stringified = wrapFieldValueIfNecessary(stringified);\n                return stringified;\n            });\n            // Join the record data by the field delimiter\n            return generateCsvRowFromRecord(processedRecordData);\n        }).join(options.delimiter.eol);\n        return params;\n    }\n    /**\n     * Helper function intended to process *just* array values when the expandArrayObjects setting is set to true\n     */\n    function processRecordFieldDataForExpandedArrayObject(recordFieldValue) {\n        const filteredRecordFieldValue = utils.removeEmptyFields(recordFieldValue);\n        // If we have an array and it's either empty of full of empty values, then use an empty value representation\n        if (!recordFieldValue.length || !filteredRecordFieldValue.length) {\n            return options.emptyFieldValue || '';\n        }\n        else if (filteredRecordFieldValue.length === 1) {\n            // Otherwise, we have an array of actual values...\n            // Since we are expanding array objects, we will want to key in on values of objects.\n            return filteredRecordFieldValue[0]; // Extract the single value in the array\n        }\n        return recordFieldValue;\n    }\n    /**\n     * Gets all field values from a particular record for the given list of fields\n     */\n    function retrieveRecordFieldData(record, fields) {\n        const recordValues = [];\n        fields.forEach((field) => {\n            let recordFieldValue = (0, doc_path_1.evaluatePath)(record, field);\n            if (!utils.isUndefined(options.emptyFieldValue) && utils.isEmptyField(recordFieldValue)) {\n                recordFieldValue = options.emptyFieldValue;\n            }\n            else if (options.expandArrayObjects && Array.isArray(recordFieldValue)) {\n                recordFieldValue = processRecordFieldDataForExpandedArrayObject(recordFieldValue);\n            }\n            recordValues.push(recordFieldValue);\n        });\n        return recordValues;\n    }\n    /**\n     * Converts a record field value to its string representation\n     */\n    function recordFieldValueToString(fieldValue) {\n        const isDate = fieldValue instanceof Date; // store to avoid checking twice\n        if (fieldValue === null || Array.isArray(fieldValue) || typeof fieldValue === 'object' && !isDate) {\n            return JSON.stringify(fieldValue);\n        }\n        else if (typeof fieldValue === 'undefined') {\n            return 'undefined';\n        }\n        else if (isDate && options.useDateIso8601Format) {\n            return fieldValue.toISOString();\n        }\n        else {\n            return !options.useLocaleFormat ? fieldValue.toString() : fieldValue.toLocaleString();\n        }\n    }\n    /**\n     * Trims the record field value, if specified by the user's provided options\n     */\n    function trimRecordFieldValue(fieldValue) {\n        if (options.trimFieldValues) {\n            if (Array.isArray(fieldValue)) {\n                return fieldValue.map(trimRecordFieldValue);\n            }\n            else if (typeof fieldValue === 'string') {\n                return fieldValue.trim();\n            }\n            return fieldValue;\n        }\n        return fieldValue;\n    }\n    /**\n     * Prevent CSV injection on strings if specified by the user's provided options.\n     * Mitigation will be done by ensuring that the first character doesn't being with:\n     * Equals (=), Plus (+), Minus (-), At (@), Tab (0x09), Carriage return (0x0D).\n     * More info: https://owasp.org/www-community/attacks/CSV_Injection\n     */\n    function preventCsvInjection(fieldValue) {\n        if (options.preventCsvInjection) {\n            if (Array.isArray(fieldValue)) {\n                return fieldValue.map(preventCsvInjection);\n            }\n            else if (typeof fieldValue === 'string' && !utils.isNumber(fieldValue)) {\n                return fieldValue.replace(/^[=+\\-@\\t\\r]+/g, '');\n            }\n            return fieldValue;\n        }\n        return fieldValue;\n    }\n    /**\n     * Escapes quotation marks in the field value, if necessary, and appropriately\n     * wraps the record field value if it contains a comma (field delimiter),\n     * quotation mark (wrap delimiter), or a line break (CRLF)\n     */\n    function wrapFieldValueIfNecessary(fieldValue) {\n        const wrapDelimiter = options.delimiter.wrap;\n        // eg. includes quotation marks (default delimiter)\n        if (fieldValue.includes(options.delimiter.wrap)) {\n            // add an additional quotation mark before each quotation mark appearing in the field value\n            fieldValue = fieldValue.replace(wrapDelimiterCheckRegex, wrapDelimiter + wrapDelimiter);\n        }\n        // if the field contains a comma (field delimiter), quotation mark (wrap delimiter), line break, or CRLF\n        //   then enclose it in quotation marks (wrap delimiter)\n        if (fieldValue.includes(options.delimiter.field) ||\n            fieldValue.includes(options.delimiter.wrap) ||\n            fieldValue.match(crlfSearchRegex) ||\n            options.wrapBooleans && (fieldValue === 'true' || fieldValue === 'false')) {\n            // wrap the field's value in a wrap delimiter (quotation marks by default)\n            fieldValue = wrapDelimiter + fieldValue + wrapDelimiter;\n        }\n        return fieldValue;\n    }\n    /**\n     * Generates the CSV record string by joining the field values together by the field delimiter\n     */\n    function generateCsvRowFromRecord(recordFieldValues) {\n        return recordFieldValues.join(options.delimiter.field);\n    }\n    /** CSV COMPONENT COMBINER/FINAL PROCESSOR **/\n    /**\n     * Performs the final CSV construction by combining the fields in the appropriate\n     * order depending on the provided options values and sends the generated CSV\n     * back to the user\n     */\n    function generateCsvFromComponents(params) {\n        const header = params.header, records = params.recordString, \n        // If we are prepending the header, then add an EOL, otherwise just return the records\n        csv = (options.excelBOM ? constants_1.excelBOM : '') +\n            (options.prependHeader ? header + options.delimiter.eol : '') +\n            records;\n        return csv;\n    }\n    /** MAIN CONVERTER FUNCTION **/\n    /**\n     * Internally exported json2csv function\n     */\n    function convert(data) {\n        // Single document, not an array\n        if (!Array.isArray(data)) {\n            data = [data]; // Convert to an array of the given document\n        }\n        // Retrieve the heading and then generate the CSV with the keys that are identified\n        const headerFields = {\n            headerFields: retrieveHeaderFields(data),\n            records: data,\n            header: '',\n            recordString: '',\n        };\n        const unwinded = unwindRecordsIfNecessary(headerFields);\n        const processed = processRecords(unwinded);\n        const wrapped = wrapHeaderFields(processed);\n        const trimmed = trimHeaderFields(wrapped);\n        const generated = generateCsvHeader(trimmed);\n        return generateCsvFromComponents(generated);\n    }\n    return {\n        convert,\n    };\n};\nexports.Json2Csv = Json2Csv;\n", "'use strict';\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Csv2Json = void 0;\nconst doc_path_1 = require(\"doc-path\");\nconst constants_1 = require(\"./constants\");\nconst utils = __importStar(require(\"./utils\"));\nconst Csv2Json = function (options) {\n    const escapedWrapDelimiterRegex = new RegExp(options.delimiter.wrap + options.delimiter.wrap, 'g'), excelBOMRegex = new RegExp('^' + constants_1.excelBOM), valueParserFn = options.parseValue && typeof options.parseValue === 'function' ? options.parseValue : JSON.parse;\n    /**\n     * Trims the header key, if specified by the user via the provided options\n     */\n    function processHeaderKey(headerKey) {\n        headerKey = removeWrapDelimitersFromValue(headerKey);\n        if (options.trimHeaderFields) {\n            return headerKey.split('.')\n                .map((component) => component.trim())\n                .join('.');\n        }\n        return headerKey;\n    }\n    /**\n     * Generate the JSON heading from the CSV\n     */\n    function retrieveHeading(lines) {\n        let headerFields = [];\n        if (options.headerFields) {\n            headerFields = options.headerFields.map((headerField, index) => ({\n                value: processHeaderKey(headerField),\n                index\n            }));\n        }\n        else {\n            // Generate and return the heading keys\n            const headerRow = lines[0];\n            headerFields = headerRow.map((headerKey, index) => ({\n                value: processHeaderKey(headerKey),\n                index\n            }));\n            // If the user provided keys, filter the generated keys to just the user provided keys so we also have the key index\n            if (options.keys) {\n                const keys = options.keys; // TypeScript type checking work around to get it to recognize the option is not undefined\n                headerFields = headerFields.filter((headerKey) => keys.includes(headerKey.value));\n            }\n        }\n        return {\n            lines,\n            headerFields,\n            recordLines: [],\n        };\n    }\n    /**\n     * Removes the Excel BOM value, if specified by the options object\n     */\n    function stripExcelBOM(csv) {\n        if (options.excelBOM) {\n            return csv.replace(excelBOMRegex, '');\n        }\n        return csv;\n    }\n    /**\n     * Helper function that splits a line so that we can handle wrapped fields\n     */\n    function splitLines(csv) {\n        // Parse out the line...\n        const lines = [], lastCharacterIndex = csv.length - 1, eolDelimiterLength = options.delimiter.eol.length, stateVariables = {\n            insideWrapDelimiter: false,\n            parsingValue: true,\n            justParsedDoubleQuote: false,\n            startIndex: 0\n        };\n        let splitLine = [], character, charBefore, charAfter, nextNChar, index = 0;\n        // Loop through each character in the line to identify where to split the values\n        while (index < csv.length) {\n            // Current character\n            character = csv[index];\n            // Previous character\n            charBefore = index ? csv[index - 1] : '';\n            // Next character\n            charAfter = index < lastCharacterIndex ? csv[index + 1] : '';\n            // Next n characters, including the current character, where n = length(EOL delimiter)\n            // This allows for the checking of an EOL delimiter when if it is more than a single character (eg. '\\r\\n')\n            nextNChar = utils.getNCharacters(csv, index, eolDelimiterLength);\n            if ((nextNChar === options.delimiter.eol && !stateVariables.insideWrapDelimiter ||\n                index === lastCharacterIndex) && charBefore === options.delimiter.field) {\n                // If we reached an EOL delimiter or the end of the csv and the previous character is a field delimiter...\n                // If the start index is the current index (and since the previous character is a comma),\n                //   then the value being parsed is an empty value accordingly, add an empty string\n                if (nextNChar === options.delimiter.eol && stateVariables.startIndex === index) {\n                    splitLine.push('');\n                }\n                else if (character === options.delimiter.field) {\n                    // If we reached the end of the CSV, there's no new line, and the current character is a comma\n                    // then add an empty string for the current value\n                    splitLine.push('');\n                }\n                else {\n                    // Otherwise, there's a valid value, and the start index isn't the current index, grab the whole value\n                    splitLine.push(csv.substring(stateVariables.startIndex));\n                }\n                // Since the last character is a comma, there's still an additional implied field value trailing the comma.\n                //   Since this value is empty, we push an extra empty value\n                splitLine.push('');\n                // Finally, push the split line values into the lines array and clear the split line\n                lines.push(splitLine);\n                splitLine = [];\n                stateVariables.startIndex = index + eolDelimiterLength;\n                stateVariables.parsingValue = true;\n                stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;\n            }\n            else if (index === lastCharacterIndex && character === options.delimiter.field) {\n                // If we reach the end of the CSV and the current character is a field delimiter\n                // Parse the previously seen value and add it to the line\n                const parsedValue = csv.substring(stateVariables.startIndex, index);\n                splitLine.push(parsedValue);\n                // Then add an empty string to the line since the last character being a field delimiter indicates an empty field\n                splitLine.push('');\n                lines.push(splitLine);\n            }\n            else if (index === lastCharacterIndex || nextNChar === options.delimiter.eol &&\n                // if we aren't inside wrap delimiters or if we are but the character before was a wrap delimiter and we didn't just see two\n                (!stateVariables.insideWrapDelimiter ||\n                    stateVariables.insideWrapDelimiter && charBefore === options.delimiter.wrap && !stateVariables.justParsedDoubleQuote)) {\n                // Otherwise if we reached the end of the line or csv (and current character is not a field delimiter)\n                const toIndex = index !== lastCharacterIndex || charBefore === options.delimiter.wrap ? index : undefined;\n                // Retrieve the remaining value and add it to the split line list of values\n                splitLine.push(csv.substring(stateVariables.startIndex, toIndex));\n                // Finally, push the split line values into the lines array and clear the split line\n                lines.push(splitLine);\n                splitLine = [];\n                stateVariables.startIndex = index + eolDelimiterLength;\n                stateVariables.parsingValue = true;\n                stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;\n            }\n            else if (character === options.delimiter.wrap && charBefore === options.delimiter.field &&\n                !stateVariables.insideWrapDelimiter && !stateVariables.parsingValue) {\n                // If we reached a wrap delimiter after a comma and we aren't inside a wrap delimiter\n                stateVariables.startIndex = index;\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.parsingValue = true;\n                // If the next character(s) are an EOL delimiter, then skip them so we don't parse what we've seen as another value\n                if (utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {\n                    index += options.delimiter.eol.length + 1; // Skip past EOL\n                }\n            }\n            else if (charBefore === options.delimiter.field && character === options.delimiter.wrap && charAfter === options.delimiter.eol) {\n                // We reached the start of a wrapped new field that begins with an EOL delimiter\n                // Retrieve the remaining value and add it to the split line list of values\n                splitLine.push(csv.substring(stateVariables.startIndex, index - 1));\n                stateVariables.startIndex = index;\n                stateVariables.parsingValue = true;\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.justParsedDoubleQuote = true;\n                index += 1;\n            }\n            else if ((charBefore !== options.delimiter.wrap || stateVariables.justParsedDoubleQuote && charBefore === options.delimiter.wrap) &&\n                character === options.delimiter.wrap && utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {\n                // If we reach a wrap which is not preceded by a wrap delim and the next character is an EOL delim (ie. *\"\\n)\n                stateVariables.insideWrapDelimiter = false;\n                stateVariables.parsingValue = false;\n                // Next iteration will substring, add the value to the line, and push the line onto the array of lines\n            }\n            else if (character === options.delimiter.wrap && (index === 0 || utils.getNCharacters(csv, index - eolDelimiterLength, eolDelimiterLength) === options.delimiter.eol && !stateVariables.insideWrapDelimiter)) {\n                // If the line starts with a wrap delimiter (ie. \"*)\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.parsingValue = true;\n                stateVariables.startIndex = index;\n            }\n            else if (character === options.delimiter.wrap && charAfter === options.delimiter.field && stateVariables.insideWrapDelimiter) {\n                // If we reached a wrap delimiter with a field delimiter after it (ie. *\",)\n                splitLine.push(csv.substring(stateVariables.startIndex, index + 1));\n                stateVariables.startIndex = index + 2; // next value starts after the field delimiter\n                stateVariables.insideWrapDelimiter = false;\n                stateVariables.parsingValue = false;\n            }\n            else if (character === options.delimiter.wrap && charBefore === options.delimiter.field &&\n                !stateVariables.insideWrapDelimiter && stateVariables.parsingValue) {\n                // If we reached a wrap delimiter with a field delimiter after it (ie. ,\"*)\n                splitLine.push(csv.substring(stateVariables.startIndex, index - 1));\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.parsingValue = true;\n                stateVariables.startIndex = index;\n            }\n            else if (character === options.delimiter.wrap && charAfter === options.delimiter.wrap && index !== stateVariables.startIndex) {\n                // If we run into an escaped quote (ie. \"\") skip past the second quote\n                index += 2;\n                stateVariables.justParsedDoubleQuote = true;\n                continue;\n            }\n            else if (character === options.delimiter.field && charBefore !== options.delimiter.wrap &&\n                charAfter !== options.delimiter.wrap && !stateVariables.insideWrapDelimiter &&\n                stateVariables.parsingValue) {\n                // If we reached a field delimiter and are not inside the wrap delimiters (ie. *,*)\n                splitLine.push(csv.substring(stateVariables.startIndex, index));\n                stateVariables.startIndex = index + 1;\n            }\n            else if (character === options.delimiter.field && charBefore === options.delimiter.wrap &&\n                charAfter !== options.delimiter.wrap && !stateVariables.parsingValue) {\n                // If we reached a field delimiter, the previous character was a wrap delimiter, and the\n                //   next character is not a wrap delimiter (ie. \",*)\n                stateVariables.insideWrapDelimiter = false;\n                stateVariables.parsingValue = true;\n                stateVariables.startIndex = index + 1;\n            }\n            // Otherwise increment to the next character\n            index++;\n            // Reset the double quote state variable\n            stateVariables.justParsedDoubleQuote = false;\n        }\n        return lines;\n    }\n    /**\n     * Retrieves the record lines from the split CSV lines and sets it on the params object\n     */\n    function retrieveRecordLines(params) {\n        if (options.headerFields) { // This option is passed for instances where the CSV has no header line\n            params.recordLines = params.lines;\n        }\n        else { // All lines except for the header line\n            params.recordLines = params.lines.splice(1);\n        }\n        return params;\n    }\n    /**\n     * Retrieves the value for the record from the line at the provided key.\n     */\n    function retrieveRecordValueFromLine(headerField, line) {\n        // If there is a value at the key's index, use it; otherwise, null\n        const value = line[headerField.index];\n        // Perform any necessary value conversions on the record value\n        return processRecordValue(value);\n    }\n    /**\n     * Processes the record's value by parsing the data to ensure the CSV is\n     * converted to the JSON that created it.\n     */\n    function processRecordValue(fieldValue) {\n        // If the value is an array representation, convert it\n        const parsedJson = parseValue(fieldValue);\n        // If parsedJson is anything aside from an error, then we want to use the parsed value\n        // This allows us to interpret values like 'null' --> null, 'false' --> false\n        if (!utils.isError(parsedJson) && !utils.isInvalid(parsedJson)) {\n            return parsedJson;\n        }\n        else if (fieldValue === 'undefined') {\n            return undefined;\n        }\n        return fieldValue;\n    }\n    /**\n     * Trims the record value, if specified by the user via the options object\n     */\n    function trimRecordValue(fieldValue) {\n        if (options.trimFieldValues && fieldValue !== null) {\n            return fieldValue.trim();\n        }\n        return fieldValue;\n    }\n    /**\n     * Create a JSON document with the given keys (designated by the CSV header)\n     *   and the values (from the given line)\n     * @returns {Object} created json document\n     */\n    function createDocument(headerFields, line) {\n        // Reduce the keys into a JSON document representing the given line\n        return headerFields.reduce((document, headerField) => {\n            // If there is a value at the key's index in the line, set the value; otherwise null\n            const value = retrieveRecordValueFromLine(headerField, line);\n            try {\n                // Otherwise add the key and value to the document\n                return (0, doc_path_1.setPath)(document, headerField.value, value);\n            }\n            catch (error) {\n                // Catch any errors where key paths are null or '' and continue\n                return document;\n            }\n        }, {});\n    }\n    /**\n     * Removes the outermost wrap delimiters from a value, if they are present\n     * Otherwise, the non-wrapped value is returned as is\n     */\n    function removeWrapDelimitersFromValue(fieldValue) {\n        const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];\n        // If the field starts and ends with a wrap delimiter\n        if (firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap) {\n            // Handle the case where the field is just a pair of wrap delimiters \n            return fieldValue.length <= 2 ? '' : fieldValue.substring(1, lastIndex);\n        }\n        return fieldValue;\n    }\n    /**\n     * Unescapes wrap delimiters by replacing duplicates with a single (eg. \"\" -> \")\n     * This is done in order to parse RFC 4180 compliant CSV back to JSON\n     */\n    function unescapeWrapDelimiterInField(fieldValue) {\n        return fieldValue.replace(escapedWrapDelimiterRegex, options.delimiter.wrap);\n    }\n    /**\n     * Main helper function to convert the CSV to the JSON document array\n     */\n    function transformRecordLines(params) {\n        // For each line, create the document and add it to the array of documents\n        return params.recordLines.reduce((generatedJsonObjects, line) => {\n            line = line.map((fieldValue) => {\n                // Perform the necessary operations on each line\n                fieldValue = removeWrapDelimitersFromValue(fieldValue);\n                fieldValue = unescapeWrapDelimiterInField(fieldValue);\n                fieldValue = trimRecordValue(fieldValue);\n                return fieldValue;\n            });\n            const generatedDocument = createDocument(params.headerFields, line);\n            return generatedJsonObjects.concat(generatedDocument);\n        }, []);\n    }\n    /**\n     * Attempts to parse the provided value. If it is not parsable, then an error is returned\n     */\n    function parseValue(value) {\n        try {\n            if (utils.isStringRepresentation(value, options) && !utils.isDateRepresentation(value)) {\n                return value;\n            }\n            const parsedJson = valueParserFn(value);\n            // If the parsed value is an array, then we also need to trim record values, if specified\n            if (Array.isArray(parsedJson)) {\n                return parsedJson.map(trimRecordValue);\n            }\n            return parsedJson;\n        }\n        catch (err) {\n            return err;\n        }\n    }\n    /**\n     * Internally exported csv2json function\n     */\n    function convert(data) {\n        // Split the CSV into lines using the specified EOL option\n        const stripped = stripExcelBOM(data);\n        const split = splitLines(stripped);\n        const heading = retrieveHeading(split); // Retrieve the headings from the CSV, unless the user specified the keys\n        const lines = retrieveRecordLines(heading); // Retrieve the record lines from the CSV\n        return transformRecordLines(lines); // Retrieve the JSON document array\n    }\n    return {\n        convert,\n    };\n};\nexports.Csv2Json = Csv2Json;\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.csv2json = exports.json2csv = void 0;\nconst constants_1 = require(\"./constants\");\nconst json2csv_1 = require(\"./json2csv\");\nconst csv2json_1 = require(\"./csv2json\");\nconst utils_1 = require(\"./utils\");\nfunction json2csv(data, options) {\n    const builtOptions = (0, utils_1.buildJ2COptions)(options ?? {});\n    // Validate the parameters before calling the converter's convert function\n    (0, utils_1.validate)(data, utils_1.isObject, constants_1.errors.json2csv);\n    return (0, json2csv_1.Json2Csv)(builtOptions).convert(data);\n}\nexports.json2csv = json2csv;\nfunction csv2json(data, options) {\n    const builtOptions = (0, utils_1.buildC2JOptions)(options ?? {});\n    // Validate the parameters before calling the converter's convert function\n    (0, utils_1.validate)(data, utils_1.isString, constants_1.errors.csv2json);\n    return (0, csv2json_1.Csv2Json)(builtOptions).convert(data);\n}\nexports.csv2json = csv2json;\n", "import type {\n  CSSProperties,\n  FunctionComponent,\n  PropsWithChildren,\n} from 'react';\n\n/**\n * @public\n */\nexport type PaneProps = PropsWithChildren<{\n  className?: string;\n  size?: number;\n  split?: 'vertical' | 'horizontal';\n  style?: CSSProperties;\n  eleRef?: (el: HTMLDivElement) => void;\n}>;\n\n/**\n * @public\n */\nexport const Pane: FunctionComponent<PaneProps> = function Pane(props) {\n  const { children, className, split, style: styleProps, size, eleRef } = props;\n\n  let style: CSSProperties = {\n    flex: 1,\n    position: 'relative',\n    outline: 'none',\n  };\n\n  if (size !== undefined) {\n    if (split === 'vertical') {\n      style.width = size;\n    } else {\n      style.height = size;\n      style.display = 'flex';\n    }\n    style.flex = 'none';\n  }\n\n  style = { ...style, ...styleProps };\n\n  const classes = ['Pane', split, className].filter(Boolean).join(' ');\n  return (\n    <div role=\"region\" ref={eleRef} className={classes} style={style}>\n      {children}\n    </div>\n  );\n};\n", "import type { CSSProperties, FunctionComponent } from 'react';\n\nexport const RESIZER_DEFAULT_CLASSNAME = 'Resizer';\n\ninterface ResizerProps {\n  className?: string;\n  onClick?: (event: MouseEvent) => void;\n  onDoubleClick?: (event: MouseEvent) => void;\n  onMouseDown: (event: MouseEvent) => void;\n  onTouchEnd: (event: TouchEvent) => void;\n  onTouchStart: (event: TouchEvent) => void;\n  resizerClassName?: string;\n  split: 'vertical' | 'horizontal';\n  style: CSSProperties;\n}\n\nexport const Resizer: FunctionComponent<ResizerProps> = function Resizer(\n  props\n) {\n  const {\n    className = RESIZER_DEFAULT_CLASSNAME,\n    onClick,\n    onDoubleClick,\n    onMouseDown,\n    onTouchEnd,\n    onTouchStart,\n    resizerClassName,\n    split,\n    style,\n  } = props;\n\n  const classes = [resizerClassName, split, className]\n    .filter(Boolean)\n    .join(' ');\n\n  return (\n    <span\n      role=\"separator\"\n      className={classes}\n      style={style}\n      onMouseDown={(event) => onMouseDown(event.nativeEvent)}\n      onTouchStart={(event) => {\n        event.preventDefault();\n        onTouchStart(event.nativeEvent);\n      }}\n      onTouchEnd={(event) => {\n        event.preventDefault();\n        onTouchEnd(event.nativeEvent);\n      }}\n      onClick={(event) => {\n        if (onClick) {\n          event.preventDefault();\n          onClick(event.nativeEvent);\n        }\n      }}\n      onDoubleClick={(event) => {\n        if (onDoubleClick) {\n          event.preventDefault();\n          onDoubleClick(event.nativeEvent);\n        }\n      }}\n    />\n  );\n};\n", "import { Children, Component, CSSProperties, ReactNode } from 'react';\nimport { Pane } from './Pane.js';\nimport { Resizer, RESIZER_DEFAULT_CLASSNAME } from './Resizer.js';\n\nconst BASE_STYLES: CSSProperties = {\n  display: 'flex',\n  flex: 1,\n  height: '100%',\n  position: 'absolute',\n  outline: 'none',\n  overflow: 'hidden',\n  MozUserSelect: 'text',\n  WebkitUserSelect: 'text',\n  msUserSelect: 'text',\n  userSelect: 'text',\n};\n\nconst VERTICAL_STYLES: CSSProperties = {\n  ...BASE_STYLES,\n  flexDirection: 'row',\n  left: 0,\n  right: 0,\n};\n\nconst HORIZONTAL_STYLES: CSSProperties = {\n  ...BASE_STYLES,\n  bottom: 0,\n  flexDirection: 'column',\n  minHeight: '100%',\n  top: 0,\n  width: '100%',\n};\n\nconst EMPTY_STYLES: CSSProperties = {};\n\n/**\n * @public\n */\nexport interface SplitPaneProps {\n  allowResize?: boolean;\n  className?: string;\n  primary?: 'first' | 'second';\n  minSize?: number;\n  maxSize?: number;\n  defaultSize?: number;\n  size?: number;\n  split?: 'vertical' | 'horizontal';\n  onDragStarted?: () => void;\n  onDragFinished?: (newSize: number) => void;\n  onChange?: (newSize: number) => void;\n  onResizerClick?: (event: MouseEvent) => void;\n  onResizerDoubleClick?: (event: MouseEvent) => void;\n  style?: CSSProperties;\n  resizerStyle?: CSSProperties;\n  paneStyle?: CSSProperties;\n  pane1Style?: CSSProperties;\n  pane2Style?: CSSProperties;\n  paneClassName?: string;\n  pane1ClassName?: string;\n  pane2ClassName?: string;\n  resizerClassName?: string;\n  step?: number;\n  children?: ReactNode;\n}\n\n/**\n * @public\n */\nexport interface SplitPaneState {\n  active: boolean;\n  resized: boolean;\n\n  pane1Size?: number;\n  pane2Size?: number;\n\n  position?: number;\n  draggedSize?: number;\n\n  instanceProps: { size?: number };\n}\n\n/**\n * @public\n */\nexport type MinimalTouchEvent = MouseEvent & {\n  touches: Array<{ clientX: number; clientY: number }>;\n};\n\n/**\n * @public\n */\nexport class SplitPane extends Component<SplitPaneProps, SplitPaneState> {\n  static defaultProps = {\n    allowResize: true,\n    minSize: 50,\n    primary: 'first',\n    split: 'vertical',\n    paneClassName: '',\n    pane1ClassName: '',\n    pane2ClassName: '',\n  };\n\n  pane1: HTMLDivElement | null = null;\n  pane2: HTMLDivElement | null = null;\n  splitPane: HTMLDivElement | null = null;\n\n  constructor(props: SplitPaneProps) {\n    super(props);\n\n    this.onMouseDown = this.onMouseDown.bind(this);\n    this.onTouchStart = this.onTouchStart.bind(this);\n    this.onMouseMove = this.onMouseMove.bind(this);\n    this.onTouchMove = this.onTouchMove.bind(this);\n    this.onMouseUp = this.onMouseUp.bind(this);\n\n    // order of setting panel sizes.\n    // 1. size\n    // 2. getDefaultSize(defaultSize, minsize, maxSize)\n\n    const { size, defaultSize, minSize, maxSize, primary } = props;\n\n    const initialSize =\n      size !== undefined\n        ? size\n        : getDefaultSize(defaultSize, minSize, maxSize, undefined);\n\n    this.state = {\n      active: false,\n      resized: false,\n      pane1Size: primary === 'first' ? initialSize : undefined,\n      pane2Size: primary === 'second' ? initialSize : undefined,\n\n      // these are props that are needed in static functions. ie: gDSFP\n      instanceProps: {\n        size,\n      },\n    };\n  }\n\n  componentDidMount() {\n    document.addEventListener('mouseup', this.onMouseUp);\n    document.addEventListener('mousemove', this.onMouseMove);\n    document.addEventListener('touchmove', this.onTouchMove);\n    this.setState(SplitPane.getSizeUpdate(this.props, this.state));\n  }\n\n  static getDerivedStateFromProps(\n    nextProps: SplitPaneProps,\n    prevState: SplitPaneState\n  ) {\n    return SplitPane.getSizeUpdate(nextProps, prevState);\n  }\n\n  componentWillUnmount() {\n    document.removeEventListener('mouseup', this.onMouseUp);\n    document.removeEventListener('mousemove', this.onMouseMove);\n    document.removeEventListener('touchmove', this.onTouchMove);\n  }\n\n  onMouseDown(event: MouseEvent) {\n    this.onTouchStart({\n      ...event,\n      touches: [{ clientX: event.clientX, clientY: event.clientY }],\n    });\n  }\n\n  onTouchStart(event: MinimalTouchEvent | TouchEvent) {\n    const { allowResize, onDragStarted, split } = this.props;\n    if (allowResize) {\n      unFocus(document, window);\n      const position =\n        split === 'vertical'\n          ? event.touches[0].clientX\n          : event.touches[0].clientY;\n\n      if (typeof onDragStarted === 'function') {\n        onDragStarted();\n      }\n      this.setState({\n        active: true,\n        position,\n      });\n    }\n  }\n\n  onMouseMove(event: MouseEvent) {\n    const eventWithTouches = Object.assign({}, event, {\n      touches: [{ clientX: event.clientX, clientY: event.clientY }],\n    });\n    this.onTouchMove(eventWithTouches);\n  }\n\n  onTouchMove(event: MinimalTouchEvent | TouchEvent) {\n    if (!this.state.active || !this.props.allowResize) {\n      return;\n    }\n\n    const { position = 0 } = this.state;\n    const {\n      maxSize,\n      minSize = SplitPane.defaultProps.minSize,\n      onChange,\n      split = SplitPane.defaultProps.split,\n      step,\n    } = this.props;\n\n    unFocus(document, window);\n    const isPrimaryFirst = this.props.primary === 'first';\n    const ref = isPrimaryFirst ? this.pane1 : this.pane2;\n    const ref2 = isPrimaryFirst ? this.pane2 : this.pane1;\n\n    if (!ref || !ref2 || !ref.getBoundingClientRect) {\n      return;\n    }\n\n    const node = ref;\n    const node2 = ref2;\n\n    const width = node.getBoundingClientRect().width;\n    const height = node.getBoundingClientRect().height;\n    const current =\n      split === 'vertical'\n        ? event.touches[0].clientX\n        : event.touches[0].clientY;\n    const size = split === 'vertical' ? width : height;\n\n    let positionDelta = position - current;\n    if (step) {\n      if (Math.abs(positionDelta) < step) {\n        return;\n      }\n      // Integer division\n      positionDelta = ~~(positionDelta / step) * step;\n    }\n    let sizeDelta = isPrimaryFirst ? positionDelta : -positionDelta;\n\n    const pane1Order = parseInt(window.getComputedStyle(node).order);\n    const pane2Order = parseInt(window.getComputedStyle(node2).order);\n    if (pane1Order > pane2Order) {\n      sizeDelta = -sizeDelta;\n    }\n\n    let newMaxSize = maxSize;\n    if (this.splitPane && maxSize !== undefined && maxSize <= 0) {\n      if (split === 'vertical') {\n        newMaxSize = this.splitPane.getBoundingClientRect().width + maxSize;\n      } else {\n        newMaxSize = this.splitPane.getBoundingClientRect().height + maxSize;\n      }\n    }\n\n    let newSize = size - sizeDelta;\n    const newPosition = position - positionDelta;\n\n    if (minSize && newSize < minSize) {\n      newSize = minSize;\n    } else if (newMaxSize !== undefined && newSize > newMaxSize) {\n      newSize = newMaxSize;\n    } else {\n      this.setState({\n        position: newPosition,\n        resized: true,\n      });\n    }\n\n    if (onChange) onChange(newSize);\n\n    const sizeState = isPrimaryFirst\n      ? { pane1Size: newSize, pane2Size: undefined }\n      : { pane2Size: newSize, pane1Size: undefined };\n\n    this.setState({ draggedSize: newSize, ...sizeState });\n  }\n\n  onMouseUp() {\n    if (!this.state.active || !this.props.allowResize) {\n      return;\n    }\n\n    const { onDragFinished } = this.props;\n    const { draggedSize } = this.state;\n\n    if (\n      typeof draggedSize !== 'undefined' &&\n      typeof onDragFinished === 'function'\n    ) {\n      onDragFinished(draggedSize);\n    }\n\n    this.setState({ active: false });\n  }\n\n  // we have to check values since gDSFP is called on every render and more in StrictMode\n  static getSizeUpdate(props: SplitPaneProps, state: SplitPaneState) {\n    const { instanceProps } = state;\n\n    if (instanceProps.size === props.size && props.size !== undefined) {\n      return {};\n    }\n\n    const newSize =\n      props.size !== undefined\n        ? props.size\n        : getDefaultSize(\n            props.defaultSize,\n            props.minSize,\n            props.maxSize,\n            state.draggedSize\n          );\n\n    const isPrimaryFirst = props.primary === 'first';\n    const sizeState = isPrimaryFirst\n      ? { pane1Size: newSize, pane2Size: undefined }\n      : { pane2Size: newSize, pane1Size: undefined };\n\n    return {\n      ...sizeState,\n      ...(typeof props.size === 'undefined' ? {} : { draggedSize: newSize }),\n      instanceProps: { size: props.size },\n    };\n  }\n\n  render() {\n    const {\n      allowResize,\n      children,\n      className,\n      onResizerClick,\n      onResizerDoubleClick,\n      paneClassName,\n      pane1ClassName,\n      pane2ClassName,\n      paneStyle,\n      pane1Style: pane1StyleProps,\n      pane2Style: pane2StyleProps,\n      resizerClassName = RESIZER_DEFAULT_CLASSNAME,\n      resizerStyle,\n      split,\n      style: styleProps,\n    } = this.props;\n\n    const { pane1Size, pane2Size } = this.state;\n\n    const disabledClass = allowResize ? '' : 'disabled';\n    const resizerClassNamesIncludingDefault = resizerClassName\n      ? `${resizerClassName} ${RESIZER_DEFAULT_CLASSNAME}`\n      : resizerClassName;\n\n    const notNullChildren = removeNullChildren(children);\n\n    const baseStyles =\n      split === 'vertical' ? VERTICAL_STYLES : HORIZONTAL_STYLES;\n\n    const style: CSSProperties = styleProps\n      ? { ...baseStyles, ...styleProps }\n      : baseStyles;\n\n    const classes = ['SplitPane', className, split, disabledClass]\n      .filter(Boolean)\n      .join(' ');\n\n    const pane1Style = coalesceOnEmpty(\n      { ...paneStyle, ...pane1StyleProps },\n      EMPTY_STYLES\n    );\n    const pane2Style = coalesceOnEmpty(\n      { ...paneStyle, ...pane2StyleProps },\n      EMPTY_STYLES\n    );\n\n    const pane1Classes = ['Pane1', paneClassName, pane1ClassName].join(' ');\n    const pane2Classes = ['Pane2', paneClassName, pane2ClassName].join(' ');\n\n    return (\n      <div\n        data-testid=\"split-pane\"\n        className={classes}\n        style={style}\n        ref={(node) => {\n          this.splitPane = node;\n        }}\n      >\n        <Pane\n          className={pane1Classes}\n          key=\"pane1\"\n          eleRef={(node) => {\n            this.pane1 = node;\n          }}\n          size={pane1Size}\n          split={split}\n          style={pane1Style}\n        >\n          {notNullChildren[0]}\n        </Pane>\n        <Resizer\n          className={disabledClass}\n          onClick={onResizerClick}\n          onDoubleClick={onResizerDoubleClick}\n          onMouseDown={this.onMouseDown}\n          onTouchStart={this.onTouchStart}\n          onTouchEnd={this.onMouseUp}\n          key=\"resizer\"\n          resizerClassName={resizerClassNamesIncludingDefault}\n          split={split || 'vertical'}\n          style={resizerStyle || EMPTY_STYLES}\n        />\n        <Pane\n          className={pane2Classes}\n          key=\"pane2\"\n          eleRef={(node) => {\n            this.pane2 = node;\n          }}\n          size={pane2Size}\n          split={split}\n          style={pane2Style}\n        >\n          {notNullChildren[1]}\n        </Pane>\n      </div>\n    );\n  }\n}\n\nfunction unFocus(\n  document: (typeof globalThis)['document'],\n  window: (typeof globalThis)['window']\n) {\n  if (\n    'selection' in document &&\n    typeof document.selection === 'object' &&\n    document.selection &&\n    'empty' in document.selection &&\n    typeof document.selection.empty === 'function'\n  ) {\n    try {\n      document.selection.empty();\n    } catch (e) {}\n  } else if (\n    typeof window !== 'undefined' &&\n    typeof window.getSelection === 'function'\n  ) {\n    try {\n      window.getSelection()?.removeAllRanges();\n    } catch (e) {}\n  }\n}\n\nfunction getDefaultSize(\n  defaultSize: number | undefined,\n  minSize: number | undefined,\n  maxSize: number | undefined,\n  draggedSize: number | undefined\n) {\n  if (typeof draggedSize === 'number') {\n    const min = typeof minSize === 'number' ? minSize : 0;\n    const max =\n      typeof maxSize === 'number' && maxSize >= 0 ? maxSize : Infinity;\n    return Math.max(min, Math.min(max, draggedSize));\n  }\n  if (defaultSize !== undefined) {\n    return defaultSize;\n  }\n  return minSize;\n}\n\nfunction removeNullChildren(children: ReactNode): ReactNode[] {\n  return Children.toArray(children).filter((c) => c);\n}\n\nfunction isEmptyish(obj: Record<string, unknown> | null | undefined): boolean {\n  return (\n    obj === null || typeof obj === 'undefined' || Object.keys(obj).length === 0\n  );\n}\n\nfunction coalesceOnEmpty<T>(\n  obj: Record<string, unknown> | null | undefined,\n  useOnEmpty: T\n): T {\n  return isEmptyish(obj) ? useOnEmpty : (obj as T);\n}\n", "// This is a generated file. Do not edit.\nvar Space_Separator = /[\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar ID_Start = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF19]|\\uD806[\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]/;\nvar ID_Continue = /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D00-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF9\\u1D00-\\u1DF9\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312E\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEA\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC6F\\uDC7F-\\uDCBA\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDCA-\\uDDCC\\uDDD0-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3C-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB7\\uDEC0-\\uDEC9\\uDF00-\\uDF19\\uDF1D-\\uDF2B\\uDF30-\\uDF39]|\\uD806[\\uDCA0-\\uDCE9\\uDCFF\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE83\\uDE86-\\uDE99\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50-\\uDF7E\\uDF8F-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4A\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uDB40[\\uDD00-\\uDDEF]/;\n\nvar unicode = {\n\tSpace_Separator: Space_Separator,\n\tID_Start: ID_Start,\n\tID_Continue: ID_Continue\n};\n\nvar util = {\n    isSpaceSeparator (c) {\n        return typeof c === 'string' && unicode.Space_Separator.test(c)\n    },\n\n    isIdStartChar (c) {\n        return typeof c === 'string' && (\n            (c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c === '$') || (c === '_') ||\n        unicode.ID_Start.test(c)\n        )\n    },\n\n    isIdContinueChar (c) {\n        return typeof c === 'string' && (\n            (c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c >= '0' && c <= '9') ||\n        (c === '$') || (c === '_') ||\n        (c === '\\u200C') || (c === '\\u200D') ||\n        unicode.ID_Continue.test(c)\n        )\n    },\n\n    isDigit (c) {\n        return typeof c === 'string' && /[0-9]/.test(c)\n    },\n\n    isHexDigit (c) {\n        return typeof c === 'string' && /[0-9A-Fa-f]/.test(c)\n    },\n};\n\nlet source;\nlet parseState;\nlet stack;\nlet pos;\nlet line;\nlet column;\nlet token;\nlet key;\nlet root;\n\nvar parse = function parse (text, reviver) {\n    source = String(text);\n    parseState = 'start';\n    stack = [];\n    pos = 0;\n    line = 1;\n    column = 0;\n    token = undefined;\n    key = undefined;\n    root = undefined;\n\n    do {\n        token = lex();\n\n        // This code is unreachable.\n        // if (!parseStates[parseState]) {\n        //     throw invalidParseState()\n        // }\n\n        parseStates[parseState]();\n    } while (token.type !== 'eof')\n\n    if (typeof reviver === 'function') {\n        return internalize({'': root}, '', reviver)\n    }\n\n    return root\n};\n\nfunction internalize (holder, name, reviver) {\n    const value = holder[name];\n    if (value != null && typeof value === 'object') {\n        if (Array.isArray(value)) {\n            for (let i = 0; i < value.length; i++) {\n                const key = String(i);\n                const replacement = internalize(value, key, reviver);\n                if (replacement === undefined) {\n                    delete value[key];\n                } else {\n                    Object.defineProperty(value, key, {\n                        value: replacement,\n                        writable: true,\n                        enumerable: true,\n                        configurable: true,\n                    });\n                }\n            }\n        } else {\n            for (const key in value) {\n                const replacement = internalize(value, key, reviver);\n                if (replacement === undefined) {\n                    delete value[key];\n                } else {\n                    Object.defineProperty(value, key, {\n                        value: replacement,\n                        writable: true,\n                        enumerable: true,\n                        configurable: true,\n                    });\n                }\n            }\n        }\n    }\n\n    return reviver.call(holder, name, value)\n}\n\nlet lexState;\nlet buffer;\nlet doubleQuote;\nlet sign;\nlet c;\n\nfunction lex () {\n    lexState = 'default';\n    buffer = '';\n    doubleQuote = false;\n    sign = 1;\n\n    for (;;) {\n        c = peek();\n\n        // This code is unreachable.\n        // if (!lexStates[lexState]) {\n        //     throw invalidLexState(lexState)\n        // }\n\n        const token = lexStates[lexState]();\n        if (token) {\n            return token\n        }\n    }\n}\n\nfunction peek () {\n    if (source[pos]) {\n        return String.fromCodePoint(source.codePointAt(pos))\n    }\n}\n\nfunction read () {\n    const c = peek();\n\n    if (c === '\\n') {\n        line++;\n        column = 0;\n    } else if (c) {\n        column += c.length;\n    } else {\n        column++;\n    }\n\n    if (c) {\n        pos += c.length;\n    }\n\n    return c\n}\n\nconst lexStates = {\n    default () {\n        switch (c) {\n        case '\\t':\n        case '\\v':\n        case '\\f':\n        case ' ':\n        case '\\u00A0':\n        case '\\uFEFF':\n        case '\\n':\n        case '\\r':\n        case '\\u2028':\n        case '\\u2029':\n            read();\n            return\n\n        case '/':\n            read();\n            lexState = 'comment';\n            return\n\n        case undefined:\n            read();\n            return newToken('eof')\n        }\n\n        if (util.isSpaceSeparator(c)) {\n            read();\n            return\n        }\n\n        // This code is unreachable.\n        // if (!lexStates[parseState]) {\n        //     throw invalidLexState(parseState)\n        // }\n\n        return lexStates[parseState]()\n    },\n\n    comment () {\n        switch (c) {\n        case '*':\n            read();\n            lexState = 'multiLineComment';\n            return\n\n        case '/':\n            read();\n            lexState = 'singleLineComment';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    multiLineComment () {\n        switch (c) {\n        case '*':\n            read();\n            lexState = 'multiLineCommentAsterisk';\n            return\n\n        case undefined:\n            throw invalidChar(read())\n        }\n\n        read();\n    },\n\n    multiLineCommentAsterisk () {\n        switch (c) {\n        case '*':\n            read();\n            return\n\n        case '/':\n            read();\n            lexState = 'default';\n            return\n\n        case undefined:\n            throw invalidChar(read())\n        }\n\n        read();\n        lexState = 'multiLineComment';\n    },\n\n    singleLineComment () {\n        switch (c) {\n        case '\\n':\n        case '\\r':\n        case '\\u2028':\n        case '\\u2029':\n            read();\n            lexState = 'default';\n            return\n\n        case undefined:\n            read();\n            return newToken('eof')\n        }\n\n        read();\n    },\n\n    value () {\n        switch (c) {\n        case '{':\n        case '[':\n            return newToken('punctuator', read())\n\n        case 'n':\n            read();\n            literal('ull');\n            return newToken('null', null)\n\n        case 't':\n            read();\n            literal('rue');\n            return newToken('boolean', true)\n\n        case 'f':\n            read();\n            literal('alse');\n            return newToken('boolean', false)\n\n        case '-':\n        case '+':\n            if (read() === '-') {\n                sign = -1;\n            }\n\n            lexState = 'sign';\n            return\n\n        case '.':\n            buffer = read();\n            lexState = 'decimalPointLeading';\n            return\n\n        case '0':\n            buffer = read();\n            lexState = 'zero';\n            return\n\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9':\n            buffer = read();\n            lexState = 'decimalInteger';\n            return\n\n        case 'I':\n            read();\n            literal('nfinity');\n            return newToken('numeric', Infinity)\n\n        case 'N':\n            read();\n            literal('aN');\n            return newToken('numeric', NaN)\n\n        case '\"':\n        case \"'\":\n            doubleQuote = (read() === '\"');\n            buffer = '';\n            lexState = 'string';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    identifierNameStartEscape () {\n        if (c !== 'u') {\n            throw invalidChar(read())\n        }\n\n        read();\n        const u = unicodeEscape();\n        switch (u) {\n        case '$':\n        case '_':\n            break\n\n        default:\n            if (!util.isIdStartChar(u)) {\n                throw invalidIdentifier()\n            }\n\n            break\n        }\n\n        buffer += u;\n        lexState = 'identifierName';\n    },\n\n    identifierName () {\n        switch (c) {\n        case '$':\n        case '_':\n        case '\\u200C':\n        case '\\u200D':\n            buffer += read();\n            return\n\n        case '\\\\':\n            read();\n            lexState = 'identifierNameEscape';\n            return\n        }\n\n        if (util.isIdContinueChar(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('identifier', buffer)\n    },\n\n    identifierNameEscape () {\n        if (c !== 'u') {\n            throw invalidChar(read())\n        }\n\n        read();\n        const u = unicodeEscape();\n        switch (u) {\n        case '$':\n        case '_':\n        case '\\u200C':\n        case '\\u200D':\n            break\n\n        default:\n            if (!util.isIdContinueChar(u)) {\n                throw invalidIdentifier()\n            }\n\n            break\n        }\n\n        buffer += u;\n        lexState = 'identifierName';\n    },\n\n    sign () {\n        switch (c) {\n        case '.':\n            buffer = read();\n            lexState = 'decimalPointLeading';\n            return\n\n        case '0':\n            buffer = read();\n            lexState = 'zero';\n            return\n\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9':\n            buffer = read();\n            lexState = 'decimalInteger';\n            return\n\n        case 'I':\n            read();\n            literal('nfinity');\n            return newToken('numeric', sign * Infinity)\n\n        case 'N':\n            read();\n            literal('aN');\n            return newToken('numeric', NaN)\n        }\n\n        throw invalidChar(read())\n    },\n\n    zero () {\n        switch (c) {\n        case '.':\n            buffer += read();\n            lexState = 'decimalPoint';\n            return\n\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n\n        case 'x':\n        case 'X':\n            buffer += read();\n            lexState = 'hexadecimal';\n            return\n        }\n\n        return newToken('numeric', sign * 0)\n    },\n\n    decimalInteger () {\n        switch (c) {\n        case '.':\n            buffer += read();\n            lexState = 'decimalPoint';\n            return\n\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    decimalPointLeading () {\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalFraction';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    decimalPoint () {\n        switch (c) {\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalFraction';\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    decimalFraction () {\n        switch (c) {\n        case 'e':\n        case 'E':\n            buffer += read();\n            lexState = 'decimalExponent';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    decimalExponent () {\n        switch (c) {\n        case '+':\n        case '-':\n            buffer += read();\n            lexState = 'decimalExponentSign';\n            return\n        }\n\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalExponentInteger';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    decimalExponentSign () {\n        if (util.isDigit(c)) {\n            buffer += read();\n            lexState = 'decimalExponentInteger';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    decimalExponentInteger () {\n        if (util.isDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    hexadecimal () {\n        if (util.isHexDigit(c)) {\n            buffer += read();\n            lexState = 'hexadecimalInteger';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    hexadecimalInteger () {\n        if (util.isHexDigit(c)) {\n            buffer += read();\n            return\n        }\n\n        return newToken('numeric', sign * Number(buffer))\n    },\n\n    string () {\n        switch (c) {\n        case '\\\\':\n            read();\n            buffer += escape();\n            return\n\n        case '\"':\n            if (doubleQuote) {\n                read();\n                return newToken('string', buffer)\n            }\n\n            buffer += read();\n            return\n\n        case \"'\":\n            if (!doubleQuote) {\n                read();\n                return newToken('string', buffer)\n            }\n\n            buffer += read();\n            return\n\n        case '\\n':\n        case '\\r':\n            throw invalidChar(read())\n\n        case '\\u2028':\n        case '\\u2029':\n            separatorChar(c);\n            break\n\n        case undefined:\n            throw invalidChar(read())\n        }\n\n        buffer += read();\n    },\n\n    start () {\n        switch (c) {\n        case '{':\n        case '[':\n            return newToken('punctuator', read())\n\n        // This code is unreachable since the default lexState handles eof.\n        // case undefined:\n        //     return newToken('eof')\n        }\n\n        lexState = 'value';\n    },\n\n    beforePropertyName () {\n        switch (c) {\n        case '$':\n        case '_':\n            buffer = read();\n            lexState = 'identifierName';\n            return\n\n        case '\\\\':\n            read();\n            lexState = 'identifierNameStartEscape';\n            return\n\n        case '}':\n            return newToken('punctuator', read())\n\n        case '\"':\n        case \"'\":\n            doubleQuote = (read() === '\"');\n            lexState = 'string';\n            return\n        }\n\n        if (util.isIdStartChar(c)) {\n            buffer += read();\n            lexState = 'identifierName';\n            return\n        }\n\n        throw invalidChar(read())\n    },\n\n    afterPropertyName () {\n        if (c === ':') {\n            return newToken('punctuator', read())\n        }\n\n        throw invalidChar(read())\n    },\n\n    beforePropertyValue () {\n        lexState = 'value';\n    },\n\n    afterPropertyValue () {\n        switch (c) {\n        case ',':\n        case '}':\n            return newToken('punctuator', read())\n        }\n\n        throw invalidChar(read())\n    },\n\n    beforeArrayValue () {\n        if (c === ']') {\n            return newToken('punctuator', read())\n        }\n\n        lexState = 'value';\n    },\n\n    afterArrayValue () {\n        switch (c) {\n        case ',':\n        case ']':\n            return newToken('punctuator', read())\n        }\n\n        throw invalidChar(read())\n    },\n\n    end () {\n        // This code is unreachable since it's handled by the default lexState.\n        // if (c === undefined) {\n        //     read()\n        //     return newToken('eof')\n        // }\n\n        throw invalidChar(read())\n    },\n};\n\nfunction newToken (type, value) {\n    return {\n        type,\n        value,\n        line,\n        column,\n    }\n}\n\nfunction literal (s) {\n    for (const c of s) {\n        const p = peek();\n\n        if (p !== c) {\n            throw invalidChar(read())\n        }\n\n        read();\n    }\n}\n\nfunction escape () {\n    const c = peek();\n    switch (c) {\n    case 'b':\n        read();\n        return '\\b'\n\n    case 'f':\n        read();\n        return '\\f'\n\n    case 'n':\n        read();\n        return '\\n'\n\n    case 'r':\n        read();\n        return '\\r'\n\n    case 't':\n        read();\n        return '\\t'\n\n    case 'v':\n        read();\n        return '\\v'\n\n    case '0':\n        read();\n        if (util.isDigit(peek())) {\n            throw invalidChar(read())\n        }\n\n        return '\\0'\n\n    case 'x':\n        read();\n        return hexEscape()\n\n    case 'u':\n        read();\n        return unicodeEscape()\n\n    case '\\n':\n    case '\\u2028':\n    case '\\u2029':\n        read();\n        return ''\n\n    case '\\r':\n        read();\n        if (peek() === '\\n') {\n            read();\n        }\n\n        return ''\n\n    case '1':\n    case '2':\n    case '3':\n    case '4':\n    case '5':\n    case '6':\n    case '7':\n    case '8':\n    case '9':\n        throw invalidChar(read())\n\n    case undefined:\n        throw invalidChar(read())\n    }\n\n    return read()\n}\n\nfunction hexEscape () {\n    let buffer = '';\n    let c = peek();\n\n    if (!util.isHexDigit(c)) {\n        throw invalidChar(read())\n    }\n\n    buffer += read();\n\n    c = peek();\n    if (!util.isHexDigit(c)) {\n        throw invalidChar(read())\n    }\n\n    buffer += read();\n\n    return String.fromCodePoint(parseInt(buffer, 16))\n}\n\nfunction unicodeEscape () {\n    let buffer = '';\n    let count = 4;\n\n    while (count-- > 0) {\n        const c = peek();\n        if (!util.isHexDigit(c)) {\n            throw invalidChar(read())\n        }\n\n        buffer += read();\n    }\n\n    return String.fromCodePoint(parseInt(buffer, 16))\n}\n\nconst parseStates = {\n    start () {\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        push();\n    },\n\n    beforePropertyName () {\n        switch (token.type) {\n        case 'identifier':\n        case 'string':\n            key = token.value;\n            parseState = 'afterPropertyName';\n            return\n\n        case 'punctuator':\n            // This code is unreachable since it's handled by the lexState.\n            // if (token.value !== '}') {\n            //     throw invalidToken()\n            // }\n\n            pop();\n            return\n\n        case 'eof':\n            throw invalidEOF()\n        }\n\n        // This code is unreachable since it's handled by the lexState.\n        // throw invalidToken()\n    },\n\n    afterPropertyName () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'punctuator' || token.value !== ':') {\n        //     throw invalidToken()\n        // }\n\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        parseState = 'beforePropertyValue';\n    },\n\n    beforePropertyValue () {\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        push();\n    },\n\n    beforeArrayValue () {\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        if (token.type === 'punctuator' && token.value === ']') {\n            pop();\n            return\n        }\n\n        push();\n    },\n\n    afterPropertyValue () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'punctuator') {\n        //     throw invalidToken()\n        // }\n\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        switch (token.value) {\n        case ',':\n            parseState = 'beforePropertyName';\n            return\n\n        case '}':\n            pop();\n        }\n\n        // This code is unreachable since it's handled by the lexState.\n        // throw invalidToken()\n    },\n\n    afterArrayValue () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'punctuator') {\n        //     throw invalidToken()\n        // }\n\n        if (token.type === 'eof') {\n            throw invalidEOF()\n        }\n\n        switch (token.value) {\n        case ',':\n            parseState = 'beforeArrayValue';\n            return\n\n        case ']':\n            pop();\n        }\n\n        // This code is unreachable since it's handled by the lexState.\n        // throw invalidToken()\n    },\n\n    end () {\n        // This code is unreachable since it's handled by the lexState.\n        // if (token.type !== 'eof') {\n        //     throw invalidToken()\n        // }\n    },\n};\n\nfunction push () {\n    let value;\n\n    switch (token.type) {\n    case 'punctuator':\n        switch (token.value) {\n        case '{':\n            value = {};\n            break\n\n        case '[':\n            value = [];\n            break\n        }\n\n        break\n\n    case 'null':\n    case 'boolean':\n    case 'numeric':\n    case 'string':\n        value = token.value;\n        break\n\n    // This code is unreachable.\n    // default:\n    //     throw invalidToken()\n    }\n\n    if (root === undefined) {\n        root = value;\n    } else {\n        const parent = stack[stack.length - 1];\n        if (Array.isArray(parent)) {\n            parent.push(value);\n        } else {\n            Object.defineProperty(parent, key, {\n                value,\n                writable: true,\n                enumerable: true,\n                configurable: true,\n            });\n        }\n    }\n\n    if (value !== null && typeof value === 'object') {\n        stack.push(value);\n\n        if (Array.isArray(value)) {\n            parseState = 'beforeArrayValue';\n        } else {\n            parseState = 'beforePropertyName';\n        }\n    } else {\n        const current = stack[stack.length - 1];\n        if (current == null) {\n            parseState = 'end';\n        } else if (Array.isArray(current)) {\n            parseState = 'afterArrayValue';\n        } else {\n            parseState = 'afterPropertyValue';\n        }\n    }\n}\n\nfunction pop () {\n    stack.pop();\n\n    const current = stack[stack.length - 1];\n    if (current == null) {\n        parseState = 'end';\n    } else if (Array.isArray(current)) {\n        parseState = 'afterArrayValue';\n    } else {\n        parseState = 'afterPropertyValue';\n    }\n}\n\n// This code is unreachable.\n// function invalidParseState () {\n//     return new Error(`JSON5: invalid parse state '${parseState}'`)\n// }\n\n// This code is unreachable.\n// function invalidLexState (state) {\n//     return new Error(`JSON5: invalid lex state '${state}'`)\n// }\n\nfunction invalidChar (c) {\n    if (c === undefined) {\n        return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n    }\n\n    return syntaxError(`JSON5: invalid character '${formatChar(c)}' at ${line}:${column}`)\n}\n\nfunction invalidEOF () {\n    return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n}\n\n// This code is unreachable.\n// function invalidToken () {\n//     if (token.type === 'eof') {\n//         return syntaxError(`JSON5: invalid end of input at ${line}:${column}`)\n//     }\n\n//     const c = String.fromCodePoint(token.value.codePointAt(0))\n//     return syntaxError(`JSON5: invalid character '${formatChar(c)}' at ${line}:${column}`)\n// }\n\nfunction invalidIdentifier () {\n    column -= 5;\n    return syntaxError(`JSON5: invalid identifier character at ${line}:${column}`)\n}\n\nfunction separatorChar (c) {\n    console.warn(`JSON5: '${formatChar(c)}' in strings is not valid ECMAScript; consider escaping`);\n}\n\nfunction formatChar (c) {\n    const replacements = {\n        \"'\": \"\\\\'\",\n        '\"': '\\\\\"',\n        '\\\\': '\\\\\\\\',\n        '\\b': '\\\\b',\n        '\\f': '\\\\f',\n        '\\n': '\\\\n',\n        '\\r': '\\\\r',\n        '\\t': '\\\\t',\n        '\\v': '\\\\v',\n        '\\0': '\\\\0',\n        '\\u2028': '\\\\u2028',\n        '\\u2029': '\\\\u2029',\n    };\n\n    if (replacements[c]) {\n        return replacements[c]\n    }\n\n    if (c < ' ') {\n        const hexString = c.charCodeAt(0).toString(16);\n        return '\\\\x' + ('00' + hexString).substring(hexString.length)\n    }\n\n    return c\n}\n\nfunction syntaxError (message) {\n    const err = new SyntaxError(message);\n    err.lineNumber = line;\n    err.columnNumber = column;\n    return err\n}\n\nvar stringify = function stringify (value, replacer, space) {\n    const stack = [];\n    let indent = '';\n    let propertyList;\n    let replacerFunc;\n    let gap = '';\n    let quote;\n\n    if (\n        replacer != null &&\n        typeof replacer === 'object' &&\n        !Array.isArray(replacer)\n    ) {\n        space = replacer.space;\n        quote = replacer.quote;\n        replacer = replacer.replacer;\n    }\n\n    if (typeof replacer === 'function') {\n        replacerFunc = replacer;\n    } else if (Array.isArray(replacer)) {\n        propertyList = [];\n        for (const v of replacer) {\n            let item;\n\n            if (typeof v === 'string') {\n                item = v;\n            } else if (\n                typeof v === 'number' ||\n                v instanceof String ||\n                v instanceof Number\n            ) {\n                item = String(v);\n            }\n\n            if (item !== undefined && propertyList.indexOf(item) < 0) {\n                propertyList.push(item);\n            }\n        }\n    }\n\n    if (space instanceof Number) {\n        space = Number(space);\n    } else if (space instanceof String) {\n        space = String(space);\n    }\n\n    if (typeof space === 'number') {\n        if (space > 0) {\n            space = Math.min(10, Math.floor(space));\n            gap = '          '.substr(0, space);\n        }\n    } else if (typeof space === 'string') {\n        gap = space.substr(0, 10);\n    }\n\n    return serializeProperty('', {'': value})\n\n    function serializeProperty (key, holder) {\n        let value = holder[key];\n        if (value != null) {\n            if (typeof value.toJSON5 === 'function') {\n                value = value.toJSON5(key);\n            } else if (typeof value.toJSON === 'function') {\n                value = value.toJSON(key);\n            }\n        }\n\n        if (replacerFunc) {\n            value = replacerFunc.call(holder, key, value);\n        }\n\n        if (value instanceof Number) {\n            value = Number(value);\n        } else if (value instanceof String) {\n            value = String(value);\n        } else if (value instanceof Boolean) {\n            value = value.valueOf();\n        }\n\n        switch (value) {\n        case null: return 'null'\n        case true: return 'true'\n        case false: return 'false'\n        }\n\n        if (typeof value === 'string') {\n            return quoteString(value, false)\n        }\n\n        if (typeof value === 'number') {\n            return String(value)\n        }\n\n        if (typeof value === 'object') {\n            return Array.isArray(value) ? serializeArray(value) : serializeObject(value)\n        }\n\n        return undefined\n    }\n\n    function quoteString (value) {\n        const quotes = {\n            \"'\": 0.1,\n            '\"': 0.2,\n        };\n\n        const replacements = {\n            \"'\": \"\\\\'\",\n            '\"': '\\\\\"',\n            '\\\\': '\\\\\\\\',\n            '\\b': '\\\\b',\n            '\\f': '\\\\f',\n            '\\n': '\\\\n',\n            '\\r': '\\\\r',\n            '\\t': '\\\\t',\n            '\\v': '\\\\v',\n            '\\0': '\\\\0',\n            '\\u2028': '\\\\u2028',\n            '\\u2029': '\\\\u2029',\n        };\n\n        let product = '';\n\n        for (let i = 0; i < value.length; i++) {\n            const c = value[i];\n            switch (c) {\n            case \"'\":\n            case '\"':\n                quotes[c]++;\n                product += c;\n                continue\n\n            case '\\0':\n                if (util.isDigit(value[i + 1])) {\n                    product += '\\\\x00';\n                    continue\n                }\n            }\n\n            if (replacements[c]) {\n                product += replacements[c];\n                continue\n            }\n\n            if (c < ' ') {\n                let hexString = c.charCodeAt(0).toString(16);\n                product += '\\\\x' + ('00' + hexString).substring(hexString.length);\n                continue\n            }\n\n            product += c;\n        }\n\n        const quoteChar = quote || Object.keys(quotes).reduce((a, b) => (quotes[a] < quotes[b]) ? a : b);\n\n        product = product.replace(new RegExp(quoteChar, 'g'), replacements[quoteChar]);\n\n        return quoteChar + product + quoteChar\n    }\n\n    function serializeObject (value) {\n        if (stack.indexOf(value) >= 0) {\n            throw TypeError('Converting circular structure to JSON5')\n        }\n\n        stack.push(value);\n\n        let stepback = indent;\n        indent = indent + gap;\n\n        let keys = propertyList || Object.keys(value);\n        let partial = [];\n        for (const key of keys) {\n            const propertyString = serializeProperty(key, value);\n            if (propertyString !== undefined) {\n                let member = serializeKey(key) + ':';\n                if (gap !== '') {\n                    member += ' ';\n                }\n                member += propertyString;\n                partial.push(member);\n            }\n        }\n\n        let final;\n        if (partial.length === 0) {\n            final = '{}';\n        } else {\n            let properties;\n            if (gap === '') {\n                properties = partial.join(',');\n                final = '{' + properties + '}';\n            } else {\n                let separator = ',\\n' + indent;\n                properties = partial.join(separator);\n                final = '{\\n' + indent + properties + ',\\n' + stepback + '}';\n            }\n        }\n\n        stack.pop();\n        indent = stepback;\n        return final\n    }\n\n    function serializeKey (key) {\n        if (key.length === 0) {\n            return quoteString(key, true)\n        }\n\n        const firstChar = String.fromCodePoint(key.codePointAt(0));\n        if (!util.isIdStartChar(firstChar)) {\n            return quoteString(key, true)\n        }\n\n        for (let i = firstChar.length; i < key.length; i++) {\n            if (!util.isIdContinueChar(String.fromCodePoint(key.codePointAt(i)))) {\n                return quoteString(key, true)\n            }\n        }\n\n        return key\n    }\n\n    function serializeArray (value) {\n        if (stack.indexOf(value) >= 0) {\n            throw TypeError('Converting circular structure to JSON5')\n        }\n\n        stack.push(value);\n\n        let stepback = indent;\n        indent = indent + gap;\n\n        let partial = [];\n        for (let i = 0; i < value.length; i++) {\n            const propertyString = serializeProperty(String(i), value);\n            partial.push((propertyString !== undefined) ? propertyString : 'null');\n        }\n\n        let final;\n        if (partial.length === 0) {\n            final = '[]';\n        } else {\n            if (gap === '') {\n                let properties = partial.join(',');\n                final = '[' + properties + ']';\n            } else {\n                let separator = ',\\n' + indent;\n                let properties = partial.join(separator);\n                final = '[\\n' + indent + properties + ',\\n' + stepback + ']';\n            }\n        }\n\n        stack.pop();\n        indent = stepback;\n        return final\n    }\n};\n\nconst JSON5 = {\n    parse,\n    stringify,\n};\n\nvar lib = JSON5;\n\nexport default lib;\n", "export const API_VERSIONS = [\n  'v1',\n  'vX',\n  'v2021-03-25',\n  'v2021-10-21',\n  'v2022-03-07',\n  'v2025-02-19',\n  `v${new Date().toISOString().split('T')[0]}`,\n]\nexport const [DEFAULT_API_VERSION] = API_VERSIONS.slice(-1)\n", "import {Spinner} from '@sanity/ui'\nimport {useEffect, useState} from 'react'\n\ninterface DelayedSpinnerProps {\n  delay?: number\n}\n\n// Waits for X ms before showing a spinner\nexport function DelayedSpinner(props: DelayedSpinnerProps) {\n  const [show, setShow] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => setShow(true), props.delay || 500)\n    return () => clearTimeout(timer)\n  }, [props.delay])\n\n  return show ? <Spinner muted size={4} /> : null\n}\n", "import {closeBrackets} from '@codemirror/autocomplete'\nimport {defaultKeymap, history, historyKeymap} from '@codemirror/commands'\nimport {javascriptLanguage} from '@codemirror/lang-javascript'\nimport {\n  bracketMatching,\n  defaultHighlightStyle,\n  indentOnInput,\n  syntaxHighlighting,\n} from '@codemirror/language'\nimport {highlightSelectionMatches} from '@codemirror/search'\nimport {type Extension} from '@codemirror/state'\nimport {\n  drawSelection,\n  highlightActiveLine,\n  highlightActiveLineGutter,\n  highlightSpecialChars,\n  keymap,\n  lineNumbers,\n} from '@codemirror/view'\n\nexport const codemirrorExtensions: Extension[] = [\n  [javascriptLanguage],\n  lineNumbers(),\n  highlightActiveLine(),\n  highlightActiveLineGutter(),\n  highlightSelectionMatches(),\n  highlightSpecialChars(),\n  indentOnInput(),\n  bracketMatching(),\n  closeBrackets(),\n  history(),\n  drawSelection(),\n  syntaxHighlighting(defaultHighlightStyle, {fallback: true}),\n  keymap.of(\n    [\n      // Override the default keymap for Mod-Enter to not insert a new line, we have a custom event handler for executing queries\n      {key: 'Mod-Enter', run: () => true},\n\n      // Add the default keymap and history keymap\n      defaultKeymap,\n      historyKeymap,\n    ]\n      .flat()\n      .filter(Boolean),\n  ),\n]\n", "import {HighlightStyle, syntaxHighlighting} from '@codemirror/language'\nimport {EditorView} from '@codemirror/view'\nimport {tags as t} from '@lezer/highlight'\nimport {hues} from '@sanity/color'\nimport {rem, type Theme} from '@sanity/ui'\nimport {useMemo} from 'react'\n\nexport function useCodemirrorTheme(theme: Theme) {\n  const cmTheme = useMemo(() => createTheme(theme), [theme])\n  const cmHighlight = useMemo(() => syntaxHighlighting(createHighlight(theme)), [theme])\n\n  return [cmTheme, cmHighlight]\n}\n\nfunction createTheme(theme: Theme) {\n  const {color, fonts} = theme.sanity\n  const card = color.card.enabled\n  const cursor = hues.blue[color.dark ? 400 : 500].hex\n  const selection = hues.gray[theme.sanity.color.dark ? 900 : 100].hex\n\n  return EditorView.theme(\n    {\n      '&': {\n        color: card.fg,\n        backgroundColor: card.bg,\n      },\n\n      '.cm-content': {\n        caretColor: cursor,\n      },\n\n      '.cm-editor': {\n        fontFamily: fonts.code.family,\n        fontSize: rem(fonts.code.sizes[1].fontSize),\n        lineHeight: 'inherit',\n      },\n\n      '.cm-cursor, .cm-dropCursor': {borderLeftColor: cursor},\n      '&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection': {\n        backgroundColor: selection,\n      },\n\n      '.cm-panels': {backgroundColor: card.bg, color: card.fg},\n      '.cm-panels.cm-panels-top': {borderBottom: `2px solid ${card.border}`},\n      '.cm-panels.cm-panels-bottom': {borderTop: `2px solid ${card.border}`},\n    },\n    {dark: color.dark},\n  )\n}\n\nfunction createHighlight(theme: Theme) {\n  const c = theme.sanity.color.base\n  const s = theme.sanity.color.syntax\n  return HighlightStyle.define([\n    {tag: t.keyword, color: s.keyword},\n    {tag: [t.propertyName, t.name, t.deleted, t.character, t.macroName], color: s.property},\n    {tag: [t.function(t.variableName), t.labelName], color: s.function},\n    {tag: [t.color, t.constant(t.name), t.standard(t.name)], color: s.variable},\n    {tag: [t.definition(t.name), t.separator], color: s.constant},\n    {\n      tag: [\n        t.typeName,\n        t.className,\n        t.number,\n        t.changed,\n        t.annotation,\n        t.modifier,\n        t.self,\n        t.namespace,\n      ],\n      color: s.number,\n    },\n    {\n      tag: [t.operator, t.operatorKeyword, t.url, t.escape, t.regexp, t.link, t.special(t.string)],\n      color: s.operator,\n    },\n    {tag: [t.meta, t.comment], color: s.comment},\n    {tag: t.strong, fontWeight: 'bold'},\n    {tag: t.emphasis, fontStyle: 'italic'},\n    {tag: t.strikethrough, textDecoration: 'line-through'},\n    {tag: t.heading, fontWeight: 'bold', color: s.property},\n    {tag: [t.atom, t.bool, t.special(t.variableName)], color: s.boolean},\n    {tag: [t.processingInstruction, t.string, t.inserted], color: s.string},\n    {tag: t.invalid, color: c.fg},\n  ])\n}\n", "import {rem} from '@sanity/ui'\nimport {styled} from 'styled-components'\n\nexport const EditorRoot = styled.div`\n  width: 100%;\n  box-sizing: border-box;\n  height: 100%;\n  overflow: hidden;\n  overflow: clip;\n  position: relative;\n  display: flex;\n\n  & .cm-theme {\n    width: 100%;\n  }\n\n  & .cm-editor {\n    height: 100%;\n\n    font-size: 16px;\n    line-height: 21px;\n  }\n\n  & .cm-line {\n    padding-left: ${({theme}) => rem(theme.sanity.space[3])};\n  }\n\n  & .cm-content {\n    border-right-width: ${({theme}) => rem(theme.sanity.space[4])} !important;\n    padding-top: ${({theme}) => rem(theme.sanity.space[5])};\n  }\n`\n", "import {useTheme} from '@sanity/ui'\nimport CodeMirror, {\n  EditorSelection,\n  type ReactCodeMirrorProps,\n  type ReactCodeMirrorRef,\n} from '@uiw/react-codemirror'\nimport {forwardRef, useCallback, useImperativeHandle, useRef, useState} from 'react'\n\nimport {codemirrorExtensions} from './extensions'\nimport {useCodemirrorTheme} from './useCodemirrorTheme'\nimport {EditorRoot} from './VisionCodeMirror.styled'\n\nexport interface VisionCodeMirrorHandle {\n  resetEditorContent: (newContent: string) => void\n}\n\nexport const VisionCodeMirror = forwardRef<\n  VisionCodeMirrorHandle,\n  Pick<ReactCodeMirrorProps, 'onChange'> & {\n    initialValue: ReactCodeMirrorProps['value']\n  }\n>((props, ref) => {\n  // The value prop is only passed for initial value, and is not updated when the parent component updates the value.\n  // If you need to update the value, use the resetEditorContent function.\n  const [initialValue] = useState(props.initialValue)\n  const sanityTheme = useTheme()\n  const theme = useCodemirrorTheme(sanityTheme)\n  const codeMirrorRef = useRef<ReactCodeMirrorRef>(null)\n\n  const resetEditorContent = useCallback((newContent: string) => {\n    const editorView = codeMirrorRef.current?.view\n    if (!editorView) return\n\n    const currentDoc = editorView.state.doc.toString()\n    if (newContent !== currentDoc) {\n      editorView.dispatch({\n        changes: {from: 0, to: currentDoc.length, insert: newContent},\n        selection: EditorSelection.cursor(newContent.length), // Place cursor at end\n      })\n    }\n  }, [])\n\n  useImperativeHandle(\n    ref,\n    () => ({\n      resetEditorContent,\n    }),\n    [resetEditorContent],\n  )\n\n  return (\n    <EditorRoot>\n      <CodeMirror\n        ref={codeMirrorRef}\n        basicSetup={false}\n        theme={theme}\n        extensions={codemirrorExtensions}\n        value={initialValue}\n        onChange={props.onChange}\n      />\n    </EditorRoot>\n  )\n})\n\n// Add display name\nVisionCodeMirror.displayName = 'VisionCodeMirror'\n", "import {type ClientPerspective} from '@sanity/client'\nimport isEqual from 'react-fast-compare'\nimport {type PerspectiveContextValue} from 'sanity'\n\nexport const SUPPORTED_PERSPECTIVES = ['pinnedRelease', 'raw', 'published', 'drafts'] as const\n\nexport type SupportedPerspective = (typeof SUPPORTED_PERSPECTIVES)[number]\n\n/**\n * Virtual perspectives are recognised by Vision, but do not concretely reflect the names of real\n * perspectives. Virtual perspectives are transformed into real perspectives before being used to\n * interact with data.\n *\n * For example, the `pinnedRelease` virtual perspective is transformed to the real perspective\n * currently pinned in Studio.\n */\nexport const VIRTUAL_PERSPECTIVES = ['pinnedRelease'] as const\n\nexport type VirtualPerspective = (typeof VIRTUAL_PERSPECTIVES)[number]\n\nexport function isSupportedPerspective(p: string): p is SupportedPerspective {\n  return SUPPORTED_PERSPECTIVES.includes(p as SupportedPerspective)\n}\n\nexport function isVirtualPerspective(\n  maybeVirtualPerspective: unknown,\n): maybeVirtualPerspective is VirtualPerspective {\n  return (\n    typeof maybeVirtualPerspective === 'string' &&\n    VIRTUAL_PERSPECTIVES.includes(maybeVirtualPerspective as VirtualPerspective)\n  )\n}\n\nexport function hasPinnedPerspective({selectedPerspectiveName}: PerspectiveContextValue): boolean {\n  return typeof selectedPerspectiveName !== 'undefined'\n}\n\nexport function hasPinnedPerspectiveChanged(\n  previous: PerspectiveContextValue,\n  next: PerspectiveContextValue,\n): boolean {\n  const hasPerspectiveStackChanged = !isEqual(previous.perspectiveStack, next.perspectiveStack)\n\n  return (\n    previous.selectedPerspectiveName !== next.selectedPerspectiveName || hasPerspectiveStackChanged\n  )\n}\n\nexport function getActivePerspective({\n  visionPerspective,\n  perspectiveStack,\n}: {\n  visionPerspective: ClientPerspective | SupportedPerspective | undefined\n  perspectiveStack: PerspectiveContextValue['perspectiveStack']\n}): ClientPerspective | undefined {\n  if (visionPerspective !== 'pinnedRelease') {\n    return visionPerspective\n  }\n  return perspectiveStack\n}\n", "export function encodeQueryString(\n  query: string,\n  params: Record<string, unknown> = {},\n  options: Record<string, string | string[]> = {},\n): string {\n  const searchParams = new URLSearchParams()\n  searchParams.set('query', query)\n\n  for (const [key, value] of Object.entries(params)) {\n    searchParams.set(`$${key}`, JSON.stringify(value))\n  }\n\n  for (const [key, value] of Object.entries(options)) {\n    if (value) searchParams.set(key, `${value}`)\n  }\n\n  return `?${searchParams}`\n}\n", "export function isPlainObject(obj: unknown): obj is Record<string, unknown> {\n  return (\n    !!obj && typeof obj === 'object' && Object.prototype.toString.call(obj) === '[object Object]'\n  )\n}\n", "import {isPlainObject} from './isPlainObject'\n\nconst hasLocalStorage = supportsLocalStorage()\nconst keyPrefix = 'sanityVision:'\n\nexport interface LocalStorageish {\n  get: <T>(key: string, defaultVal: T) => T\n  set: <T>(key: string, value: T) => T\n  merge: <T>(props: T) => T\n}\n\nexport function clearLocalStorage() {\n  if (!hasLocalStorage) {\n    return\n  }\n\n  for (let i = 0; i < localStorage.length; i++) {\n    const key = localStorage.key(i)\n    if (key?.startsWith(keyPrefix)) {\n      localStorage.removeItem(key)\n    }\n  }\n}\n\nexport function getLocalStorage(namespace: string): LocalStorageish {\n  const storageKey = `${keyPrefix}${namespace}`\n  let loadedState: Record<string, unknown> | null = null\n\n  return {get, set, merge}\n\n  function get<T>(key: string, defaultVal: T): T {\n    const state = ensureState()\n    return typeof state[key] === 'undefined' ? defaultVal : (state[key] as T)\n  }\n\n  function set<T>(key: string, value: T): T {\n    const state = ensureState()\n    state[key] = value\n    localStorage.setItem(storageKey, JSON.stringify(loadedState))\n    return value\n  }\n\n  function merge<T>(props: T): T {\n    const state = {...ensureState(), ...props}\n    localStorage.setItem(storageKey, JSON.stringify(state))\n    return state\n  }\n\n  function ensureState(): Record<string, unknown> {\n    if (loadedState === null) {\n      loadedState = loadState()\n    }\n\n    return loadedState\n  }\n\n  function loadState() {\n    if (!hasLocalStorage) {\n      return {}\n    }\n\n    try {\n      const stored = JSON.parse(localStorage.getItem(storageKey) || '{}')\n      return isPlainObject(stored) ? stored : {}\n    } catch (err) {\n      return {}\n    }\n  }\n}\n\nfunction supportsLocalStorage() {\n  const mod = 'lsCheck'\n  try {\n    localStorage.setItem(mod, mod)\n    localStorage.removeItem(mod)\n    return true\n  } catch (err) {\n    return false\n  }\n}\n", "export interface ParsedApiQueryString {\n  query: string\n  params: Record<string, unknown>\n  options: Record<string, string>\n}\n\nexport function parseApiQueryString(qs: URLSearchParams): ParsedApiQueryString {\n  const params: Record<string, unknown> = {}\n  const options: Record<string, string> = {}\n\n  for (const [key, value] of qs.entries()) {\n    if (key[0] === '$') {\n      params[key.slice(1)] = JSON.parse(value)\n      continue\n    }\n\n    if (key === 'perspective') {\n      options[key] = value\n      continue\n    }\n  }\n\n  return {query: qs.get('query') || '', params, options}\n}\n", "export function prefixApiVersion(version: string): string {\n  if (version[0] !== 'v' && version !== 'other') {\n    return `v${version}`\n  }\n\n  return version\n}\n", "export function validateApiVersion(apiVersion: string): boolean {\n  const parseableApiVersion = apiVersion.replace(/^v/, '').trim().toUpperCase()\n\n  const isValidApiVersion =\n    parseableApiVersion.length > 0 &&\n    (parseableApiVersion === 'X' ||\n      parseableApiVersion === '1' ||\n      (/^\\d{4}-\\d{2}-\\d{2}$/.test(parseableApiVersion) && !isNaN(Date.parse(parseableApiVersion))))\n\n  return isValidApiVersion\n}\n", "import JSON5 from 'json5'\nimport {type TFunction} from 'sanity'\n\nexport function tryParseParams(\n  val: string,\n  t: TFunction<'vision', undefined>,\n): Record<string, unknown> | Error {\n  try {\n    const parsed = val ? JSON5.parse(val) : {}\n    return typeof parsed === 'object' && parsed && !Array.isArray(parsed) ? parsed : {}\n  } catch (err) {\n    // JSON5 always prefixes the error message with JSON5:, so we remove it\n    // to clean up the error tooltip\n    err.message = `${t('params.error.params-invalid-json')}:\\n\\n${err.message.replace(\n      'JSON5:',\n      '',\n    )}`\n    return err\n  }\n}\n", "import {Box, Card, Flex, Label, rem, Text} from '@sanity/ui'\nimport {css, styled} from 'styled-components'\n\nexport const Root = styled(Flex)`\n  .sidebarPanes .Pane {\n    overflow-y: auto;\n    overflow-x: hidden;\n  }\n\n  & .Resizer {\n    background: var(--card-border-color);\n    opacity: 1;\n    z-index: 1;\n    box-sizing: border-box;\n    background-clip: padding-box;\n    border: solid transparent;\n  }\n\n  & .Resizer:hover {\n    border-color: var(--card-shadow-ambient-color);\n  }\n\n  & .Resizer.horizontal {\n    height: 11px;\n    margin: -5px 0;\n    border-width: 5px 0;\n    cursor: row-resize;\n    width: 100%;\n    z-index: 4;\n  }\n\n  & .Resizer.vertical {\n    width: 11px;\n    margin: 0 -5px;\n    border-width: 0 5px;\n    cursor: col-resize;\n    z-index: 2; /* To prevent the resizer from being hidden behind CodeMirror scroll area */\n  }\n\n  .Resizer.disabled {\n    cursor: not-allowed;\n  }\n\n  .Resizer.disabled:hover {\n    border-color: transparent;\n  }\n`\n\nRoot.displayName = 'Root'\n\nexport const Header = styled(Card)`\n  border-bottom: 1px solid var(--card-border-color);\n`\n\nexport const StyledLabel = styled(Label)`\n  flex: 1;\n`\n\nexport const SplitpaneContainer = styled(Box)`\n  position: relative;\n`\n\nexport const QueryCopyLink = styled.a`\n  cursor: pointer;\n  margin-right: auto;\n`\n\nexport const InputBackgroundContainer = styled(Box)`\n  position: absolute;\n  top: 1rem;\n  left: 0;\n  padding: 0;\n  margin: 0;\n  z-index: 10;\n  right: 0;\n\n  ${StyledLabel} {\n    user-select: none;\n  }\n`\n\nexport const InputBackgroundContainerLeft = styled(InputBackgroundContainer)`\n  // This is so its aligned with the gutters of CodeMirror\n  left: 33px;\n`\n\nexport const InputContainer = styled(Card)`\n  width: 100%;\n  height: 100%;\n  position: relative;\n  flex-direction: column;\n`\n\nexport const ResultOuterContainer = styled(Flex)`\n  height: 100%;\n`\n\nexport const ResultInnerContainer = styled(Box)`\n  position: relative;\n`\n\nexport const ResultContainer = styled(Card)<{$isInvalid: boolean}>`\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  max-width: 100%;\n\n  ${({$isInvalid}) =>\n    $isInvalid &&\n    css`\n      &:after {\n        background-color: var(--card-bg-color);\n        content: '';\n        position: absolute;\n        top: 0;\n        bottom: 0;\n        left: 0;\n        width: 100%;\n      }\n    `}\n`\n\nexport const Result = styled(Box)`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 20;\n`\n\nexport const ResultFooter = styled(Flex)`\n  border-top: 1px solid var(--card-border-color);\n`\n\nexport const TimingsCard = styled(Card)`\n  position: relative;\n`\n\nexport const TimingsContainer = styled(Box)`\n  width: 100%;\n  height: 100%;\n`\n\nexport const TimingsTextContainer = styled(Flex)`\n  height: 100%;\n  min-height: ${({theme}) =>\n    rem(\n      theme.sanity.space[3] * 2 +\n        theme.sanity.fonts.text.sizes[2].lineHeight -\n        theme.sanity.fonts.text.sizes[2].ascenderHeight -\n        theme.sanity.fonts.text.sizes[2].descenderHeight,\n    )};\n`\n\nexport const DownloadsCard = styled(Card)`\n  position: relative;\n`\n\nexport const SaveResultLabel = styled(Text)`\n  transform: initial;\n  &:before,\n  &:after {\n    content: none;\n  }\n  > span {\n    display: flex !important;\n    gap: ${({theme}) => rem(theme.sanity.space[3])};\n    align-items: center;\n  }\n`\n\nexport const ControlsContainer = styled(Box)`\n  border-top: 1px solid var(--card-border-color);\n`\n", "import {ErrorOutlineIcon} from '@sanity/icons'\nimport {Box, Card, Flex, Text, Tooltip} from '@sanity/ui'\nimport {debounce} from 'lodash'\nimport {type RefObject, useCallback, useEffect, useMemo, useState} from 'react'\nimport {type TFunction, useTranslation} from 'sanity'\n\nimport {VisionCodeMirror, type VisionCodeMirrorHandle} from '../codemirror/VisionCodeMirror'\nimport {visionLocaleNamespace} from '../i18n'\nimport {tryParseParams} from '../util/tryParseParams'\nimport {type Params} from './VisionGui'\nimport {InputBackgroundContainerLeft, StyledLabel} from './VisionGui.styled'\n\nconst defaultValue = `{\\n  \\n}`\n\nexport interface ParamsEditorProps {\n  value: string\n  onChange: (changeEvt: Params) => void\n  paramsError: string | undefined\n  hasValidParams: boolean\n  editorRef: RefObject<VisionCodeMirrorHandle | null>\n}\n\nexport interface ParamsEditorChange {\n  valid: boolean\n}\n\nexport function ParamsEditor(props: ParamsEditorProps) {\n  const {onChange, paramsError, hasValidParams, editorRef} = props\n  const {t} = useTranslation(visionLocaleNamespace)\n  const {raw: value, error, parsed, valid} = parseParams(props.value, t)\n  const [isValid, setValid] = useState(valid)\n  const [init, setInit] = useState(false)\n\n  // Emit onChange on very first render\n  useEffect(() => {\n    if (!init) {\n      onChange({parsed, raw: value, valid: isValid, error})\n      setInit(true)\n    }\n  }, [error, init, isValid, onChange, parsed, value])\n\n  const handleChangeRaw = useCallback(\n    (newValue: string) => {\n      const event = parseParams(newValue, t)\n      setValid(event.valid)\n      onChange(event)\n    },\n    [onChange, t],\n  )\n\n  const handleChange = useMemo(() => debounce(handleChangeRaw, 333), [handleChangeRaw])\n  return (\n    <Card flex={1} tone={hasValidParams ? 'default' : 'critical'} data-testid=\"params-editor\">\n      <InputBackgroundContainerLeft>\n        <Flex>\n          <StyledLabel muted>{t('params.label')}</StyledLabel>\n          {paramsError && (\n            <Tooltip animate placement=\"top\" portal content={<Text size={1}>{paramsError}</Text>}>\n              <Box padding={1} marginX={2}>\n                <Text>\n                  <ErrorOutlineIcon />\n                </Text>\n              </Box>\n            </Tooltip>\n          )}\n        </Flex>\n      </InputBackgroundContainerLeft>\n      <VisionCodeMirror\n        ref={editorRef}\n        initialValue={props.value || defaultValue}\n        onChange={handleChange}\n      />\n    </Card>\n  )\n}\n\nexport function parseParams(\n  value: string,\n  t: TFunction<typeof visionLocaleNamespace, undefined>,\n): Params {\n  const parsedParams = tryParseParams(value, t)\n  const params = parsedParams instanceof Error ? {} : parsedParams\n  const validationError = parsedParams instanceof Error ? parsedParams.message : undefined\n  const isValid = !validationError\n\n  return {\n    parsed: params,\n    raw: value,\n    valid: isValid,\n    error: validationError,\n  }\n}\n", "import {uuid} from '@sanity/uuid' // Import the UUID library\nimport {useCallback, useEffect, useMemo, useState} from 'react'\nimport {map, startWith} from 'rxjs/operators'\nimport {type KeyValueStoreValue, useKeyValueStore} from 'sanity'\n\nconst STORED_QUERIES_NAMESPACE = 'studio.vision-tool.saved-queries'\n\nexport interface QueryConfig {\n  _key: string\n  url: string\n  savedAt: string\n  title?: string\n  shared?: boolean\n}\n\nexport interface StoredQueries {\n  queries: QueryConfig[]\n}\n\nconst defaultValue = {\n  queries: [],\n}\nconst keyValueStoreKey = STORED_QUERIES_NAMESPACE\n\nexport function useSavedQueries(): {\n  queries: QueryConfig[]\n  saveQuery: (query: Omit<QueryConfig, '_key'>) => void\n  updateQuery: (query: QueryConfig) => void\n  deleteQuery: (key: string) => void\n  saving: boolean\n  deleting: string[]\n  saveQueryError: Error | undefined\n  deleteQueryError: Error | undefined\n  error: Error | undefined\n} {\n  const keyValueStore = useKeyValueStore()\n\n  const [value, setValue] = useState<StoredQueries>(defaultValue)\n  const [saving, setSaving] = useState(false)\n  const [deleting, setDeleting] = useState<string[]>([])\n  const [saveQueryError, setSaveQueryError] = useState<Error | undefined>()\n  const [deleteQueryError, setDeleteQueryError] = useState<Error | undefined>()\n  const [error, setError] = useState<Error | undefined>()\n\n  const queries = useMemo(() => {\n    return keyValueStore.getKey(keyValueStoreKey)\n  }, [keyValueStore])\n\n  useEffect(() => {\n    const sub = queries\n      .pipe(\n        startWith(defaultValue as any),\n        map((data: StoredQueries) => {\n          if (!data) {\n            return defaultValue\n          }\n          return data\n        }),\n      )\n      .subscribe({\n        next: setValue,\n        error: (err) => setError(err as Error),\n      })\n\n    return () => sub?.unsubscribe()\n  }, [queries, keyValueStore])\n\n  const saveQuery = useCallback(\n    (query: Omit<QueryConfig, '_key'>) => {\n      setSaving(true)\n      setSaveQueryError(undefined)\n      try {\n        const newQuery = {...query, _key: uuid()} // Add a unique _key to the query\n        const newQueries = [newQuery, ...value.queries]\n        setValue({queries: newQueries})\n        keyValueStore.setKey(keyValueStoreKey, {\n          queries: newQueries,\n        } as unknown as KeyValueStoreValue)\n      } catch (err) {\n        setSaveQueryError(err as Error)\n      } finally {\n        setSaving(false)\n      }\n    },\n    [keyValueStore, value.queries],\n  )\n\n  const updateQuery = useCallback(\n    (query: QueryConfig) => {\n      setSaving(true)\n      setSaveQueryError(undefined)\n      try {\n        const updatedQueries = value.queries.map((q) =>\n          q._key === query._key ? {...q, ...query} : q,\n        )\n        setValue({queries: updatedQueries})\n        keyValueStore.setKey(keyValueStoreKey, {\n          queries: updatedQueries,\n        } as unknown as KeyValueStoreValue)\n      } catch (err) {\n        setSaveQueryError(err as Error)\n      } finally {\n        setSaving(false)\n      }\n    },\n    [keyValueStore, value.queries],\n  )\n\n  const deleteQuery = useCallback(\n    (key: string) => {\n      setDeleting((prev) => [...prev, key])\n      setDeleteQueryError(undefined)\n      try {\n        const filteredQueries = value.queries.filter((q) => q._key !== key)\n        setValue({queries: filteredQueries})\n        keyValueStore.setKey(keyValueStoreKey, {\n          queries: filteredQueries,\n        } as unknown as KeyValueStoreValue)\n      } catch (err) {\n        setDeleteQueryError(err as Error)\n      } finally {\n        setDeleting((prev) => prev.filter((k) => k !== key))\n      }\n    },\n    [keyValueStore, value.queries],\n  )\n\n  return {\n    queries: value.queries,\n    saveQuery,\n    updateQuery,\n    deleteQuery,\n    saving,\n    deleting,\n    saveQueryError,\n    deleteQueryError,\n    error,\n  }\n}\n", "import {Box, Stack} from '@sanity/ui'\nimport {styled} from 'styled-components'\n\nexport const FixedHeader = styled(Stack)`\n  position: sticky;\n  top: 0;\n  background: ${({theme}) => theme.sanity.color.base.bg};\n  z-index: 1;\n`\n\nexport const ScrollContainer = styled(Box)`\n  height: 100%;\n  overflow-y: auto;\n  overflow-x: hidden;\n\n  &::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  &::-webkit-scrollbar-track {\n    background: transparent;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    background: ${({theme}) => theme.sanity.color.base.border};\n    border-radius: 4px;\n  }\n`\n", "import {AddIcon, SearchIcon, TrashIcon} from '@sanity/icons'\nimport {\n  Box,\n  Button,\n  Card,\n  Code,\n  Flex,\n  Menu,\n  MenuButton,\n  MenuItem,\n  Stack,\n  Text,\n  TextInput,\n  useToast,\n} from '@sanity/ui'\nimport {isEqual} from 'lodash'\nimport {type ReactElement, useCallback, useState} from 'react'\nimport {ContextMenuButton, useDateTimeFormat, useTranslation} from 'sanity'\n\nimport {type QueryConfig, useSavedQueries} from '../hooks/useSavedQueries'\nimport {visionLocaleNamespace} from '../i18n'\nimport {FixedHeader, ScrollContainer} from './QueryRecall.styled'\nimport {type ParsedUrlState} from './VisionGui'\n\nexport function QueryRecall({\n  url,\n  getStateFromUrl,\n  setStateFromParsedUrl,\n  currentQuery,\n  currentParams,\n  generateUrl,\n}: {\n  url?: string\n  getStateFromUrl: (data: string) => ParsedUrlState | null\n  setStateFromParsedUrl: (parsedUrlObj: ParsedUrlState) => void\n  currentQuery: string\n  currentParams: Record<string, unknown>\n  generateUrl: (query: string, params: Record<string, unknown>) => string\n}): ReactElement {\n  const toast = useToast()\n  const {saveQuery, updateQuery, queries, deleteQuery, saving, deleting, saveQueryError} =\n    useSavedQueries()\n  const {t} = useTranslation(visionLocaleNamespace)\n  const formatDate = useDateTimeFormat({\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric',\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  })\n  const [editingKey, setEditingKey] = useState<string | null>(null)\n  const [editingTitle, setEditingTitle] = useState('')\n  const [optimisticTitles, setOptimisticTitles] = useState<Record<string, string>>({})\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedUrl, setSelectedUrl] = useState<string | undefined>(url)\n\n  const handleSave = useCallback(async () => {\n    // Generate the correct URL first\n    const newUrl = generateUrl(currentQuery, currentParams)\n\n    // Check for duplicates by comparing query content and params\n    const isDuplicate = queries?.some((q) => {\n      const savedQueryObj = getStateFromUrl(q.url)\n      return (\n        savedQueryObj &&\n        savedQueryObj.query === currentQuery &&\n        isEqual(savedQueryObj.params, currentParams)\n      )\n    })\n\n    if (isDuplicate) {\n      const duplicateQuery = queries?.find((q) => {\n        const savedQueryObj = getStateFromUrl(q.url)\n        return (\n          savedQueryObj &&\n          savedQueryObj.query === currentQuery &&\n          isEqual(savedQueryObj.params, currentParams)\n        )\n      })\n      toast.push({\n        closable: true,\n        status: 'warning',\n        title: t('save-query.already-saved'),\n        description: `${duplicateQuery?.title} - ${formatDate.format(new Date(duplicateQuery?.savedAt || ''))}`,\n      })\n      return\n    }\n\n    if (newUrl) {\n      await saveQuery({\n        url: newUrl,\n        savedAt: new Date().toISOString(),\n        title: 'Untitled',\n      })\n      // Set the selected URL to the newly saved query's URL\n      setSelectedUrl(newUrl)\n    }\n    if (saveQueryError) {\n      toast.push({\n        closable: true,\n        status: 'error',\n        title: t('save-query.error'),\n        description: saveQueryError.message,\n      })\n    } else {\n      toast.push({\n        closable: true,\n        status: 'success',\n        title: t('save-query.success'),\n      })\n    }\n  }, [\n    queries,\n    saveQueryError,\n    toast,\n    t,\n    currentQuery,\n    currentParams,\n    getStateFromUrl,\n    generateUrl,\n    formatDate,\n    saveQuery,\n  ])\n\n  const handleTitleSave = useCallback(\n    async (query: QueryConfig, newTitle: string) => {\n      setEditingKey(null)\n      setOptimisticTitles((prev) => ({...prev, [query._key]: newTitle}))\n\n      try {\n        await updateQuery({\n          ...query,\n          title: newTitle,\n        })\n        // Clear optimistic title on success\n        setOptimisticTitles((prev) => {\n          const next = {...prev}\n          delete next[query._key]\n          return next\n        })\n      } catch (err) {\n        // Clear optimistic title on error\n        setOptimisticTitles((prev) => {\n          const next = {...prev}\n          delete next[query._key]\n          return next\n        })\n        toast.push({\n          closable: true,\n          status: 'error',\n          title: t('save-query.error'),\n          description: err.message,\n        })\n      }\n    },\n    [updateQuery, toast, t],\n  )\n\n  const handleUpdate = useCallback(\n    async (query: QueryConfig) => {\n      const newUrl = generateUrl(currentQuery, currentParams)\n\n      // Check for duplicates by comparing query content and params\n      const isDuplicate = queries?.some((q) => {\n        // Skip the current query when checking for duplicates\n        if (q._key === query._key) return false\n        const savedQueryObj = getStateFromUrl(q.url)\n        return (\n          savedQueryObj &&\n          savedQueryObj.query === currentQuery &&\n          isEqual(savedQueryObj.params, currentParams)\n        )\n      })\n\n      if (isDuplicate) {\n        const duplicateQuery = queries?.find((q) => {\n          if (q._key === query._key) return false\n          const savedQueryObj = getStateFromUrl(q.url)\n          return (\n            savedQueryObj &&\n            savedQueryObj.query === currentQuery &&\n            isEqual(savedQueryObj.params, currentParams)\n          )\n        })\n        toast.push({\n          closable: true,\n          status: 'warning',\n          title: t('save-query.already-saved'),\n          description: `${duplicateQuery?.title} - ${formatDate.format(\n            new Date(duplicateQuery?.savedAt || ''),\n          )}`,\n        })\n        return\n      }\n\n      try {\n        await updateQuery({\n          ...query,\n          url: newUrl,\n          savedAt: new Date().toISOString(),\n        })\n        setSelectedUrl(newUrl)\n        toast.push({\n          closable: true,\n          status: 'success',\n          title: t('save-query.success'),\n        })\n      } catch (err) {\n        toast.push({\n          closable: true,\n          status: 'error',\n          title: t('save-query.error'),\n          description: err.message,\n        })\n      }\n    },\n    [\n      currentQuery,\n      currentParams,\n      formatDate,\n      generateUrl,\n      updateQuery,\n      toast,\n      t,\n      queries,\n      getStateFromUrl,\n    ],\n  )\n\n  const filteredQueries = queries?.filter((q) => {\n    return q?.title?.toLowerCase().includes(searchQuery.toLowerCase())\n  })\n\n  return (\n    <ScrollContainer>\n      <FixedHeader space={3}>\n        <Flex padding={3} paddingTop={4} paddingBottom={0} justify=\"space-between\" align=\"center\">\n          <Text weight=\"semibold\" style={{textTransform: 'capitalize'}} size={4}>\n            {t('label.saved-queries')}\n          </Text>\n          <Button\n            label={t('action.save-query')}\n            icon={AddIcon}\n            disabled={saving}\n            onClick={handleSave}\n            mode=\"bleed\"\n          />\n        </Flex>\n        <Box padding={3} paddingTop={0}>\n          <TextInput\n            placeholder={t('label.search-queries')}\n            icon={SearchIcon}\n            value={searchQuery}\n            onChange={(event) => setSearchQuery(event.currentTarget.value)}\n          />\n        </Box>\n      </FixedHeader>\n      <Stack paddingY={3}>\n        {filteredQueries?.map((q) => {\n          const queryObj = getStateFromUrl(q.url)\n          const isSelected = selectedUrl === q.url\n\n          // Compare against current live state\n          const areQueriesEqual =\n            queryObj && currentQuery === queryObj.query && isEqual(currentParams, queryObj.params)\n\n          const isEdited = isSelected && !areQueriesEqual\n          return (\n            <Card\n              key={q._key}\n              width={'fill'}\n              padding={4}\n              border\n              tone={isSelected ? 'positive' : 'default'}\n              onClick={() => {\n                setSelectedUrl(q.url) // Update the selected query immediately\n                const parsedUrl = getStateFromUrl(q.url)\n                if (parsedUrl) {\n                  setStateFromParsedUrl(parsedUrl)\n                }\n              }}\n              style={{position: 'relative'}}\n            >\n              <Stack space={3}>\n                <Flex justify=\"space-between\" align={'center'}>\n                  <Flex align=\"center\" gap={2} paddingRight={1}>\n                    {editingKey === q._key ? (\n                      <TextInput\n                        value={editingTitle}\n                        onChange={(event) => setEditingTitle(event.currentTarget.value)}\n                        onKeyDown={(event) => {\n                          if (event.key === 'Enter') {\n                            handleTitleSave(q, editingTitle)\n                          } else if (event.key === 'Escape') {\n                            setEditingKey(null)\n                          }\n                        }}\n                        onBlur={() => handleTitleSave(q, editingTitle)}\n                        autoFocus\n                        style={{maxWidth: '170px', height: '24px'}}\n                      />\n                    ) : (\n                      <Text\n                        weight=\"bold\"\n                        size={3}\n                        textOverflow=\"ellipsis\"\n                        style={{maxWidth: '170px', cursor: 'pointer', padding: '4px 0'}}\n                        title={\n                          optimisticTitles[q._key] ||\n                          q.title ||\n                          q._key.slice(q._key.length - 5, q._key.length)\n                        }\n                        onClick={() => {\n                          setEditingKey(q._key)\n                          setEditingTitle(q.title || q._key.slice(0, 5))\n                        }}\n                      >\n                        {optimisticTitles[q._key] ||\n                          q.title ||\n                          q._key.slice(q._key.length - 5, q._key.length)}\n                      </Text>\n                    )}\n                    {isEdited && (\n                      <Box\n                        style={{\n                          width: '6px',\n                          height: '6px',\n                          borderRadius: '50%',\n                          backgroundColor: 'var(--card-focus-ring-color)',\n                        }}\n                      />\n                    )}\n                  </Flex>\n                  <MenuButton\n                    button={<ContextMenuButton />}\n                    id={`${q._key}-menu`}\n                    menu={\n                      <Menu\n                      // style={{background: 'white', backgroundColor: 'white', borderRadius: '11%'}}\n                      >\n                        <MenuItem\n                          tone=\"critical\"\n                          padding={3}\n                          icon={TrashIcon}\n                          text={t('action.delete')}\n                          onClick={(event) => {\n                            event.stopPropagation()\n                            deleteQuery(q._key)\n                          }}\n                        />\n                      </Menu>\n                    }\n                    popover={{portal: true, placement: 'bottom-end', tone: 'default'}}\n                  />\n                </Flex>\n\n                <Code muted>{queryObj?.query.split('{')[0]}</Code>\n\n                <Flex align=\"center\" gap={1}>\n                  <Text size={1} muted>\n                    {formatDate.format(new Date(q.savedAt || ''))}\n                  </Text>\n                </Flex>\n\n                {isEdited && (\n                  <Button\n                    mode=\"ghost\"\n                    tone=\"default\"\n                    size={1}\n                    padding={2}\n                    style={{\n                      height: '24px',\n                      position: 'absolute',\n                      right: '16px',\n                      bottom: '16px',\n                      fontSize: '12px',\n                    }}\n                    text={t('action.update')}\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      handleUpdate(q)\n                    }}\n                  />\n                )}\n              </Stack>\n            </Card>\n          )\n        })}\n      </Stack>\n    </ScrollContainer>\n  )\n}\n", "import {useEffect, useState} from 'react'\n\ninterface PaneSizeOptions {\n  defaultSize: number\n  size?: number\n  allowResize: boolean\n  minSize: number\n  maxSize: number\n}\nfunction narrowBreakpoint(): boolean {\n  return typeof window !== 'undefined' && window.innerWidth > 600\n}\n\nfunction calculatePaneSizeOptions(height: number | undefined): PaneSizeOptions {\n  let rootHeight = height\n\n  if (!rootHeight) {\n    // Initial root height without header\n    rootHeight =\n      typeof window !== 'undefined' && typeof document !== 'undefined'\n        ? document.body.getBoundingClientRect().height - 60\n        : 0\n  }\n  return {\n    defaultSize: rootHeight / (narrowBreakpoint() ? 2 : 1),\n    size: rootHeight > 550 ? undefined : rootHeight * 0.4,\n    allowResize: rootHeight > 550,\n    minSize: Math.min(170, Math.max(170, rootHeight / 2)),\n    maxSize: rootHeight > 650 ? rootHeight * 0.7 : rootHeight * 0.6,\n  }\n}\n\nexport function usePaneSize({\n  visionRootRef,\n}: {\n  visionRootRef: React.RefObject<HTMLDivElement | null>\n}) {\n  const [isNarrowBreakpoint, setIsNarrowBreakpoint] = useState(() => narrowBreakpoint())\n  const [paneSizeOptions, setPaneSizeOptions] = useState<PaneSizeOptions>(() =>\n    calculatePaneSizeOptions(undefined),\n  )\n\n  useEffect(() => {\n    if (!visionRootRef.current) {\n      return undefined\n    }\n    const handleResize = (entries: ResizeObserverEntry[]) => {\n      setIsNarrowBreakpoint(narrowBreakpoint())\n      const entry = entries?.[0]\n      if (entry) {\n        setPaneSizeOptions(calculatePaneSizeOptions(entry.contentRect.height))\n      }\n    }\n    const resizeObserver = new ResizeObserver(handleResize)\n    resizeObserver.observe(visionRootRef.current)\n\n    return () => {\n      resizeObserver.disconnect()\n    }\n  }, [visionRootRef])\n\n  return {paneSizeOptions, isNarrowBreakpoint}\n}\n", "import {PlayIcon, StopIcon} from '@sanity/icons'\nimport {Box, Button, Card, Flex, Hotkeys, Text, Tooltip} from '@sanity/ui'\nimport {useTranslation} from 'sanity'\n\nimport {visionLocaleNamespace} from '../i18n'\nimport {ControlsContainer} from './VisionGui.styled'\n\nexport interface VisionGuiControlsProps {\n  hasValidParams: boolean\n  queryInProgress: boolean\n  listenInProgress: boolean\n  onQueryExecution: () => void\n  onListenExecution: () => void\n}\n\n/**\n * Vision GUI controls\n * To handle query and listen execution.\n */\nexport function VisionGuiControls({\n  hasValidParams,\n  listenInProgress,\n  queryInProgress,\n  onQueryExecution,\n  onListenExecution,\n}: VisionGuiControlsProps) {\n  const {t} = useTranslation(visionLocaleNamespace)\n\n  return (\n    <ControlsContainer>\n      <Card padding={3} paddingX={3}>\n        <Tooltip\n          content={\n            <Card radius={4}>\n              <Text size={1} muted>\n                {t('params.error.params-invalid-json')}\n              </Text>\n            </Card>\n          }\n          placement=\"top\"\n          disabled={hasValidParams}\n          portal\n        >\n          <Flex justify=\"space-evenly\">\n            <Box flex={1}>\n              <Tooltip\n                content={\n                  <Card radius={4}>\n                    <Hotkeys keys={['Ctrl', 'Enter']} />\n                  </Card>\n                }\n                placement=\"top\"\n                portal\n              >\n                <Button\n                  width=\"fill\"\n                  onClick={onQueryExecution}\n                  type=\"button\"\n                  icon={queryInProgress ? StopIcon : PlayIcon}\n                  disabled={listenInProgress || !hasValidParams}\n                  tone={queryInProgress ? 'positive' : 'primary'}\n                  text={queryInProgress ? t('action.query-cancel') : t('action.query-execute')}\n                />\n              </Tooltip>\n            </Box>\n            <Box flex={1} marginLeft={3}>\n              <Button\n                width=\"fill\"\n                onClick={onListenExecution}\n                type=\"button\"\n                icon={listenInProgress ? StopIcon : PlayIcon}\n                text={listenInProgress ? t('action.listen-cancel') : t('action.listen-execute')}\n                mode=\"ghost\"\n                disabled={!hasValidParams}\n                tone={listenInProgress ? 'positive' : 'default'}\n              />\n            </Box>\n          </Flex>\n        </Tooltip>\n      </Card>\n    </ControlsContainer>\n  )\n}\n", "import {Box} from '@sanity/ui'\nimport {styled} from 'styled-components'\n\nexport const PerspectivePopoverContent = styled(Box)`\n  /* This limits the width of the popover content */\n  max-width: 240px;\n`\n\nexport const PerspectivePopoverLink = styled.a`\n  cursor: pointer;\n  margin-right: auto;\n`\n", "import {HelpCircleIcon} from '@sanity/icons'\nimport {\n  Badge,\n  Box,\n  Button,\n  Card,\n  type CardTone,\n  Inline,\n  Popover,\n  Stack,\n  Text,\n  useClickOutsideEvent,\n} from '@sanity/ui'\nimport {useCallback, useRef, useState} from 'react'\nimport {Translate, useTranslation} from 'sanity'\nimport {styled} from 'styled-components'\n\nimport {visionLocaleNamespace} from '../i18n'\nimport {PerspectivePopoverContent, PerspectivePopoverLink} from './PerspectivePopover.styled'\n\nconst Dot = styled.div<{$tone: CardTone}>`\n  width: 4px;\n  height: 4px;\n  border-radius: 3px;\n  box-shadow: 0 0 0 1px var(--card-bg-color);\n  background-color: ${({$tone}) => `var(--card-badge-${$tone}-dot-color)`};\n`\n\nconst SHOW_DEFAULT_PERSPECTIVE_NOTIFICATION = false\n\nexport function PerspectivePopover() {\n  const [open, setOpen] = useState(false)\n  const buttonRef = useRef<HTMLButtonElement | null>(null)\n  const popoverRef = useRef<HTMLDivElement | null>(null)\n\n  const handleClick = useCallback(() => setOpen((o) => !o), [])\n\n  const {t} = useTranslation(visionLocaleNamespace)\n\n  useClickOutsideEvent(\n    () => setOpen(false),\n    () => [buttonRef.current, popoverRef.current],\n  )\n\n  return (\n    <Popover\n      content={\n        <PerspectivePopoverContent>\n          <Stack space={4}>\n            <Inline space={2}>\n              <Text weight=\"medium\">{t('settings.perspectives.title')}</Text>\n            </Inline>\n\n            <Card>\n              <Text muted>{t('settings.perspectives.description')}</Text>\n            </Card>\n            <Card>\n              <Stack space={2}>\n                <Box>\n                  <Badge tone=\"primary\">{t('label.new')}</Badge>\n                </Box>\n                <Text muted>\n                  <Translate\n                    t={t}\n                    i18nKey=\"settings.perspective.preview-drafts-renamed-to-drafts.description\"\n                  />\n                </Text>\n              </Stack>\n            </Card>\n            {SHOW_DEFAULT_PERSPECTIVE_NOTIFICATION ? (\n              <Card>\n                <Badge tone=\"caution\">{t('label.new')}</Badge>\n                <Card>\n                  <Text muted>\n                    <Translate t={t} i18nKey=\"settings.perspectives.new-default.description\" />\n                  </Text>\n                </Card>\n              </Card>\n            ) : null}\n\n            <Card>\n              <Text>\n                <PerspectivePopoverLink href=\"https://sanity.io/docs/perspectives\" target=\"_blank\">\n                  {t('settings.perspectives.action.docs-link')} &rarr;\n                </PerspectivePopoverLink>\n              </Text>\n            </Card>\n          </Stack>\n        </PerspectivePopoverContent>\n      }\n      placement=\"bottom-start\"\n      portal\n      padding={3}\n      ref={popoverRef}\n      open={open}\n    >\n      <Button\n        icon={HelpCircleIcon}\n        mode=\"bleed\"\n        padding={2}\n        paddingRight={1}\n        tone=\"primary\"\n        fontSize={1}\n        ref={buttonRef}\n        onClick={handleClick}\n        selected={open}\n      >\n        <Dot $tone={SHOW_DEFAULT_PERSPECTIVE_NOTIFICATION ? 'caution' : 'primary'} />\n      </Button>\n    </Popover>\n  )\n}\n", "import {CopyIcon} from '@sanity/icons'\nimport {Box, <PERSON>ton, Card, Flex, Grid, Inline, Select, Stack, TextInput, Tooltip} from '@sanity/ui'\nimport {\n  type ChangeEvent,\n  type ComponentType,\n  Fragment,\n  type RefObject,\n  useCallback,\n  useMemo,\n  useRef,\n} from 'react'\nimport {type PerspectiveContextValue, type TFunction, usePerspective, useTranslation} from 'sanity'\n\nimport {API_VERSIONS} from '../apiVersions'\nimport {visionLocaleNamespace} from '../i18n'\nimport {\n  hasPinnedPerspective,\n  SUPPORTED_PERSPECTIVES,\n  type SupportedPerspective,\n} from '../perspectives'\nimport {PerspectivePopover} from './PerspectivePopover'\nimport {Header, QueryCopyLink, StyledLabel} from './VisionGui.styled'\n\nconst PinnedReleasePerspectiveOption: ComponentType<{\n  pinnedPerspective: PerspectiveContextValue\n  t: TFunction\n}> = ({pinnedPerspective, t}) => {\n  const name =\n    typeof pinnedPerspective.selectedPerspective === 'object'\n      ? pinnedPerspective.selectedPerspective.metadata.title\n      : pinnedPerspective.selectedPerspectiveName\n\n  const label = hasPinnedPerspective(pinnedPerspective)\n    ? `(${t('settings.perspectives.pinned-release-label')})`\n    : t('settings.perspectives.pinned-release-label')\n\n  const text = useMemo(\n    () => [name, label].filter((value) => typeof value !== 'undefined').join(' '),\n    [label, name],\n  )\n\n  return (\n    <option value=\"pinnedRelease\" disabled={!hasPinnedPerspective(pinnedPerspective)}>\n      {text}\n    </option>\n  )\n}\n\nexport interface VisionGuiHeaderProps {\n  onChangeDataset: (evt: ChangeEvent<HTMLSelectElement>) => void\n  dataset: string\n  customApiVersion: string | false\n  apiVersion: string\n  onChangeApiVersion: (evt: ChangeEvent<HTMLSelectElement>) => void\n  datasets: string[]\n  customApiVersionElementRef: RefObject<HTMLInputElement | null>\n  onCustomApiVersionChange: (evt: ChangeEvent<HTMLInputElement>) => void\n  isValidApiVersion: boolean\n  onChangePerspective: (evt: ChangeEvent<HTMLSelectElement>) => void\n  url?: string\n  perspective?: SupportedPerspective\n}\n\nexport function VisionGuiHeader({\n  onChangeDataset,\n  dataset,\n  customApiVersion,\n  apiVersion,\n  onChangeApiVersion,\n  datasets,\n  customApiVersionElementRef,\n  onCustomApiVersionChange,\n  isValidApiVersion,\n  onChangePerspective,\n  url,\n  perspective,\n}: VisionGuiHeaderProps) {\n  const pinnedPerspective = usePerspective()\n  const {t} = useTranslation(visionLocaleNamespace)\n  const operationUrlElement = useRef<HTMLInputElement | null>(null)\n  const handleCopyUrl = useCallback(() => {\n    const el = operationUrlElement.current\n    if (!el) return\n\n    try {\n      el.select()\n      document.execCommand('copy')\n    } catch (err) {\n      console.error('Unable to copy to clipboard :(')\n    }\n  }, [])\n\n  return (\n    <Header paddingX={3} paddingY={2}>\n      <Grid columns={[1, 4, 8, 12]}>\n        {/* Dataset selector */}\n        <Box padding={1} column={2}>\n          <Stack>\n            <Card paddingTop={2} paddingBottom={3}>\n              <StyledLabel>{t('settings.dataset-label')}</StyledLabel>\n            </Card>\n            <Select value={dataset} onChange={onChangeDataset}>\n              {datasets.map((ds: string) => (\n                <option key={ds}>{ds}</option>\n              ))}\n            </Select>\n          </Stack>\n        </Box>\n\n        {/* API version selector */}\n        <Box padding={1} column={2}>\n          <Stack>\n            <Card paddingTop={2} paddingBottom={3}>\n              <StyledLabel>{t('settings.api-version-label')}</StyledLabel>\n            </Card>\n            <Select\n              data-testid=\"api-version-selector\"\n              value={customApiVersion === false ? apiVersion : 'other'}\n              onChange={onChangeApiVersion}\n            >\n              {API_VERSIONS.map((version) => (\n                <option key={version}>{version}</option>\n              ))}\n              <option key=\"other\" value=\"other\">\n                {t('settings.other-api-version-label')}\n              </option>\n            </Select>\n          </Stack>\n        </Box>\n\n        {/* Custom API version input */}\n        {customApiVersion !== false && (\n          <Box padding={1} column={2}>\n            <Stack>\n              <Card paddingTop={2} paddingBottom={3}>\n                <StyledLabel textOverflow=\"ellipsis\">\n                  {t('settings.custom-api-version-label')}\n                </StyledLabel>\n              </Card>\n\n              <TextInput\n                ref={customApiVersionElementRef}\n                value={customApiVersion}\n                onChange={onCustomApiVersionChange}\n                customValidity={\n                  isValidApiVersion ? undefined : t('settings.error.invalid-api-version')\n                }\n                maxLength={11}\n              />\n            </Stack>\n          </Box>\n        )}\n\n        {/* Perspective selector */}\n        <Box padding={1} column={2}>\n          <Stack>\n            <Card paddingBottom={1}>\n              <Inline space={1}>\n                <Box>\n                  <StyledLabel>{t('settings.perspective-label')}</StyledLabel>\n                </Box>\n\n                <Box>\n                  <PerspectivePopover />\n                </Box>\n              </Inline>\n            </Card>\n            <Select value={perspective || 'default'} onChange={onChangePerspective}>\n              {SUPPORTED_PERSPECTIVES.map((perspectiveName) => {\n                if (perspectiveName === 'pinnedRelease') {\n                  return (\n                    <Fragment key=\"pinnedRelease\">\n                      <PinnedReleasePerspectiveOption pinnedPerspective={pinnedPerspective} t={t} />\n                      <option key=\"default\" value=\"default\">\n                        {t('settings.perspectives.default')}\n                      </option>\n                      <hr />\n                    </Fragment>\n                  )\n                }\n                return <option key={perspectiveName}>{perspectiveName}</option>\n              })}\n            </Select>\n          </Stack>\n        </Box>\n\n        {/* Query URL (for copying) */}\n        {typeof url === 'string' ? (\n          <Box padding={1} flex={1} column={customApiVersion === false ? 6 : 4}>\n            <Stack>\n              <Card paddingTop={2} paddingBottom={3}>\n                <StyledLabel>\n                  {t('query.url')}&nbsp;\n                  <QueryCopyLink onClick={handleCopyUrl}>\n                    [{t('action.copy-url-to-clipboard')}]\n                  </QueryCopyLink>\n                </StyledLabel>\n              </Card>\n              <Flex flex={1} gap={1}>\n                <Box flex={1}>\n                  <TextInput readOnly type=\"url\" ref={operationUrlElement} value={url} />\n                </Box>\n                <Tooltip content={t('action.copy-url-to-clipboard')}>\n                  <Button\n                    aria-label={t('action.copy-url-to-clipboard')}\n                    type=\"button\"\n                    mode=\"ghost\"\n                    icon={CopyIcon}\n                    onClick={handleCopyUrl}\n                  />\n                </Tooltip>\n              </Flex>\n            </Stack>\n          </Box>\n        ) : (\n          <Box flex={1} />\n        )}\n      </Grid>\n    </Header>\n  )\n}\n", "import {json2csv} from 'json-2-csv'\n\nfunction getBlobUrl(content: string, mimeType: string): string {\n  return URL.createObjectURL(\n    new Blob([content], {\n      type: mimeType,\n    }),\n  )\n}\n\nfunction getMemoizedBlobUrlResolver(mimeType: string, stringEncoder: (input: any) => string) {\n  return (() => {\n    let prevResult = ''\n    let prevContent = ''\n    return (input: unknown) => {\n      const content = stringEncoder(input)\n      if (typeof content !== 'string' || content === '') {\n        return undefined\n      }\n\n      if (content === prevContent) {\n        return prevResult\n      }\n\n      prevContent = content\n      if (prevResult) {\n        URL.revokeObjectURL(prevResult)\n      }\n\n      prevResult = getBlobUrl(content, mimeType)\n      return prevResult\n    }\n  })()\n}\n\nexport const getJsonBlobUrl = getMemoizedBlobUrlResolver('application/json', (input) =>\n  JSON.stringify(input, null, 2),\n)\n\nexport const getCsvBlobUrl = getMemoizedBlobUrlResolver('text/csv', (input) => {\n  return json2csv(Array.isArray(input) ? input : [input]).trim()\n})\n", "import {Code} from '@sanity/ui'\nimport {styled} from 'styled-components'\n\nexport const ErrorCode = styled(Code)`\n  color: ${({theme}) => theme.sanity.color.muted.critical.enabled.fg};\n`\n", "import {Box} from '@sanity/ui'\nimport {useTranslation} from 'sanity'\n\nimport {visionLocaleNamespace} from '../i18n'\nimport {ErrorCode} from './QueryErrorDialog.styled'\n\ninterface ContentLakeQueryError {\n  details: {\n    query: string\n    start: number\n    end: number\n\n    lineNumber?: number\n    column?: number\n  }\n}\n\nexport function QueryErrorDetails({error}: {error: ContentLakeQueryError | Error}) {\n  const {t} = useTranslation(visionLocaleNamespace)\n\n  if (!('details' in error)) {\n    return null\n  }\n\n  const details = {...error.details, ...mapToLegacyDetails(error.details)}\n  if (!details.line) {\n    return null\n  }\n\n  return (\n    <div>\n      <ErrorCode size={1}>{`${details.line}\\n${dashLine(\n        details.column,\n        details.columnEnd,\n      )}`}</ErrorCode>\n      <Box marginTop={4}>\n        <ErrorCode size={1}>{`${t('query.error.line')}:   ${details.lineNumber}\\n${t(\n          'query.error.column',\n        )}: ${details.column}`}</ErrorCode>\n      </Box>\n    </div>\n  )\n}\n\nfunction mapToLegacyDetails(details: ContentLakeQueryError['details']) {\n  if (!details || typeof details.query !== 'string' || typeof details.start !== 'number') {\n    return {}\n  }\n\n  const {query, start, end} = details\n  const lineStart = query.slice(0, start).lastIndexOf('\\n') + 1\n  const lineNumber = (query.slice(0, lineStart).match(/\\n/g) || []).length\n  const line = query.slice(lineStart, query.indexOf('\\n', lineStart))\n  const column = start - lineStart\n  const columnEnd = typeof end === 'number' ? end - lineStart : undefined\n\n  return {line, lineNumber, column, columnEnd}\n}\n\nfunction dashLine(column: number, columnEnd: number | undefined): string {\n  const line = '-'.repeat(column)\n  const hats = `^`.repeat(columnEnd ? columnEnd - column : 1)\n  return `${line}${hats}`\n}\n", "import {Stack} from '@sanity/ui'\n\nimport {QueryErrorDetails} from './QueryErrorDetails'\nimport {ErrorCode} from './QueryErrorDialog.styled'\n\nexport function QueryErrorDialog(props: {error: Error}) {\n  return (\n    <Stack space={5} marginTop={2}>\n      <ErrorCode size={1}>{props.error.message}</ErrorCode>\n      <QueryErrorDetails error={props.error} />\n    </Stack>\n  )\n}\n", "import {rem, type Theme} from '@sanity/ui'\nimport {css, styled} from 'styled-components'\n\nexport const ResultViewWrapper = styled.div<{theme: Theme}>(({theme}) => {\n  const {color, fonts, space} = theme.sanity\n\n  return css`\n    & .json-inspector,\n    & .json-inspector .json-inspector__selection {\n      font-family: ${fonts.code.family};\n      font-size: ${fonts.code.sizes[2].fontSize}px;\n      line-height: ${fonts.code.sizes[2].lineHeight}px;\n      color: var(--card-code-fg-color);\n    }\n\n    & .json-inspector .json-inspector__leaf {\n      padding-left: ${rem(space[4])};\n    }\n\n    & .json-inspector .json-inspector__leaf.json-inspector__leaf_root {\n      padding-top: ${rem(space[0])};\n      padding-left: 0;\n    }\n\n    & .json-inspector > .json-inspector__leaf_root > .json-inspector__line > .json-inspector__key {\n      display: none;\n    }\n\n    & .json-inspector .json-inspector__line {\n      display: block;\n      position: relative;\n      cursor: default;\n    }\n\n    & .json-inspector .json-inspector__line::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -200px;\n      right: -50px;\n      bottom: 0;\n      z-index: -1;\n      pointer-events: none;\n    }\n\n    & .json-inspector .json-inspector__line:hover::after {\n      background: var(--card-code-bg-color);\n    }\n\n    & .json-inspector .json-inspector__leaf_composite > .json-inspector__line {\n      cursor: pointer;\n    }\n\n    & .json-inspector .json-inspector__leaf_composite > .json-inspector__line::before {\n      content: '▸ ';\n      margin-left: calc(0px - ${rem(space[4])});\n      font-size: ${fonts.code.sizes[2].fontSize}px;\n      line-height: ${fonts.code.sizes[2].lineHeight}px;\n    }\n\n    &\n      .json-inspector\n      .json-inspector__leaf_expanded.json-inspector__leaf_composite\n      > .json-inspector__line::before {\n      content: '▾ ';\n      font-size: ${fonts.code.sizes[2].fontSize}px;\n      line-height: ${fonts.code.sizes[2].lineHeight}px;\n    }\n\n    & .json-inspector .json-inspector__radio,\n    & .json-inspector .json-inspector__flatpath {\n      display: none;\n    }\n\n    & .json-inspector .json-inspector__value {\n      margin-left: ${rem(space[4] / 2)};\n    }\n\n    &\n      .json-inspector\n      > .json-inspector__leaf_root\n      > .json-inspector__line\n      > .json-inspector__key\n      + .json-inspector__value {\n      margin: 0;\n    }\n\n    & .json-inspector .json-inspector__key {\n      color: ${color.syntax.property};\n    }\n\n    & .json-inspector .json-inspector__value_helper,\n    & .json-inspector .json-inspector__value_null {\n      color: ${color.syntax.constant};\n    }\n\n    & .json-inspector .json-inspector__not-found {\n      padding-top: ${rem(space[2])};\n    }\n\n    & .json-inspector .json-inspector__value_string {\n      color: ${color.syntax.string};\n      word-break: break-word;\n    }\n\n    & .json-inspector .json-inspector__value_boolean {\n      color: ${color.syntax.boolean};\n    }\n\n    & .json-inspector .json-inspector__value_number {\n      color: ${color.syntax.number};\n    }\n\n    & .json-inspector .json-inspector__show-original {\n      display: inline-block;\n      padding: 0 6px;\n      cursor: pointer;\n    }\n\n    & .json-inspector .json-inspector__show-original:hover {\n      color: inherit;\n    }\n\n    & .json-inspector .json-inspector__show-original::before {\n      content: '↔';\n    }\n\n    & .json-inspector .json-inspector__show-original:hover::after {\n      content: ' expand';\n    }\n  `\n})\n", "import {JsonInspector} from '@rexxars/react-json-inspector'\nimport {LinkIcon} from '@sanity/icons'\nimport {Code} from '@sanity/ui'\nimport LRU from 'quick-lru'\nimport {useDataset} from 'sanity'\nimport {IntentLink} from 'sanity/router'\n\nimport {ResultViewWrapper} from './ResultView.styled'\n\nconst lru = new LRU({maxSize: 50000})\n\nexport function ResultView(props: {data: unknown; datasetName: string}): React.JSX.Element {\n  const {data, datasetName} = props\n  const workspaceDataset = useDataset()\n\n  if (isRecord(data) || Array.isArray(data)) {\n    return (\n      <ResultViewWrapper>\n        <JsonInspector\n          data={data}\n          search={false}\n          isExpanded={isExpanded}\n          onClick={toggleExpanded}\n          interactiveLabel={workspaceDataset === datasetName ? DocumentEditLabel : undefined}\n        />\n      </ResultViewWrapper>\n    )\n  }\n\n  return <Code language=\"json\">{JSON.stringify(data)}</Code>\n}\n\nfunction DocumentEditLabel(props: {value: string; isKey: boolean; keypath: string}) {\n  if (props.isKey || (!props.keypath.endsWith('_id') && !props.keypath.endsWith('_ref'))) {\n    return null\n  }\n\n  return (\n    <IntentLink intent=\"edit\" params={{id: props.value}}>\n      <LinkIcon />\n    </IntentLink>\n  )\n}\n\nfunction isExpanded(keyPath: string, value: unknown): boolean {\n  const depthLimit = 4\n  const cached = lru.get(keyPath) as boolean | undefined\n\n  if (typeof cached === 'boolean') {\n    return cached\n  }\n\n  const segments = keyPath.split('.', depthLimit)\n  if (segments.length === depthLimit) {\n    return false\n  }\n\n  if (Array.isArray(value)) {\n    return true\n  }\n\n  return isRecord(value) && !segments.some((key) => isArrayKeyOverLimit(key))\n}\n\nfunction toggleExpanded(event: {path: string}): void {\n  const {path} = event\n  const current = lru.get(path)\n\n  if (current === undefined) {\n    // something is wrong\n    return\n  }\n\n  lru.set(path, !current)\n}\n\nfunction isRecord(value: unknown): value is Record<string, unknown> {\n  return value !== null && typeof value === 'object' && !Array.isArray(value)\n}\n\nconst numeric = /^\\d+$/\nfunction isArrayKeyOverLimit(segment: string, limit = 10) {\n  return numeric.test(segment) && parseInt(segment, 10) > limit\n}\n", "import {DocumentSheetIcon} from '@sanity/icons'\nimport {Button, Tooltip} from '@sanity/ui'\nimport {type MouseEvent} from 'react'\nimport {useTranslation} from 'sanity'\n\nimport {visionLocaleNamespace} from '../i18n'\n\ninterface SaveButtonProps {\n  blobUrl: string | undefined\n}\n\nfunction preventSave(evt: MouseEvent<HTMLButtonElement>) {\n  return evt.preventDefault()\n}\n\nexport function SaveCsvButton({blobUrl}: SaveButtonProps) {\n  const {t} = useTranslation(visionLocaleNamespace)\n  const isDisabled = !blobUrl\n\n  const button = (\n    <Button\n      as=\"a\"\n      disabled={isDisabled}\n      download={isDisabled ? undefined : 'query-result.csv'}\n      href={blobUrl}\n      icon={DocumentSheetIcon}\n      mode=\"ghost\"\n      onClick={isDisabled ? preventSave : undefined}\n      // eslint-disable-next-line @sanity/i18n/no-attribute-string-literals\n      text=\"CSV\" // String is a File extension\n      tone=\"default\"\n    />\n  )\n\n  return isDisabled ? (\n    <Tooltip content={t('result.save-result-as-csv.not-csv-encodable')} placement=\"top\">\n      {button}\n    </Tooltip>\n  ) : (\n    button\n  )\n}\n\nexport function SaveJsonButton({blobUrl}: SaveButtonProps) {\n  return (\n    <Button\n      as=\"a\"\n      download={'query-result.json'}\n      href={blobUrl}\n      icon={DocumentSheetIcon}\n      mode=\"ghost\"\n      // eslint-disable-next-line @sanity/i18n/no-attribute-string-literals\n      text=\"JSON\" // String is a File extension\n      tone=\"default\"\n    />\n  )\n}\n", "import {type MutationEvent} from '@sanity/client'\nimport {Box, Text} from '@sanity/ui'\nimport {Translate, useTranslation} from 'sanity'\n\nimport {visionLocaleNamespace} from '../i18n'\nimport {getCsvBlobUrl, getJsonBlobUrl} from '../util/getBlobUrl'\nimport {DelayedSpinner} from './DelayedSpinner'\nimport {QueryErrorDialog} from './QueryErrorDialog'\nimport {ResultView} from './ResultView'\nimport {SaveCsvButton, SaveJsonButton} from './SaveResultButtons'\nimport {\n  DownloadsCard,\n  InputBackgroundContainer,\n  Result,\n  ResultContainer,\n  ResultFooter,\n  ResultInnerContainer,\n  ResultOuterContainer,\n  SaveResultLabel,\n  StyledLabel,\n  TimingsCard,\n  TimingsTextContainer,\n} from './VisionGui.styled'\n\ninterface VisionGuiResultProps {\n  error?: Error | undefined\n  queryInProgress: boolean\n  queryResult?: unknown | undefined\n  listenInProgress: boolean\n  listenMutations: MutationEvent[]\n  dataset: string\n  queryTime: number | undefined\n  e2eTime: number | undefined\n}\n\nexport function VisionGuiResult({\n  error,\n  queryInProgress,\n  queryResult,\n  listenInProgress,\n  listenMutations,\n  dataset,\n  queryTime,\n  e2eTime,\n}: VisionGuiResultProps) {\n  const {t} = useTranslation(visionLocaleNamespace)\n  const hasResult = !error && !queryInProgress && typeof queryResult !== 'undefined'\n\n  const jsonUrl = hasResult ? getJsonBlobUrl(queryResult) : ''\n  const csvUrl = hasResult ? getCsvBlobUrl(queryResult) : ''\n\n  return (\n    <ResultOuterContainer direction=\"column\" data-testid=\"vision-result\">\n      <ResultInnerContainer flex={1}>\n        <ResultContainer\n          flex={1}\n          overflow=\"hidden\"\n          tone={error ? 'critical' : 'default'}\n          $isInvalid={Boolean(error)}\n        >\n          <Result overflow=\"auto\">\n            <InputBackgroundContainer>\n              <Box marginLeft={3}>\n                <StyledLabel muted>{t('result.label')}</StyledLabel>\n              </Box>\n            </InputBackgroundContainer>\n            <Box padding={3} paddingTop={5}>\n              {(queryInProgress || (listenInProgress && listenMutations.length === 0)) && (\n                <Box marginTop={3}>\n                  <DelayedSpinner />\n                </Box>\n              )}\n              {error && <QueryErrorDialog error={error} />}\n              {hasResult && <ResultView data={queryResult} datasetName={dataset} />}\n              {listenInProgress && listenMutations.length > 0 && (\n                <ResultView data={listenMutations} datasetName={dataset} />\n              )}\n            </Box>\n          </Result>\n        </ResultContainer>\n      </ResultInnerContainer>\n      {/* Execution time */}\n      <ResultFooter justify=\"space-between\" direction={['column', 'column', 'row']}>\n        <TimingsCard paddingX={4} paddingY={3} sizing=\"border\">\n          <TimingsTextContainer align=\"center\">\n            <Box>\n              <Text muted>\n                {t('result.execution-time-label')}:{' '}\n                {typeof queryTime === 'number'\n                  ? `${queryTime}ms`\n                  : t('result.timing-not-applicable')}\n              </Text>\n            </Box>\n            <Box marginLeft={4}>\n              <Text muted>\n                {t('result.end-to-end-time-label')}:{' '}\n                {typeof e2eTime === 'number' ? `${e2eTime}ms` : t('result.timing-not-applicable')}\n              </Text>\n            </Box>\n          </TimingsTextContainer>\n        </TimingsCard>\n\n        {hasResult && (\n          <DownloadsCard paddingX={4} paddingY={3} sizing=\"border\">\n            <SaveResultLabel muted>\n              <Translate\n                components={{\n                  SaveResultButtons: () => (\n                    <>\n                      <SaveJsonButton blobUrl={jsonUrl} />\n                      <SaveCsvButton blobUrl={csvUrl} />\n                    </>\n                  ),\n                }}\n                i18nKey=\"result.save-result-as-format\"\n                t={t}\n              />\n            </SaveResultLabel>\n          </DownloadsCard>\n        )}\n      </ResultFooter>\n    </ResultOuterContainer>\n  )\n}\n", "/* eslint-disable max-statements */\nimport {SplitPane} from '@rexxars/react-split-pane'\nimport {\n  type ClientPerspective,\n  type ListenEvent,\n  type MutationEvent,\n  type StackablePerspective,\n} from '@sanity/client'\nimport {ChevronLeftIcon, ChevronRightIcon} from '@sanity/icons'\nimport {Box, Button, Flex, useToast} from '@sanity/ui'\nimport {isHotkey} from 'is-hotkey-esm'\nimport {type ChangeEvent, useCallback, useEffect, useMemo, useRef, useState} from 'react'\nimport {useClient, usePerspective, useTranslation} from 'sanity'\nimport {useEffectEvent} from 'use-effect-event'\n\nimport {API_VERSIONS, DEFAULT_API_VERSION} from '../apiVersions'\nimport {VisionCodeMirror, type VisionCodeMirrorHandle} from '../codemirror/VisionCodeMirror'\nimport {visionLocaleNamespace} from '../i18n'\nimport {\n  getActivePerspective,\n  isSupportedPerspective,\n  isVirtualPerspective,\n  type SupportedPerspective,\n} from '../perspectives'\nimport {type VisionProps} from '../types'\nimport {encodeQueryString} from '../util/encodeQueryString'\nimport {getLocalStorage} from '../util/localStorage'\nimport {parseApiQueryString, type ParsedApiQueryString} from '../util/parseApiQueryString'\nimport {prefixApiVersion} from '../util/prefixApiVersion'\nimport {validateApiVersion} from '../util/validateApiVersion'\nimport {ParamsEditor, parseParams} from './ParamsEditor'\nimport {QueryRecall} from './QueryRecall'\nimport {usePaneSize} from './usePaneSize'\nimport {\n  InputBackgroundContainerLeft,\n  InputContainer,\n  Root,\n  SplitpaneContainer,\n  StyledLabel,\n} from './VisionGui.styled'\nimport {VisionGuiControls} from './VisionGuiControls'\nimport {VisionGuiHeader} from './VisionGuiHeader'\nimport {VisionGuiResult} from './VisionGuiResult'\n\nfunction nodeContains(node: Node, other: EventTarget | Node | null): boolean {\n  if (!node || !other) {\n    return false\n  }\n\n  // eslint-disable-next-line no-bitwise\n  return node === other || !!(node.compareDocumentPosition(other as Node) & 16)\n}\n\nconst sanityUrl =\n  /\\.(?:api|apicdn)\\.sanity\\.(?:io|work)\\/(vX|v1|v\\d{4}-\\d\\d-\\d\\d)\\/.*?(?:query|listen)\\/(.*?)\\?(.*)/\n\nconst isRunHotkey = (event: KeyboardEvent) =>\n  isHotkey('ctrl+enter', event) || isHotkey('mod+enter', event)\n\ninterface Subscription {\n  unsubscribe: () => void\n}\nexport interface Params {\n  raw: string\n  parsed: Record<string, unknown> | undefined\n  valid: boolean\n  error: string | undefined\n}\n\ninterface QueryExecutionOptions {\n  apiVersion?: string\n  dataset?: string\n  perspective?: SupportedPerspective\n  query?: string\n  params?: Record<string, unknown>\n}\n\ninterface VisionGuiProps extends VisionProps {\n  datasets: string[]\n  projectId: string | undefined\n  defaultDataset: string\n}\n\nexport interface ParsedUrlState {\n  query: string\n  params: Record<string, unknown>\n  rawParams: string\n  dataset: string\n  apiVersion: string\n  customApiVersion: string | false | undefined\n  perspective: SupportedPerspective\n  url: string\n}\n\nexport function VisionGui(props: VisionGuiProps) {\n  const {datasets, config, projectId, defaultDataset} = props\n  const toast = useToast()\n  const {t} = useTranslation(visionLocaleNamespace)\n  const {perspectiveStack} = usePerspective()\n\n  const defaultApiVersion = prefixApiVersion(`${config.defaultApiVersion}`)\n  const editorQueryRef = useRef<VisionCodeMirrorHandle>(null)\n  const editorParamsRef = useRef<VisionCodeMirrorHandle>(null)\n  const visionRootRef = useRef<HTMLDivElement | null>(null)\n  const customApiVersionElementRef = useRef<HTMLInputElement | null>(null)\n  const querySubscriptionRef = useRef<Subscription | undefined>(undefined)\n  const listenSubscriptionRef = useRef<Subscription | undefined>(undefined)\n\n  const [localStorage] = useState(() => getLocalStorage(projectId || 'default'))\n\n  const {storedDataset, storedApiVersion, storedQuery, storedParams, storedPerspective} =\n    useMemo(() => {\n      return {\n        storedDataset: localStorage.get('dataset', defaultDataset),\n        storedApiVersion: localStorage.get('apiVersion', defaultApiVersion),\n        storedQuery: localStorage.get('query', ''),\n        storedParams: localStorage.get('params', '{\\n  \\n}'),\n        storedPerspective: localStorage.get<SupportedPerspective | undefined>(\n          'perspective',\n          undefined,\n        ),\n      }\n    }, [defaultDataset, defaultApiVersion, localStorage])\n\n  const [dataset, setDataset] = useState<string>(() => {\n    if (datasets.includes(storedDataset)) {\n      return storedDataset\n    }\n    if (datasets.includes(defaultDataset)) {\n      return defaultDataset\n    }\n    return datasets[0]\n  })\n  const [apiVersion, setApiVersion] = useState<string>(() =>\n    API_VERSIONS.includes(storedApiVersion) ? storedApiVersion : DEFAULT_API_VERSION,\n  )\n  const [customApiVersion, setCustomApiVersion] = useState<string | false>(() =>\n    API_VERSIONS.includes(storedApiVersion) ? false : storedApiVersion,\n  )\n  const [perspective, setPerspectiveState] = useState<SupportedPerspective>(\n    storedPerspective || 'raw',\n  )\n  const isValidApiVersion = customApiVersion ? validateApiVersion(customApiVersion) : true\n\n  const [url, setUrl] = useState<string | undefined>(undefined)\n  const [query, setQuery] = useState<string>(() =>\n    typeof storedQuery === 'string' ? storedQuery : '',\n  )\n  const [params, setParams] = useState<Params>(() => parseParams(storedParams, t))\n  const [queryResult, setQueryResult] = useState<unknown | undefined>(undefined)\n  const [listenMutations, setListenMutations] = useState<MutationEvent[]>([])\n  const [error, setError] = useState<Error | undefined>(undefined)\n  const [queryTime, setQueryTime] = useState<number | undefined>(undefined)\n  const [e2eTime, setE2eTime] = useState<number | undefined>(undefined)\n  const [queryInProgress, setQueryInProgress] = useState<boolean>(false)\n  const [listenInProgress, setListenInProgress] = useState<boolean>(false)\n  const [isQueryRecallCollapsed, setIsQueryRecallCollapsed] = useState(false)\n\n  const {paneSizeOptions, isNarrowBreakpoint} = usePaneSize({visionRootRef})\n\n  // Client  with memoized initial value\n  const _client = useClient({\n    apiVersion: isValidApiVersion && customApiVersion ? customApiVersion : apiVersion,\n  })\n  const client = useMemo(() => {\n    return _client.withConfig({\n      apiVersion: isValidApiVersion && customApiVersion ? customApiVersion : apiVersion,\n      perspective: getActivePerspective({visionPerspective: perspective, perspectiveStack}),\n      dataset,\n      allowReconfigure: true,\n    })\n  }, [\n    perspectiveStack,\n    perspective,\n    customApiVersion,\n    apiVersion,\n    dataset,\n    _client,\n    isValidApiVersion,\n  ])\n\n  const cancelQuerySubscription = useCallback(() => {\n    if (!querySubscriptionRef.current) {\n      return\n    }\n    querySubscriptionRef.current.unsubscribe()\n    querySubscriptionRef.current = undefined\n  }, [])\n\n  const cancelListenerSubscription = useCallback(() => {\n    if (!listenSubscriptionRef.current) {\n      return\n    }\n    listenSubscriptionRef.current.unsubscribe()\n    listenSubscriptionRef.current = undefined\n  }, [])\n\n  const handleQueryExecution = useCallback(\n    (options?: QueryExecutionOptions) => {\n      if (queryInProgress) {\n        cancelQuerySubscription()\n        cancelListenerSubscription()\n        setQueryInProgress(false)\n        return\n      }\n\n      const context: Required<Omit<QueryExecutionOptions, 'params' | 'perspective'>> & {\n        params: Params\n        perspective: ClientPerspective | undefined\n      } = {\n        query: options?.query || query,\n        dataset: options?.dataset || dataset,\n        params: parseParams(JSON.stringify(options?.params || params.parsed, null, 2), t),\n        perspective: getActivePerspective({\n          visionPerspective: options?.perspective || perspective,\n          perspectiveStack,\n        }),\n        apiVersion:\n          options?.apiVersion ||\n          (customApiVersion && isValidApiVersion ? customApiVersion : apiVersion),\n      }\n\n      localStorage.set('query', context.query)\n      localStorage.set('params', context.params.raw)\n\n      cancelListenerSubscription()\n\n      setQueryInProgress(!context.params.error && Boolean(context.query))\n      setListenInProgress(false)\n      setListenMutations([])\n      setError(context.params.error ? new Error(context.params.error) : undefined)\n      setQueryResult(undefined)\n      setQueryTime(undefined)\n      setE2eTime(undefined)\n\n      if (context.params.error) {\n        return\n      }\n\n      const urlQueryOpts: Record<string, string | string[]> = {\n        perspective: context.perspective ?? [],\n      }\n\n      const ctxClient = client.withConfig({\n        apiVersion: context.apiVersion,\n        dataset: context.dataset,\n        perspective: context.perspective,\n      })\n\n      const newUrl = ctxClient.getUrl(\n        ctxClient.getDataUrl(\n          'query',\n          encodeQueryString(context.query, context.params.parsed, urlQueryOpts),\n        ),\n      )\n      setUrl(newUrl)\n\n      const queryStart = Date.now()\n\n      querySubscriptionRef.current = ctxClient.observable\n        .fetch(context.query, context.params.parsed, {filterResponse: false, tag: 'vision'})\n        .subscribe({\n          next: (res) => {\n            setQueryTime(res.ms)\n            setE2eTime(Date.now() - queryStart)\n            setQueryResult(res.result)\n            setQueryInProgress(false)\n            setError(undefined)\n          },\n          error: (err) => {\n            setError(err)\n            setQueryInProgress(false)\n          },\n        })\n    },\n    [\n      queryInProgress,\n      query,\n      dataset,\n      params.parsed,\n      t,\n      perspective,\n      perspectiveStack,\n      customApiVersion,\n      isValidApiVersion,\n      apiVersion,\n      localStorage,\n      cancelListenerSubscription,\n      client,\n      cancelQuerySubscription,\n    ],\n  )\n\n  const setPerspective = useCallback(\n    (newPerspective: string | undefined): void => {\n      if (newPerspective !== undefined && !isSupportedPerspective(newPerspective)) {\n        return\n      }\n\n      setPerspectiveState(newPerspective as SupportedPerspective)\n      localStorage.set('perspective', newPerspective)\n\n      handleQueryExecution({perspective: newPerspective})\n    },\n    [localStorage, handleQueryExecution],\n  )\n\n  const handleChangeDataset = useCallback(\n    (evt: ChangeEvent<HTMLSelectElement>) => {\n      const newDataset = evt.target.value\n      localStorage.set('dataset', newDataset)\n      setDataset(newDataset)\n      handleQueryExecution({dataset: newDataset})\n    },\n    [localStorage, handleQueryExecution],\n  )\n\n  const handleChangeApiVersion = useCallback(\n    (evt: ChangeEvent<HTMLSelectElement>) => {\n      const newApiVersion = evt.target.value\n      if (newApiVersion?.toLowerCase() === 'other') {\n        setCustomApiVersion('v')\n        customApiVersionElementRef.current?.focus()\n        return\n      }\n\n      setApiVersion(newApiVersion)\n      setCustomApiVersion(false)\n      localStorage.set('apiVersion', newApiVersion)\n      handleQueryExecution({apiVersion: newApiVersion})\n    },\n    [localStorage, handleQueryExecution],\n  )\n\n  // Handle custom API version change\n  const handleCustomApiVersionChange = useCallback(\n    (evt: ChangeEvent<HTMLInputElement>) => {\n      const newCustomApiVersion = evt.target.value || ''\n      setCustomApiVersion(newCustomApiVersion || 'v')\n\n      if (validateApiVersion(newCustomApiVersion)) {\n        setApiVersion(newCustomApiVersion)\n        localStorage.set('apiVersion', newCustomApiVersion)\n        handleQueryExecution({apiVersion: newCustomApiVersion})\n      }\n    },\n    [localStorage, handleQueryExecution],\n  )\n\n  // Handle perspective change\n  const handleChangePerspective = useCallback(\n    (evt: ChangeEvent<HTMLSelectElement>) => {\n      const newPerspective = evt.target.value\n      setPerspective(newPerspective === 'default' ? undefined : newPerspective)\n    },\n    [setPerspective],\n  )\n\n  const handleListenerEvent = useCallback((evt: ListenEvent<any>) => {\n    if (evt.type !== 'mutation') {\n      return\n    }\n\n    setListenMutations((prevMutations) =>\n      prevMutations.length === 50 ? [evt, ...prevMutations.slice(0, 49)] : [evt, ...prevMutations],\n    )\n  }, [])\n  const handleListenExecution = useCallback(() => {\n    if (listenInProgress) {\n      cancelListenerSubscription()\n      setListenInProgress(false)\n      return\n    }\n\n    const newUrl = client.getDataUrl('listen', encodeQueryString(query, params.parsed, {}))\n\n    const shouldExecute = !params.error && query.trim().length > 0\n\n    localStorage.set('query', query)\n    localStorage.set('params', params.raw)\n\n    cancelQuerySubscription()\n\n    setUrl(newUrl)\n    setListenMutations([])\n    setQueryInProgress(false)\n    setQueryResult(undefined)\n    setListenInProgress(shouldExecute)\n    setError(params.error ? new Error(params.error) : undefined)\n    setQueryTime(undefined)\n    setE2eTime(undefined)\n\n    if (!shouldExecute) {\n      return\n    }\n\n    listenSubscriptionRef.current = client\n      .listen(query, params.parsed, {events: ['mutation', 'welcome'], includeAllVersions: true})\n      .subscribe({\n        next: handleListenerEvent,\n        error: (err) => {\n          setError(err)\n          setListenInProgress(false)\n        },\n      })\n  }, [\n    listenInProgress,\n    params,\n    query,\n    localStorage,\n    cancelQuerySubscription,\n    handleListenerEvent,\n    cancelListenerSubscription,\n    client,\n  ])\n\n  const handleParamsChange = useCallback(\n    (value: Params) => {\n      setParams(value)\n      localStorage.set('params', value.raw)\n    },\n    [localStorage],\n  )\n\n  // Get object of state values from provided URL\n  const getStateFromUrl = useCallback(\n    (data: string): ParsedUrlState | null => {\n      const match = data.match(sanityUrl)\n      if (!match) {\n        return null\n      }\n\n      const [, usedApiVersion, usedDataset, urlQuery] = match\n\n      const qs = new URLSearchParams(urlQuery)\n      const parts: ParsedApiQueryString = parseApiQueryString(qs)\n      if (!parts) return null\n      let newApiVersion: string | undefined\n      let newCustomApiVersion: string | false | undefined\n\n      if (validateApiVersion(usedApiVersion)) {\n        if (API_VERSIONS.includes(usedApiVersion)) {\n          newApiVersion = usedApiVersion\n          newCustomApiVersion = false\n        } else {\n          newCustomApiVersion = usedApiVersion\n        }\n      }\n\n      const newPerspective =\n        isSupportedPerspective(parts.options.perspective) &&\n        !isVirtualPerspective(parts.options.perspective)\n          ? parts.options.perspective\n          : undefined\n\n      if (\n        newPerspective &&\n        (!isSupportedPerspective(parts.options.perspective) ||\n          isVirtualPerspective(parts.options.perspective))\n      ) {\n        toast.push({\n          closable: true,\n          id: 'vision-paste-unsupported-perspective',\n          status: 'warning',\n          title: 'Perspective in pasted url is currently not supported. Falling back to \"raw\"',\n        })\n      }\n\n      return {\n        query: parts.query,\n        params: parts.params,\n        rawParams: JSON.stringify(parts.params, null, 2),\n        dataset: datasets.includes(usedDataset) ? usedDataset : dataset,\n        apiVersion: newApiVersion || apiVersion,\n        customApiVersion: newCustomApiVersion,\n        perspective: newPerspective || perspective,\n        url: data,\n      }\n    },\n    [datasets, dataset, apiVersion, perspective, toast],\n  )\n\n  // Use state object from parsed URL to update state\n  const setStateFromParsedUrl = useCallback(\n    (parsedUrlObj: ParsedUrlState) => {\n      // Update state with pasted values\n      setDataset(parsedUrlObj.dataset)\n      setQuery(parsedUrlObj.query)\n      setParams({\n        parsed: parsedUrlObj.params,\n        raw: parsedUrlObj.rawParams,\n        valid: true,\n        error: undefined,\n      })\n      setApiVersion(parsedUrlObj.apiVersion)\n      if (parsedUrlObj.customApiVersion) {\n        setCustomApiVersion(parsedUrlObj.customApiVersion)\n      }\n      setPerspectiveState(parsedUrlObj.perspective)\n      setUrl(parsedUrlObj.url)\n      // Update the codemirror editor content\n      editorQueryRef.current?.resetEditorContent(parsedUrlObj.query)\n      editorParamsRef.current?.resetEditorContent(parsedUrlObj.rawParams)\n\n      // Update localStorage and client config\n      localStorage.merge({\n        query: parsedUrlObj.query,\n        params: parsedUrlObj.rawParams,\n        dataset: parsedUrlObj.dataset,\n        apiVersion: parsedUrlObj.customApiVersion || parsedUrlObj.apiVersion,\n        perspective: parsedUrlObj.perspective,\n      })\n\n      // Execute query with new values\n      handleQueryExecution(parsedUrlObj)\n    },\n    [localStorage, handleQueryExecution],\n  )\n\n  const handlePaste = useCallback(\n    (evt: ClipboardEvent) => {\n      if (!evt.clipboardData) {\n        return\n      }\n\n      const data = evt.clipboardData.getData('text/plain')\n      evt.preventDefault()\n      const urlState = getStateFromUrl(data)\n      if (urlState) {\n        setStateFromParsedUrl(urlState)\n        toast.push({\n          closable: true,\n          id: 'vision-paste',\n          status: 'info',\n          title: 'Parsed URL to query',\n        })\n      }\n    },\n    [getStateFromUrl, setStateFromParsedUrl, toast],\n  )\n\n  const handleKeyDown = useCallback(\n    (event: KeyboardEvent) => {\n      const isWithinRoot =\n        visionRootRef.current && nodeContains(visionRootRef.current, event.target)\n      if (isRunHotkey(event) && isWithinRoot && params.valid) {\n        handleQueryExecution()\n        event.preventDefault()\n        event.stopPropagation()\n      }\n    },\n    [params.valid, handleQueryExecution],\n  )\n\n  useEffect(() => {\n    window.document.addEventListener('paste', handlePaste)\n    window.document.addEventListener('keydown', handleKeyDown)\n\n    return () => {\n      window.document.removeEventListener('paste', handlePaste)\n      window.document.removeEventListener('keydown', handleKeyDown)\n    }\n  }, [handleKeyDown, handlePaste])\n\n  useEffect(() => {\n    return () => {\n      cancelQuerySubscription()\n      cancelListenerSubscription()\n    }\n  }, [cancelQuerySubscription, cancelListenerSubscription])\n\n  const handleStudioPerspectiveChange = useEffectEvent((stack: StackablePerspective[]) => {\n    if (stack.length > 0) {\n      setPerspective('pinnedRelease')\n    }\n  })\n  // Handle pinned perspective changes\n  useEffect(() => {\n    handleStudioPerspectiveChange(perspectiveStack)\n  }, [perspectiveStack])\n\n  const generateUrl = useCallback(\n    (queryString: string, queryParams: Record<string, unknown>) => {\n      const urlQueryOpts: Record<string, string | string[]> = {\n        perspective: getActivePerspective({visionPerspective: perspective, perspectiveStack}) ?? [],\n      }\n      return client.getUrl(\n        client.getDataUrl('query', encodeQueryString(queryString, queryParams, urlQueryOpts)),\n      )\n    },\n    [client, perspective, perspectiveStack],\n  )\n\n  return (\n    <Root\n      direction=\"column\"\n      height=\"fill\"\n      ref={visionRootRef}\n      sizing=\"border\"\n      overflow=\"hidden\"\n      data-testid=\"vision-root\"\n    >\n      <VisionGuiHeader\n        apiVersion={apiVersion}\n        customApiVersion={customApiVersion}\n        dataset={dataset}\n        datasets={datasets}\n        onChangeDataset={handleChangeDataset}\n        onChangeApiVersion={handleChangeApiVersion}\n        customApiVersionElementRef={customApiVersionElementRef}\n        onCustomApiVersionChange={handleCustomApiVersionChange}\n        isValidApiVersion={isValidApiVersion}\n        onChangePerspective={handleChangePerspective}\n        url={url}\n        perspective={perspective}\n      />\n\n      <SplitpaneContainer flex=\"auto\">\n        <SplitPane\n          minSize={800}\n          defaultSize={window.innerWidth - 275}\n          size={isQueryRecallCollapsed ? window.innerWidth : window.innerWidth - 275}\n          maxSize={-225}\n          primary=\"first\"\n        >\n          <Box height=\"stretch\" flex={1}>\n            <SplitPane\n              className=\"sidebarPanes\"\n              // eslint-disable-next-line @sanity/i18n/no-attribute-string-literals\n              split={isNarrowBreakpoint ? 'vertical' : 'horizontal'}\n              minSize={300}\n            >\n              <Box height=\"stretch\" flex={1}>\n                <SplitPane\n                  className=\"sidebarPanes\"\n                  split=\"horizontal\"\n                  defaultSize={\n                    isNarrowBreakpoint ? paneSizeOptions.defaultSize : paneSizeOptions.minSize\n                  }\n                  size={paneSizeOptions.size}\n                  allowResize={paneSizeOptions.allowResize}\n                  minSize={isNarrowBreakpoint ? paneSizeOptions.minSize : 100}\n                  maxSize={paneSizeOptions.maxSize}\n                  primary=\"first\"\n                >\n                  <InputContainer display=\"flex\" data-testid=\"vision-query-editor\">\n                    <Box flex={1}>\n                      <InputBackgroundContainerLeft>\n                        <Flex>\n                          <StyledLabel muted>{t('query.label')}</StyledLabel>\n                        </Flex>\n                      </InputBackgroundContainerLeft>\n                      <VisionCodeMirror\n                        initialValue={query}\n                        onChange={setQuery}\n                        ref={editorQueryRef}\n                      />\n                    </Box>\n                  </InputContainer>\n                  <InputContainer display=\"flex\">\n                    <ParamsEditor\n                      value={params.raw}\n                      onChange={handleParamsChange}\n                      paramsError={params.error}\n                      hasValidParams={params.valid}\n                      editorRef={editorParamsRef}\n                    />\n\n                    <VisionGuiControls\n                      hasValidParams={params.valid}\n                      queryInProgress={queryInProgress}\n                      listenInProgress={listenInProgress}\n                      onQueryExecution={handleQueryExecution}\n                      onListenExecution={handleListenExecution}\n                    />\n                  </InputContainer>\n                </SplitPane>\n              </Box>\n              <VisionGuiResult\n                error={error}\n                queryInProgress={queryInProgress}\n                queryResult={queryResult}\n                listenInProgress={listenInProgress}\n                listenMutations={listenMutations}\n                dataset={dataset}\n                queryTime={queryTime}\n                e2eTime={e2eTime}\n              />\n            </SplitPane>\n          </Box>\n          <Box style={{position: 'relative', height: '100%'}}>\n            <Button\n              mode=\"ghost\"\n              padding={2}\n              style={{\n                position: 'absolute',\n                left: -32,\n                top: '50%',\n                transform: 'translateY(-50%)',\n                zIndex: 100,\n                pointerEvents: 'auto',\n              }}\n              onClick={() => setIsQueryRecallCollapsed(!isQueryRecallCollapsed)}\n            >\n              <div style={{display: 'flex', alignItems: 'center', height: '100%'}}>\n                {isQueryRecallCollapsed ? <ChevronLeftIcon /> : <ChevronRightIcon />}\n              </div>\n            </Button>\n            <QueryRecall\n              url={url}\n              getStateFromUrl={getStateFromUrl}\n              setStateFromParsedUrl={setStateFromParsedUrl}\n              currentQuery={query}\n              currentParams={params.parsed || {}}\n              generateUrl={generateUrl}\n            />\n          </Box>\n        </SplitPane>\n      </SplitpaneContainer>\n    </Root>\n  )\n}\n", "import {type SanityClient} from '@sanity/client'\nimport {useEffect, useState} from 'react'\n\nexport function useDatasets(client: SanityClient): string[] | Error | undefined {\n  const projectId = client.config().projectId\n  const [datasets, setDatasets] = useState<string[] | Error | undefined>()\n\n  useEffect(() => {\n    const datasets$ = client.observable.datasets.list().subscribe({\n      next: (result) => setDatasets(result.map((ds) => ds.name)),\n      error: (err) => setDatasets(err),\n    })\n\n    return () => datasets$.unsubscribe()\n  }, [client, projectId])\n\n  return datasets || undefined\n}\n", "import {Flex} from '@sanity/ui'\n\nimport {DelayedSpinner} from '../components/DelayedSpinner'\nimport {VisionGui} from '../components/VisionGui'\nimport {useDatasets} from '../hooks/useDatasets'\nimport {type VisionProps} from '../types'\n\nexport function VisionContainer(props: VisionProps) {\n  const loadedDatasets = useDatasets(props.client)\n\n  if (!loadedDatasets) {\n    return (\n      <Flex align=\"center\" height=\"fill\" justify=\"center\">\n        <DelayedSpinner />\n      </Flex>\n    )\n  }\n\n  const datasets =\n    loadedDatasets instanceof Error\n      ? // On error, use the clients configured dataset\n        [props.client.config().dataset || 'production']\n      : // Otherwise use the loaded list, obviously\n        loadedDatasets\n\n  const projectId = props.client.config().projectId\n  const defaultDataset = props.config.defaultDataset || props.client.config().dataset || datasets[0]\n\n  return (\n    <VisionGui\n      key={projectId}\n      {...props}\n      datasets={datasets}\n      projectId={projectId}\n      defaultDataset={defaultDataset}\n    />\n  )\n}\n", "/* eslint-disable @sanity/i18n/no-attribute-string-literals */\n/* eslint-disable i18next/no-literal-string */\nimport {Button, Card, Code, Container, Heading, Stack} from '@sanity/ui'\nimport {Component, type PropsWithChildren} from 'react'\n\nimport {clearLocalStorage} from '../util/localStorage'\n\n/**\n * @internal\n */\nexport type VisionErrorBoundaryProps = PropsWithChildren\n\n/**\n * @internal\n */\ninterface VisionErrorBoundaryState {\n  error: string | null\n  numRetries: number\n}\n\n/**\n * @internal\n */\nexport class VisionErrorBoundary extends Component<\n  VisionErrorBoundaryProps,\n  VisionErrorBoundaryState\n> {\n  constructor(props: VisionErrorBoundaryProps) {\n    super(props)\n    this.state = {error: null, numRetries: 0}\n  }\n\n  static getDerivedStateFromError(error: unknown) {\n    return {error: error instanceof Error ? error.message : `${error}`}\n  }\n\n  handleRetryRender = () =>\n    this.setState((prev) => ({error: null, numRetries: prev.numRetries + 1}))\n\n  handleRetryWithCacheClear = () => {\n    clearLocalStorage()\n    this.handleRetryRender()\n  }\n\n  render() {\n    if (!this.state.error) {\n      return this.props.children\n    }\n\n    const message = this.state.error\n    const withCacheClear = this.state.numRetries > 0\n\n    return (\n      <Card\n        height=\"fill\"\n        overflow=\"auto\"\n        paddingY={[4, 5, 6, 7]}\n        paddingX={4}\n        sizing=\"border\"\n        tone=\"critical\"\n      >\n        <Container width={3}>\n          <Stack space={4}>\n            <div>\n              <Button\n                onClick={withCacheClear ? this.handleRetryWithCacheClear : this.handleRetryRender}\n                text={withCacheClear ? 'Clear cache and retry' : 'Retry'}\n                tone=\"default\"\n              />\n            </div>\n\n            <Heading>An error occurred</Heading>\n\n            <Card border radius={2} overflow=\"auto\" padding={4} tone=\"inherit\">\n              <Stack space={4}>\n                {message && (\n                  <Code size={1}>\n                    <strong>Error: {message}</strong>\n                  </Code>\n                )}\n              </Stack>\n            </Card>\n          </Stack>\n        </Container>\n      </Card>\n    )\n  }\n}\n", "import {type Tool, useClient} from 'sanity'\n\nimport {DEFAULT_API_VERSION} from './apiVersions'\nimport {VisionContainer} from './containers/VisionContainer'\nimport {VisionErrorBoundary} from './containers/VisionErrorBoundary'\nimport {type VisionConfig} from './types'\n\ninterface SanityVisionProps {\n  tool: Tool<VisionConfig>\n}\n\nfunction SanityVision(props: SanityVisionProps) {\n  const client = useClient({apiVersion: '1'})\n  const config: VisionConfig = {\n    defaultApiVersion: DEFAULT_API_VERSION,\n    ...props.tool.options,\n  }\n\n  return (\n    <VisionErrorBoundary>\n      <VisionContainer client={client} config={config} />\n    </VisionErrorBoundary>\n  )\n}\n\nexport default SanityVision\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW,QAAQ,yBAAyB,QAAQ,yBAAyB,QAAQ,SAAS;AACtG,YAAQ,SAAS;AAAA,MACb,iBAAiB;AAAA,MACjB,UAAU;AAAA,QACN,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,eAAe;AAAA,MACnB;AAAA,MACA,UAAU;AAAA,QACN,cAAc;AAAA,QACd,kBAAkB;AAAA,MACtB;AAAA,IACJ;AACA,YAAQ,yBAAyB;AAAA,MAC7B,oBAAoB;AAAA,MACpB,wBAAwB;AAAA,MACxB,WAAW;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,KAAK;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,MACjB,wBAAwB;AAAA,MACxB,UAAU;AAAA,MACV,aAAa,CAAC;AAAA,MACd,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,cAAc;AAAA,IAClB;AACA,YAAQ,yBAAyB;AAAA,MAC7B,WAAW;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,KAAK;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACtB;AACA,YAAQ,WAAW;AAAA;AAAA;;;AClDnB;AAAA;AAAA;AAMA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU,QAAQ,eAAe;AAKzC,aAAS,aAAa,KAAK,IAAI;AAC3B,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AACA,YAAM,EAAE,UAAU,KAAAA,MAAK,UAAU,IAAI,MAAM,EAAE;AAC7C,YAAM,QAAQ,OAAO,QAAQ,YAAY,MAAM,MAAM,IAAI,EAAE,IAAI;AAC/D,YAAM,SAAS,OAAO,QAAQ,YAAYA,QAAO,MAAM,IAAIA,IAAG,IAAI;AAClE,UAAI,YAAY,KAAK,OAAO,QAAQ,YAAY,EAAE,MAAM,MAAM;AAC1D,cAAM,EAAE,KAAK,QAAQ,IAAI,MAAM,SAAS;AACxC,cAAM,eAAe,SAAS,OAAO;AAErC,YAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,YAAY,GAAG;AAC9C,iBAAO,OAAO,IAAI,CAAC,QAAQ,aAAa,KAAK,SAAS,CAAC;AAAA,QAC3D;AAEA,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC,WACS,MAAM,QAAQ,GAAG,GAAG;AACzB,cAAM,WAAW,SAASA,IAAG;AAC7B,YAAI,OAAOA,QAAO,aAAa,MAAM,CAAC,MAAM,QAAQ,GAAG;AACnD,iBAAO;AAAA,QACX;AAEA,eAAO,IAAI,IAAI,CAAC,QAAQ,aAAa,KAAK,EAAE,CAAC;AAAA,MACjD,WACS,YAAY,KAAK,OAAOA,QAAO,OAAO,QAAQ,YAAYA,QAAO,KAAK;AAE3E,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC,WACS,aAAa,MAAM,OAAO,QAAQ,YAAYA,QAAO,OAAO,EAAE,MAAM,MAAM;AAE/E,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AACA,YAAQ,eAAe;AAKvB,aAAS,QAAQ,KAAK,IAAI,GAAG;AACzB,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C,WACS,CAAC,IAAI;AACV,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,aAAO,IAAI,KAAK,IAAI,CAAC;AAAA,IACzB;AACA,YAAQ,UAAU;AAElB,aAAS,IAAI,KAAK,IAAI,GAAG;AACrB,YAAM,EAAE,UAAU,KAAAA,MAAK,UAAU,IAAI,MAAM,EAAE;AAE7C,UAAI,GAAG,WAAW,WAAW,KAAK,GAAG,WAAW,aAAa,KAAK,GAAG,WAAW,WAAW,GAAG;AAC1F,eAAO;AAAA,MACX;AACA,UAAI,YAAY,GAAG;AACf,cAAM,WAAW,SAASA,IAAG;AAE7B,YAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,EAAEA,QAAO,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,QAAQ,GAAG;AAEpG,cAAIA,IAAG,IAAI,IAAIA,IAAG,KAAK,CAAC;AAExB,cAAI,IAAIA,IAAG,GAAG,WAAW,CAAC;AAC1B,iBAAO;AAAA,QACX,WACS,OAAO,QAAQ,YAAY,QAAQ,QAAQ,EAAEA,QAAO,QAAQ,MAAM,QAAQ,GAAG,GAAG;AAErF,cAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,CAAC;AACpC,iBAAO;AAAA,QACX,WACS,OAAO,QAAQ,YAAY,QAAQ,QAAQ,EAAEA,QAAO,QAAQ,CAAC,MAAM,QAAQ,GAAG,GAAG;AACtF,gBAAM,EAAE,KAAK,QAAQ,IAAI,MAAM,SAAS;AACxC,gBAAM,eAAe,SAAS,OAAO;AACrC,cAAI,CAAC,MAAM,YAAY,GAAG;AAEtB,gBAAIA,IAAG,IAAI,CAAC;AAAA,UAChB,WACS,cAAc,IAAI;AAEvB,gBAAI,EAAE,IAAI;AACV,mBAAO;AAAA,UACX,OACK;AAED,gBAAIA,IAAG,IAAI,CAAC;AAAA,UAChB;AAAA,QACJ;AACA,YAAI,IAAIA,IAAG,GAAG,WAAW,CAAC;AAAA,MAC9B,WACS,MAAM,QAAQ,GAAG,GAAG;AACzB,cAAM,WAAW,SAASA,IAAG;AAE7B,YAAI,OAAOA,QAAO,aAAa,MAAM,CAAC,MAAM,QAAQ,GAAG;AACnD,cAAIA,IAAG,IAAI;AACX,iBAAO;AAAA,QACX;AAEA,YAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,WAAW,CAAC,CAAC;AAC3C,eAAO;AAAA,MACX,OACK;AAED,YAAIA,IAAG,IAAI;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,IAAI;AACf,YAAM,WAAW,4BAA4B,EAAE;AAC/C,aAAO;AAAA,QACH;AAAA,QACA,KAAK,GAAG,MAAM,GAAG,YAAY,IAAI,WAAW,MAAS,EAAE,QAAQ,QAAQ,GAAG;AAAA,QAC1E,WAAW,GAAG,MAAM,WAAW,CAAC;AAAA,MACpC;AAAA,IACJ;AACA,aAAS,4BAA4B,IAAI;AACrC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,cAAM,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,cAAc,GAAG,CAAC;AAC/D,YAAI,gBAAgB,OAAO,iBAAiB;AACxC,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACzIA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,sBAAsB,QAAQ,UAAU,QAAQ,SAAS;AACjE,aAAS,OAAO,OAAO;AACnB,aAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAAA,IAC7B;AACA,YAAQ,SAAS;AACjB,aAAS,QAAQ,OAAO;AACpB,aAAO,CAAC,EAAE,OAAO,GAAG,KAAK;AAAA,IAC7B;AACA,YAAQ,UAAU;AAMlB,aAAS,oBAAoB,KAAK;AAC9B,aAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE;AAAA,IAC9F;AACA,YAAQ,sBAAsB;AAAA;AAAA;;;ACnB9B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGC,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB,QAAQ,WAAW;AAC9C,QAAM,QAAQ,aAAa,eAAkB;AAC7C,iBAAa,iBAAoB,OAAO;AAOxC,aAAS,SAAS,QAAQ,SAAS;AAC/B,YAAM,gBAAgB,aAAa,OAAO;AAC1C,UAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAC/C,eAAO,qBAAqB,IAAI,QAAQ,aAAa;AAAA,MACzD;AACA,aAAO,CAAC;AAAA,IACZ;AACA,YAAQ,WAAW;AAOnB,aAAS,iBAAiB,MAAM,SAAS;AACrC,YAAM,gBAAgB,aAAa,OAAO;AAC1C,aAAO,KAAK,IAAI,CAACC,cAAa;AAC1B,YAAI,OAAOA,cAAa,YAAYA,cAAa,MAAM;AAEnD,iBAAO,SAASA,WAAU,aAAa;AAAA,QAC3C;AACA,eAAO,CAAC;AAAA,MACZ,CAAC;AAAA,IACL;AACA,YAAQ,mBAAmB;AAC3B,aAAS,qBAAqB,SAAS,MAAM,SAAS;AAClD,YAAM,OAAO,OAAO,KAAK,IAAI,EAAE,IAAI,CAAC,eAAe;AAE/C,cAAM,UAAU,aAAa,SAAS,4BAA4B,YAAY,OAAO,CAAC;AAEtF,YAAI,QAAQ,uBAAuB,MAAM,oBAAoB,KAAK,UAAU,CAAC,KAAM,QAAQ,sBAAsB,MAAM,QAAQ,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,EAAE,QAAS;AAC1K,iBAAO,qBAAqB,SAAS,KAAK,UAAU,GAAG,OAAO;AAAA,QAClE,WACS,QAAQ,sBAAsB,MAAM,QAAQ,KAAK,UAAU,CAAC,GAAG;AAEpE,iBAAO,iBAAiB,KAAK,UAAU,GAAG,SAAS,OAAO;AAAA,QAC9D,WACS,QAAQ,qBAAqB,MAAM,QAAQ,KAAK,UAAU,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE,QAAQ;AAC/F,iBAAO,CAAC;AAAA,QACZ;AAEA,eAAO;AAAA,MACX,CAAC;AACD,aAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AASA,aAAS,iBAAiB,UAAU,gBAAgB,SAAS;AACzD,UAAI,eAAe,iBAAiB,UAAU,OAAO;AACrD,UAAI,CAAC,SAAS,QAAQ;AAClB,eAAO,QAAQ,iCAAiC,CAAC,IAAI,CAAC,cAAc;AAAA,MACxE,WACS,SAAS,UAAU,MAAM,QAAQ,YAAY,EAAE,WAAW,GAAG;AAElE,eAAO,CAAC,cAAc;AAAA,MAC1B,OACK;AACD,uBAAe,aAAa,IAAI,CAAC,eAAe;AAC5C,cAAI,MAAM,QAAQ,UAAU,KAAK,WAAW,WAAW,GAAG;AACtD,mBAAO,CAAC,cAAc;AAAA,UAC1B;AACA,iBAAO,WAAW,IAAI,CAAC,WAAW,aAAa,gBAAgB,4BAA4B,QAAQ,OAAO,CAAC,CAAC;AAAA,QAChH,CAAC;AACD,eAAO,MAAM,OAAO,MAAM,QAAQ,YAAY,CAAC;AAAA,MACnD;AAAA,IACJ;AACA,aAAS,4BAA4BC,MAAK,SAAS;AAC/C,UAAI,QAAQ,kBAAkB;AAC1B,eAAOA,KAAI,QAAQ,OAAO,KAAK;AAAA,MACnC;AACA,aAAOA;AAAA,IACX;AAOA,aAAS,aAAa,cAAc,gBAAgB;AAChD,UAAI,cAAc;AACd,eAAO,eAAe,MAAM;AAAA,MAChC;AACA,aAAO;AAAA,IACX;AACA,aAAS,aAAa,SAAS;AAC3B,aAAO;AAAA,QACH,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,gCAAgC;AAAA,QAChC,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,GAAI,WAAW,CAAC;AAAA,MACpB;AAAA,IACJ;AAAA;AAAA;;;ACzIA,IAAAC,iBAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,YAAY,QAAQ,UAAU,QAAQ,SAAS,QAAQ,kBAAkB,QAAQ,UAAU,QAAQ,cAAc,QAAQ,SAAS,QAAQ,WAAW,QAAQ,WAAW,QAAQ,WAAW,QAAQ,SAAS,QAAQ,iBAAiB,QAAQ,oBAAoB,QAAQ,eAAe,QAAQ,2BAA2B,QAAQ,uBAAuB,QAAQ,yBAAyB,QAAQ,WAAW,QAAQ,WAAW,QAAQ,kBAAkB,QAAQ,kBAAkB;AAC7d,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,QAAM,kBAAkB;AAAxB,QAAsE,mBAAmB;AAMzF,aAAS,gBAAgB,MAAM;AAX/B;AAYI,aAAO;AAAA,QACH,GAAG,YAAY;AAAA,QACf,GAAG;AAAA,QACH,WAAW;AAAA,UACP,SAAO,kCAAM,cAAN,mBAAiB,UAAS,YAAY,uBAAuB,UAAU;AAAA,UAC9E,QAAM,kCAAM,cAAN,mBAAiB,SAAQ,YAAY,uBAAuB,UAAU;AAAA,UAC5E,OAAK,kCAAM,cAAN,mBAAiB,QAAO,YAAY,uBAAuB,UAAU;AAAA,QAC9E;AAAA,QACA,eAAe,uBAAO,OAAO,CAAC,CAAC;AAAA,MACnC;AAAA,IACJ;AACA,YAAQ,kBAAkB;AAM1B,aAAS,gBAAgB,MAAM;AA7B/B;AA8BI,aAAO;AAAA,QACH,GAAG,YAAY;AAAA,QACf,GAAG;AAAA,QACH,WAAW;AAAA,UACP,SAAO,kCAAM,cAAN,mBAAiB,UAAS,YAAY,uBAAuB,UAAU;AAAA,UAC9E,QAAM,kCAAM,cAAN,mBAAiB,SAAQ,YAAY,uBAAuB,UAAU;AAAA,UAC5E,OAAK,kCAAM,cAAN,mBAAiB,QAAO,YAAY,uBAAuB,UAAU;AAAA,QAC9E;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,kBAAkB;AAC1B,aAAS,SAAS,MAAM,cAAc,eAAe;AACjD,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,GAAG,cAAc,YAAY,IAAI,IAAI,GAAG;AAC5D,UAAI,CAAC,aAAa,IAAI;AAClB,cAAM,IAAI,MAAM,cAAc,gBAAgB;AAClD,aAAO;AAAA,IACX;AACA,YAAQ,WAAW;AAInB,aAAS,SAAS,KAAK;AACnB,aAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAAA,IACzC;AACA,YAAQ,WAAW;AAMnB,aAAS,uBAAuB,YAAY,SAAS;AACjD,YAAM,YAAY,WAAW,CAAC,GAAG,YAAY,WAAW,SAAS,GAAG,WAAW,WAAW,SAAS;AAEnG,aAAO,cAAc,QAAQ,UAAU,QAAQ,aAAa,QAAQ,UAAU;AAAA,IAClF;AACA,YAAQ,yBAAyB;AAKjC,aAAS,qBAAqB,YAAY;AACtC,aAAO,gBAAgB,KAAK,UAAU;AAAA,IAC1C;AACA,YAAQ,uBAAuB;AAI/B,aAAS,yBAAyB,SAAS,SAAS;AAChD,aAAO,gBAAgB,SAAS,OAAO,EAClC,OAAO,gBAAgB,SAAS,OAAO,CAAC;AAAA,IACjD;AACA,YAAQ,2BAA2B;AAInC,aAAS,aAAa,YAAY;AAC9B,aAAO,YAAY,UAAU,KAAK,OAAO,UAAU,KAAK,eAAe;AAAA,IAC3E;AACA,YAAQ,eAAe;AAIvB,aAAS,kBAAkB,QAAQ;AAC/B,aAAO,OAAO,OAAO,CAAC,UAAU,CAAC,aAAa,KAAK,CAAC;AAAA,IACxD;AACA,YAAQ,oBAAoB;AAO5B,aAAS,eAAe,KAAK,OAAO,GAAG;AACnC,aAAO,IAAI,UAAU,OAAO,QAAQ,CAAC;AAAA,IACzC;AACA,YAAQ,iBAAiB;AAczB,aAAS,WAAW,aAAa,MAAM,WAAW;AAC9C,YAAM,iBAAiB,GAAG,WAAW,cAAc,MAAM,SAAS;AAClE,UAAI,SAAS,SAAS,IAAI;AAC1B,UAAI,MAAM,QAAQ,aAAa,KAAK,cAAc,QAAQ;AACtD,sBAAc,QAAQ,CAAC,QAAQ;AAC3B,mBAAS,SAAS,IAAI;AACtB,sBAAY,MAAM,GAAG,WAAW,SAAS,QAAQ,WAAW,GAAG,CAAC;AAAA,QACpE,CAAC;AAAA,MACL,WACS,MAAM,QAAQ,aAAa,KAAK,cAAc,WAAW,GAAG;AAEjE,SAAC,GAAG,WAAW,SAAS,QAAQ,WAAW,EAAE;AAC7C,oBAAY,KAAK,MAAM;AAAA,MAC3B,OACK;AACD,oBAAY,KAAK,MAAM;AAAA,MAC3B;AAAA,IACJ;AAIA,aAAS,OAAO,OAAO,OAAO;AAC1B,YAAM,SAAS,CAAC;AAChB,YAAM,QAAQ,CAAC,SAAS;AACpB,mBAAW,QAAQ,MAAM,KAAK;AAAA,MAClC,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,SAAS;AAIjB,aAAS,SAAS,OAAO;AACrB,aAAO,CAAC,MAAM,OAAO,KAAK,CAAC;AAAA,IAC/B;AACA,YAAQ,WAAW;AAInB,aAAS,SAAS,OAAO;AACrB,aAAO,OAAO,UAAU;AAAA,IAC5B;AACA,YAAQ,WAAW;AACnB,aAAS,SAAS,OAAO;AACrB,aAAO,OAAO,UAAU;AAAA,IAC5B;AACA,YAAQ,WAAW;AACnB,aAAS,OAAO,OAAO;AACnB,aAAO,UAAU;AAAA,IACrB;AACA,YAAQ,SAAS;AACjB,aAAS,YAAY,OAAO;AACxB,aAAO,OAAO,UAAU;AAAA,IAC5B;AACA,YAAQ,cAAc;AACtB,aAAS,QAAQ,OAAO;AAGpB,aAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAAA,IACrD;AACA,YAAQ,UAAU;AAClB,aAAS,gBAAgB,GAAG,GAAG;AAC3B,aAAO,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAAA,IACzC;AACA,YAAQ,kBAAkB;AAC1B,aAAS,OAAO,OAAO;AACnB,aAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAAA,IAC7B;AACA,YAAQ,SAAS;AACjB,aAAS,QAAQ,OAAO;AAEpB,UAAI,MAAM,MAAM;AACZ,eAAO,MAAM,KAAK;AAAA,MACtB;AAEA,UAAI,MAAM,SAAS,kBAAkB;AACjC,YAAI,YAAY,CAAC;AACjB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,kBAAkB;AACrD,sBAAY,UAAU,OAAO,GAAG,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC;AAAA,QACxE;AACA,eAAO;AAAA,MACX;AACA,aAAO,MAAM,OAAO,CAAC,aAAa,UAAU,YAAY,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,IAC7E;AACA,YAAQ,UAAU;AAKlB,aAAS,UAAU,YAAY;AAC3B,aAAO,eAAe,YAClB,eAAe;AAAA,IACvB;AACA,YAAQ,YAAY;AAAA;AAAA;;;ACrNpB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AACnB,QAAM,aAAa;AACnB,QAAM,UAAU;AAChB,QAAM,cAAc;AACpB,QAAM,QAAQ,aAAa,gBAAkB;AAC7C,QAAM,WAAW,SAAU,SAAS;AAChC,YAAM,0BAA0B,IAAI,OAAO,QAAQ,UAAU,MAAM,GAAG,GAAG,kBAAkB,YAAY,oBAAoB,QAAQ,cAAc,OAAO,QAAQ,eAAe,aAAa,QAAQ,aAAa,MAAM,4BAA4B,QAAQ,sBAAsB,CAAC,QAAQ,cAAc,eAAe;AAAA,QACnT,oBAAoB,QAAQ;AAAA,QAC5B,qBAAqB,QAAQ;AAAA,QAC7B,oBAAoB;AAAA,QACpB,gCAAgC;AAAA,QAChC,kBAAkB;AAAA,MACtB;AAKA,eAAS,iBAAiB,MAAM;AAE5B,gBAAQ,GAAG,QAAQ,kBAAkB,MAAM,YAAY;AAAA,MAC3D;AAMA,eAAS,eAAe,iBAAiB;AAErC,YAAI,gBAAgB,WAAW,GAAG;AAC9B,iBAAO,CAAC;AAAA,QACZ;AAEA,YAAI,QAAQ,wBAAwB;AAChC,iBAAO,uBAAuB,eAAe;AAAA,QACjD,OACK;AAED,gBAAM,mBAAmB,MAAM,OAAO,MAAM,QAAQ,eAAe,CAAC;AACpE,iBAAO;AAAA,QACX;AAAA,MACJ;AAMA,eAAS,uBAAuB,iBAAiB;AAE7C,cAAM,iBAAiB,gBAAgB,CAAC,GAAG,wBAAwB,gBAAgB,MAAM,CAAC,GAAG,oBAAoB,iCAAiC,gBAAgB,qBAAqB;AAEvL,YAAI,mBAAmB;AACnB,gBAAM,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAA,QAC7D;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iCAAiC,gBAAgB,uBAAuB;AAC7E,eAAO,sBAAsB,OAAO,CAAC,mBAAmB,mBAAmB;AAEvE,gBAAM,sBAAsB,MAAM,yBAAyB,gBAAgB,cAAc,EAAE;AAC3F,iBAAO,sBAAsB,IACvB,oBAAoB,IACpB;AAAA,QACV,GAAG,CAAC;AAAA,MACR;AAIA,eAAS,mBAAmB,UAAU;AAClC,YAAI,QAAQ,aAAa;AACrB,iBAAO,SAAS,OAAO,CAAC,YAAY;AAChC,uBAAW,eAAe,QAAQ,aAAa;AAE3C,oBAAM,QAAQ,uBAAuB,SAAS,cAAc,IAAI,OAAO,IAAI,WAAW,EAAE;AACxF,kBAAI,gBAAgB,WAAW,QAAQ,MAAM,KAAK,GAAG;AACjD,uBAAO;AAAA,cACX;AAAA,YACJ;AACA,mBAAO;AAAA,UACX,CAAC;AAAA,QACL;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iBAAiB,YAAY;AAClC,YAAI,QAAQ,cAAc,OAAO,QAAQ,eAAe,YAAY;AAChE,iBAAO,WAAW,KAAK,QAAQ,UAAU;AAAA,QAC7C,WACS,QAAQ,YAAY;AACzB,iBAAO,WAAW,KAAK;AAAA,QAC3B;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iBAAiB,QAAQ;AAC9B,YAAI,QAAQ,kBAAkB;AAC1B,iBAAO,eAAe,OAAO,aAAa,IAAI,CAAC,UAAU,MAAM,MAAM,GAAG,EACnE,IAAI,CAAC,cAAc,UAAU,KAAK,CAAC,EACnC,KAAK,GAAG,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iBAAiB,QAAQ;AAE9B,YAAI,QAAQ,eAAe;AACvB,iBAAO,eAAe,OAAO,aAAa,IAAI,SAAU,YAAY;AAChE,mBAAO,0BAA0B,UAAU;AAAA,UAC/C,CAAC;AAAA,QACL;AACA,eAAO;AAAA,MACX;AAIA,eAAS,kBAAkB,QAAQ;AAE/B,cAAM,oBAAoB,OAAO,KAAK,QAAQ,aAAa;AAC3D,eAAO,SAAS,OAAO,aAClB,IAAI,SAAU,OAAO;AACtB,cAAI,YAAY;AAEhB,cAAI,kBAAkB,SAAS,KAAK,GAAG;AACnC,wBAAY,QAAQ,cAAc,KAAK;AAAA,UAC3C,WACS,CAAC,QAAQ,wBAAwB;AAEtC,wBAAY,UAAU,QAAQ,SAAS,GAAG;AAAA,UAC9C;AACA,iBAAO;AAAA,QACX,CAAC,EACI,KAAK,QAAQ,UAAU,KAAK;AACjC,eAAO;AAAA,MACX;AACA,eAAS,4BAA4B;AACjC,YAAI,CAAC,QAAQ;AACT,iBAAO,CAAC;AACZ,eAAO,QAAQ,KAAK,IAAI,CAACC,SAAQ;AAC7B,cAAI,OAAOA,SAAQ,YAAY,WAAWA,MAAK;AAC3C,oBAAQ,cAAcA,KAAI,KAAK,IAAIA,KAAI,SAASA,KAAI;AACpD,mBAAOA,KAAI;AAAA,UACf;AACA,iBAAOA;AAAA,QACX,CAAC;AAAA,MACL;AACA,eAAS,2BAA2B;AAChC,YAAI,CAAC,QAAQ;AACT,iBAAO,CAAC;AACZ,eAAO,QAAQ,KAAK,QAAQ,UAAQ;AAChC,cAAI,OAAO,SAAS,UAAU;AAE1B,mBAAO,CAAC;AAAA,UACZ,WACS,6BAAM,eAAe;AAE1B,mBAAO,KAAK;AAAA,UAChB;AAEA,iBAAO,CAAC;AAAA,QACZ,CAAC;AAAA,MACL;AAKA,eAAS,qBAAqB,MAAM;AAChC,cAAM,oBAAoB,yBAAyB;AACnD,cAAM,aAAa,0BAA0B;AAC7C,cAAM,aAAa,iBAAiB,IAAI;AACxC,cAAM,YAAY,eAAe,UAAU;AAC3C,YAAI,QAAQ,MAAM;AACd,kBAAQ,OAAO;AACf,gBAAM,cAAc,WAAW,QAAQ,CAAC,oBAAoB;AAExD,gBAAI,CAAC,kBAAkB,SAAS,eAAe,GAAG;AAC9C,qBAAO;AAAA,YACX;AAEA,kBAAM,UAAU,CAAC;AACjB,kBAAM,QAAQ,IAAI,OAAO,IAAI,eAAe,EAAE;AAC9C,uBAAW,eAAe,WAAW;AACjC,kBAAI,oBAAoB,eAAe,YAAY,MAAM,KAAK,GAAG;AAC7D,wBAAQ,KAAK,WAAW;AAAA,cAC5B;AAAA,YACJ;AACA,mBAAO;AAAA,UACX,CAAC;AACD,cAAI,CAAC,QAAQ,cAAc;AACvB,kBAAMC,YAAW,mBAAmB,WAAW;AAC/C,mBAAO,iBAAiBA,SAAQ;AAAA,UACpC;AAAA,QACJ;AACA,cAAM,WAAW,mBAAmB,SAAS;AAC7C,eAAO,iBAAiB,QAAQ;AAAA,MACpC;AAEA,eAAS,iBAAiB,QAAQ;AAC9B,mBAAW,UAAU,OAAO,SAAS;AACjC,qBAAW,SAAS,OAAO,cAAc;AACrC,kBAAM,SAAS,GAAG,WAAW,cAAc,QAAQ,KAAK;AACxD,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AASA,eAAS,yBAAyB,QAAQ,YAAY,OAAO;AACzD,YAAI,QAAQ,cAAc;AAEtB,iBAAO,aAAa,QAAQ,CAAC,gBAAgB;AACzC,mBAAO,UAAU,MAAM,OAAO,OAAO,SAAS,WAAW;AAAA,UAC7D,CAAC;AACD,iBAAO,eAAe,qBAAqB,OAAO,OAAO;AAEzD,cAAI,iBAAiB,MAAM,GAAG;AAC1B,mBAAO,yBAAyB,QAAQ,SAAS;AAAA,UACrD;AAGA,cAAI,CAAC,WAAW;AACZ,mBAAO,yBAAyB,QAAQ,IAAI;AAAA,UAChD;AAEA,cAAI,QAAQ,MAAM;AACd,kBAAM,qBAAqB,0BAA0B;AACrD,mBAAO,eAAe,mBAAmB,kBAAkB;AAAA,UAC/D;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAYA,eAAS,eAAe,QAAQ;AAC5B,eAAO,eAAe,OAAO,QAAQ,IAAI,CAAC,WAAW;AAEjD,gBAAM,kBAAkB,wBAAwB,QAAQ,OAAO,YAAY,GAE3E,sBAAsB,gBAAgB,IAAI,CAAC,eAAe;AACtD,yBAAa,qBAAqB,UAAU;AAC5C,yBAAa,oBAAoB,UAAU;AAC3C,gBAAI,cAAc,oBAAoB,kBAAkB,YAAY,wBAAwB,IAAI,yBAAyB,UAAU;AACnI,0BAAc,0BAA0B,WAAW;AACnD,mBAAO;AAAA,UACX,CAAC;AAED,iBAAO,yBAAyB,mBAAmB;AAAA,QACvD,CAAC,EAAE,KAAK,QAAQ,UAAU,GAAG;AAC7B,eAAO;AAAA,MACX;AAIA,eAAS,6CAA6C,kBAAkB;AACpE,cAAM,2BAA2B,MAAM,kBAAkB,gBAAgB;AAEzE,YAAI,CAAC,iBAAiB,UAAU,CAAC,yBAAyB,QAAQ;AAC9D,iBAAO,QAAQ,mBAAmB;AAAA,QACtC,WACS,yBAAyB,WAAW,GAAG;AAG5C,iBAAO,yBAAyB,CAAC;AAAA,QACrC;AACA,eAAO;AAAA,MACX;AAIA,eAAS,wBAAwB,QAAQ,QAAQ;AAC7C,cAAM,eAAe,CAAC;AACtB,eAAO,QAAQ,CAAC,UAAU;AACtB,cAAI,oBAAoB,GAAG,WAAW,cAAc,QAAQ,KAAK;AACjE,cAAI,CAAC,MAAM,YAAY,QAAQ,eAAe,KAAK,MAAM,aAAa,gBAAgB,GAAG;AACrF,+BAAmB,QAAQ;AAAA,UAC/B,WACS,QAAQ,sBAAsB,MAAM,QAAQ,gBAAgB,GAAG;AACpE,+BAAmB,6CAA6C,gBAAgB;AAAA,UACpF;AACA,uBAAa,KAAK,gBAAgB;AAAA,QACtC,CAAC;AACD,eAAO;AAAA,MACX;AAIA,eAAS,yBAAyB,YAAY;AAC1C,cAAM,SAAS,sBAAsB;AACrC,YAAI,eAAe,QAAQ,MAAM,QAAQ,UAAU,KAAK,OAAO,eAAe,YAAY,CAAC,QAAQ;AAC/F,iBAAO,KAAK,UAAU,UAAU;AAAA,QACpC,WACS,OAAO,eAAe,aAAa;AACxC,iBAAO;AAAA,QACX,WACS,UAAU,QAAQ,sBAAsB;AAC7C,iBAAO,WAAW,YAAY;AAAA,QAClC,OACK;AACD,iBAAO,CAAC,QAAQ,kBAAkB,WAAW,SAAS,IAAI,WAAW,eAAe;AAAA,QACxF;AAAA,MACJ;AAIA,eAAS,qBAAqB,YAAY;AACtC,YAAI,QAAQ,iBAAiB;AACzB,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,mBAAO,WAAW,IAAI,oBAAoB;AAAA,UAC9C,WACS,OAAO,eAAe,UAAU;AACrC,mBAAO,WAAW,KAAK;AAAA,UAC3B;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAOA,eAAS,oBAAoB,YAAY;AACrC,YAAI,QAAQ,qBAAqB;AAC7B,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,mBAAO,WAAW,IAAI,mBAAmB;AAAA,UAC7C,WACS,OAAO,eAAe,YAAY,CAAC,MAAM,SAAS,UAAU,GAAG;AACpE,mBAAO,WAAW,QAAQ,kBAAkB,EAAE;AAAA,UAClD;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAMA,eAAS,0BAA0B,YAAY;AAC3C,cAAM,gBAAgB,QAAQ,UAAU;AAExC,YAAI,WAAW,SAAS,QAAQ,UAAU,IAAI,GAAG;AAE7C,uBAAa,WAAW,QAAQ,yBAAyB,gBAAgB,aAAa;AAAA,QAC1F;AAGA,YAAI,WAAW,SAAS,QAAQ,UAAU,KAAK,KAC3C,WAAW,SAAS,QAAQ,UAAU,IAAI,KAC1C,WAAW,MAAM,eAAe,KAChC,QAAQ,iBAAiB,eAAe,UAAU,eAAe,UAAU;AAE3E,uBAAa,gBAAgB,aAAa;AAAA,QAC9C;AACA,eAAO;AAAA,MACX;AAIA,eAAS,yBAAyB,mBAAmB;AACjD,eAAO,kBAAkB,KAAK,QAAQ,UAAU,KAAK;AAAA,MACzD;AAOA,eAAS,0BAA0B,QAAQ;AACvC,cAAM,SAAS,OAAO,QAAQ,UAAU,OAAO,cAE/C,OAAO,QAAQ,WAAW,YAAY,WAAW,OAC5C,QAAQ,gBAAgB,SAAS,QAAQ,UAAU,MAAM,MAC1D;AACJ,eAAO;AAAA,MACX;AAKA,eAAS,QAAQ,MAAM;AAEnB,YAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACtB,iBAAO,CAAC,IAAI;AAAA,QAChB;AAEA,cAAM,eAAe;AAAA,UACjB,cAAc,qBAAqB,IAAI;AAAA,UACvC,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AACA,cAAM,WAAW,yBAAyB,YAAY;AACtD,cAAM,YAAY,eAAe,QAAQ;AACzC,cAAM,UAAU,iBAAiB,SAAS;AAC1C,cAAM,UAAU,iBAAiB,OAAO;AACxC,cAAM,YAAY,kBAAkB,OAAO;AAC3C,eAAO,0BAA0B,SAAS;AAAA,MAC9C;AACA,aAAO;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,WAAW;AAAA;AAAA;;;ACvcnB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AACnB,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,QAAM,QAAQ,aAAa,gBAAkB;AAC7C,QAAM,WAAW,SAAU,SAAS;AAChC,YAAM,4BAA4B,IAAI,OAAO,QAAQ,UAAU,OAAO,QAAQ,UAAU,MAAM,GAAG,GAAG,gBAAgB,IAAI,OAAO,MAAM,YAAY,QAAQ,GAAG,gBAAgB,QAAQ,cAAc,OAAO,QAAQ,eAAe,aAAa,QAAQ,aAAa,KAAK;AAIvQ,eAAS,iBAAiB,WAAW;AACjC,oBAAY,8BAA8B,SAAS;AACnD,YAAI,QAAQ,kBAAkB;AAC1B,iBAAO,UAAU,MAAM,GAAG,EACrB,IAAI,CAAC,cAAc,UAAU,KAAK,CAAC,EACnC,KAAK,GAAG;AAAA,QACjB;AACA,eAAO;AAAA,MACX;AAIA,eAAS,gBAAgB,OAAO;AAC5B,YAAI,eAAe,CAAC;AACpB,YAAI,QAAQ,cAAc;AACtB,yBAAe,QAAQ,aAAa,IAAI,CAAC,aAAa,WAAW;AAAA,YAC7D,OAAO,iBAAiB,WAAW;AAAA,YACnC;AAAA,UACJ,EAAE;AAAA,QACN,OACK;AAED,gBAAM,YAAY,MAAM,CAAC;AACzB,yBAAe,UAAU,IAAI,CAAC,WAAW,WAAW;AAAA,YAChD,OAAO,iBAAiB,SAAS;AAAA,YACjC;AAAA,UACJ,EAAE;AAEF,cAAI,QAAQ,MAAM;AACd,kBAAM,OAAO,QAAQ;AACrB,2BAAe,aAAa,OAAO,CAAC,cAAc,KAAK,SAAS,UAAU,KAAK,CAAC;AAAA,UACpF;AAAA,QACJ;AACA,eAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA,aAAa,CAAC;AAAA,QAClB;AAAA,MACJ;AAIA,eAAS,cAAc,KAAK;AACxB,YAAI,QAAQ,UAAU;AAClB,iBAAO,IAAI,QAAQ,eAAe,EAAE;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AAIA,eAAS,WAAW,KAAK;AAErB,cAAM,QAAQ,CAAC,GAAG,qBAAqB,IAAI,SAAS,GAAG,qBAAqB,QAAQ,UAAU,IAAI,QAAQ,iBAAiB;AAAA,UACvH,qBAAqB;AAAA,UACrB,cAAc;AAAA,UACd,uBAAuB;AAAA,UACvB,YAAY;AAAA,QAChB;AACA,YAAI,YAAY,CAAC,GAAG,WAAW,YAAY,WAAW,WAAW,QAAQ;AAEzE,eAAO,QAAQ,IAAI,QAAQ;AAEvB,sBAAY,IAAI,KAAK;AAErB,uBAAa,QAAQ,IAAI,QAAQ,CAAC,IAAI;AAEtC,sBAAY,QAAQ,qBAAqB,IAAI,QAAQ,CAAC,IAAI;AAG1D,sBAAY,MAAM,eAAe,KAAK,OAAO,kBAAkB;AAC/D,eAAK,cAAc,QAAQ,UAAU,OAAO,CAAC,eAAe,uBACxD,UAAU,uBAAuB,eAAe,QAAQ,UAAU,OAAO;AAIzE,gBAAI,cAAc,QAAQ,UAAU,OAAO,eAAe,eAAe,OAAO;AAC5E,wBAAU,KAAK,EAAE;AAAA,YACrB,WACS,cAAc,QAAQ,UAAU,OAAO;AAG5C,wBAAU,KAAK,EAAE;AAAA,YACrB,OACK;AAED,wBAAU,KAAK,IAAI,UAAU,eAAe,UAAU,CAAC;AAAA,YAC3D;AAGA,sBAAU,KAAK,EAAE;AAEjB,kBAAM,KAAK,SAAS;AACpB,wBAAY,CAAC;AACb,2BAAe,aAAa,QAAQ;AACpC,2BAAe,eAAe;AAC9B,2BAAe,sBAAsB,cAAc,QAAQ,UAAU;AAAA,UACzE,WACS,UAAU,sBAAsB,cAAc,QAAQ,UAAU,OAAO;AAG5E,kBAAM,cAAc,IAAI,UAAU,eAAe,YAAY,KAAK;AAClE,sBAAU,KAAK,WAAW;AAE1B,sBAAU,KAAK,EAAE;AACjB,kBAAM,KAAK,SAAS;AAAA,UACxB,WACS,UAAU,sBAAsB,cAAc,QAAQ,UAAU;AAAA,WAEpE,CAAC,eAAe,uBACb,eAAe,uBAAuB,eAAe,QAAQ,UAAU,QAAQ,CAAC,eAAe,wBAAwB;AAE3H,kBAAM,UAAU,UAAU,sBAAsB,eAAe,QAAQ,UAAU,OAAO,QAAQ;AAEhG,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,OAAO,CAAC;AAEhE,kBAAM,KAAK,SAAS;AACpB,wBAAY,CAAC;AACb,2BAAe,aAAa,QAAQ;AACpC,2BAAe,eAAe;AAC9B,2BAAe,sBAAsB,cAAc,QAAQ,UAAU;AAAA,UACzE,WACS,cAAc,QAAQ,UAAU,QAAQ,eAAe,QAAQ,UAAU,SAC9E,CAAC,eAAe,uBAAuB,CAAC,eAAe,cAAc;AAErE,2BAAe,aAAa;AAC5B,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAE9B,gBAAI,MAAM,eAAe,KAAK,QAAQ,GAAG,kBAAkB,MAAM,QAAQ,UAAU,KAAK;AACpF,uBAAS,QAAQ,UAAU,IAAI,SAAS;AAAA,YAC5C;AAAA,UACJ,WACS,eAAe,QAAQ,UAAU,SAAS,cAAc,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,KAAK;AAG5H,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,QAAQ,CAAC,CAAC;AAClE,2BAAe,aAAa;AAC5B,2BAAe,eAAe;AAC9B,2BAAe,sBAAsB;AACrC,2BAAe,wBAAwB;AACvC,qBAAS;AAAA,UACb,YACU,eAAe,QAAQ,UAAU,QAAQ,eAAe,yBAAyB,eAAe,QAAQ,UAAU,SACxH,cAAc,QAAQ,UAAU,QAAQ,MAAM,eAAe,KAAK,QAAQ,GAAG,kBAAkB,MAAM,QAAQ,UAAU,KAAK;AAE5H,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAAA,UAElC,WACS,cAAc,QAAQ,UAAU,SAAS,UAAU,KAAK,MAAM,eAAe,KAAK,QAAQ,oBAAoB,kBAAkB,MAAM,QAAQ,UAAU,OAAO,CAAC,eAAe,sBAAsB;AAE1M,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAC9B,2BAAe,aAAa;AAAA,UAChC,WACS,cAAc,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,SAAS,eAAe,qBAAqB;AAE1H,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,QAAQ,CAAC,CAAC;AAClE,2BAAe,aAAa,QAAQ;AACpC,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAAA,UAClC,WACS,cAAc,QAAQ,UAAU,QAAQ,eAAe,QAAQ,UAAU,SAC9E,CAAC,eAAe,uBAAuB,eAAe,cAAc;AAEpE,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,QAAQ,CAAC,CAAC;AAClE,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAC9B,2BAAe,aAAa;AAAA,UAChC,WACS,cAAc,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,QAAQ,UAAU,eAAe,YAAY;AAE1H,qBAAS;AACT,2BAAe,wBAAwB;AACvC;AAAA,UACJ,WACS,cAAc,QAAQ,UAAU,SAAS,eAAe,QAAQ,UAAU,QAC/E,cAAc,QAAQ,UAAU,QAAQ,CAAC,eAAe,uBACxD,eAAe,cAAc;AAE7B,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,KAAK,CAAC;AAC9D,2BAAe,aAAa,QAAQ;AAAA,UACxC,WACS,cAAc,QAAQ,UAAU,SAAS,eAAe,QAAQ,UAAU,QAC/E,cAAc,QAAQ,UAAU,QAAQ,CAAC,eAAe,cAAc;AAGtE,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAC9B,2BAAe,aAAa,QAAQ;AAAA,UACxC;AAEA;AAEA,yBAAe,wBAAwB;AAAA,QAC3C;AACA,eAAO;AAAA,MACX;AAIA,eAAS,oBAAoB,QAAQ;AACjC,YAAI,QAAQ,cAAc;AACtB,iBAAO,cAAc,OAAO;AAAA,QAChC,OACK;AACD,iBAAO,cAAc,OAAO,MAAM,OAAO,CAAC;AAAA,QAC9C;AACA,eAAO;AAAA,MACX;AAIA,eAAS,4BAA4B,aAAaC,OAAM;AAEpD,cAAM,QAAQA,MAAK,YAAY,KAAK;AAEpC,eAAO,mBAAmB,KAAK;AAAA,MACnC;AAKA,eAAS,mBAAmB,YAAY;AAEpC,cAAM,aAAa,WAAW,UAAU;AAGxC,YAAI,CAAC,MAAM,QAAQ,UAAU,KAAK,CAAC,MAAM,UAAU,UAAU,GAAG;AAC5D,iBAAO;AAAA,QACX,WACS,eAAe,aAAa;AACjC,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAIA,eAAS,gBAAgB,YAAY;AACjC,YAAI,QAAQ,mBAAmB,eAAe,MAAM;AAChD,iBAAO,WAAW,KAAK;AAAA,QAC3B;AACA,eAAO;AAAA,MACX;AAMA,eAAS,eAAe,cAAcA,OAAM;AAExC,eAAO,aAAa,OAAO,CAACC,WAAU,gBAAgB;AAElD,gBAAM,QAAQ,4BAA4B,aAAaD,KAAI;AAC3D,cAAI;AAEA,oBAAQ,GAAG,WAAW,SAASC,WAAU,YAAY,OAAO,KAAK;AAAA,UACrE,SACO,OAAO;AAEV,mBAAOA;AAAA,UACX;AAAA,QACJ,GAAG,CAAC,CAAC;AAAA,MACT;AAKA,eAAS,8BAA8B,YAAY;AAC/C,cAAM,YAAY,WAAW,CAAC,GAAG,YAAY,WAAW,SAAS,GAAG,WAAW,WAAW,SAAS;AAEnG,YAAI,cAAc,QAAQ,UAAU,QAAQ,aAAa,QAAQ,UAAU,MAAM;AAE7E,iBAAO,WAAW,UAAU,IAAI,KAAK,WAAW,UAAU,GAAG,SAAS;AAAA,QAC1E;AACA,eAAO;AAAA,MACX;AAKA,eAAS,6BAA6B,YAAY;AAC9C,eAAO,WAAW,QAAQ,2BAA2B,QAAQ,UAAU,IAAI;AAAA,MAC/E;AAIA,eAAS,qBAAqB,QAAQ;AAElC,eAAO,OAAO,YAAY,OAAO,CAAC,sBAAsBD,UAAS;AAC7D,UAAAA,QAAOA,MAAK,IAAI,CAAC,eAAe;AAE5B,yBAAa,8BAA8B,UAAU;AACrD,yBAAa,6BAA6B,UAAU;AACpD,yBAAa,gBAAgB,UAAU;AACvC,mBAAO;AAAA,UACX,CAAC;AACD,gBAAM,oBAAoB,eAAe,OAAO,cAAcA,KAAI;AAClE,iBAAO,qBAAqB,OAAO,iBAAiB;AAAA,QACxD,GAAG,CAAC,CAAC;AAAA,MACT;AAIA,eAAS,WAAW,OAAO;AACvB,YAAI;AACA,cAAI,MAAM,uBAAuB,OAAO,OAAO,KAAK,CAAC,MAAM,qBAAqB,KAAK,GAAG;AACpF,mBAAO;AAAA,UACX;AACA,gBAAM,aAAa,cAAc,KAAK;AAEtC,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,mBAAO,WAAW,IAAI,eAAe;AAAA,UACzC;AACA,iBAAO;AAAA,QACX,SACO,KAAK;AACR,iBAAO;AAAA,QACX;AAAA,MACJ;AAIA,eAAS,QAAQ,MAAM;AAEnB,cAAM,WAAW,cAAc,IAAI;AACnC,cAAM,QAAQ,WAAW,QAAQ;AACjC,cAAM,UAAU,gBAAgB,KAAK;AACrC,cAAM,QAAQ,oBAAoB,OAAO;AACzC,eAAO,qBAAqB,KAAK;AAAA,MACrC;AACA,aAAO;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,WAAW;AAAA;AAAA;;;ACnXnB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW,QAAQ,WAAW;AACtC,QAAM,cAAc;AACpB,QAAM,aAAa;AACnB,QAAM,aAAa;AACnB,QAAM,UAAU;AAChB,aAASE,UAAS,MAAM,SAAS;AAC7B,YAAM,gBAAgB,GAAG,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAE/D,OAAC,GAAG,QAAQ,UAAU,MAAM,QAAQ,UAAU,YAAY,OAAO,QAAQ;AACzE,cAAQ,GAAG,WAAW,UAAU,YAAY,EAAE,QAAQ,IAAI;AAAA,IAC9D;AACA,YAAQ,WAAWA;AACnB,aAAS,SAAS,MAAM,SAAS;AAC7B,YAAM,gBAAgB,GAAG,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAE/D,OAAC,GAAG,QAAQ,UAAU,MAAM,QAAQ,UAAU,YAAY,OAAO,QAAQ;AACzE,cAAQ,GAAG,WAAW,UAAU,YAAY,EAAE,QAAQ,IAAI;AAAA,IAC9D;AACA,YAAQ,WAAW;AAAA;AAAA;A;;;;;;;;;;;;;;;;;;;;;;ACAN,IAAA,OAAqC,SAAc,OAAO;AAC/D,QAAA,EAAE,UAAU,WAAW,OAAO,OAAO,YAAY,MAAM,OAAA,IAAW;AAExE,MAAI,QAAuB;IACzB,MAAM;IACN,UAAU;IACV,SAAS;EACX;AAEI,WAAS,WACP,UAAU,aACZ,MAAM,QAAQ,QAEd,MAAM,SAAS,MACf,MAAM,UAAU,SAElB,MAAM,OAAO,SAGf,QAAQC,iBAAAA,iBAAAA,CAAAA,GAAK,KAAU,GAAA,UAAA;AAEjB,QAAA,UAAU,CAAC,QAAQ,OAAO,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAEjE,aAAA,wBAAC,OAAA,EAAI,MAAK,UAAS,KAAK,QAAQ,WAAW,SAAS,OACjD,SACH,CAAA;AAEJ;AA3Ba,IClBA,4BAA4B;ADkB5B,ICJA,UAA2C,SACtD,OACA;AACM,QAAA;IACJ,YAAY;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACE,IAAA,OAEE,UAAU,CAAC,kBAAkB,OAAO,SAAS,EAChD,OAAO,OAAO,EACd,KAAK,GAAG;AAGT,aAAA;IAAC;IAAA;MACC,MAAK;MACL,WAAW;MACX;MACA,aAAa,CAAC,UAAU,YAAY,MAAM,WAAW;MACrD,cAAc,CAAC,UAAU;AACvB,cAAM,eAAe,GACrB,aAAa,MAAM,WAAW;MAChC;MACA,YAAY,CAAC,UAAU;AACrB,cAAM,eAAe,GACrB,WAAW,MAAM,WAAW;MAC9B;MACA,SAAS,CAAC,UAAU;AACd,oBACF,MAAM,eAAA,GACN,QAAQ,MAAM,WAAW;MAE7B;MACA,eAAe,CAAC,UAAU;AACpB,0BACF,MAAM,eAAA,GACN,cAAc,MAAM,WAAW;MAAA;IAEnC;EACF;AAEJ;;;;;;;;;;;;;;;;;;AC3DA,IAAM,cAA6B;EACjC,SAAS;EACT,MAAM;EACN,QAAQ;EACR,UAAU;EACV,SAAS;EACT,UAAU;EACV,eAAe;EACf,kBAAkB;EAClB,cAAc;EACd,YAAY;AACd;AAXA,IAaM,kBAAiC,cAAA,eAAA,CAAA,GAClC,WADkC,GAAA;EAErC,eAAe;EACf,MAAM;EACN,OAAO;AACT,CAEM;AApBN,IAoBM,oBAAmC,cAAA,eAAA,CAAA,GACpC,WADoC,GAAA;EAEvC,QAAQ;EACR,eAAe;EACf,WAAW;EACX,KAAK;EACL,OAAO;AACT,CAAA;AA3BA,IA6BM,eAA8B,CAAC;AA7BrC,IAuFa,aAAN,MAAMC,oBAAkB,uBAA0C;EAevE,YAAY,OAAuB;AACjC,UAAM,KAAK,GALkBC,eAAA,MAAA,SAAA,IAAA,GACAA,eAAA,MAAA,SAAA,IAAA,GACIA,eAAA,MAAA,aAAA,IAAA,GAKjC,KAAK,cAAc,KAAK,YAAY,KAAK,IAAI,GAC7C,KAAK,eAAe,KAAK,aAAa,KAAK,IAAI,GAC/C,KAAK,cAAc,KAAK,YAAY,KAAK,IAAI,GAC7C,KAAK,cAAc,KAAK,YAAY,KAAK,IAAI,GAC7C,KAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAMzC,UAAM,EAAE,MAAM,aAAa,SAAS,SAAS,QAAA,IAAY,OAEnD,cACJ,SAAS,SACL,OACA,eAAe,aAAa,SAAS,SAAS,MAAS;AAE7D,SAAK,QAAQ;MACX,QAAQ;MACR,SAAS;MACT,WAAW,YAAY,UAAU,cAAc;MAC/C,WAAW,YAAY,WAAW,cAAc;;MAGhD,eAAe;QACb;MAAA;IAEJ;EAAA;EAGF,oBAAoB;AACT,aAAA,iBAAiB,WAAW,KAAK,SAAS,GACnD,SAAS,iBAAiB,aAAa,KAAK,WAAW,GACvD,SAAS,iBAAiB,aAAa,KAAK,WAAW,GACvD,KAAK,SAASD,YAAU,cAAc,KAAK,OAAO,KAAK,KAAK,CAAC;EAAA;EAG/D,OAAO,yBACL,WACA,WACA;AACO,WAAAA,YAAU,cAAc,WAAW,SAAS;EAAA;EAGrD,uBAAuB;AACrB,aAAS,oBAAoB,WAAW,KAAK,SAAS,GACtD,SAAS,oBAAoB,aAAa,KAAK,WAAW,GAC1D,SAAS,oBAAoB,aAAa,KAAK,WAAW;EAAA;EAG5D,YAAY,OAAmB;AACxB,SAAA,aAAa,cAAA,eAAA,CAAA,GACb,KADa,GAAA;MAEhB,SAAS,CAAC,EAAE,SAAS,MAAM,SAAS,SAAS,MAAM,QAAS,CAAA;IAAA,CAC7D,CAAA;EAAA;EAGH,aAAa,OAAuC;AAClD,UAAM,EAAE,aAAa,eAAe,MAAA,IAAU,KAAK;AACnD,QAAI,aAAa;AACf,cAAQ,UAAU,MAAM;AAClB,YAAA,WACJ,UAAU,aACN,MAAM,QAAQ,CAAC,EAAE,UACjB,MAAM,QAAQ,CAAC,EAAE;AAEnB,aAAO,iBAAkB,cAC3B,cAAc,GAEhB,KAAK,SAAS;QACZ,QAAQ;QACR;MAAA,CACD;IAAA;EACH;EAGF,YAAY,OAAmB;AAC7B,UAAM,mBAAmB,OAAO,OAAO,CAAA,GAAI,OAAO;MAChD,SAAS,CAAC,EAAE,SAAS,MAAM,SAAS,SAAS,MAAM,QAAS,CAAA;IAAA,CAC7D;AACD,SAAK,YAAY,gBAAgB;EAAA;EAGnC,YAAY,OAAuC;AACjD,QAAI,CAAC,KAAK,MAAM,UAAU,CAAC,KAAK,MAAM;AACpC;AAGF,UAAM,EAAE,WAAW,EAAA,IAAM,KAAK,OACxB;MACJ;MACA,UAAUA,YAAU,aAAa;MACjC;MACA,QAAQA,YAAU,aAAa;MAC/B;IAAA,IACE,KAAK;AAET,YAAQ,UAAU,MAAM;AACxB,UAAM,iBAAiB,KAAK,MAAM,YAAY,SACxC,MAAM,iBAAiB,KAAK,QAAQ,KAAK,OACzC,OAAO,iBAAiB,KAAK,QAAQ,KAAK;AAEhD,QAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI;AACxB;AAGF,UAAM,OAAO,KACP,QAAQ,MAER,QAAQ,KAAK,sBAAsB,EAAE,OACrC,SAAS,KAAK,sBAAA,EAAwB,QACtC,UACJ,UAAU,aACN,MAAM,QAAQ,CAAC,EAAE,UACjB,MAAM,QAAQ,CAAC,EAAE,SACjB,OAAO,UAAU,aAAa,QAAQ;AAE5C,QAAI,gBAAgB,WAAW;AAC/B,QAAI,MAAM;AACJ,UAAA,KAAK,IAAI,aAAa,IAAI;AAC5B;AAGc,sBAAA,CAAC,EAAE,gBAAgB,QAAQ;IAAA;AAEzC,QAAA,YAAY,iBAAiB,gBAAgB,CAAC;AAElD,UAAM,aAAa,SAAS,OAAO,iBAAiB,IAAI,EAAE,KAAK,GACzD,aAAa,SAAS,OAAO,iBAAiB,KAAK,EAAE,KAAK;AAC5D,iBAAa,eACf,YAAY,CAAC;AAGf,QAAI,aAAa;AACb,SAAK,aAAa,YAAY,UAAa,WAAW,MACpD,UAAU,aACZ,aAAa,KAAK,UAAU,sBAAsB,EAAE,QAAQ,UAE5D,aAAa,KAAK,UAAU,sBAAA,EAAwB,SAAS;AAIjE,QAAI,UAAU,OAAO;AACrB,UAAM,cAAc,WAAW;AAE3B,eAAW,UAAU,UACvB,UAAU,UACD,eAAe,UAAa,UAAU,aAC/C,UAAU,aAEV,KAAK,SAAS;MACZ,UAAU;MACV,SAAS;IAAA,CACV,GAGC,YAAU,SAAS,OAAO;AAE9B,UAAM,YAAY,iBACd,EAAE,WAAW,SAAS,WAAW,OAAU,IAC3C,EAAE,WAAW,SAAS,WAAW,OAAU;AAE/C,SAAK,SAAS,eAAA,EAAE,aAAa,QAAA,GAAY,SAAW,CAAA;EAAA;EAGtD,YAAY;AACV,QAAI,CAAC,KAAK,MAAM,UAAU,CAAC,KAAK,MAAM;AACpC;AAGI,UAAA,EAAE,eAAA,IAAmB,KAAK,OAC1B,EAAE,YAAA,IAAgB,KAAK;AAG3B,WAAO,cAAgB,OACvB,OAAO,kBAAmB,cAE1B,eAAe,WAAW,GAG5B,KAAK,SAAS,EAAE,QAAQ,MAAA,CAAO;EAAA;;EAIjC,OAAO,cAAc,OAAuB,OAAuB;AAC3D,UAAA,EAAE,cAAA,IAAkB;AAE1B,QAAI,cAAc,SAAS,MAAM,QAAQ,MAAM,SAAS;AACtD,aAAO,CAAC;AAGV,UAAM,UACJ,MAAM,SAAS,SACX,MAAM,OACN;MACE,MAAM;MACN,MAAM;MACN,MAAM;MACN,MAAM;IAAA,GAIR,YADiB,MAAM,YAAY,UAErC,EAAE,WAAW,SAAS,WAAW,OACjC,IAAA,EAAE,WAAW,SAAS,WAAW,OAAU;AAExC,WAAA,cAAA,eAAA,eAAA,CAAA,GACF,SACC,GAAA,OAAO,MAAM,OAAS,MAAc,CAAK,IAAA,EAAE,aAAa,QAFvD,CAAA,GAAA;MAGL,eAAe,EAAE,MAAM,MAAM,KAAK;IAAA,CACpC;EAAA;EAGF,SAAS;AACD,UAAA;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,YAAY;MACZ,YAAY;MACZ,mBAAmB;MACnB;MACA;MACA,OAAO;IACT,IAAI,KAAK,OAEH,EAAE,WAAW,UAAU,IAAI,KAAK,OAEhC,gBAAgB,cAAc,KAAK,YACnC,oCAAoC,oBACtC,GAAG,gBAAgB,IAAI,yBAAyB,IAG9C,kBAAkB,mBAAmB,QAAQ,GAE7C,aACJ,UAAU,aAAa,kBAAkB,mBAErC,QAAuB,aACzB,eAAK,eAAA,CAAA,GAAA,UAAA,GAAe,UACpB,IAAA,YAEE,UAAU,CAAC,aAAa,WAAW,OAAO,aAAa,EAC1D,OAAO,OAAO,EACd,KAAK,GAAG,GAEL,aAAa;MACjB,eAAA,eAAA,CAAA,GAAK,SAAc,GAAA,eAAA;MACnB;IAAA,GAEI,aAAa;MACjB,eAAA,eAAA,CAAA,GAAK,SAAc,GAAA,eAAA;MACnB;IAAA,GAGI,eAAe,CAAC,SAAS,eAAe,cAAc,EAAE,KAAK,GAAG,GAChE,eAAe,CAAC,SAAS,eAAe,cAAc,EAAE,KAAK,GAAG;AAGpE,eAAA;MAAC;MAAA;QACC,eAAY;QACZ,WAAW;QACX;QACA,KAAK,CAAC,SAAS;AACb,eAAK,YAAY;QACnB;QAEA,UAAA;cAAA;YAAC;YAAA;cACC,WAAW;cAEX,QAAQ,CAAC,SAAS;AAChB,qBAAK,QAAQ;cACf;cACA,MAAM;cACN;cACA,OAAO;cAEN,UAAA,gBAAgB,CAAC;YAAA;YARd;UASN;cACA;YAAC;YAAA;cACC,WAAW;cACX,SAAS;cACT,eAAe;cACf,aAAa,KAAK;cAClB,cAAc,KAAK;cACnB,YAAY,KAAK;cAEjB,kBAAkB;cAClB,OAAO,SAAS;cAChB,OAAO,gBAAgB;YAAA;YAHnB;UAIN;cACA;YAAC;YAAA;cACC,WAAW;cAEX,QAAQ,CAAC,SAAS;AAChB,qBAAK,QAAQ;cACf;cACA,MAAM;cACN;cACA,OAAO;cAEN,UAAA,gBAAgB,CAAC;YAAA;YARd;UAAA;QASN;MAAA;IACF;EAAA;AAGN;AAzUEC,eADW,YACJ,gBAAe;EACpB,aAAa;EACb,SAAS;EACT,SAAS;EACT,OAAO;EACP,eAAe;EACf,gBAAgB;EAChB,gBAAgB;AAClB,CAAA;AATK,IAAM,YAAN;AA4UP,SAAS,QACPC,WACAC,SACA;AA1aF,MAAA;AA2aE,MACE,eAAeD,aACf,OAAOA,UAAS,aAAc,YAC9BA,UAAS,aACT,WAAWA,UAAS,aACpB,OAAOA,UAAS,UAAU,SAAU;AAEhC,QAAA;AACFA,gBAAS,UAAU,MAAM;IAAA,SAClB,GAAG;IAAA;WAEZ,OAAOC,UAAW,OAClB,OAAOA,QAAO,gBAAiB;AAE3B,QAAA;AACFA,OAAAA,KAAAA,QAAO,aAAa,MAApB,QAAuB,GAAA,gBAAA;IAAA,SAChB,GAAG;IAAA;AAEhB;AAEA,SAAS,eACP,aACA,SACA,SACA,aACA;AACI,MAAA,OAAO,eAAgB,UAAU;AACnC,UAAM,MAAM,OAAO,WAAY,WAAW,UAAU,GAC9C,MACJ,OAAO,WAAY,YAAY,WAAW,IAAI,UAAU,IAAA;AAC1D,WAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,WAAW,CAAC;EAAA;AAE7C,SAAA,gBAAgB,SACX,cAEF;AACT;AAEA,SAAS,mBAAmB,UAAkC;AAC5D,SAAO,sBAAS,QAAQ,QAAQ,EAAE,OAAO,CAACC,OAAMA,EAAC;AACnD;AAEA,SAAS,WAAW,KAA0D;AAE1E,SAAA,QAAQ,QAAQ,OAAO,MAAQ,OAAe,OAAO,KAAK,GAAG,EAAE,WAAW;AAE9E;AAEA,SAAS,gBACP,KACA,YACG;AACI,SAAA,WAAW,GAAG,IAAI,aAAc;AACzC;A;;;;;AC/dA,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,cAAc;AAElB,IAAI,UAAU;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAI,OAAO;AAAA,EACP,iBAAkBC,IAAG;AACjB,WAAO,OAAOA,OAAM,YAAY,QAAQ,gBAAgB,KAAKA,EAAC;AAAA,EAClE;AAAA,EAEA,cAAeA,IAAG;AACd,WAAO,OAAOA,OAAM,aACfA,MAAK,OAAOA,MAAK,OACrBA,MAAK,OAAOA,MAAK,OACjBA,OAAM,OAASA,OAAM,OACtB,QAAQ,SAAS,KAAKA,EAAC;AAAA,EAE3B;AAAA,EAEA,iBAAkBA,IAAG;AACjB,WAAO,OAAOA,OAAM,aACfA,MAAK,OAAOA,MAAK,OACrBA,MAAK,OAAOA,MAAK,OACjBA,MAAK,OAAOA,MAAK,OACjBA,OAAM,OAASA,OAAM,OACrBA,OAAM,OAAcA,OAAM,OAC3B,QAAQ,YAAY,KAAKA,EAAC;AAAA,EAE9B;AAAA,EAEA,QAASA,IAAG;AACR,WAAO,OAAOA,OAAM,YAAY,QAAQ,KAAKA,EAAC;AAAA,EAClD;AAAA,EAEA,WAAYA,IAAG;AACX,WAAO,OAAOA,OAAM,YAAY,cAAc,KAAKA,EAAC;AAAA,EACxD;AACJ;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,QAAQ,SAASC,OAAO,MAAM,SAAS;AACvC,WAAS,OAAO,IAAI;AACpB,eAAa;AACb,UAAQ,CAAC;AACT,QAAM;AACN,SAAO;AACP,WAAS;AACT,UAAQ;AACR,QAAM;AACN,SAAO;AAEP,KAAG;AACC,YAAQ,IAAI;AAOZ,gBAAY,UAAU,EAAE;AAAA,EAC5B,SAAS,MAAM,SAAS;AAExB,MAAI,OAAO,YAAY,YAAY;AAC/B,WAAO,YAAY,EAAC,IAAI,KAAI,GAAG,IAAI,OAAO;AAAA,EAC9C;AAEA,SAAO;AACX;AAEA,SAAS,YAAa,QAAQ,MAAM,SAAS;AACzC,QAAM,QAAQ,OAAO,IAAI;AACzB,MAAI,SAAS,QAAQ,OAAO,UAAU,UAAU;AAC5C,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAMC,OAAM,OAAO,CAAC;AACpB,cAAM,cAAc,YAAY,OAAOA,MAAK,OAAO;AACnD,YAAI,gBAAgB,QAAW;AAC3B,iBAAO,MAAMA,IAAG;AAAA,QACpB,OAAO;AACH,iBAAO,eAAe,OAAOA,MAAK;AAAA,YAC9B,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,OAAO;AACH,iBAAWA,QAAO,OAAO;AACrB,cAAM,cAAc,YAAY,OAAOA,MAAK,OAAO;AACnD,YAAI,gBAAgB,QAAW;AAC3B,iBAAO,MAAMA,IAAG;AAAA,QACpB,OAAO;AACH,iBAAO,eAAe,OAAOA,MAAK;AAAA,YAC9B,OAAO;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,QAAQ,KAAK,QAAQ,MAAM,KAAK;AAC3C;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,MAAO;AACZ,aAAW;AACX,WAAS;AACT,gBAAc;AACd,SAAO;AAEP,aAAS;AACL,QAAI,KAAK;AAOT,UAAMC,SAAQ,UAAU,QAAQ,EAAE;AAClC,QAAIA,QAAO;AACP,aAAOA;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,SAAS,OAAQ;AACb,MAAI,OAAO,GAAG,GAAG;AACb,WAAO,OAAO,cAAc,OAAO,YAAY,GAAG,CAAC;AAAA,EACvD;AACJ;AAEA,SAAS,OAAQ;AACb,QAAMH,KAAI,KAAK;AAEf,MAAIA,OAAM,MAAM;AACZ;AACA,aAAS;AAAA,EACb,WAAWA,IAAG;AACV,cAAUA,GAAE;AAAA,EAChB,OAAO;AACH;AAAA,EACJ;AAEA,MAAIA,IAAG;AACH,WAAOA,GAAE;AAAA,EACb;AAEA,SAAOA;AACX;AAEA,IAAM,YAAY;AAAA,EACd,UAAW;AACP,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,eAAO,SAAS,KAAK;AAAA,IACzB;AAEA,QAAI,KAAK,iBAAiB,CAAC,GAAG;AAC1B,WAAK;AACL;AAAA,IACJ;AAOA,WAAO,UAAU,UAAU,EAAE;AAAA,EACjC;AAAA,EAEA,UAAW;AACP,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,mBAAoB;AAChB,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,2BAA4B;AACxB,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AACL,eAAW;AAAA,EACf;AAAA,EAEA,oBAAqB;AACjB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,eAAO,SAAS,KAAK;AAAA,IACzB;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,QAAS;AACL,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,MAExC,KAAK;AACD,aAAK;AACL,gBAAQ,KAAK;AACb,eAAO,SAAS,QAAQ,IAAI;AAAA,MAEhC,KAAK;AACD,aAAK;AACL,gBAAQ,KAAK;AACb,eAAO,SAAS,WAAW,IAAI;AAAA,MAEnC,KAAK;AACD,aAAK;AACL,gBAAQ,MAAM;AACd,eAAO,SAAS,WAAW,KAAK;AAAA,MAEpC,KAAK;AAAA,MACL,KAAK;AACD,YAAI,KAAK,MAAM,KAAK;AAChB,iBAAO;AAAA,QACX;AAEA,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,gBAAQ,SAAS;AACjB,eAAO,SAAS,WAAW,QAAQ;AAAA,MAEvC,KAAK;AACD,aAAK;AACL,gBAAQ,IAAI;AACZ,eAAO,SAAS,WAAW,GAAG;AAAA,MAElC,KAAK;AAAA,MACL,KAAK;AACD,sBAAe,KAAK,MAAM;AAC1B,iBAAS;AACT,mBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,4BAA6B;AACzB,QAAI,MAAM,KAAK;AACX,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AACL,UAAM,IAAI,cAAc;AACxB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MAEJ;AACI,YAAI,CAAC,KAAK,cAAc,CAAC,GAAG;AACxB,gBAAM,kBAAkB;AAAA,QAC5B;AAEA;AAAA,IACJ;AAEA,cAAU;AACV,eAAW;AAAA,EACf;AAAA,EAEA,iBAAkB;AACd,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,iBAAiB,CAAC,GAAG;AAC1B,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,cAAc,MAAM;AAAA,EACxC;AAAA,EAEA,uBAAwB;AACpB,QAAI,MAAM,KAAK;AACX,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AACL,UAAM,IAAI,cAAc;AACxB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MAEJ;AACI,YAAI,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC3B,gBAAM,kBAAkB;AAAA,QAC5B;AAEA;AAAA,IACJ;AAEA,cAAU;AACV,eAAW;AAAA,EACf;AAAA,EAEA,OAAQ;AACJ,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,gBAAQ,SAAS;AACjB,eAAO,SAAS,WAAW,OAAO,QAAQ;AAAA,MAE9C,KAAK;AACD,aAAK;AACL,gBAAQ,IAAI;AACZ,eAAO,SAAS,WAAW,GAAG;AAAA,IAClC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,OAAQ;AACJ,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,CAAC;AAAA,EACvC;AAAA,EAEA,iBAAkB;AACd,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,sBAAuB;AACnB,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,eAAgB;AACZ,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,kBAAmB;AACf,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,kBAAmB;AACf,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,kBAAU,KAAK;AACf,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,sBAAuB;AACnB,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,yBAA0B;AACtB,QAAI,KAAK,QAAQ,CAAC,GAAG;AACjB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,cAAe;AACX,QAAI,KAAK,WAAW,CAAC,GAAG;AACpB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,qBAAsB;AAClB,QAAI,KAAK,WAAW,CAAC,GAAG;AACpB,gBAAU,KAAK;AACf;AAAA,IACJ;AAEA,WAAO,SAAS,WAAW,OAAO,OAAO,MAAM,CAAC;AAAA,EACpD;AAAA,EAEA,SAAU;AACN,YAAQ,GAAG;AAAA,MACX,KAAK;AACD,aAAK;AACL,kBAAU,OAAO;AACjB;AAAA,MAEJ,KAAK;AACD,YAAI,aAAa;AACb,eAAK;AACL,iBAAO,SAAS,UAAU,MAAM;AAAA,QACpC;AAEA,kBAAU,KAAK;AACf;AAAA,MAEJ,KAAK;AACD,YAAI,CAAC,aAAa;AACd,eAAK;AACL,iBAAO,SAAS,UAAU,MAAM;AAAA,QACpC;AAEA,kBAAU,KAAK;AACf;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,MAE5B,KAAK;AAAA,MACL,KAAK;AACD,sBAAc,CAAC;AACf;AAAA,MAEJ,KAAK;AACD,cAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,cAAU,KAAK;AAAA,EACnB;AAAA,EAEA,QAAS;AACL,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IAKxC;AAEA,eAAW;AAAA,EACf;AAAA,EAEA,qBAAsB;AAClB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,KAAK;AACd,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,aAAK;AACL,mBAAW;AACX;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,MAExC,KAAK;AAAA,MACL,KAAK;AACD,sBAAe,KAAK,MAAM;AAC1B,mBAAW;AACX;AAAA,IACJ;AAEA,QAAI,KAAK,cAAc,CAAC,GAAG;AACvB,gBAAU,KAAK;AACf,iBAAW;AACX;AAAA,IACJ;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,oBAAqB;AACjB,QAAI,MAAM,KAAK;AACX,aAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,sBAAuB;AACnB,eAAW;AAAA,EACf;AAAA,EAEA,qBAAsB;AAClB,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,mBAAoB;AAChB,QAAI,MAAM,KAAK;AACX,aAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,eAAW;AAAA,EACf;AAAA,EAEA,kBAAmB;AACf,YAAQ,GAAG;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,eAAO,SAAS,cAAc,KAAK,CAAC;AAAA,IACxC;AAEA,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,MAAO;AAOH,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AACJ;AAEA,SAAS,SAAU,MAAM,OAAO;AAC5B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,QAAS,GAAG;AACjB,aAAWA,MAAK,GAAG;AACf,UAAM,IAAI,KAAK;AAEf,QAAI,MAAMA,IAAG;AACT,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,SAAK;AAAA,EACT;AACJ;AAEA,SAAS,SAAU;AACf,QAAMA,KAAI,KAAK;AACf,UAAQA,IAAG;AAAA,IACX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,UAAI,KAAK,QAAQ,KAAK,CAAC,GAAG;AACtB,cAAM,YAAY,KAAK,CAAC;AAAA,MAC5B;AAEA,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,aAAO,UAAU;AAAA,IAErB,KAAK;AACD,WAAK;AACL,aAAO,cAAc;AAAA,IAEzB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,WAAK;AACL,aAAO;AAAA,IAEX,KAAK;AACD,WAAK;AACL,UAAI,KAAK,MAAM,MAAM;AACjB,aAAK;AAAA,MACT;AAEA,aAAO;AAAA,IAEX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,YAAM,YAAY,KAAK,CAAC;AAAA,IAE5B,KAAK;AACD,YAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAEA,SAAO,KAAK;AAChB;AAEA,SAAS,YAAa;AAClB,MAAII,UAAS;AACb,MAAIJ,KAAI,KAAK;AAEb,MAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACrB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAEA,EAAAI,WAAU,KAAK;AAEf,EAAAJ,KAAI,KAAK;AACT,MAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACrB,UAAM,YAAY,KAAK,CAAC;AAAA,EAC5B;AAEA,EAAAI,WAAU,KAAK;AAEf,SAAO,OAAO,cAAc,SAASA,SAAQ,EAAE,CAAC;AACpD;AAEA,SAAS,gBAAiB;AACtB,MAAIA,UAAS;AACb,MAAI,QAAQ;AAEZ,SAAO,UAAU,GAAG;AAChB,UAAMJ,KAAI,KAAK;AACf,QAAI,CAAC,KAAK,WAAWA,EAAC,GAAG;AACrB,YAAM,YAAY,KAAK,CAAC;AAAA,IAC5B;AAEA,IAAAI,WAAU,KAAK;AAAA,EACnB;AAEA,SAAO,OAAO,cAAc,SAASA,SAAQ,EAAE,CAAC;AACpD;AAEA,IAAM,cAAc;AAAA,EAChB,QAAS;AACL,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,qBAAsB;AAClB,YAAQ,MAAM,MAAM;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AACD,cAAM,MAAM;AACZ,qBAAa;AACb;AAAA,MAEJ,KAAK;AAMD,YAAI;AACJ;AAAA,MAEJ,KAAK;AACD,cAAM,WAAW;AAAA,IACrB;AAAA,EAIJ;AAAA,EAEA,oBAAqB;AAMjB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,iBAAa;AAAA,EACjB;AAAA,EAEA,sBAAuB;AACnB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,mBAAoB;AAChB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,QAAI,MAAM,SAAS,gBAAgB,MAAM,UAAU,KAAK;AACpD,UAAI;AACJ;AAAA,IACJ;AAEA,SAAK;AAAA,EACT;AAAA,EAEA,qBAAsB;AAMlB,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,YAAQ,MAAM,OAAO;AAAA,MACrB,KAAK;AACD,qBAAa;AACb;AAAA,MAEJ,KAAK;AACD,YAAI;AAAA,IACR;AAAA,EAIJ;AAAA,EAEA,kBAAmB;AAMf,QAAI,MAAM,SAAS,OAAO;AACtB,YAAM,WAAW;AAAA,IACrB;AAEA,YAAQ,MAAM,OAAO;AAAA,MACrB,KAAK;AACD,qBAAa;AACb;AAAA,MAEJ,KAAK;AACD,YAAI;AAAA,IACR;AAAA,EAIJ;AAAA,EAEA,MAAO;AAAA,EAKP;AACJ;AAEA,SAAS,OAAQ;AACb,MAAI;AAEJ,UAAQ,MAAM,MAAM;AAAA,IACpB,KAAK;AACD,cAAQ,MAAM,OAAO;AAAA,QACrB,KAAK;AACD,kBAAQ,CAAC;AACT;AAAA,QAEJ,KAAK;AACD,kBAAQ,CAAC;AACT;AAAA,MACJ;AAEA;AAAA,IAEJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,cAAQ,MAAM;AACd;AAAA,EAKJ;AAEA,MAAI,SAAS,QAAW;AACpB,WAAO;AAAA,EACX,OAAO;AACH,UAAM,SAAS,MAAM,MAAM,SAAS,CAAC;AACrC,QAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,aAAO,KAAK,KAAK;AAAA,IACrB,OAAO;AACH,aAAO,eAAe,QAAQ,KAAK;AAAA,QAC/B;AAAA,QACA,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,MAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC7C,UAAM,KAAK,KAAK;AAEhB,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,mBAAa;AAAA,IACjB,OAAO;AACH,mBAAa;AAAA,IACjB;AAAA,EACJ,OAAO;AACH,UAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,QAAI,WAAW,MAAM;AACjB,mBAAa;AAAA,IACjB,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC/B,mBAAa;AAAA,IACjB,OAAO;AACH,mBAAa;AAAA,IACjB;AAAA,EACJ;AACJ;AAEA,SAAS,MAAO;AACZ,QAAM,IAAI;AAEV,QAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,MAAI,WAAW,MAAM;AACjB,iBAAa;AAAA,EACjB,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC/B,iBAAa;AAAA,EACjB,OAAO;AACH,iBAAa;AAAA,EACjB;AACJ;AAYA,SAAS,YAAaJ,IAAG;AACrB,MAAIA,OAAM,QAAW;AACjB,WAAO,YAAY,kCAAkC,IAAI,IAAI,MAAM,EAAE;AAAA,EACzE;AAEA,SAAO,YAAY,6BAA6B,WAAWA,EAAC,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;AACzF;AAEA,SAAS,aAAc;AACnB,SAAO,YAAY,kCAAkC,IAAI,IAAI,MAAM,EAAE;AACzE;AAYA,SAAS,oBAAqB;AAC1B,YAAU;AACV,SAAO,YAAY,0CAA0C,IAAI,IAAI,MAAM,EAAE;AACjF;AAEA,SAAS,cAAeA,IAAG;AACvB,UAAQ,KAAK,WAAW,WAAWA,EAAC,CAAC,yDAAyD;AAClG;AAEA,SAAS,WAAYA,IAAG;AACpB,QAAM,eAAe;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AAEA,MAAI,aAAaA,EAAC,GAAG;AACjB,WAAO,aAAaA,EAAC;AAAA,EACzB;AAEA,MAAIA,KAAI,KAAK;AACT,UAAM,YAAYA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AAC7C,WAAO,SAAS,OAAO,WAAW,UAAU,UAAU,MAAM;AAAA,EAChE;AAEA,SAAOA;AACX;AAEA,SAAS,YAAa,SAAS;AAC3B,QAAM,MAAM,IAAI,YAAY,OAAO;AACnC,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,SAAO;AACX;AAEA,IAAI,YAAY,SAASK,WAAW,OAAO,UAAU,OAAO;AACxD,QAAMC,SAAQ,CAAC;AACf,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM;AACV,MAAI;AAEJ,MACI,YAAY,QACZ,OAAO,aAAa,YACpB,CAAC,MAAM,QAAQ,QAAQ,GACzB;AACE,YAAQ,SAAS;AACjB,YAAQ,SAAS;AACjB,eAAW,SAAS;AAAA,EACxB;AAEA,MAAI,OAAO,aAAa,YAAY;AAChC,mBAAe;AAAA,EACnB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAChC,mBAAe,CAAC;AAChB,eAAW,KAAK,UAAU;AACtB,UAAI;AAEJ,UAAI,OAAO,MAAM,UAAU;AACvB,eAAO;AAAA,MACX,WACI,OAAO,MAAM,YACb,aAAa,UACb,aAAa,QACf;AACE,eAAO,OAAO,CAAC;AAAA,MACnB;AAEA,UAAI,SAAS,UAAa,aAAa,QAAQ,IAAI,IAAI,GAAG;AACtD,qBAAa,KAAK,IAAI;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,iBAAiB,QAAQ;AACzB,YAAQ,OAAO,KAAK;AAAA,EACxB,WAAW,iBAAiB,QAAQ;AAChC,YAAQ,OAAO,KAAK;AAAA,EACxB;AAEA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,QAAQ,GAAG;AACX,cAAQ,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACtC,YAAM,aAAa,OAAO,GAAG,KAAK;AAAA,IACtC;AAAA,EACJ,WAAW,OAAO,UAAU,UAAU;AAClC,UAAM,MAAM,OAAO,GAAG,EAAE;AAAA,EAC5B;AAEA,SAAO,kBAAkB,IAAI,EAAC,IAAI,MAAK,CAAC;AAExC,WAAS,kBAAmBJ,MAAK,QAAQ;AACrC,QAAIK,SAAQ,OAAOL,IAAG;AACtB,QAAIK,UAAS,MAAM;AACf,UAAI,OAAOA,OAAM,YAAY,YAAY;AACrC,QAAAA,SAAQA,OAAM,QAAQL,IAAG;AAAA,MAC7B,WAAW,OAAOK,OAAM,WAAW,YAAY;AAC3C,QAAAA,SAAQA,OAAM,OAAOL,IAAG;AAAA,MAC5B;AAAA,IACJ;AAEA,QAAI,cAAc;AACd,MAAAK,SAAQ,aAAa,KAAK,QAAQL,MAAKK,MAAK;AAAA,IAChD;AAEA,QAAIA,kBAAiB,QAAQ;AACzB,MAAAA,SAAQ,OAAOA,MAAK;AAAA,IACxB,WAAWA,kBAAiB,QAAQ;AAChC,MAAAA,SAAQ,OAAOA,MAAK;AAAA,IACxB,WAAWA,kBAAiB,SAAS;AACjC,MAAAA,SAAQA,OAAM,QAAQ;AAAA,IAC1B;AAEA,YAAQA,QAAO;AAAA,MACf,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAM,eAAO;AAAA,MAClB,KAAK;AAAO,eAAO;AAAA,IACnB;AAEA,QAAI,OAAOA,WAAU,UAAU;AAC3B,aAAO,YAAYA,QAAO,KAAK;AAAA,IACnC;AAEA,QAAI,OAAOA,WAAU,UAAU;AAC3B,aAAO,OAAOA,MAAK;AAAA,IACvB;AAEA,QAAI,OAAOA,WAAU,UAAU;AAC3B,aAAO,MAAM,QAAQA,MAAK,IAAI,eAAeA,MAAK,IAAI,gBAAgBA,MAAK;AAAA,IAC/E;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,YAAaA,QAAO;AACzB,UAAM,SAAS;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AAEA,UAAM,eAAe;AAAA,MACjB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AAEA,QAAI,UAAU;AAEd,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACnC,YAAMP,KAAIO,OAAM,CAAC;AACjB,cAAQP,IAAG;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AACD,iBAAOA,EAAC;AACR,qBAAWA;AACX;AAAA,QAEJ,KAAK;AACD,cAAI,KAAK,QAAQO,OAAM,IAAI,CAAC,CAAC,GAAG;AAC5B,uBAAW;AACX;AAAA,UACJ;AAAA,MACJ;AAEA,UAAI,aAAaP,EAAC,GAAG;AACjB,mBAAW,aAAaA,EAAC;AACzB;AAAA,MACJ;AAEA,UAAIA,KAAI,KAAK;AACT,YAAI,YAAYA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE;AAC3C,mBAAW,SAAS,OAAO,WAAW,UAAU,UAAU,MAAM;AAChE;AAAA,MACJ;AAEA,iBAAWA;AAAA,IACf;AAEA,UAAM,YAAY,SAAS,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,GAAG,MAAO,OAAO,CAAC,IAAI,OAAO,CAAC,IAAK,IAAI,CAAC;AAE/F,cAAU,QAAQ,QAAQ,IAAI,OAAO,WAAW,GAAG,GAAG,aAAa,SAAS,CAAC;AAE7E,WAAO,YAAY,UAAU;AAAA,EACjC;AAEA,WAAS,gBAAiBO,QAAO;AAC7B,QAAID,OAAM,QAAQC,MAAK,KAAK,GAAG;AAC3B,YAAM,UAAU,wCAAwC;AAAA,IAC5D;AAEA,IAAAD,OAAM,KAAKC,MAAK;AAEhB,QAAI,WAAW;AACf,aAAS,SAAS;AAElB,QAAI,OAAO,gBAAgB,OAAO,KAAKA,MAAK;AAC5C,QAAI,UAAU,CAAC;AACf,eAAWL,QAAO,MAAM;AACpB,YAAM,iBAAiB,kBAAkBA,MAAKK,MAAK;AACnD,UAAI,mBAAmB,QAAW;AAC9B,YAAI,SAAS,aAAaL,IAAG,IAAI;AACjC,YAAI,QAAQ,IAAI;AACZ,oBAAU;AAAA,QACd;AACA,kBAAU;AACV,gBAAQ,KAAK,MAAM;AAAA,MACvB;AAAA,IACJ;AAEA,QAAI;AACJ,QAAI,QAAQ,WAAW,GAAG;AACtB,cAAQ;AAAA,IACZ,OAAO;AACH,UAAI;AACJ,UAAI,QAAQ,IAAI;AACZ,qBAAa,QAAQ,KAAK,GAAG;AAC7B,gBAAQ,MAAM,aAAa;AAAA,MAC/B,OAAO;AACH,YAAI,YAAY,QAAQ;AACxB,qBAAa,QAAQ,KAAK,SAAS;AACnC,gBAAQ,QAAQ,SAAS,aAAa,QAAQ,WAAW;AAAA,MAC7D;AAAA,IACJ;AAEA,IAAAI,OAAM,IAAI;AACV,aAAS;AACT,WAAO;AAAA,EACX;AAEA,WAAS,aAAcJ,MAAK;AACxB,QAAIA,KAAI,WAAW,GAAG;AAClB,aAAO,YAAYA,MAAK,IAAI;AAAA,IAChC;AAEA,UAAM,YAAY,OAAO,cAAcA,KAAI,YAAY,CAAC,CAAC;AACzD,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAChC,aAAO,YAAYA,MAAK,IAAI;AAAA,IAChC;AAEA,aAAS,IAAI,UAAU,QAAQ,IAAIA,KAAI,QAAQ,KAAK;AAChD,UAAI,CAAC,KAAK,iBAAiB,OAAO,cAAcA,KAAI,YAAY,CAAC,CAAC,CAAC,GAAG;AAClE,eAAO,YAAYA,MAAK,IAAI;AAAA,MAChC;AAAA,IACJ;AAEA,WAAOA;AAAA,EACX;AAEA,WAAS,eAAgBK,QAAO;AAC5B,QAAID,OAAM,QAAQC,MAAK,KAAK,GAAG;AAC3B,YAAM,UAAU,wCAAwC;AAAA,IAC5D;AAEA,IAAAD,OAAM,KAAKC,MAAK;AAEhB,QAAI,WAAW;AACf,aAAS,SAAS;AAElB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACnC,YAAM,iBAAiB,kBAAkB,OAAO,CAAC,GAAGA,MAAK;AACzD,cAAQ,KAAM,mBAAmB,SAAa,iBAAiB,MAAM;AAAA,IACzE;AAEA,QAAI;AACJ,QAAI,QAAQ,WAAW,GAAG;AACtB,cAAQ;AAAA,IACZ,OAAO;AACH,UAAI,QAAQ,IAAI;AACZ,YAAI,aAAa,QAAQ,KAAK,GAAG;AACjC,gBAAQ,MAAM,aAAa;AAAA,MAC/B,OAAO;AACH,YAAI,YAAY,QAAQ;AACxB,YAAI,aAAa,QAAQ,KAAK,SAAS;AACvC,gBAAQ,QAAQ,SAAS,aAAa,QAAQ,WAAW;AAAA,MAC7D;AAAA,IACJ;AAEA,IAAAD,OAAM,IAAI;AACV,aAAS;AACT,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,QAAQ;AAAA,EACV;AAAA,EACA;AACJ;AAEA,IAAI,MAAM;AAEV,IAAO,eAAQ;A;;;;;ACj5CFE,IAAAA,eAAe,CAC1B,MACA,MACA,eACA,eACA,eACA,eACA,KAAQC,oBAAAA,KAAOC,GAAAA,YAAAA,EAAcC,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE;AAPjCH,IASA,CAACI,mBAAmB,IAAIJ,aAAaK,MAAM,EAAE;ACDnD,SAAAC,eAAAC,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GACL,CAAAC,MAAAC,OAAA,QAAwBC,wBAAAA,KAAc;AAAC,MAAAC,IAAAC;AAAAN,IAAA,CAAA,MAAAD,MAAAQ,SAE7BF,KAAAA,MAAA;AACRG,UAAAA,QAAcC,WAAA,MAAiBN,QAAO,IAAK,GAAGJ,MAAKQ,SAAA,GAAa;AACnDG,WAAAA,MAAAA,aAAaF,KAAK;EAC9BF,GAAAA,KAAA,CAACP,MAAKQ,KAAA,GAAOP,EAAA,CAAA,IAAAD,MAAAQ,OAAAP,EAAAA,CAAAA,IAAAK,IAAAL,EAAAA,CAAAA,IAAAM,OAAAD,KAAAL,EAAA,CAAA,GAAAM,KAAAN,EAAA,CAAA,QAHhBW,yBAAUN,IAGPC,EAAa;AAACM,MAAAA;AAAAZ,SAAAA,EAAAA,CAAAA,MAAAE,QAEVU,KAAAV,WAAQ,yBAAA,SAAA,EAAQ,OAAA,MAAY,MAAA,EAAA,CAAA,IAAY,MAAAF,EAAAA,CAAAA,IAAAE,MAAAF,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA,GAAxCY;AAAwC;ACIpCC,IAAAA,uBAAoC,CAC/C,CAACC,kBAAkB,GACnBC,YAAY,GACZC,oBAAAA,GACAC,0BAAAA,GACAC,0BAAAA,GACAC,sBACAC,GAAAA,cACAC,GAAAA,gBACAC,GAAAA,cACAC,GAAAA,QAAAA,GACAC,cAAAA,GACAC,mBAAmBC,uBAAuB;EAACC,UAAU;AAAI,CAAC,GAC1DC,OAAOC,GACL;;EAEE;IAACC,KAAK;IAAaC,KAAKA,MAAM;EAAI;;EAGlCC;EACAC;AAAa,EAEZC,KAAK,EACLC,OAAOC,OAAO,CACnB,CAAC;ACrCI,SAAAC,mBAAAC,OAAA;AAAAtC,QAAAA,QAAAC,iCAAA,CAAA;AAAA,MAAAI,IAAAC;AAAAN,IAAAA,CAAAA,MAAAsC,SACyBhC,KAAAiC,YAAYD,KAAK,GAACtC,EAAAA,CAAAA,IAAAsC,OAAAtC,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA,GAAAK,KAAlBC;AAA9B,QAAAkC,UAAgBnC;AAA0C,MAAAO,IAAA6B;AAAAzC,IAAAA,CAAAA,MAAAsC,SACxBG,KAAAhB,mBAAmBiB,gBAAgBJ,KAAK,CAAC,GAACtC,EAAAA,CAAAA,IAAAsC,OAAAtC,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA,GAAAY,KAA1C6B;AAAlC,QAAAE,cAAoB/B;AAAkEgC,MAAAA;AAAA,SAAA5C,EAAA2C,CAAAA,MAAAA,eAAA3C,EAAAA,CAAAA,MAAAwC,WAE/EI,KAAA,CAACJ,SAASG,WAAW,GAAC3C,EAAAA,CAAAA,IAAA2C,aAAA3C,EAAAA,CAAAA,IAAAwC,SAAAxC,EAAAA,CAAAA,IAAA4C,MAAAA,KAAA5C,EAAA,CAAA,GAAtB4C;AAAsB;AAG/B,SAASL,YAAYD,OAAc;AAC3B,QAAA;IAACO;IAAOC;EAAK,IAAIR,MAAMS,QACvBC,OAAOH,MAAMG,KAAKC,SAClBC,SAASC,KAAKC,KAAKP,MAAMQ,OAAO,MAAM,GAAG,EAAEC,KAC3CC,YAAYJ,KAAKK,KAAKlB,MAAMS,OAAOF,MAAMQ,OAAO,MAAM,GAAG,EAAEC;AAEjE,SAAOG,WAAWnB,MAChB;IACE,KAAK;MACHO,OAAOG,KAAKU;MACZC,iBAAiBX,KAAKY;IACxB;IAEA,eAAe;MACbC,YAAYX;IACd;IAEA,cAAc;MACZY,YAAYhB,MAAMiB,KAAKC;MACvBC,UAAUC,IAAIpB,MAAMiB,KAAKI,MAAM,CAAC,EAAEF,QAAQ;MAC1CG,YAAY;IACd;IAEA,8BAA8B;MAACC,iBAAiBnB;IAAM;IACtD,0FAA0F;MACxFS,iBAAiBJ;IACnB;IAEA,cAAc;MAACI,iBAAiBX,KAAKY;MAAIf,OAAOG,KAAKU;IAAE;IACvD,4BAA4B;MAACY,cAAc,aAAatB,KAAKuB,MAAM;IAAE;IACrE,+BAA+B;MAACC,WAAW,aAAaxB,KAAKuB,MAAM;IAAA;EAAE,GAEvE;IAAClB,MAAMR,MAAMQ;EAAAA,CACf;AACF;AAEA,SAASX,gBAAgBJ,OAAc;AAC/BmC,QAAAA,MAAInC,MAAMS,OAAOF,MAAM6B,MACvBC,IAAIrC,MAAMS,OAAOF,MAAM+B;AACtBC,SAAAA,eAAeC,OAAO,CAC3B;IAACC,KAAKC,KAAEC;IAASpC,OAAO8B,EAAEM;EAAAA,GAC1B;IAACF,KAAK,CAACC,KAAEE,cAAcF,KAAEG,MAAMH,KAAEI,SAASJ,KAAEK,WAAWL,KAAEM,SAAS;IAAGzC,OAAO8B,EAAEY;EAAAA,GAC9E;IAACR,KAAK,CAACC,KAAEQ,SAASR,KAAES,YAAY,GAAGT,KAAEU,SAAS;IAAG7C,OAAO8B,EAAEa;EAAAA,GAC1D;IAACT,KAAK,CAACC,KAAEnC,OAAOmC,KAAEW,SAASX,KAAEG,IAAI,GAAGH,KAAEY,SAASZ,KAAEG,IAAI,CAAC;IAAGtC,OAAO8B,EAAEkB;EAAAA,GAClE;IAACd,KAAK,CAACC,KAAEc,WAAWd,KAAEG,IAAI,GAAGH,KAAEe,SAAS;IAAGlD,OAAO8B,EAAEgB;EAAAA,GACpD;IACEZ,KAAK,CACHC,KAAEgB,UACFhB,KAAEiB,WACFjB,KAAEkB,QACFlB,KAAEmB,SACFnB,KAAEoB,YACFpB,KAAEqB,UACFrB,KAAEsB,MACFtB,KAAEuB,SAAS;IAEb1D,OAAO8B,EAAEuB;EAAAA,GAEX;IACEnB,KAAK,CAACC,KAAEwB,UAAUxB,KAAEyB,iBAAiBzB,KAAE0B,KAAK1B,KAAE2B,QAAQ3B,KAAE4B,QAAQ5B,KAAE6B,MAAM7B,KAAE8B,QAAQ9B,KAAE+B,MAAM,CAAC;IAC3FlE,OAAO8B,EAAE6B;EAAAA,GAEX;IAACzB,KAAK,CAACC,KAAEgC,MAAMhC,KAAEiC,OAAO;IAAGpE,OAAO8B,EAAEsC;EAAAA,GACpC;IAAClC,KAAKC,KAAEkC;IAAQC,YAAY;EAAA,GAC5B;IAACpC,KAAKC,KAAEoC;IAAUC,WAAW;EAAA,GAC7B;IAACtC,KAAKC,KAAEsC;IAAeC,gBAAgB;EAAA,GACvC;IAACxC,KAAKC,KAAEwC;IAASL,YAAY;IAAQtE,OAAO8B,EAAEY;EAAAA,GAC9C;IAACR,KAAK,CAACC,KAAEyC,MAAMzC,KAAE0C,MAAM1C,KAAE8B,QAAQ9B,KAAES,YAAY,CAAC;IAAG5C,OAAO8B,EAAEgD;EAAAA,GAC5D;IAAC5C,KAAK,CAACC,KAAE4C,uBAAuB5C,KAAE+B,QAAQ/B,KAAE6C,QAAQ;IAAGhF,OAAO8B,EAAEoC;EAAAA,GAChE;IAAChC,KAAKC,KAAE8C;IAASjF,OAAO4B,IAAEf;EAAAA,CAAG,CAC9B;AACH;AClFO,IAAMqE,aAAaC,GAAOC;;;;;;;;;;;;;;;;;;;;;oBAqBb,CAAC;EAAC3F;AAAK,MAAM4B,IAAI5B,MAAMS,OAAOmF,MAAM,CAAC,CAAC,CAAC;;;;0BAIjC,CAAC;EAAC5F;AAAK,MAAM4B,IAAI5B,MAAMS,OAAOmF,MAAM,CAAC,CAAC,CAAC;mBAC9C,CAAC;EAAC5F;AAAK,MAAM4B,IAAI5B,MAAMS,OAAOmF,MAAM,CAAC,CAAC,CAAC;;;AA1BnD,ICaMC,uBAAmBC,0BAK9B,CAAArI,OAAAsI,QAAA;AAAArI,QAAAA,QAAAC,iCAAA,CAAA,GAGA,CAAAqI,YAAA,QAAuBlI,wBAASL,MAAKuI,YAAa,GAClDC,cAAoBC,SAAAA,GACpBlG,QAAcD,mBAAmBkG,WAAW,GAC5CE,oBAAsBC,sBAAA,IAA+B;AAACrI,MAAAA;AAAAL,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAEfvI,KAAAwI,CAAA,eAAA;;AACrCC,UAAAA,cAAmBL,mBAAaM,YAAbN,mBAAaO;AAAc,QAAA,CACzCF;AAAU;AAEf,UAAAG,aAAmBH,WAAUI,MAAAC,IAAAC,SAAoB;AAC7CP,mBAAeI,cACjBH,WAAUO,SAAA;MAAAC,SAAA;QAAAC,MAAA;QAAAC,IACeP,WAAUQ;QAAAC,QAAiBb;MAAU;MAAAtF,WACjDoG,gBAAAzG,OAAuB2F,WAAUY,MAAO;IAAA,CACpD;EAAA,GAEJzJ,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAXD,QAAA4J,qBAA2BvJ;AAWrB,MAAAC,IAAAM;AAAAZ,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAIJtI,KAAAA,OAAA;IAAAsJ;EAAA,IAGAhJ,KAAAA,CAACgJ,kBAAkB,GAAC5J,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAY,OAAAN,KAAAN,EAAA,CAAA,GAAAY,KAAAZ,EAAA,CAAA,QALtB6J,mCACExB,KACA/H,IAGAM,EACF;AAAC6B,MAAAA;AAAAzC,SAAAA,EAAA,CAAA,MAAAsI,gBAAAtI,EAAA,CAAA,MAAAD,MAAA+J,YAAA9J,EAAA,CAAA,MAAAsC,SAGCG,SAAAA,yBAAC,YACC,EAAA,cAAA,yBAAC,aACMgG,EAAAA,KAAAA,eACO,YAAA,OACLnG,OACKzB,YAAmBA,sBACxByH,OAAAA,cACG,UAAAvI,MAAK+J,SAEnB,CAAA,EAAA,CAAA,GAAa9J,EAAAA,CAAAA,IAAAsI,cAAAtI,EAAA,CAAA,IAAAD,MAAA+J,UAAA9J,EAAAA,CAAAA,IAAAsC,OAAAtC,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA,GATbyC;AASa,CAEhB;AAGD0F,iBAAiB4B,cAAc;AC7DlBC,IAAAA,yBAAyB,CAAC,iBAAiB,OAAO,aAAa,QAAQ;AAAvEA,IAYAC,uBAAuB,CAAC,eAAe;AAI7C,SAASC,uBAAuBC,GAAsC;AACpEH,SAAAA,uBAAuBI,SAASD,CAAyB;AAClE;AAEO,SAASE,qBACdC,yBAC+C;AAC/C,SACE,OAAOA,2BAA4B,YACnCL,qBAAqBG,SAASE,uBAA6C;AAE/E;AAEO,SAASC,qBAAqB;EAACC;AAAgD,GAAY;AAChG,SAAO,OAAOA,0BAA4B;AAC5C;AAaO,SAASC,qBAAqB;EACnCC;EACAC;AAIF,GAAkC;AAC5BD,SAAAA,sBAAsB,kBACjBA,oBAEFC;AACT;AC3DO,SAASC,kBACdC,OACAC,SAAkC,CAAA,GAClCC,UAA6C,CAAA,GACrC;AACFC,QAAAA,eAAe,IAAIC,gBAAgB;AAC5BC,eAAAA,IAAI,SAASL,KAAK;AAE/B,aAAW,CAAC/I,MAAKqJ,KAAK,KAAKC,OAAOC,QAAQP,MAAM;AAC9CE,iBAAaE,IAAI,IAAIpJ,IAAG,IAAIwJ,KAAKC,UAAUJ,KAAK,CAAC;AAGnD,aAAW,CAACrJ,MAAKqJ,KAAK,KAAKC,OAAOC,QAAQN,OAAO;AAC3CI,aAAOH,aAAaE,IAAIpJ,MAAK,GAAGqJ,KAAK,EAAE;AAG7C,SAAO,IAAIH,YAAY;AACzB;ACjBO,SAASQ,cAAcC,KAA8C;AAExE,SAAA,CAAC,CAACA,OAAO,OAAOA,OAAQ,YAAYL,OAAOM,UAAUtC,SAASuC,KAAKF,GAAG,MAAM;AAEhF;ACFA,IAAMG,kBAAkBC,qBAAAA;AAAxB,IACMC,YAAY;AAQX,SAASC,oBAAoB;AAC7BH,MAAAA;AAIL,aAASI,IAAI,GAAGA,IAAIC,aAAaxC,QAAQuC,KAAK;AACtClK,YAAAA,OAAMmK,aAAanK,IAAIkK,CAAC;AAC1BlK,OAAAA,QAAAA,gBAAAA,KAAKoK,WAAWJ,eAClBG,aAAaE,WAAWrK,IAAG;IAAA;AAGjC;AAEO,SAASsK,gBAAgB7F,WAAoC;AAClE,QAAM8F,aAAa,GAAGP,SAAS,GAAGvF,SAAS;AAC3C,MAAI+F,cAA8C;AAE3C,SAAA;IAACC;IAAKrB;IAAKsB;EAAK;AAEdD,WAAAA,IAAOzK,MAAa2K,YAAkB;AAC7C,UAAMvD,QAAQwD,YAAY;AAC1B,WAAO,OAAOxD,MAAMpH,IAAG,IAAM,MAAc2K,aAAcvD,MAAMpH,IAAG;EAAA;AAG3DoJ,WAAAA,IAAOpJ,MAAaqJ,OAAa;AACxC,UAAMjC,QAAQwD,YAAY;AACpB5K,WAAAA,MAAAA,IAAG,IAAIqJ,OACbc,aAAaU,QAAQN,YAAYf,KAAKC,UAAUe,WAAW,CAAC,GACrDnB;EAAAA;AAGT,WAASqB,MAASzM,OAAa;AAC7B,UAAMmJ,QAAQ;MAAC,GAAGwD,YAAY;MAAG,GAAG3M;IAAK;AACzCkM,WAAAA,aAAaU,QAAQN,YAAYf,KAAKC,UAAUrC,KAAK,CAAC,GAC/CA;EAAAA;AAGT,WAASwD,cAAuC;AAC9C,WAAIJ,gBAAgB,SAClBA,cAAcM,UAAAA,IAGTN;EAAAA;AAGT,WAASM,YAAY;AACnB,QAAI,CAAChB;AACH,aAAO,CAAC;AAGN,QAAA;AACF,YAAMiB,SAASvB,KAAKwB,MAAMb,aAAac,QAAQV,UAAU,KAAK,IAAI;AAClE,aAAOb,cAAcqB,MAAM,IAAIA,SAAS,CAAC;IAAA,QAC7B;AACZ,aAAO,CAAC;IAAA;EACV;AAEJ;AAEA,SAAShB,uBAAuB;AAC9B,QAAMmB,MAAM;AACR,MAAA;AACFf,WAAAA,aAAaU,QAAQK,KAAKA,GAAG,GAC7Bf,aAAaE,WAAWa,GAAG,GACpB;EAAA,QACK;AACL,WAAA;EAAA;AAEX;ACzEO,SAASC,oBAAoBC,IAA2C;AAC7E,QAAMpC,SAAkC,CAAA,GAClCC,UAAkC,CAAC;AAEzC,aAAW,CAACjJ,MAAKqJ,KAAK,KAAK+B,GAAG7B,QAAAA,GAAW;AACnCvJ,QAAAA,KAAI,CAAC,MAAM,KAAK;AAClBgJ,aAAOhJ,KAAIjC,MAAM,CAAC,CAAC,IAAIyL,KAAKwB,MAAM3B,KAAK;AACvC;IAAA;AAGF,QAAIrJ,SAAQ,eAAe;AACzBiJ,cAAQjJ,IAAG,IAAIqJ;AACf;IAAA;EACF;AAGK,SAAA;IAACN,OAAOqC,GAAGX,IAAI,OAAO,KAAK;IAAIzB;IAAQC;EAAO;AACvD;ACvBO,SAASoC,iBAAiBC,SAAyB;AACpDA,SAAAA,QAAQ,CAAC,MAAM,OAAOA,YAAY,UAC7B,IAAIA,OAAO,KAGbA;AACT;ACNO,SAASC,mBAAmBC,YAA6B;AACxDC,QAAAA,sBAAsBD,WAAWE,QAAQ,MAAM,EAAE,EAAEC,KAAAA,EAAOC,YAAY;AAQ5E,SALEH,oBAAoB9D,SAAS,MAC5B8D,wBAAwB,OACvBA,wBAAwB,OACvB,sBAAsBI,KAAKJ,mBAAmB,KAAK,CAACK,MAAMnO,KAAKqN,MAAMS,mBAAmB,CAAC;AAGhG;ACPgBM,SAAAA,eACdC,KACA9I,GACiC;AAC7B,MAAA;AACF,UAAM+I,SAASD,MAAME,aAAMlB,MAAMgB,GAAG,IAAI,CAAC;AAClC,WAAA,OAAOC,UAAW,YAAYA,UAAU,CAACE,MAAMC,QAAQH,MAAM,IAAIA,SAAS,CAAC;EAAA,SAC3EI,KAAK;AAGZA,WAAAA,IAAIC,UAAU,GAAGpJ,EAAE,kCAAkC,CAAC;;EAAQmJ,IAAIC,QAAQZ,QACxE,UACA,EACF,CAAC,IACMW;EAAAA;AAEX;AChBaE,IAAAA,OAAOrG,GAAOsG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6C/BD,KAAKtE,cAAc;AAENwE,IAAAA,SAASvG,GAAOwG,IAAI;;;AAApBD,IAIAE,cAAczG,GAAO0G,KAAK;;;AAJ1BH,IAQAI,qBAAqB3G,GAAO4G,GAAG;;;AAR/BL,IAYAM,gBAAgB7G,GAAO8G;;;;AAZvBP,IAiBAQ,2BAA2B/G,GAAO4G,GAAG;;;;;;;;;IAS9CH,WAAW;;;;AA1BFF,IA+BAS,+BAA+BhH,GAAO+G,wBAAwB;;;;AA/B9DR,IAoCAU,iBAAiBjH,GAAOwG,IAAI;;;;;;AApC5BD,IA2CAW,uBAAuBlH,GAAOsG,IAAI;;;AA3ClCC,IA+CAY,uBAAuBnH,GAAO4G,GAAG;;;AA/CjCL,IAmDAa,kBAAkBpH,GAAOwG,IAAI;;;;;;IAMtC,CAAC;EAACa;AAAU,MACZA,cACAC;;;;;;;;;;KAUC;;AArEQf,IAwEAgB,SAASvH,GAAO4G,GAAG;;;;;;AAxEnBL,IA+EAiB,eAAexH,GAAOsG,IAAI;;;AA/E1BC,IAmFAkB,cAAczH,GAAOwG,IAAI;;;AAINxG,GAAO4G,GAAG;;;;AAK7Bc,IAAAA,uBAAuB1H,GAAOsG,IAAI;;gBAE/B,CAAC;EAAChM;AAAK,MACnB4B,IACE5B,MAAMS,OAAOmF,MAAM,CAAC,IAAI,IACtB5F,MAAMS,OAAOD,MAAM6M,KAAKxL,MAAM,CAAC,EAAEC,aACjC9B,MAAMS,OAAOD,MAAM6M,KAAKxL,MAAM,CAAC,EAAEyL,iBACjCtN,MAAMS,OAAOD,MAAM6M,KAAKxL,MAAM,CAAC,EAAE0L,eACrC,CAAC;;AARQH,IAWAI,gBAAgB9H,GAAOwG,IAAI;;;AAX3BkB,IAeAK,kBAAkB/H,GAAOgI,IAAI;;;;;;;;WAQ/B,CAAC;EAAC1N;AAAK,MAAM4B,IAAI5B,MAAMS,OAAOmF,MAAM,CAAC,CAAC,CAAC;;;;AAvBrCwH,IA4BAO,oBAAoBjI,GAAO4G,GAAG;;;AA5B9Bc,IClIPQ,iBAAe;;;AAcd,SAAAC,aAAApQ,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GACL;IAAA6J;IAAAsG;IAAAC;IAAAC;EAAAA,IAA2DvQ,OAC3D;IAAAiF;EAAAA,IAAYuL,eAAAC,qBAAoC;AAACnQ,MAAAA;AAAAL,IAAAA,CAAAA,MAAAD,MAAAoL,SAAAnL,EAAAA,CAAAA,MAAAgF,KACN3E,KAAAoQ,YAAY1Q,MAAKoL,OAAQnG,CAAC,GAAChF,EAAA,CAAA,IAAAD,MAAAoL,OAAAnL,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAtE,QAAA;IAAA0Q,KAAAvF;IAAAwF;IAAA5C;IAAA6C;EAA2CvQ,IAAAA,IAC3C,CAAAwQ,SAAAC,QAAA,QAA4B1Q,wBAASwQ,KAAK,GAC1C,CAAAG,MAAAC,OAAA,QAAwB5Q,wBAAAA,KAAc;AAAC,MAAAE,IAAAM;AAAAZ,IAAAA,CAAAA,MAAA2Q,SAAA3Q,EAAA+Q,CAAAA,MAAAA,QAAA/Q,EAAA6Q,CAAAA,MAAAA,WAAA7Q,EAAA,CAAA,MAAA8J,YAAA9J,EAAAA,CAAAA,MAAA+N,UAAA/N,EAAA,CAAA,MAAAmL,SAG7B7K,KAAAA,MAAA;AACHyQ,aACHjH,SAAQ;MAAAiE;MAAA2C,KAAevF;MAAKyF,OAASC;MAAOF;IAAAA,CAAQ,GACpDK,QAAAA,IAAY;EAAC,GAEdpQ,KAAA,CAAC+P,OAAOI,MAAMF,SAAS/G,UAAUiE,QAAQ5C,KAAK,GAACnL,EAAAA,CAAAA,IAAA2Q,OAAA3Q,EAAAA,CAAAA,IAAA+Q,MAAA/Q,EAAAA,CAAAA,IAAA6Q,SAAA7Q,EAAAA,CAAAA,IAAA8J,UAAA9J,EAAAA,CAAAA,IAAA+N,QAAA/N,EAAAA,CAAAA,IAAAmL,OAAAnL,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,EAAAA,IAAAY,OAAAN,KAAAN,EAAA,CAAA,GAAAY,KAAAZ,EAAA,EAAA,QALlDW,yBAAUL,IAKPM,EAA+C;AAAC6B,MAAAA;AAAAzC,IAAA8J,EAAAA,MAAAA,YAAA9J,EAAAA,EAAAA,MAAAgF,KAGjDvC,KAAAwO,CAAA,aAAA;AACEC,UAAAA,QAAcT,YAAYQ,UAAUjM,CAAC;AACrC8L,aAASI,MAAKN,KAAM,GACpB9G,SAASoH,KAAK;EACflR,GAAAA,EAAAA,EAAAA,IAAA8J,UAAA9J,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,EAAA;AALH,QAAAmR,kBAAwB1O;AAOvB,MAAAG,IAAAwO;AAAApR,IAAAA,EAAAA,MAAAmR,mBAEkCC,SAAAC,gBAAAA,SAASF,iBAAAA,GAAoB,GAACnR,EAAAA,EAAAA,IAAAmR,iBAAAnR,EAAAA,EAAAA,IAAAoR,MAAAA,KAAApR,EAAA,EAAA,GAAA4C,KAA9BwO;AAAnC,QAAAE,eAAqB1O,IAEE2O,KAAAlB,iBAAiB,YAAY;AAAUmB,MAAAA;AAAAxR,IAAAA,EAAAA,MAAAgF,KAGlCwM,KAAAxM,EAAE,cAAc,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAwR,MAAAA,KAAAxR,EAAA,EAAA;AAAAyR,MAAAA;AAAAzR,IAAAA,EAAAA,MAAAwR,MAArCC,SAAC,yBAAA,aAAA,EAAY,OAAA,MAAOD,UAAAA,GAAAA,CAAkB,GAAcxR,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAAyR,MAAAA,KAAAzR,EAAA,EAAA;AAAA0R,MAAAA;AAAA1R,IAAAA,EAAAA,MAAAoQ,eACnDsB,KAAAtB,mBACC,yBAAC,SAAA,EAAQ,SAAM,MAAY,WAAA,OAAM,QAAA,MAAgB,aAAC,yBAAA,MAAA,EAAW,MAAA,GAAIA,UAAAA,YAAAA,CAAY,GAC3E,cAAA,yBAAC,KAAA,EAAa,SAAA,GAAY,SAAA,GACxB,cAAA,yBAAC,MAAA,EACC,cAAC,yBAAA,kBAAA,CAAA,CACH,EAAA,CAAA,EACF,CAAA,EAAA,CACF,GACDpQ,EAAAA,EAAAA,IAAAoQ,aAAApQ,EAAAA,EAAAA,IAAA0R,MAAAA,KAAA1R,EAAA,EAAA;AAAA2R,MAAAA;AAAA3R,IAAAyR,EAAAA,MAAAA,MAAAzR,EAAAA,EAAAA,MAAA0R,MAXLC,UAAAA,yBAAC,8BACC,EAAA,cAAA,0BAAC,MACCF,EAAAA,UAAAA;IAAAA;IACCC;EAAAA,EASH,CAAA,EACF,CAAA,GAA+B1R,EAAAA,EAAAA,IAAAyR,IAAAzR,EAAAA,EAAAA,IAAA0R,IAAA1R,EAAAA,EAAAA,IAAA2R,OAAAA,MAAA3R,EAAA,EAAA;AAGf4R,QAAAA,MAAA7R,MAAKoL,SAAA+E;AAAsB2B,MAAAA;AAAA7R,IAAAsQ,EAAAA,MAAAA,aAAAtQ,EAAAA,EAAAA,MAAAsR,gBAAAtR,EAAA,EAAA,MAAA4R,OAF3CC,UAAAA,yBAAC,kBAAA,EACMvB,KAAAA,WACS,cAAAsB,KACJN,UAAAA,aAAAA,CACV,GAAAtR,EAAAA,EAAAA,IAAAsQ,WAAAtQ,EAAAA,EAAAA,IAAAsR,cAAAtR,EAAAA,EAAAA,IAAA4R,KAAA5R,EAAAA,EAAAA,IAAA6R,OAAAA,MAAA7R,EAAA,EAAA;AAAA8R,MAAAA;AAAA9R,SAAAA,EAAA2R,EAAAA,MAAAA,OAAA3R,EAAAA,EAAAA,MAAA6R,OAAA7R,EAAA,EAAA,MAAAuR,MAnBJO,UAAAA,0BAAC,MAAW,EAAA,MAAA,GAAS,MAAAP,IAAqD,eAAA,iBACxEI,UAAAA;IAAAA;IAcAE;EAAAA,EAAAA,CAKF,GAAO7R,EAAAA,EAAAA,IAAA2R,KAAA3R,EAAAA,EAAAA,IAAA6R,KAAA7R,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAA8R,OAAAA,MAAA9R,EAAA,EAAA,GApBP8R;AAoBO;AAIKrB,SAAAA,YACdtF,OACAnG,GACQ;AACR,QAAM+M,eAAelE,eAAe1C,OAAOnG,CAAC,GACtC8F,SAASiH,wBAAwBC,QAAQ,CAAA,IAAKD,cAC9CE,kBAAkBF,wBAAwBC,QAAQD,aAAa3D,UAAU8D;AAGxE,SAAA;IACLnE,QAAQjD;IACR4F,KAAKvF;IACLyF,OALc,CAACqB;IAMftB,OAAOsB;EACT;AACF;ACtFA,IAAME,2BAA2B;AAAjC,IAcMjC,eAAe;EACnBkC,SAAS,CAAA;AACX;AAhBA,IAiBMC,mBAAmBF;AAElB,SAASG,kBAUd;AACMC,QAAAA,gBAAgBC,iBAAiB,GAEjC,CAACrH,OAAOsH,QAAQ,QAAIrS,wBAAwB8P,YAAY,GACxD,CAACwC,QAAQC,SAAS,QAAIvS,wBAAS,KAAK,GACpC,CAACwS,UAAUC,WAAW,QAAIzS,wBAAmB,CAAA,CAAE,GAC/C,CAAC0S,gBAAgBC,iBAAiB,QAAI3S,wBAA4B,GAClE,CAAC4S,kBAAkBC,mBAAmB,QAAI7S,wBAAAA,GAC1C,CAACuQ,OAAOuC,QAAQ,QAAI9S,wBAA4B,GAEhDgS,cAAUe,uBAAQ,MACfZ,cAAca,OAAOf,gBAAgB,GAC3C,CAACE,aAAa,CAAC;AAElB5R,+BAAU,MAAM;AACd,UAAM0S,MAAMjB,QACTkB,KACCC,UAAUrD,YAAmB,GAC7BsD,IAAKC,CAAAA,SACEA,QACIvD,YAGV,CACH,EACCwD,UAAU;MACTC,MAAMlB;MACN9B,OAAQxC,CAAQ+E,QAAAA,SAAS/E,GAAY;IAAA,CACtC;AAEI,WAAA,MAAMkF,2BAAKO;EAAY,GAC7B,CAACxB,SAASG,aAAa,CAAC;AAErBsB,QAAAA,gBAAYC,2BACfjJ,CAAqC,UAAA;AAC1B,cAAA,IAAI,GACdkI,kBAAkBb,MAAS;AACvB,QAAA;AAEF,YAAM6B,aAAa,CADF;QAAC,GAAGlJ;QAAOmJ,MAAMC,WAAK;MAAA,GACT,GAAG9I,MAAMiH,OAAO;AACrC,eAAA;QAACA,SAAS2B;MAAAA,CAAW,GAC9BxB,cAAc2B,OAAO7B,kBAAkB;QACrCD,SAAS2B;MAAAA,CACuB;IAAA,SAC3B5F,OAAK;AACZ4E,wBAAkB5E,KAAY;IAAA,UAAA;AAE9BwE,gBAAU,KAAK;IAAA;EACjB,GAEF,CAACJ,eAAepH,MAAMiH,OAAO,CAC/B,GAEM+B,kBAAcL,2BACjBjJ,CAAuB,YAAA;AACZ,cAAA,IAAI,GACdkI,kBAAkBb,MAAS;AACvB,QAAA;AACIkC,YAAAA,iBAAiBjJ,MAAMiH,QAAQoB,IAAKa,CAAAA,MACxCA,EAAEL,SAASnJ,QAAMmJ,OAAO;QAAC,GAAGK;QAAG,GAAGxJ;MAAAA,IAASwJ,CAC7C;AACS,eAAA;QAACjC,SAASgC;MAAAA,CAAe,GAClC7B,cAAc2B,OAAO7B,kBAAkB;QACrCD,SAASgC;MAAAA,CACuB;IAAA,SAC3BjG,OAAK;AACZ4E,wBAAkB5E,KAAY;IAAA,UAAA;AAE9BwE,gBAAU,KAAK;IAAA;EACjB,GAEF,CAACJ,eAAepH,MAAMiH,OAAO,CAC/B,GAEMkC,kBAAcR,2BACjBhS,CAAgBA,SAAA;AACFyS,gBAAAA,CAAAA,SAAS,CAAC,GAAGA,MAAMzS,IAAG,CAAC,GACpCmR,oBAAoBf,MAAS;AACzB,QAAA;AACF,YAAMsC,kBAAkBrJ,MAAMiH,QAAQjQ,OAAQkS,CAAMA,QAAAA,IAAEL,SAASlS,IAAG;AACzD,eAAA;QAACsQ,SAASoC;MAAAA,CAAgB,GACnCjC,cAAc2B,OAAO7B,kBAAkB;QACrCD,SAASoC;MAAAA,CACuB;IAAA,SAC3BrG,OAAK;AACZ8E,0BAAoB9E,KAAY;IAAA,UAAA;AAEhC0E,kBAAa0B,CAAAA,SAASA,KAAKpS,OAAQsS,CAAMA,MAAAA,MAAM3S,IAAG,CAAC;IAAA;EAGvD,GAAA,CAACyQ,eAAepH,MAAMiH,OAAO,CAC/B;AAEO,SAAA;IACLA,SAASjH,MAAMiH;IACfyB;IACAM;IACAG;IACA5B;IACAE;IACAE;IACAE;IACArC;EACF;AACF;ACvIa+D,IAAAA,cAAc1M,GAAO2M,KAAK;;;gBAGvB,CAAC;EAACrS;AAAK,MAAMA,MAAMS,OAAOF,MAAM6B,KAAKd,EAAE;;;AAH1C8Q,IAOAE,kBAAkB5M,GAAO4G,GAAG;;;;;;;;;;;;;;kBAcvB,CAAC;EAACtM;AAAK,MAAMA,MAAMS,OAAOF,MAAM6B,KAAKH,MAAM;;;;ACAtD,SAAAsQ,YAAAxU,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,EAAA,GAAqB;IAAAyG;IAAAoO;IAAAC;IAAAC;IAAAC;IAAAC;EAAAA,IAAA7U,IAe1B8U,QAAcC,SAAAA,GACd;IAAAvB;IAAAM;IAAA/B;IAAAkC;IAAA5B;IAAAI;EAAA,IACER,gBAAAA,GACF;IAAAtN;EAAAA,IAAYuL,eAAAC,qBAAoC;AAAClQ,MAAAA;AAAAN,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KACZtI,KAAA;IAAA+U,OAC5B;IAAOC,KACT;IAASC,MACR;IAASC,MACT;IAASC,QACP;IAASC,QAAA;EAAA,GAElB1V,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAPD,QAAA2V,aAAmBC,kBAAkBtV,EAOpC,GACD,CAAAuV,YAAAC,aAAA,QAAoC1V,wBAAAA,IAA4B,GAChE,CAAA2V,cAAAC,eAAA,QAAwC5V,wBAAS,EAAE;AAACQ,MAAAA;AAAAZ,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAC6BhI,KAAA,CAAA,GAAEZ,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA;AAAnF,QAAA,CAAAiW,kBAAAC,mBAAA,QAAgD9V,wBAAiCQ,EAAE,GACnF,CAAAuV,aAAAC,cAAA,QAAsChW,wBAAS,EAAE,GACjD,CAAAiW,aAAAC,cAAA,QAAsClW,wBAA6BsG,GAAG;AAACjE,MAAAA;AAAAzC,IAAAiV,CAAAA,MAAAA,iBAAAjV,EAAAA,CAAAA,MAAAgV,gBAAAhV,EAAA,CAAA,MAAA2V,cAAA3V,EAAAA,CAAAA,MAAAkV,eAAAlV,EAAA8U,CAAAA,MAAAA,mBAAA9U,EAAAA,CAAAA,MAAAoS,WAAApS,EAAA,CAAA,MAAA6T,aAAA7T,EAAA8S,CAAAA,MAAAA,kBAAA9S,EAAA,EAAA,MAAAgF,KAAAhF,EAAAA,EAAAA,MAAAmV,SAExC1S,KAAAA,YAAA;AAE7B8T,UAAAA,SAAerB,YAAYF,cAAcC,aAAa;AAGlC7C,QAAAA,mCAAOoE,KAAAnC,CAAA,MAAA;AACzBoC,YAAAA,gBAAsB3B,gBAAgBT,EAAC3N,GAAI;AAAC,aAE1C+P,iBACAA,cAAa5L,UAAWmK,oBACxB0B,eAAAA,SAAQD,cAAa3L,QAASmK,aAAa;IAAA,IAIhC;AACb0B,YAAAA,iBAAuBvE,mCAAOwE,KAAAC,CAAA,QAAA;AAC5BC,cAAAA,kBAAsBhC,gBAAgBT,IAAC3N,GAAI;AAAC,eAE1C+P,mBACAA,gBAAa5L,UAAWmK,oBACxB0B,eAAAA,SAAQD,gBAAa3L,QAASmK,aAAa;MAAA;AAG/CE,YAAK4B,KAAA;QAAAC,UAAA;QAAAC,QAEK;QAASC,OACVlS,EAAE,0BAA0B;QAACmS,aACvB,GAAGR,iDAAcO,KAAA,MAAavB,WAAUyB,OAAA3X,IAAAA,MAAiBkX,iDAAcU,YAAa,EAAE,CAAC,CAAC;MAAA,CACtG;AAAC;IAAA;AAIAd,eAAM,MACF1C,UAAS;MAAAnN,KACR6P;MAAMc,UACF,oBAAA5X,KAAAC,GAAAA,YAAuB;MAACwX,OAC1B;IAAA,CACR,GAEDZ,eAAeC,MAAM,IAEnBzD,iBACFqC,MAAK4B,KAAA;MAAAC,UAAA;MAAAC,QAEK;MAAOC,OACRlS,EAAE,kBAAkB;MAACmS,aACfrE,eAAc1E;IAAAA,CAC5B,IAED+G,MAAK4B,KAAA;MAAAC,UAAA;MAAAC,QAEK;MAASC,OACVlS,EAAE,oBAAoB;IAAA,CAC9B;EAAA,GAEJhF,EAAAA,CAAAA,IAAAiV,eAAAjV,EAAAA,CAAAA,IAAAgV,cAAAhV,EAAAA,CAAAA,IAAA2V,YAAA3V,EAAAA,CAAAA,IAAAkV,aAAAlV,EAAAA,CAAAA,IAAA8U,iBAAA9U,EAAAA,CAAAA,IAAAoS,SAAApS,EAAAA,CAAAA,IAAA6T,WAAA7T,EAAAA,CAAAA,IAAA8S,gBAAA9S,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAmV,OAAAnV,EAAAA,EAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,EAAA;AAvDD,QAAAsX,aAAmB7U;AAkEjBG,MAAAA;AAAA5C,IAAAgF,EAAAA,MAAAA,KAAAhF,EAAAA,EAAAA,MAAAmV,SAAAnV,EAAA,EAAA,MAAAmU,eAGAvR,KAAAA,OAAAiI,OAAA0M,aAAA;sBACoB,GAClBrB,oBAAmB3B,CAAA,UAAA;MAAA,GAAgBA;MAAI,CAAG1J,MAAKmJ,IAAA,GAAQuD;IAAAA,EAAU;AAAC,QAAA;AAAA,YAG1DpD,YAAW;QAAA,GACZtJ;QAAKqM,OACDK;MAAAA,CACR,GAEDrB,oBAAmBsB,CAAA,WAAA;AACjB,cAAAC,SAAA;UAAA,GAAiBlD;QAAI;AACdZ,eAAAA,OAAAA,OAAK9I,MAAKmJ,IAAA,GACVL;MAAAA,CACR;IAAA,SAACvC,KAAA;AACKjD,YAAAA,MAAAA;AAEP+H,0BAAmBwB,CAAA,WAAA;AACjB,cAAA/D,OAAA;UAAA,GAAiBY;QAAI;AACdZ,eAAAA,OAAAA,KAAK9I,MAAKmJ,IAAA,GACVL;MAAAA,CACR,GACDwB,MAAK4B,KAAA;QAAAC,UAAA;QAAAC,QAEK;QAAOC,OACRlS,EAAE,kBAAkB;QAACmS,aACfhJ,IAAGC;MAAAA,CACjB;IAAA;EAAC,GAELpO,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAmV,OAAAnV,EAAAA,EAAAA,IAAAmU,aAAAnU,EAAAA,EAAAA,IAAA4C,MAAAA,KAAA5C,EAAA,EAAA;AA9BH,QAAA2X,kBAAwB/U;AAgCvBwO,MAAAA;AAAApR,IAAA,EAAA,MAAAiV,iBAAAjV,EAAAgV,EAAAA,MAAAA,gBAAAhV,EAAAA,EAAAA,MAAA2V,cAAA3V,EAAA,EAAA,MAAAkV,eAAAlV,EAAA8U,EAAAA,MAAAA,mBAAA9U,EAAAoS,EAAAA,MAAAA,WAAApS,EAAA,EAAA,MAAAgF,KAAAhF,EAAAmV,EAAAA,MAAAA,SAAAnV,EAAAA,EAAAA,MAAAmU,eAGC/C,KAAAA,OAAAwG,YAAA;AACEC,UAAAA,WAAe3C,YAAYF,cAAcC,aAAa;AAGlC7C,QAAAA,mCAAOoE,KAAAsB,CAAA,QAAA;AAErBzD,UAAAA,IAACL,SAAUnJ,QAAKmJ;AAAK,eAAA;AACzB+D,YAAAA,kBAAsBjD,gBAAgBT,IAAC3N,GAAI;AAAC,aAE1C+P,mBACAA,gBAAa5L,UAAWmK,oBACxB0B,eAAAA,SAAQD,gBAAa3L,QAASmK,aAAa;IAAA,IAIhC;AACb+C,YAAAA,mBAAuB5F,mCAAOwE,KAAAqB,CAAA,QAAA;AACxB5D,YAAAA,IAACL,SAAUnJ,QAAKmJ;AAAK,iBAAA;AACzBkE,cAAAA,kBAAsBpD,gBAAgBT,IAAC3N,GAAI;AAAC,eAE1C+P,mBACAA,gBAAa5L,UAAWmK,oBACxB0B,eAAAA,SAAQD,gBAAa3L,QAASmK,aAAa;MAAA;AAG/CE,YAAK4B,KAAA;QAAAC,UAAA;QAAAC,QAEK;QAASC,OACVlS,EAAE,0BAA0B;QAACmS,aACvB,GAAGR,qDAAcO,KAAA,MAAavB,WAAUyB,OAAA3X,IAAAA,MAC1CkX,qDAAcU,YAAa,EAAE,CACxC,CAAC;MAAA,CACF;AAAC;IAAA;AAAA,QAAA;AAAA,YAKIlD,YAAW;QAAA,GACZtJ;QAAKnE,KACH6P;QAAMc,UACF,oBAAA5X,KAAA,GAAAC,YAAuB;MACjC,CAAA,GACD4W,eAAeC,QAAM,GACrBpB,MAAK4B,KAAA;QAAAC,UAAA;QAAAC,QAEK;QAASC,OACVlS,EAAE,oBAAoB;MAAA,CAC9B;IAAA,SAACuM,KAAA;AACKpD,YAAAA,QAAAA;AACPgH,YAAK4B,KAAA;QAAAC,UAAA;QAAAC,QAEK;QAAOC,OACRlS,EAAE,kBAAkB;QAACmS,aACfhJ,MAAGC;MAAAA,CACjB;IAAA;EAAC,GAELpO,EAAAA,EAAAA,IAAAiV,eAAAjV,EAAAA,EAAAA,IAAAgV,cAAAhV,EAAAA,EAAAA,IAAA2V,YAAA3V,EAAAA,EAAAA,IAAAkV,aAAAlV,EAAAA,EAAAA,IAAA8U,iBAAA9U,EAAAA,EAAAA,IAAAoS,SAAApS,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAmV,OAAAnV,EAAAA,EAAAA,IAAAmU,aAAAnU,EAAAA,EAAAA,IAAAoR,MAAAA,KAAApR,EAAA,EAAA;AAzDH,QAAAmY,eAAqB/G;AAqEpBgH,MAAAA,IAAAC,IAAA9G,IAAAC,IAAAC;AAAA,MAAAzR,EAAA,EAAA,MAAAiV,iBAAAjV,EAAA,EAAA,MAAAgV,gBAAAhV,EAAA,EAAA,MAAAsU,eAAAtU,EAAA6V,EAAAA,MAAAA,cAAA7V,EAAA+V,EAAAA,MAAAA,gBAAA/V,EAAAA,EAAAA,MAAA2V,cAAA3V,EAAA,EAAA,MAAA8U,mBAAA9U,EAAA,EAAA,MAAAsX,cAAAtX,EAAA2X,EAAAA,MAAAA,mBAAA3X,EAAAmY,EAAAA,MAAAA,gBAAAnY,EAAAiW,EAAAA,MAAAA,oBAAAjW,EAAAoS,EAAAA,MAAAA,WAAApS,EAAA,EAAA,MAAA0S,UAAA1S,EAAA,EAAA,MAAAmW,eAAAnW,EAAAqW,EAAAA,MAAAA,eAAArW,EAAA+U,EAAAA,MAAAA,yBAAA/U,EAAAA,EAAAA,MAAAgF,GAAA;AAED,UAAAwP,kBAAwBpC,mCAAOjQ,OAAAmW,CAAAA,QAAAA;;AACtBjE,8CAAC6C,UAAD7C,mBAACkE,cAAAnO,SAAgC+L,YAAWoC,YAAc;;AAIhE3D,SAAAA;AAAelD,QAAAA;AAAA1R,MAAA,EAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAGqB8I,MAAA;MAAA8G,eAAgB;IAAA,GAAaxY,EAAAA,EAAAA,IAAA0R,OAAAA,MAAA1R,EAAA,EAAA;AAAA2R,QAAAA;AAAA3R,MAAAA,EAAAA,MAAAgF,KACzD2M,OAAA3M,EAAE,qBAAqB,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA2R,QAAAA,OAAA3R,EAAA,EAAA;AAAA4R,QAAAA;AAAA5R,MAAAA,EAAAA,MAAA2R,QAD3BC,UAAC,yBAAA,MAAA,EAAY,QAAA,YAAkB,OAAAF,KAAqC,MAAC,GAClEC,UAAAA,KACH,CAAA,GAAO3R,EAAAA,EAAAA,IAAA2R,MAAA3R,EAAAA,EAAAA,IAAA4R,OAAAA,MAAA5R,EAAA,EAAA;AAAA6R,QAAAA;AAAA7R,MAAAA,EAAAA,MAAAgF,KAEE6M,MAAA7M,EAAE,mBAAmB,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA6R,OAAAA,MAAA7R,EAAA,EAAA;AAAA8R,QAAAA;AAAA9R,MAAAsX,EAAAA,MAAAA,cAAAtX,EAAAA,EAAAA,MAAA0S,UAAA1S,EAAA,EAAA,MAAA6R,OAD/BC,UAAAA,yBAAC,QACQ,EAAA,OAAAD,KACD4G,MAAMA,SACF/F,UAAAA,QACD4E,SAAS,YACb,MAAA,QACL,CAAA,GAAAtX,EAAAA,EAAAA,IAAAsX,YAAAtX,EAAAA,EAAAA,IAAA0S,QAAA1S,EAAAA,EAAAA,IAAA6R,KAAA7R,EAAAA,EAAAA,IAAA8R,OAAAA,MAAA9R,EAAA,EAAA;AAAA0Y,QAAAA;AAAA1Y,MAAA4R,EAAAA,MAAAA,OAAA5R,EAAAA,EAAAA,MAAA8R,OAVJ4G,UAAC,0BAAA,MAAA,EAAc,SAAA,GAAe,YAAA,GAAkB,eAAC,GAAU,SAAA,iBAAsB,OAAA,UAC/E9G,UAAAA;MAAAA;MAGAE;IAAAA,EAOF,CAAA,GAAO9R,EAAAA,EAAAA,IAAA4R,KAAA5R,EAAAA,EAAAA,IAAA8R,KAAA9R,EAAAA,EAAAA,IAAA0Y,OAAAA,MAAA1Y,EAAA,EAAA;AAAA2Y,QAAAA;AAAA3Y,MAAAA,EAAAA,MAAAgF,KAGU2T,MAAA3T,EAAE,sBAAsB,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA2Y,OAAAA,MAAA3Y,EAAA,EAAA;AAAA4Y,QAAAA;AAAA5Y,MAAA,EAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAG5BgQ,MAAA1H,CAAWkF,UAAAA,eAAelF,MAAK2H,cAAA1N,KAAoB,GAACnL,EAAAA,EAAAA,IAAA4Y,OAAAA,MAAA5Y,EAAA,EAAA;AAAA8Y,QAAAA;AAAA9Y,MAAAmW,EAAAA,MAAAA,eAAAnW,EAAAA,EAAAA,MAAA2Y,OALlEG,UAAAA,yBAAC,KAAa,EAAA,SAAA,GAAe,YAAA,GAC3B,cAAC,yBAAA,WAAA,EACc,aAAAH,KACPI,MAASA,YACR5C,OAAU,aACP,UAAAyC,IAEd,CAAA,EAAA,CAAA,GAAM5Y,EAAAA,EAAAA,IAAAmW,aAAAnW,EAAAA,EAAAA,IAAA2Y,KAAA3Y,EAAAA,EAAAA,IAAA8Y,OAAAA,MAAA9Y,EAAA,EAAA,GAAAA,EAAA0Y,EAAAA,MAAAA,OAAA1Y,EAAAA,EAAAA,MAAA8Y,OApBRrH,SAAC,0BAAA,aAAmB,EAAA,OAAC,GACnBiH,UAAAA;MAAAA;MAYAI;IAQF,EAAA,CAAA,GAAc9Y,EAAAA,EAAAA,IAAA0Y,KAAA1Y,EAAAA,EAAAA,IAAA8Y,KAAA9Y,EAAAA,EAAAA,IAAAyR,MAAAA,KAAAzR,EAAA,EAAA,GACboY,KAAAzD,OAAgBpD,KAAA,GACdC,KAAAgD,mDAAehB,IAAAwF,CAAA,QAAA;AACdC,YAAAA,WAAiBnE,gBAAgBT,IAAC3N,GAAI,GACtCwS,aAAmB7C,gBAAgBhC,IAAC3N,KAGpCyS,kBACEF,YAAYjE,iBAAiBiE,SAAQpO,aAAU6L,eAAAA,SAAQzB,eAAegE,SAAQnO,MAAO,GAEvFsO,WAAiBF,cAAU,CAAKC;AAAe,iBAE5C,yBAAA,MAAA,EAEQ,OAAA,QACE,SAAC,GACV,QAAA,MACM,MAAAD,aAAa,aAAa,WACvB,SAAA,MAAA;AACP5C,uBAAejC,IAAC3N,GAAI;AACpB2S,cAAAA,YAAkBvE,gBAAgBT,IAAC3N,GAAI;AACnC2S,qBACFtE,sBAAsBsE,SAAS;MAAA,GAG5B,OAAA;QAAAC,UAAW;MAAA,GAElB,cAAA,0BAAC,OAAa,EAAA,OAAC,GACb,UAAA;YAAA,0BAAC,MAAa,EAAA,SAAA,iBAAuB,OAAA,UACnC,UAAA;cAAA,0BAAC,MAAA,EAAW,OAAA,UAAc,KAAC,GAAgB,cAAA,GACxCzD,UAAAA;YAAAA,eAAexB,IAACL,WACd,yBAAA,WAAA,EACQ+B,OAAAA,cACG,UAAAwD,CAAWvD,YAAAA,gBAAgB9E,QAAK2H,cAAA1N,KAAoB,GACnD,WAAAqO,CAAA,YAAA;AACLtI,sBAAKpP,QAAS,UAChB6V,gBAAgBtD,KAAG0B,YAAY,IACtB7E,QAAKpP,QAAS,YACvBgU,cAAAA,IAAkB;YAAA,GAGd,QAAM6B,MAAAA,gBAAgBtD,KAAG0B,YAAY,GAC7C,WAAA,MACO,OAAA;cAAA0D,UAAW;cAAOC,QAAU;YAAM,EAAA,CAAA,QAG3C,yBAAC,MACQ,EAAA,QAAA,QACD,MAAA,GACO,cAAA,YACN,OAAA;cAAAD,UAAW;cAAOvW,QAAU;cAASyW,SAAW;YAAA,GAErD,OAAA1D,iBAAiB5B,IAACL,IAAA,KAClBK,IAAC6C,SACD7C,IAACL,KAAAnU,MAAYwU,IAACL,KAAAvK,SAAgB,GAAE4K,IAACL,KAAAvK,MAAY,GAEtC,SAAA,MAAA;AACO4K,4BAAAA,IAACL,IAAK,GACpBgC,gBAAgB3B,IAAC6C,SAAU7C,IAACL,KAAAnU,MAAgB,GAAA,CAAA,CAAC;YAAA,GAG9CoW,UAAiB5B,iBAAAA,IAACL,IAAA,KACjBK,IAAC6C,SACD7C,IAACL,KAAAnU,MAAYwU,IAACL,KAAAvK,SAAAA,GAAkB4K,IAACL,KAAAvK,MAAY,EAAA,CACjD;YAED2P,gBACE,yBAAA,KAAA,EACQ,OAAA;cAAAQ,OACE;cAAKF,QACJ;cAAKG,cACC;cAAKlW,iBACF;YAAA,EAGvB,CAAA;UAAA,EAAA,CACF;cACA,yBAAC,YACS,EAAA,YAAC,yBAAA,mBAAA,CAAA,CAAA,GACL,IAAG0Q,GAAAA,IAACL,IAAA,SAEN,UAAA,yBAAC,MAGC,EAAA,cAAA,yBAAC,UACM,EAAA,MAAA,YACI,SAAC,GACJ8F,MAAQA,WACR,MAAA9U,EAAE,eAAe,GACd,SAAA+U,CAAA,YAAA;AACP7I,oBAAK8I,gBAAiB,GACtB1F,YAAYD,IAACL,IAAK;UAAA,EAAA,CAAA,EAGxB,CAAA,GAEO,SAAA;YAAAiG,QAAA;YAAAC,WAA0B;YAAYC,MAAQ;UAAA,EAAA,CAAA;WAE3D;YAEA,yBAAC,MAAK,EAAA,OAAA,MAAOlB,UAAAA,qCAAQpO,MAAAlL,MAAc,KAAG,GAAK,CAAA;YAE3C,yBAAC,MAAA,EAAW,OAAA,UAAc,KAAC,GACzB,cAAA,yBAAC,MAAA,EAAW,MAAA,GAAG,OAAA,MACZgW,UAAAA,WAAUyB,OAAA3X,IAAAA,KAAiB4U,IAACgD,WAAY,EAAE,CAAC,EAAA,CAC9C,EACF,CAAA;QAEC+B,gBACE,yBAAA,QAAA,EACM,MAAA,SACA,MAAA,WACC,MAAA,GACG,SAAC,GACH,OAAA;UAAAM,QACG;UAAMJ,UACJ;UAAUc,OACb;UAAMC,QACL;UAAMpW,UACJ;QAAA,GAEN,MAAAe,EAAE,eAAe,GACd,SAAAsV,CAAA,MAAA;AACNN,YAAAA,gBAAAA,GACD7B,aAAa9D,GAAC;QAAA,EAAA,CAAA;SAItB,EAAA,GAnHKA,IAACL,IAoHR;IAAA,IAEFhU,EAAAA,EAAAA,IAAAiV,eAAAjV,EAAAA,EAAAA,IAAAgV,cAAAhV,EAAAA,EAAAA,IAAAsU,aAAAtU,EAAAA,EAAAA,IAAA6V,YAAA7V,EAAAA,EAAAA,IAAA+V,cAAA/V,EAAAA,EAAAA,IAAA2V,YAAA3V,EAAAA,EAAAA,IAAA8U,iBAAA9U,EAAAA,EAAAA,IAAAsX,YAAAtX,EAAAA,EAAAA,IAAA2X,iBAAA3X,EAAAA,EAAAA,IAAAmY,cAAAnY,EAAAA,EAAAA,IAAAiW,kBAAAjW,EAAAA,EAAAA,IAAAoS,SAAApS,EAAAA,EAAAA,IAAA0S,QAAA1S,EAAAA,EAAAA,IAAAmW,aAAAnW,EAAAA,EAAAA,IAAAqW,aAAArW,EAAAA,EAAAA,IAAA+U,uBAAA/U,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAoY,IAAApY,EAAAA,EAAAA,IAAAqY,IAAArY,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAAyR;EAAA;AAAA2G,SAAApY,EAAA,EAAA,GAAAqY,KAAArY,EAAA,EAAA,GAAAuR,KAAAvR,EAAA,EAAA,GAAAwR,KAAAxR,EAAA,EAAA,GAAAyR,KAAAzR,EAAA,EAAA;AAAA0R,MAAAA;AAAA1R,IAAAoY,EAAAA,MAAAA,MAAApY,EAAAA,EAAAA,MAAAuR,MAAAvR,EAAA,EAAA,MAAAwR,MAlIJE,SAAC,yBAAA,IAAA,EAAgB,UAAAH,IACdC,UAAAA,GAkIH,CAAA,GAAQxR,EAAAA,EAAAA,IAAAoY,IAAApY,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAA0R,MAAAA,KAAA1R,EAAA,EAAA;AAAA2R,MAAAA;AAAA,SAAA3R,EAAAqY,EAAAA,MAAAA,MAAArY,EAAAA,EAAAA,MAAAyR,MAAAzR,EAAA,EAAA,MAAA0R,MA1JVC,UAAAA,0BAAC,IACCF,EAAAA,UAAAA;IAAAA;IAsBAC;EAAAA,EAAAA,CAoIF,GAAkB1R,EAAAA,EAAAA,IAAAqY,IAAArY,EAAAA,EAAAA,IAAAyR,IAAAzR,EAAAA,EAAAA,IAAA0R,IAAA1R,EAAAA,EAAAA,IAAA2R,OAAAA,MAAA3R,EAAA,EAAA,GA3JlB2R;AA2JkB;AC7XtB,SAAS4I,mBAA4B;AACnC,SAAO,OAAOC,SAAW,OAAeA,OAAOC,aAAa;AAC9D;AAEA,SAASC,yBAAyBhB,QAA6C;AAC7E,MAAIiB,aAAajB;AAEjB,SAAKiB,eAEHA,aACE,OAAOH,SAAW,OAAe,OAAOI,WAAa,MACjDA,SAASC,KAAKC,sBAAwBpB,EAAAA,SAAS,KAC/C,IAED;IACLqB,aAAaJ,cAAcJ,iBAAAA,IAAqB,IAAI;IACpDS,MAAML,aAAa,MAAMzI,SAAYyI,aAAa;IAClDM,aAAaN,aAAa;IAC1BO,SAASC,KAAKC,IAAI,KAAKD,KAAKE,IAAI,KAAKV,aAAa,CAAC,CAAC;IACpDW,SAASX,aAAa,MAAMA,aAAa,MAAMA,aAAa;EAC9D;AACF;AAEO,SAAAY,YAAAlb,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,CAAA,GAAqB;IAAAub;EAAAnb,IAAAA,IAK1B,CAAAob,oBAAAC,qBAAA,QAAoDtb,wBAAAub,OAAiC,GACrF,CAAAC,iBAAAC,kBAAA,QAA8Czb,wBAAA0b,QAE9C;AAACxb,MAAAA;AAAAN,IAAA,CAAA,MAAAwb,cAAAzS,WAESzI,KAAAA,MAAA;AAAA,QACHkb,CAAAA,cAAazS;AAAA;AAGlB,UAAAgT,eAAA1Q,CAAA,YAAA;AACEqQ,4BAAsBnB,iBAAAA,CAAkB;AACxCyB,YAAAA,QAAc3Q,mCAAO;AACjB2Q,eACFH,mBAAmBnB,yBAAyBsB,MAAKC,YAAAvC,MAAmB,CAAC;IAAA,GAGzEwC,iBAAA,IAAAC,eAA0CJ,YAAY;AACtDG,WAAAA,eAAcE,QAASZ,cAAazS,OAAQ,GAAC,MAAA;AAG3CmT,qBAAcG,WAAY;IAAC;EAE9Brc,GAAAA,EAAA,CAAA,IAAAwb,cAAAzS,SAAA/I,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAY,MAAAA;AAAAZ,IAAAA,CAAAA,MAAAwb,iBAAE5a,KAAAA,CAAC4a,aAAa,GAACxb,EAAAA,CAAAA,IAAAwb,eAAAxb,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA,OAjBlBW,yBAAUL,IAiBPM,EAAe;AAAC6B,MAAAA;AAAAzC,SAAAA,EAAAyb,CAAAA,MAAAA,sBAAAzb,EAAAA,CAAAA,MAAA4b,mBAEZnZ,KAAA;IAAAmZ;IAAAH;EAAAA,GAAqCzb,EAAAA,CAAAA,IAAAyb,oBAAAzb,EAAAA,CAAAA,IAAA4b,iBAAA5b,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA,GAArCyC;AAAqC;AA7BvC,SAAAqZ,WAAA;AAAA,SAOHpB,yBAAAxI,MAAkC;AAAC;AAPhC,SAAAyJ,UAAA;AAAA,SAK8DpB,iBAAiB;AAAC;AClBhF,SAAA+B,kBAAAjc,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,EAAA,GAA2B;IAAAoQ;IAAAkM;IAAAC;IAAAC;IAAAC;EAAAA,IAAArc,IAOhC;IAAA2E;EAAAA,IAAYuL,eAAAC,qBAAoC;AAAClQ,MAAAA;AAAAN,IAAAA,CAAAA,MAAAgF,KASlC1E,KAAA0E,EAAE,kCAAkC,GAAChF,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAY,MAAAA;AAAAZ,IAAAA,CAAAA,MAAAM,MAF1CM,SAAA,yBAAC,MAAa,EAAA,QAAC,GACb,cAAA,yBAAC,MAAW,EAAA,MAAA,GAAG,OAAA,MACZN,UAAAA,GACH,CAAA,EACF,CAAA,GAAON,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA;AAAAyC,MAAAA;AAAAzC,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAUDnG,SAAA,yBAAC,MAAa,EAAA,QAAC,GACb,cAAA,yBAAC,SAAc,EAAA,MAAA,CAAC,QAAQ,OAAO,EACjC,CAAA,EAAA,CAAA,GAAOzC,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA;AASD4C,QAAAA,KAAA4Z,kBAAeG,WAAAC,UACXxL,KAAAmL,oBAAgB,CAAKlM,gBACzBkB,KAAAiL,kBAAkB,aAAa;AAAShL,MAAAA;AAAAxR,IAAAwc,CAAAA,MAAAA,mBAAAxc,EAAAA,CAAAA,MAAAgF,KACxCwM,KAAkBxM,EAAlBwX,kBAAoB,wBAA2B,sBAAN,GAA6Bxc,EAAAA,CAAAA,IAAAwc,iBAAAxc,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAwR,MAAAA,KAAAxR,EAAA,CAAA;AAAAyR,MAAAA;AAAAzR,IAAAyc,CAAAA,MAAAA,oBAAAzc,EAAA,CAAA,MAAA4C,MAAA5C,EAAAoR,EAAAA,MAAAA,MAAApR,EAAA,EAAA,MAAAuR,MAAAvR,EAAAA,EAAAA,MAAAwR,MAjBlFC,SAAC,yBAAA,KAAA,EAAU,MAAC,GACV,cAAA,yBAAC,SAEG,EAAA,SAAAhP,IAIQ,WAAA,OACV,QAAA,MAEA,cAAA,yBAAC,QACO,EAAA,OAAA,QACGga,SAAAA,kBACJ,MAAA,UACC,MAAA7Z,IACI,UAAAwO,IACJ,MAAAG,IACA,MAAAC,GAAAA,CAAAA,EAEV,CAAA,EACF,CAAA,GAAMxR,EAAAA,CAAAA,IAAAyc,kBAAAzc,EAAAA,CAAAA,IAAA4C,IAAA5C,EAAAA,EAAAA,IAAAoR,IAAApR,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAAyR,MAAAA,KAAAzR,EAAA,EAAA;AAMI0R,QAAAA,KAAA6K,mBAAgBI,WAAAC;AAAsBjL,MAAAA;AAAA3R,IAAAuc,EAAAA,MAAAA,oBAAAvc,EAAAA,EAAAA,MAAAgF,KACtC2M,MAAmB3M,EAAnBuX,mBAAqB,yBAA4B,uBAAN,GAA8Bvc,EAAAA,EAAAA,IAAAuc,kBAAAvc,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA2R,OAAAA,MAAA3R,EAAA,EAAA;AAErE,QAAA4R,MAAAA,CAACvB,gBACLwB,MAAA0K,mBAAmB,aAAa;AAASzK,MAAAA;AAAA9R,IAAA0c,EAAAA,MAAAA,qBAAA1c,EAAA,EAAA,MAAA2R,OAAA3R,EAAA4R,EAAAA,MAAAA,OAAA5R,EAAA,EAAA,MAAA6R,OAAA7R,EAAAA,EAAAA,MAAA0R,MATnDI,UAAC,yBAAA,KAAA,EAAU,MAAC,GAAc,YAAC,GACzB,cAAA,yBAAC,QAAA,EACO,OAAA,QACG4K,SAAgB,mBACpB,MAAA,UACC,MAAAhL,IACA,MAAAC,KACD,MAAA,SACK,UAAAC,KACJ,MAAAC,IAAyC,CAAA,EAEnD,CAAA,GAAM7R,EAAAA,EAAAA,IAAA0c,mBAAA1c,EAAAA,EAAAA,IAAA2R,KAAA3R,EAAAA,EAAAA,IAAA4R,KAAA5R,EAAAA,EAAAA,IAAA6R,KAAA7R,EAAAA,EAAAA,IAAA0R,IAAA1R,EAAAA,EAAAA,IAAA8R,OAAAA,MAAA9R,EAAA,EAAA;AAAA0Y,MAAAA;AAAA1Y,IAAA8R,EAAAA,MAAAA,OAAA9R,EAAAA,EAAAA,MAAAyR,MAjCRiH,UAAC,0BAAA,MAAa,EAAA,SAAA,gBACZjH,UAAAA;IAAAA;IAqBAK;EAAAA,EAYF,CAAA,GAAO9R,EAAAA,EAAAA,IAAA8R,KAAA9R,EAAAA,EAAAA,IAAAyR,IAAAzR,EAAAA,EAAAA,IAAA0Y,OAAAA,MAAA1Y,EAAA,EAAA;AAAA2Y,MAAAA;AAAA3Y,SAAAA,EAAAqQ,EAAAA,MAAAA,kBAAArQ,EAAAA,EAAAA,MAAA0Y,OAAA1Y,EAAA,EAAA,MAAAY,MAhDb+X,UAAC,yBAAA,mBAAA,EACC,cAAC,yBAAA,MAAA,EAAc,SAAA,GAAa,UAAC,GAC3B,cAAC,yBAAA,SAAA,EAEG,SAAA/X,IAMQ,WAAA,OACAyP,UAAAA,gBACV,QAAA,MAEAqI,UAAAA,IAAAA,CAmCF,EACF,CAAA,EAAA,CACF,GAAoB1Y,EAAAA,EAAAA,IAAAqQ,gBAAArQ,EAAAA,EAAAA,IAAA0Y,KAAA1Y,EAAAA,EAAAA,IAAAY,IAAAZ,EAAAA,EAAAA,IAAA2Y,OAAAA,MAAA3Y,EAAA,EAAA,GAnDpB2Y;AAmDoB;AC7EXkE,IAAAA,4BAA4B7U,GAAO4G,GAAG;;;;AAAtCiO,IAKAC,yBAAyB9U,GAAO8G;;;;AALhC+N,ICiBPE,MAAM/U,GAAOC;;;;;sBAKG,CAAC;EAAC+U;AAAK,MAAM,oBAAoBA,KAAK,aAAa;;AAKlE,SAAAC,qBAAA;AAAA,QAAAjd,QAAAC,iCAAA,EAAA,GACL,CAAAid,MAAAC,OAAA,QAAwB/c,wBAAAA,KAAc,GACtCgd,gBAAkB1U,sBAAA,IAAqC,GACvD2U,iBAAmB3U,sBAAA,IAAkC;AAACrI,MAAAA;AAAAL,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAEtBvI,KAAAA,MAAM8c,QAAOxB,OAAU,GAAC3b,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAxD,QAAAsd,cAAoBjd,IAEpB;IAAA2E;EAAAA,IAAYuL,eAAAC,qBAAoC;AAAC,MAAAlQ,IAAAM;AAAAZ,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAG/CtI,KAAAA,MAAM6c,QAAAA,KAAa,GACnBvc,KAAAA,MAAOwc,CAAAA,UAASrU,SAAUsU,WAAUtU,OAAA,GAAS/I,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAY,OAAAN,KAAAN,EAAA,CAAA,GAAAY,KAAAZ,EAAA,CAAA,IAF/Cud,qBACEjd,IACAM,EACF;AAAC6B,MAAAA;AAAAzC,IAAAA,CAAAA,MAAAgF,KAQkCvC,KAAAuC,EAAE,6BAA6B,GAAChF,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA;AAAA4C,MAAAA;AAAA5C,IAAAA,CAAAA,MAAAyC,MADzDG,SAAC,yBAAA,QAAA,EAAc,OAAC,GACd,cAAC,yBAAA,MAAA,EAAY,QAAA,UAAUH,UAAAA,GAAAA,CAAiC,EAC1D,CAAA,GAASzC,EAAAA,CAAAA,IAAAyC,IAAAzC,EAAAA,CAAAA,IAAA4C,MAAAA,KAAA5C,EAAA,CAAA;AAAAoR,MAAAA;AAAApR,IAAAA,CAAAA,MAAAgF,KAGMoM,KAAApM,EAAE,mCAAmC,GAAChF,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAoR,MAAAA,KAAApR,EAAA,CAAA;AAAAuR,MAAAA;AAAAvR,IAAAA,CAAAA,MAAAoR,MADrDG,SAAAA,yBAAC,MACC,EAAA,cAAA,yBAAC,MAAK,EAAA,OAAA,MAAOH,UAAAA,GAAAA,CAAuC,EAAA,CACtD,GAAOpR,EAAAA,CAAAA,IAAAoR,IAAApR,EAAAA,EAAAA,IAAAuR,MAAAA,KAAAvR,EAAA,EAAA;AAAAwR,MAAAA;AAAAxR,IAAAA,EAAAA,MAAAgF,KAIsBwM,KAAAxM,EAAE,WAAW,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAwR,MAAAA,KAAAxR,EAAA,EAAA;AAAAyR,MAAAA;AAAAzR,IAAAA,EAAAA,MAAAwR,MADvCC,SAAAA,yBAAC,KACC,EAAA,cAAA,yBAAC,OAAW,EAAA,MAAA,WAAWD,UAAAA,GAAAA,CAAe,EAAA,CACxC,GAAMxR,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAAyR,MAAAA,KAAAzR,EAAA,EAAA;AAAA0R,MAAAA;AAAA1R,IAAAA,EAAAA,MAAAgF,KACN0M,SAAC,yBAAA,MAAA,EAAK,OAAI,MACR,cAAC,yBAAA,WAAA,EACI1M,GACK,SAAA,oEAAmE,CAAA,EAE/E,CAAA,GAAOhF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA0R,MAAAA,KAAA1R,EAAA,EAAA;AAAA2R,MAAAA;AAAA3R,IAAAyR,EAAAA,MAAAA,MAAAzR,EAAAA,EAAAA,MAAA0R,MAVXC,UAAC,yBAAA,MAAA,EACC,cAAC,0BAAA,OAAA,EAAa,OAAC,GACbF,UAAAA;IAAAA;IAGAC;EAAAA,EAMF,CAAA,EACF,CAAA,GAAO1R,EAAAA,EAAAA,IAAAyR,IAAAzR,EAAAA,EAAAA,IAAA0R,IAAA1R,EAAAA,EAAAA,IAAA2R,OAAAA,MAAA3R,EAAA,EAAA;AAAA4R,MAAAA;AAAA5R,IAAAA,EAAAA,MAAAgF,KACN4M,MASO,MAAA5R,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA4R,OAAAA,MAAA5R,EAAA,EAAA;AAAA6R,MAAAA;AAAA7R,IAAAA,EAAAA,MAAAgF,KAKD6M,MAAA7M,EAAE,wCAAwC,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA6R,OAAAA,MAAA7R,EAAA,EAAA;AAAA8R,MAAAA;AAAA9R,IAAAA,EAAAA,MAAA6R,OAHlDC,UAAC,yBAAA,MAAA,EACC,cAAC,yBAAA,MAAA,EACC,cAAC,0BAAA,wBAAA,EAA4B,MAAA,uCAA6C,QAAA,UACvED,UAAAA;IAAAA;IAA4C;EAAA,EAAA,CAC/C,EACF,CAAA,EAAA,CACF,GAAO7R,EAAAA,EAAAA,IAAA6R,KAAA7R,EAAAA,EAAAA,IAAA8R,OAAAA,MAAA9R,EAAA,EAAA;AAAA0Y,MAAAA;AAAA1Y,IAAA2R,EAAAA,MAAAA,OAAA3R,EAAA,EAAA,MAAA4R,OAAA5R,EAAA8R,EAAAA,MAAAA,OAAA9R,EAAA,EAAA,MAAA4C,MAAA5C,EAAAA,EAAAA,MAAAuR,MAvCXmH,UAAC,yBAAA,2BACC,EAAA,cAAA,0BAAC,OAAa,EAAA,OAAC,GACb9V,UAAAA;IAAAA;IAIA2O;IAGAI;IAaCC;IAWDE;EAAAA,EAAAA,CAOF,EACF,CAAA,GAA4B9R,EAAAA,EAAAA,IAAA2R,KAAA3R,EAAAA,EAAAA,IAAA4R,KAAA5R,EAAAA,EAAAA,IAAA8R,KAAA9R,EAAAA,EAAAA,IAAA4C,IAAA5C,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAA0Y,OAAAA,MAAA1Y,EAAA,EAAA;AAAA2Y,MAAAA;AAAA3Y,IAAA,EAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAmB5B+P,UAAC,yBAAA,KAAA,EAAW,OAAoD,UAAA,CAAa,GAAA3Y,EAAAA,EAAAA,IAAA2Y,OAAAA,MAAA3Y,EAAA,EAAA;AAAA4Y,MAAAA;AAAA5Y,IAAAA,EAAAA,MAAAkd,QAX/EtE,UAAAA,yBAAC,QACO4E,EAAAA,MAAaA,gBACd,MAAA,SACI,SAAA,GACK,cAAC,GACV,MAAA,WACK,UAAA,GACLJ,KAAAA,WACIE,SAAU,aACTJ,UAAAA,MAEVvE,UAAAA,IAAAA,CACF,GAAS3Y,EAAAA,EAAAA,IAAAkd,MAAAld,EAAAA,EAAAA,IAAA4Y,OAAAA,MAAA5Y,EAAA,EAAA;AAAA8Y,MAAAA;AAAA9Y,SAAAA,EAAAkd,EAAAA,MAAAA,QAAAld,EAAAA,EAAAA,MAAA0Y,OAAA1Y,EAAA,EAAA,MAAA4Y,OA/DXE,UAAAA,yBAAC,SAEG,EAAA,SAAAJ,KA2CQ,WAAA,gBACV,QAAA,MACS,SAAA,GACJ2E,KAAAA,YACCH,MAENtE,UAAAA,IAAAA,CAaF,GAAU5Y,EAAAA,EAAAA,IAAAkd,MAAAld,EAAAA,EAAAA,IAAA0Y,KAAA1Y,EAAAA,EAAAA,IAAA4Y,KAAA5Y,EAAAA,EAAAA,IAAA8Y,OAAAA,MAAA9Y,EAAA,EAAA,GAhEV8Y;AAgEU;AA/EP,SAAA6C,QAAA8B,GAAA;AAAA,SAAA,CAKiDA;AAAC;ACZzD,IAAMC,iCAGDrd,CAAA,OAAA;AAAAL,QAAAA,QAAAC,iCAAA,CAAA,GAAC;IAAA0d;IAAA3Y;EAAA,IAAA3E,IACJ8E,OACE,OAAOwY,kBAAiBC,uBAAyB,WAC7CD,kBAAiBC,oBAAAC,SAAA3G,QACjByG,kBAAiBnT;AAAwBlK,MAAAA;AAAAN,IAAA2d,CAAAA,MAAAA,qBAAA3d,EAAAA,CAAAA,MAAAgF,KAEjC1E,KAAAiK,qBAAqBoT,iBAAiB,IAChD,IAAI3Y,EAAE,4CAA4C,CAAC,MACnDA,EAAE,4CAA4C,GAAChF,EAAAA,CAAAA,IAAA2d,mBAAA3d,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAFnD,QAAA8d,QAAcxd;AAEqC,MAAAM,IAAA6B;AAAAzC,IAAA8d,CAAAA,MAAAA,SAAA9d,EAAAA,CAAAA,MAAAmF,QAG3C1C,KAAA,CAAC0C,MAAM2Y,KAAK,EAAA3b,OAAAwZ,OAAgD,GAAC3b,EAAAA,CAAAA,IAAA8d,OAAA9d,EAAAA,CAAAA,IAAAmF,MAAAnF,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA,GAAAY,KAA7D6B,GAA6Dsb,KAAM,GAAG;AAD9E,QAAApO,OAAa/O,IAM6BgC,KAAA,CAAC2H,qBAAqBoT,iBAAiB;AAACvM,MAAAA;AAAA,SAAApR,EAAA4C,CAAAA,MAAAA,MAAA5C,EAAAA,CAAAA,MAAA2P,QAAhFyB,SAES,yBAAA,UAAA,EAFK,OAAA,iBAA0B,UAAAxO,IAClC,UAAA,KAAA,CACN,GAAS5C,EAAAA,CAAAA,IAAA4C,IAAA5C,EAAAA,CAAAA,IAAA2P,MAAA3P,EAAAA,CAAAA,IAAAoR,MAAAA,KAAApR,EAAA,CAAA,GAFToR;AAES;AAmBN,SAAA4M,gBAAA3d,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,EAAA,GAAyB;IAAAge;IAAAC;IAAAC;IAAA7Q;IAAA8Q;IAAAC;IAAAC;IAAAC;IAAAC;IAAAC;IAAA/X;IAAAgY;EAAAA,IAAAre,IAc9Bsd,oBAA0BgB,eAAAA,GAC1B;IAAA3Z;EAAAA,IAAYuL,eAAAC,qBAAoC,GAChDoO,0BAA4BlW,sBAAA,IAAoC;AAACpI,MAAAA;AAAAN,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAC/BtI,KAAAA,MAAA;AAChC,UAAAue,KAAWD,oBAAmB7V;AACzB8V,QAAAA;AAAE,UAAA;AAGLA,WAAEC,OAAQ,GACVlE,SAAAmE,YAAqB,MAAM;MAAA,QAAC;AAE5BC,gBAAArO,MAAc,gCAAgC;MAAA;EAAC,GAElD3Q,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAVD,QAAAif,gBAAsB3e;AAUhBM,MAAAA;AAAAZ,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAIahI,KAAA,CAAA,GAAA,GAAA,GAAA,EAAA,GAAaZ,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA;AAAAyC,MAAAA;AAAAzC,IAAAA,CAAAA,MAAAgF,KAKNvC,KAAAuC,EAAE,wBAAwB,GAAChF,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA;AAAA4C,MAAAA;AAAA5C,IAAAA,CAAAA,MAAAyC,MAD3CG,SAAC,yBAAA,MAAA,EAAiB,YAAC,GAAiB,eAAC,GACnC,cAAC,yBAAA,aAAA,EAAaH,UAAAA,GAAAA,CAA4B,EAC5C,CAAA,GAAOzC,EAAAA,CAAAA,IAAAyC,IAAAzC,EAAAA,CAAAA,IAAA4C,MAAAA,KAAA5C,EAAA,CAAA;AAAAoR,MAAAA;AAAApR,IAAAA,CAAAA,MAAAqe,YAEJjN,KAAAiN,SAAQ7K,IAAAsI,MAER,GAAC9b,EAAAA,CAAAA,IAAAqe,UAAAre,EAAAA,CAAAA,IAAAoR,MAAAA,KAAApR,EAAA,CAAA;AAAAuR,MAAAA;AAAAvR,IAAAke,CAAAA,MAAAA,WAAAle,EAAAA,CAAAA,MAAAie,mBAAAje,EAAA,EAAA,MAAAoR,MAHJG,SAAAA,yBAAC,QAAc2M,EAAAA,OAAAA,SAAmBD,UAAAA,iBAC/B7M,UAGH,GAAA,CAAA,GAASpR,EAAAA,CAAAA,IAAAke,SAAAle,EAAAA,CAAAA,IAAAie,iBAAAje,EAAAA,EAAAA,IAAAoR,IAAApR,EAAAA,EAAAA,IAAAuR,MAAAA,KAAAvR,EAAA,EAAA;AAAAwR,MAAAA;AAAAxR,IAAA4C,EAAAA,MAAAA,MAAA5C,EAAAA,EAAAA,MAAAuR,MATbC,SAAA,yBAAC,KAAA,EAAa,SAAA,GAAW,QAAA,GACvB,cAAA,0BAAC,OACC5O,EAAAA,UAAAA;IAAAA;IAGA2O;EAAAA,EAKF,CAAA,EACF,CAAA,GAAMvR,EAAAA,EAAAA,IAAA4C,IAAA5C,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAAwR,MAAAA,KAAAxR,EAAA,EAAA;AAAAyR,MAAAA;AAAAzR,IAAAA,EAAAA,MAAAgF,KAMcyM,KAAAzM,EAAE,4BAA4B,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAyR,MAAAA,KAAAzR,EAAA,EAAA;AAAA0R,MAAAA;AAAA1R,IAAAA,EAAAA,MAAAyR,MAD/CC,SAAC,yBAAA,MAAA,EAAiB,YAAC,GAAiB,eAAC,GACnC,cAAC,yBAAA,aAAA,EAAaD,UAAAA,GAAAA,CAAgC,EAChD,CAAA,GAAOzR,EAAAA,EAAAA,IAAAyR,IAAAzR,EAAAA,EAAAA,IAAA0R,MAAAA,KAAA1R,EAAA,EAAA;AAGE2R,QAAAA,MAAAwM,qBAAAA,QAA6B7Q,aAAa;AAAOsE,MAAAA;AAAA5R,IAAA,EAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAGvDgJ,MAAApS,aAAAgU,IAAA0L,MAEA,GAAClf,EAAAA,EAAAA,IAAA4R,OAAAA,MAAA5R,EAAA,EAAA;AAAA6R,MAAAA;AAAA7R,IAAAA,EAAAA,MAAAgF,KAEC6M,MAAA7M,EAAE,kCAAkC,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA6R,OAAAA,MAAA7R,EAAA,EAAA;AAAA8R,MAAAA;AAAA9R,IAAAA,EAAAA,MAAA6R,OADxCC,UAES,yBAAA,UAAA,EAFiB,OAAA,SACvBD,UADS,IAAA,GAAA,OAEZ,GAAS7R,EAAAA,EAAAA,IAAA6R,KAAA7R,EAAAA,EAAAA,IAAA8R,OAAAA,MAAA9R,EAAA,EAAA;AAAA0Y,MAAAA;AAAA1Y,IAAAoe,EAAAA,MAAAA,sBAAApe,EAAAA,EAAAA,MAAA2R,OAAA3R,EAAA,EAAA,MAAA8R,OAVX4G,UAAAA,0BAAC,QACa,EAAA,eAAA,wBACL,OAAA/G,KACGyM,UAAAA,oBAETxM,UAAAA;IAAAA;IAGDE;EAAAA,EAAAA,CAGF,GAAS9R,EAAAA,EAAAA,IAAAoe,oBAAApe,EAAAA,EAAAA,IAAA2R,KAAA3R,EAAAA,EAAAA,IAAA8R,KAAA9R,EAAAA,EAAAA,IAAA0Y,OAAAA,MAAA1Y,EAAA,EAAA;AAAA2Y,MAAAA;AAAA3Y,IAAA0Y,EAAAA,MAAAA,OAAA1Y,EAAAA,EAAAA,MAAA0R,MAhBbiH,UAAA,yBAAC,KAAA,EAAa,SAAA,GAAW,QAAA,GACvB,cAAA,0BAAC,OACCjH,EAAAA,UAAAA;IAAAA;IAGAgH;EAAAA,EAYF,CAAA,EACF,CAAA,GAAM1Y,EAAAA,EAAAA,IAAA0Y,KAAA1Y,EAAAA,EAAAA,IAAA0R,IAAA1R,EAAAA,EAAAA,IAAA2Y,OAAAA,MAAA3Y,EAAA,EAAA;AAAA4Y,MAAAA;AAAA5Y,IAAAme,EAAAA,MAAAA,oBAAAne,EAAA,EAAA,MAAAse,8BAAAte,EAAAwe,EAAAA,MAAAA,qBAAAxe,EAAA,EAAA,MAAAue,4BAAAve,EAAAA,EAAAA,MAAAgF,KAGL4T,MAAAuF,qBAAgB,aACd,yBAAA,KAAA,EAAa,SAAC,GAAU,QAAA,GACvB,cAAA,0BAAC,OACC,EAAA,UAAA;QAAA,yBAAC,MAAiB,EAAA,YAAC,GAAiB,eAAA,GAClC,cAAA,yBAAC,aAAyB,EAAA,cAAA,YACvBnZ,UAAAA,EAAE,mCAAmC,EACxC,CAAA,EAAA,CACF;QAEC,yBAAA,WAAA,EACMsZ,KAAyB,4BACvBH,OAAAA,kBACGI,UAAAA,0BAER,gBAAAC,oBAAiBtM,SAAelN,EAAE,oCAAoC,GAE7D,WAAA,GAEf,CAAA;EAAA,EAAA,CAAA,EACF,CAAA,GACDhF,EAAAA,EAAAA,IAAAme,kBAAAne,EAAAA,EAAAA,IAAAse,4BAAAte,EAAAA,EAAAA,IAAAwe,mBAAAxe,EAAAA,EAAAA,IAAAue,0BAAAve,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA4Y,OAAAA,MAAA5Y,EAAA,EAAA;AAAA8Y,MAAAA;AAAA9Y,IAAAA,EAAAA,MAAAgF,KAQuB8T,MAAA9T,EAAE,4BAA4B,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA8Y,OAAAA,MAAA9Y,EAAA,EAAA;AAAAmf,MAAAA;AAAAnf,IAAAA,EAAAA,MAAA8Y,OAD/CqG,UAAC,yBAAA,KAAA,EACC,cAAC,yBAAA,aAAA,EAAarG,UAAgC,IAAA,CAAA,EAAA,CAChD,GAAM9Y,EAAAA,EAAAA,IAAA8Y,KAAA9Y,EAAAA,EAAAA,IAAAmf,OAAAA,MAAAnf,EAAA,EAAA;AAAAof,MAAAA;AAAApf,IAAA,EAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAENwW,UAAC,yBAAA,KAAA,EACC,cAAA,yBAAC,oBACH,CAAA,CAAA,EAAA,CAAA,GAAMpf,EAAAA,EAAAA,IAAAof,OAAAA,MAAApf,EAAA,EAAA;AAAAqf,MAAAA;AAAArf,IAAAA,EAAAA,MAAAmf,OARVE,UAAA,yBAAC,MAAoB,EAAA,eAAA,GACnB,cAAA,0BAAC,QAAc,EAAA,OAAA,GACbF,UAAAA;IAAAA;IAIAC;EAAAA,EAAAA,CAGF,EACF,CAAA,GAAOpf,EAAAA,EAAAA,IAAAmf,KAAAnf,EAAAA,EAAAA,IAAAqf,OAAAA,MAAArf,EAAA,EAAA;AACQ,QAAAsf,MAAAZ,eAAe;AAASa,MAAAA;AAAAvf,IAAA2d,EAAAA,MAAAA,qBAAA3d,EAAAA,EAAAA,MAAAgF,KACpCua,MAAAvV,uBAAAwJ,IAAAgM,CAAAA,oBACKA,oBAAoB,sBAAA,0BAEnB,wBACC,EAAA,UAAA;QAAC,yBAAA,gCAAA,EAAkD7B,mBAAsB3Y,EACzE,CAAA;QAAA,yBAAA,UAA4B,EAAA,OAAA,WACzBA,UAAE,EAAA,+BAA+B,EAAA,GADxB,SAEZ;QAAA,yBAAA,MAEF,CAAA,CAAA;EANc,EAAA,GAAA,eAMd,QAGG,yBAAA,UAAA,EAA+Bwa,UAAAA,gBAAAA,GAAlBA,eAAkC,CACvD,GAACxf,EAAAA,EAAAA,IAAA2d,mBAAA3d,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAuf,OAAAA,MAAAvf,EAAA,EAAA;AAAAyf,MAAAA;AAAAzf,IAAAye,EAAAA,MAAAA,uBAAAze,EAAAA,EAAAA,MAAAsf,OAAAtf,EAAA,EAAA,MAAAuf,OAdJE,UAAAA,yBAAC,QAAc,EAAA,OAAAH,KAAoCb,UAAAA,qBAChDc,UAcH,IAAA,CAAA,GAASvf,EAAAA,EAAAA,IAAAye,qBAAAze,EAAAA,EAAAA,IAAAsf,KAAAtf,EAAAA,EAAAA,IAAAuf,KAAAvf,EAAAA,EAAAA,IAAAyf,OAAAA,MAAAzf,EAAA,EAAA;AAAA0f,MAAAA;AAAA1f,IAAAqf,EAAAA,MAAAA,OAAArf,EAAAA,EAAAA,MAAAyf,OA5BbC,UAAA,yBAAC,KAAA,EAAa,SAAA,GAAW,QAAA,GACvB,cAAA,0BAAC,OACCL,EAAAA,UAAAA;IAAAA;IAWAI;EAAAA,EAgBF,CAAA,EACF,CAAA,GAAMzf,EAAAA,EAAAA,IAAAqf,KAAArf,EAAAA,EAAAA,IAAAyf,KAAAzf,EAAAA,EAAAA,IAAA0f,OAAAA,MAAA1f,EAAA,EAAA;AAAA2f,MAAAA;AAAA3f,IAAAme,EAAAA,MAAAA,oBAAAne,EAAAA,EAAAA,MAAAgF,KAAAhF,EAAA,EAAA,MAAA0G,OAGLiZ,MAAAA,OAAOjZ,OAAQ,eACb,yBAAA,KAAA,EAAa,SAAA,GAAS,MAAA,GAAW,QAAAyX,qBAA0B,QAAA,IAAA,GAC1D,cAAA,0BAAC,OACC,EAAA,UAAA;QAAA,yBAAC,MAAA,EAAiB,YAAA,GAAkB,eAAA,GAClC,cAAA,0BAAC,aACEnZ,EAAAA,UAAAA;MAAAA,EAAE,WAAW;MAAE;UAChB,0BAAC,eAAuBia,EAAAA,SAAAA,eAAe,UAAA;QAAA;QACnCja,EAAE,8BAA8B;QAAE;MAAA,EACtC,CAAA;IAAA,EAAA,CACF,EACF,CAAA;QACC,0BAAA,MAAA,EAAW,MAAA,GAAQ,KAAA,GAClB,UAAA;UAAA,yBAAC,KAAU,EAAA,MAAA,GACT,cAAA,yBAAC,WAAU,EAAA,UAAA,MAAc,MAAA,OAAW4Z,KAAkB,qBAAUlY,OAAE,IAAA,CAAA,EAAA,CACpE;UACA,yBAAC,SAAA,EAAiB,SAAA1B,EAAE,8BAA8B,GAChD,cAAA,yBAAC,QAAA,EACa,cAAAA,EAAE,8BAA8B,GACvC,MAAA,UACA,MAAA,SACC4a,MAAAA,UACGX,SAAAA,cAAAA,CAAa,EAE1B,CAAA;IAAA,EACF,CAAA;EACF,EAAA,CAAA,EACF,CAAA,QAEA,yBAAC,KAAU,EAAA,MAAA,EAAA,CACZ,GAAAjf,EAAAA,EAAAA,IAAAme,kBAAAne,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA0G,KAAA1G,EAAAA,EAAAA,IAAA2f,OAAAA,MAAA3f,EAAA,EAAA;AAAA6f,MAAAA;AAAA,SAAA7f,EAAA2Y,EAAAA,MAAAA,OAAA3Y,EAAA,EAAA,MAAA4Y,OAAA5Y,EAAA0f,EAAAA,MAAAA,OAAA1f,EAAA,EAAA,MAAA2f,OAAA3f,EAAAA,EAAAA,MAAAwR,MA3HLqO,UAAC,yBAAA,QAAA,EAAiB,UAAA,GAAa,UAAC,GAC9B,cAAC,0BAAA,MAAA,EAAc,SAAAjf,IAEb4Q,UAAAA;IAAAA;IAcAmH;IAqBCC;IAuBD8G;IAiCCC;EAAAA,EAAAA,CA8BH,EACF,CAAA,GAAS3f,EAAAA,EAAAA,IAAA2Y,KAAA3Y,EAAAA,EAAAA,IAAA4Y,KAAA5Y,EAAAA,EAAAA,IAAA0f,KAAA1f,EAAAA,EAAAA,IAAA2f,KAAA3f,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAA6f,OAAAA,MAAA7f,EAAA,EAAA,GA7HT6f;AA6HS;AA3JN,SAAAX,OAAA9R,SAAA;AA0DS,aAAA,yBAAA,UAAuBA,EAAAA,UAAAA,QAAAA,GAAVA,OAAkB;AAAS;AA1DjD,SAAA0O,OAAAgE,IAAA;AAwCS,aAAA,yBAAA,UAAkBA,EAAAA,UAAAA,GAAAA,GAALA,EAAQ;AAAS;AA7EzC,SAAAnE,QAAAxQ,OAAA;AAAA,SAWqC,OAAOA,QAAU;AAAW;ACnCtE,SAAS4U,WAAWC,SAAiBC,UAA0B;AAC7D,SAAOC,IAAIC,gBACT,IAAIC,KAAK,CAACJ,OAAO,GAAG;IAClBK,MAAMJ;EAAAA,CACP,CACH;AACF;AAEA,SAASK,2BAA2BL,UAAkBM,eAAuC;AAC3F,SAAc,uBAAA;AACRC,QAAAA,aAAa,IACbC,cAAc;AAClB,WAAQC,CAAmB,UAAA;AACnBV,YAAAA,UAAUO,cAAcG,KAAK;AAC/B,UAAA,EAAA,OAAOV,WAAY,YAAYA,YAAY;AAI/C,eAAIA,YAAYS,gBAIhBA,cAAcT,SACVQ,cACFN,IAAIS,gBAAgBH,UAAU,GAGhCA,aAAaT,WAAWC,SAASC,QAAQ,IAClCO;IACT;EAAA,GACC;AACL;AAEaI,IAAAA,iBAAiBN,2BAA2B,oBAAqBI,CAC5EpV,UAAAA,KAAKC,UAAUmV,OAAO,MAAM,CAAC,CAC/B;AAFaE,IAIAC,gBAAgBP,2BAA2B,YAAaI,CAC5DI,cAAAA,4BAAS7S,MAAMC,QAAQwS,KAAK,IAAIA,QAAQ,CAACA,KAAK,CAAC,EAAEjT,KAAAA,CACzD;AANYmT,IChCAG,YAAY/Y,GAAOgZ,IAAI;WACzB,CAAC;EAAC1e;AAAK,MAAMA,MAAMS,OAAOF,MAAMoe,MAAMC,SAASje,QAAQS,EAAE;;ACa7D,SAAAyd,kBAAA9gB,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,CAAA,GAA2B;IAAA0Q;EAAAA,IAAAtQ,IAChC;IAAA2E;EAAAA,IAAYuL,eAAAC,qBAAoC;AAAC,MAE3C,EAAA,aAAaG;AAAK,WAAA;AAIxB,QAAAyQ,UAAA;IAAA,GAAoBzQ,MAAKyQ;IAAA,GAAaC,mBAAmB1Q,MAAKyQ,OAAQ;EAAC;AAAC,MACnEA,CAAAA,QAAOE;AAAA,WAAA;AAMahhB,QAAAA,KAAG8gB,GAAAA,QAAOE,IAAA;EAAUC,SACvCH,QAAOI,QACPJ,QAAOK,SACT,CAAC;AAAE7gB,MAAAA;AAAAZ,IAAAA,CAAAA,MAAAM,MAHHM,SAAC,yBAAA,WAAA,EAAgB,MAAA,GAAIN,UAAAA,GAAAA,CAGjB,GAAYN,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA;AAEO,QAAAyC,KAAGuC,GAAAA,EAAE,kBAAkB,CAAC,OAAOoc,QAAOM,UAAA;EAAgB1c,EACzE,oBACF,CAAC,KAAKoc,QAAOI,MAAA;AAAS5e,MAAAA;AAAA5C,IAAAA,CAAAA,MAAAyC,MAHxBG,SAAC,yBAAA,KAAA,EAAe,WAAC,GACf,cAAC,yBAAA,WAAA,EAAgB,MAAC,GAAGH,UAAAA,GAAAA,CAEE,EACzB,CAAA,GAAMzC,EAAAA,CAAAA,IAAAyC,IAAAzC,EAAAA,CAAAA,IAAA4C,MAAAA,KAAA5C,EAAA,CAAA;AAAAoR,MAAAA;AAAApR,SAAAA,EAAAY,CAAAA,MAAAA,MAAAZ,EAAAA,CAAAA,MAAA4C,MATRwO,SAAA,0BAAA,OACExQ,EAAAA,UAAAA;IAAAA;IAIAgC;EAAAA,EAKF,CAAA,GAAM5C,EAAAA,CAAAA,IAAAY,IAAAZ,EAAAA,CAAAA,IAAA4C,IAAA5C,EAAAA,CAAAA,IAAAoR,MAAAA,KAAApR,EAAA,CAAA,GAVNoR;AAUM;AAIV,SAASiQ,mBAAmBD,SAA2C;AACjE,MAAA,CAACA,WAAW,OAAOA,QAAQvW,SAAU,YAAY,OAAOuW,QAAQO,SAAU;AAC5E,WAAO,CAAC;AAGJ,QAAA;IAAC9W;IAAO8W;IAAOC;EAAAA,IAAOR,SACtBS,YAAYhX,MAAMhL,MAAM,GAAG8hB,KAAK,EAAEG,YAAY;CAAI,IAAI,GACtDJ,cAAc7W,MAAMhL,MAAM,GAAGgiB,SAAS,EAAEE,MAAM,KAAK,KAAK,CAAA,GAAItY,QAC5D6X,QAAOzW,MAAMhL,MAAMgiB,WAAWhX,MAAMmX,QAAQ;GAAMH,SAAS,CAAC,GAC5DL,UAASG,QAAQE,WACjBJ,YAAY,OAAOG,OAAQ,WAAWA,MAAMC,YAAY3P;AAEvD,SAAA;IAACoP,MAAAA;IAAMI;IAAYF,QAAAA;IAAQC;EAAS;AAC7C;AAEA,SAASF,SAASC,SAAgBC,WAAuC;AACjEH,QAAAA,QAAO,IAAIW,OAAOT,OAAM,GACxBU,OAAO,IAAID,OAAOR,YAAYA,YAAYD,UAAS,CAAC;AACnD,SAAA,GAAGF,KAAI,GAAGY,IAAI;AACvB;AC1DO,SAAAC,iBAAApiB,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA;AAAAI,MAAAA;AAAAL,IAAAD,CAAAA,MAAAA,MAAA4Q,MAAAvC,WAGD/N,SAAC,yBAAA,WAAA,EAAgB,MAAA,GAAIN,UAAK4Q,MAAAA,MAAAvC,QAAe,CAAA,GAAYpO,EAAAD,CAAAA,IAAAA,MAAA4Q,MAAAvC,SAAApO,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAAM,MAAAA;AAAAN,IAAA,CAAA,MAAAD,MAAA4Q,SACrDrQ,SAAA,yBAAC,mBAAyB,EAAA,OAAAP,MAAK4Q,MAAAA,CAAU,GAAA3Q,EAAA,CAAA,IAAAD,MAAA4Q,OAAA3Q,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAY,MAAAA;AAAA,SAAAZ,EAAAK,CAAAA,MAAAA,MAAAL,EAAAA,CAAAA,MAAAM,MAF3CM,SAAC,0BAAA,OAAA,EAAa,OAAA,GAAc,WAAA,GAC1BP,UAAAA;IAAAA;IACAC;EAAAA,EACF,CAAA,GAAQN,EAAAA,CAAAA,IAAAK,IAAAL,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA,GAHRY;AAGQ;ACPCwhB,IAAAA,oBAAoBpa,GAAOC,IAAoB,CAAC;EAAC3F;AAAK,MAAM;AACjE,QAAA;IAACO;IAAOC;IAAOoF;EAAAA,IAAS5F,MAAMS;AAE7BuM,SAAAA;;;qBAGYxM,MAAMiB,KAAKC,MAAM;mBACnBlB,MAAMiB,KAAKI,MAAM,CAAC,EAAEF,QAAQ;qBAC1BnB,MAAMiB,KAAKI,MAAM,CAAC,EAAEC,UAAU;;;;;sBAK7BF,IAAIgE,MAAM,CAAC,CAAC,CAAC;;;;qBAIdhE,IAAIgE,MAAM,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAmCFhE,IAAIgE,MAAM,CAAC,CAAC,CAAC;mBAC1BpF,MAAMiB,KAAKI,MAAM,CAAC,EAAEF,QAAQ;qBAC1BnB,MAAMiB,KAAKI,MAAM,CAAC,EAAEC,UAAU;;;;;;;;mBAQhCtB,MAAMiB,KAAKI,MAAM,CAAC,EAAEF,QAAQ;qBAC1BnB,MAAMiB,KAAKI,MAAM,CAAC,EAAEC,UAAU;;;;;;;;;qBAS9BF,IAAIgE,MAAM,CAAC,IAAI,CAAC,CAAC;;;;;;;;;;;;;eAavBrF,MAAM+B,OAAOW,QAAQ;;;;;eAKrB1C,MAAM+B,OAAOe,QAAQ;;;;qBAIfzB,IAAIgE,MAAM,CAAC,CAAC,CAAC;;;;eAInBrF,MAAM+B,OAAOmC,MAAM;;;;;eAKnBlE,MAAM+B,OAAO+C,OAAO;;;;eAIpB9E,MAAM+B,OAAOsB,MAAM;;;;;;;;;;;;;;;;;;;;;AAqBlC,CAAC;AAhIYkc,ICMPC,MAAM,IAAIC,iBAAAA,QAAI;EAAChH,SAAS;AAAK,CAAC;AAE7B,SAAAiH,WAAAxiB,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GACL;IAAAwT;IAAA+O;EAAAA,IAA4BziB,OAC5B0iB,mBAAyBC,WAAW;AAAC,MAEjCC,SAASlP,IAAI,KAAKxF,MAAAC,QAAcuF,IAAI,GAAC;AAQfpT,UAAAA,MAAAoiB,qBAAqBD,cAAWI,oBAAA1Q;AAAgC5R,QAAAA;AAAA,WAAAN,EAAAyT,CAAAA,MAAAA,QAAAzT,EAAAA,CAAAA,MAAAK,OANtFC,UAAC,yBAAA,mBAAA,EACC,cAAC,yBAAA,eAAA,EACOmT,MACE,QAAA,OACIoP,YACHC,SAAAA,gBACS,kBAAAziB,IAAAA,CAEtB,EAAA,CAAA,GAAoBL,EAAAA,CAAAA,IAAAyT,MAAAzT,EAAAA,CAAAA,IAAAK,KAAAL,EAAAA,CAAAA,IAAAM,OAAAA,MAAAN,EAAA,CAAA,GARpBM;EAAAA;AAQoBD,MAAAA;AAAAL,IAAAA,CAAAA,MAAAyT,QAIMpT,KAAAiL,KAAAC,UAAekI,IAAI,GAACzT,EAAAA,CAAAA,IAAAyT,MAAAzT,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAAM,MAAAA;AAAAN,SAAAA,EAAAA,CAAAA,MAAAK,MAA3CC,SAAC,yBAAA,MAAA,EAAc,UAAA,QAAQD,UAAqB,GAAA,CAAA,GAAOL,EAAAA,CAAAA,IAAAK,IAAAL,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA,GAAnDM;AAAmD;AAG5D,SAAAsiB,kBAAA7iB,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA;AAAA,MACMF,MAAKgjB,SAAW,CAAChjB,MAAKijB,QAAAC,SAAkB,KAAK,KAAA,CAAMljB,MAAKijB,QAAAC,SAAkB,MAAM;AAAE,WAAA;AAAA5iB,MAAAA;AAAAL,IAAA,CAAA,MAAAD,MAAAoL,SAKlD9K,KAAA;IAAA6iB,IAAKnjB,MAAKoL;EAAOnL,GAAAA,EAAA,CAAA,IAAAD,MAAAoL,OAAAnL,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAAM,MAAAA;AAAAN,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KACjDtI,SAAC,yBAAA,UAAA,CAAW,CAAA,GAAAN,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAY,MAAAA;AAAAZ,SAAAA,EAAAA,CAAAA,MAAAK,MADdO,SAAAA,yBAAC,YAAkB,EAAA,QAAA,QAAe,QAAAP,IAChCC,UAAAA,GACF,CAAA,GAAaN,EAAAA,CAAAA,IAAAK,IAAAL,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA,GAFbY;AAEa;AAIjB,SAASiiB,WAAWM,SAAiBhY,OAAyB;AAEtDiY,QAAAA,SAASf,IAAI9V,IAAI4W,OAAO;AAE9B,MAAI,OAAOC,UAAW;AACbA,WAAAA;AAGT,QAAMC,WAAWF,QAAQxjB,MAAM,KAAK2jB,CAAU;AAC9C,SAAID,SAAS5Z,WAAW6Z,IACf,QAGLrV,MAAMC,QAAQ/C,KAAK,IACd,OAGFwX,SAASxX,KAAK,KAAK,CAACkY,SAAS7M,KAAM1U,CAAQyhB,SAAAA,oBAAoBzhB,IAAG,CAAC;AAC5E;AAEA,SAASghB,eAAe5R,OAA6B;AAC7C,QAAA;IAACsS;EAAQtS,IAAAA,OACTnI,UAAUsZ,IAAI9V,IAAIiX,IAAI;AAExBza,cAAYmJ,UAKhBmQ,IAAInX,IAAIsY,MAAM,CAACza,OAAO;AACxB;AAEA,SAAS4Z,SAASxX,OAAkD;AAC3DA,SAAAA,UAAU,QAAQ,OAAOA,SAAU,YAAY,CAAC8C,MAAMC,QAAQ/C,KAAK;AAC5E;AAEA,IAAMsY,UAAU;AAChB,SAASF,oBAAoBG,SAAiBC,QAAQ,IAAI;AACxD,SAAOF,QAAQ9V,KAAK+V,OAAO,KAAKE,SAASF,SAAS,EAAE,IAAIC;AAC1D;ACxEA,SAASE,YAAYC,KAAoC;AACvD,SAAOA,IAAIC,eAAe;AAC5B;AAEO,SAAAC,cAAA3jB,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,CAAA,GAAuB;IAAAgkB;EAAAA,IAAA5jB,IAC5B;IAAA2E;EAAYuL,IAAAA,eAAAC,qBAAoC,GAChD0T,aAAAA,CAAoBD,SAMN3jB,KAAA4jB,aAAUhS,SAAe,oBAI1BtR,KAAAsjB,aAAUL,cAAA3R;AAA0BzP,MAAAA;AAAAzC,IAAA,CAAA,MAAAikB,WAAAjkB,EAAAkkB,CAAAA,MAAAA,cAAAlkB,EAAAM,CAAAA,MAAAA,MAAAN,EAAAA,CAAAA,MAAAY,MAP/C6B,SAAA,yBAAC,QACI,EAAA,IAAA,KACOyhB,UAAAA,YACA,UAAA5jB,IACJ2jB,MAAM,SACNE,MAAAA,mBACD,MAAA,SACI,SAAAvjB,IAEJ,MAAA,OACA,MAAA,UAAA,CACL,GAAAZ,EAAAA,CAAAA,IAAAikB,SAAAjkB,EAAAA,CAAAA,IAAAkkB,YAAAlkB,EAAAA,CAAAA,IAAAM,IAAAN,EAAAA,CAAAA,IAAAY,IAAAZ,EAAAA,CAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,CAAA;AAZJ,QAAAokB,SACE3hB;AAYDG,MAAAA;AAAA5C,SAAAA,EAAAokB,CAAAA,MAAAA,UAAApkB,EAAAA,CAAAA,MAAAkkB,cAAAlkB,EAAA,CAAA,MAAAgF,KAEMpC,KAAAshB,iBACJ,yBAAA,SAAA,EAAiB,SAAAlf,EAAE,6CAA6C,GAAa,WAAA,OAC3Eof,UAAAA,OACH,CAAA,IAEAA,QACDpkB,EAAAA,CAAAA,IAAAokB,QAAApkB,EAAAA,CAAAA,IAAAkkB,YAAAlkB,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAA4C,MAAAA,KAAA5C,EAAA,CAAA,GANM4C;AAMN;AAGI,SAAAyhB,eAAAhkB,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,CAAA,GAAwB;IAAAgkB;EAAAA,IAAA5jB;AAA0BC,MAAAA;AAAA,SAAAN,EAAAA,CAAAA,MAAAikB,WAErD3jB,SAAC,yBAAA,QAAA,EACI,IAAA,KACO,UAAA,qBACJ2jB,MAAAA,SACAE,MAAgBA,mBACjB,MAAA,SAEA,MAAA,QACA,MAAA,UACL,CAAA,GAAAnkB,EAAAA,CAAAA,IAAAikB,SAAAjkB,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA,GATFM;AASE;ACnBC,SAAAgkB,gBAAAjkB,IAAA;AAAAL,QAAAA,QAAAC,iCAAA,EAAA,GAAyB;IAAA0Q;IAAA6L;IAAA+H;IAAAhI;IAAAiI;IAAAtG;IAAAuG;IAAAC;EAAAA,IAAArkB,IAU9B;IAAA2E;EAAAA,IAAYuL,eAAAC,qBAAoC,GAChDmU,YAAkB,CAAChU,SAAK,CAAK6L,mBAAmB,OAAO+H,cAAgB;AAAWjkB,MAAAA;AAAAN,IAAA2kB,CAAAA,MAAAA,aAAA3kB,EAAAA,CAAAA,MAAAukB,eAElEjkB,KAAAqkB,YAAY/D,eAAe2D,WAAW,IAAI,IAAEvkB,EAAAA,CAAAA,IAAA2kB,WAAA3kB,EAAAA,CAAAA,IAAAukB,aAAAvkB,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAA5D,QAAA4kB,UAAgBtkB;AAA4CM,MAAAA;AAAAZ,IAAA2kB,CAAAA,MAAAA,aAAA3kB,EAAAA,CAAAA,MAAAukB,eAC7C3jB,KAAA+jB,YAAY9D,cAAc0D,WAAW,IAAI,IAAEvkB,EAAAA,CAAAA,IAAA2kB,WAAA3kB,EAAAA,CAAAA,IAAAukB,aAAAvkB,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA;AAA1D,QAAA6kB,SAAejkB,IAQD6B,KAAAkO,QAAQ,aAAa,WACf/N,KAAAR,CAAQuO,CAAAA;AAAMS,MAAAA;AAAApR,IAAAA,CAAAA,MAAAgF,KAKAoM,KAAApM,EAAE,cAAc,GAAChF,EAAAA,CAAAA,IAAAgF,GAAAhF,EAAAA,CAAAA,IAAAoR,MAAAA,KAAApR,EAAA,CAAA;AAAAuR,MAAAA;AAAAvR,IAAAA,CAAAA,MAAAoR,MAFzCG,SAAC,yBAAA,0BACC,EAAA,cAAA,yBAAC,KAAgB,EAAA,YAAC,GAChB,cAAA,yBAAC,aAAY,EAAA,OAAA,MAAOH,UAAAA,GAAAA,CAAkB,EACxC,CAAA,EAAA,CACF,GAA2BpR,EAAAA,CAAAA,IAAAoR,IAAApR,EAAAA,CAAAA,IAAAuR,MAAAA,KAAAvR,EAAA,CAAA;AAAAwR,MAAAA;AAAAxR,IAAAuc,EAAAA,MAAAA,oBAAAvc,EAAAA,EAAAA,MAAAwkB,mBAAAxkB,EAAA,EAAA,MAAAwc,mBAExBhL,MAACgL,mBAAoBD,oBAAoBiI,gBAAe/a,WAAA,UACvD,yBAAC,KAAA,EAAe,WAAC,GACf,cAAA,yBAAC,gBAAc,CAAA,CAAA,EAAA,CACjB,GACDzJ,EAAAA,EAAAA,IAAAuc,kBAAAvc,EAAAA,EAAAA,IAAAwkB,iBAAAxkB,EAAAA,EAAAA,IAAAwc,iBAAAxc,EAAAA,EAAAA,IAAAwR,MAAAA,KAAAxR,EAAA,EAAA;AAAAyR,MAAAA;AAAAzR,IAAAA,EAAAA,MAAA2Q,SACAc,KAAAd,aAAS,yBAAC,kBAAA,EAAwBA,MAAS,CAAA,GAAA3Q,EAAAA,EAAAA,IAAA2Q,OAAA3Q,EAAAA,EAAAA,IAAAyR,MAAAA,KAAAzR,EAAA,EAAA;AAAA0R,MAAAA;AAAA1R,IAAAke,EAAAA,MAAAA,WAAAle,EAAAA,EAAAA,MAAA2kB,aAAA3kB,EAAA,EAAA,MAAAukB,eAC3C7S,KAAAiT,iBAAc,yBAAA,YAAA,EAAiBJ,MAAAA,aAA0BrG,aAAAA,QAAW,CAAA,GAAAle,EAAAA,EAAAA,IAAAke,SAAAle,EAAAA,EAAAA,IAAA2kB,WAAA3kB,EAAAA,EAAAA,IAAAukB,aAAAvkB,EAAAA,EAAAA,IAAA0R,MAAAA,KAAA1R,EAAA,EAAA;AAAA2R,MAAAA;AAAA3R,IAAAke,EAAAA,MAAAA,WAAAle,EAAAA,EAAAA,MAAAuc,oBAAAvc,EAAA,EAAA,MAAAwkB,mBACpE7S,MAAA4K,oBAAoBiI,gBAAe/a,SAAA,SAClC,yBAAC,YAAA,EAAiB+a,MAAAA,iBAA8BtG,aAAAA,QAAAA,CACjD,GAAAle,EAAAA,EAAAA,IAAAke,SAAAle,EAAAA,EAAAA,IAAAuc,kBAAAvc,EAAAA,EAAAA,IAAAwkB,iBAAAxkB,EAAAA,EAAAA,IAAA2R,OAAAA,MAAA3R,EAAA,EAAA;AAAA4R,MAAAA;AAAA5R,IAAA,EAAA,MAAA2R,OAAA3R,EAAAwR,EAAAA,MAAAA,MAAAxR,EAAAyR,EAAAA,MAAAA,MAAAzR,EAAAA,EAAAA,MAAA0R,MAVHE,UAAAA,0BAAC,KAAa,EAAA,SAAA,GAAe,YAAA,GAC1BJ,UAAAA;IAAAA;IAKAC;IACAC;IACAC;EAAAA,EAAAA,CAGH,GAAM3R,EAAAA,EAAAA,IAAA2R,KAAA3R,EAAAA,EAAAA,IAAAwR,IAAAxR,EAAAA,EAAAA,IAAAyR,IAAAzR,EAAAA,EAAAA,IAAA0R,IAAA1R,EAAAA,EAAAA,IAAA4R,OAAAA,MAAA5R,EAAA,EAAA;AAAA6R,MAAAA;AAAA7R,IAAA4R,EAAAA,MAAAA,OAAA5R,EAAAA,EAAAA,MAAAuR,MAjBRM,UAAC,0BAAA,QAAgB,EAAA,UAAA,QACfN,UAAAA;IAAAA;IAKAK;EAAAA,EAYF,CAAA,GAAS5R,EAAAA,EAAAA,IAAA4R,KAAA5R,EAAAA,EAAAA,IAAAuR,IAAAvR,EAAAA,EAAAA,IAAA6R,OAAAA,MAAA7R,EAAA,EAAA;AAAA8R,MAAAA;AAAA9R,IAAA6R,EAAAA,MAAAA,OAAA7R,EAAAA,EAAAA,MAAAyC,MAAAzC,EAAA,EAAA,MAAA4C,MAzBbkP,UAAAA,yBAAC,sBAAA,EAA2B,MAAA,GAC1B,cAAA,yBAAC,iBACO,EAAA,MAAA,GACG,UAAA,UACH,MAAArP,IACM,YAAAG,IAEZiP,UAAAA,IAmBF,CAAA,EAAA,CACF,GAAuB7R,EAAAA,EAAAA,IAAA6R,KAAA7R,EAAAA,EAAAA,IAAAyC,IAAAzC,EAAAA,EAAAA,IAAA4C,IAAA5C,EAAAA,EAAAA,IAAA8R,OAAAA,MAAA9R,EAAA,EAAA;AAAA0Y,MAAAA;AAAA1Y,IAAA,EAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAE0B8P,MAAC,CAAA,UAAU,UAAU,KAAK,GAAC1Y,EAAAA,EAAAA,IAAA0Y,OAAAA,MAAA1Y,EAAA,EAAA;AAAA2Y,MAAAA;AAAA3Y,IAAAA,EAAAA,MAAAgF,KAKjE2T,MAAA3T,EAAE,6BAA6B,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA2Y,OAAAA,MAAA3Y,EAAA,EAAA;AAAA4Y,MAAAA;AAAA5Y,IAAAykB,EAAAA,MAAAA,aAAAzkB,EAAAA,EAAAA,MAAAgF,KAChC4T,MAAO6L,OAAAA,aAAc,WAClB,GAAGA,SAAS,OACZzf,EAAE,8BAA8B,GAAChF,EAAAA,EAAAA,IAAAykB,WAAAzkB,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAA4Y,OAAAA,MAAA5Y,EAAA,EAAA;AAAA8Y,MAAAA;AAAA9Y,IAAA2Y,EAAAA,MAAAA,OAAA3Y,EAAAA,EAAAA,MAAA4Y,OALzCE,UAAC,yBAAA,KAAA,EACC,cAAC,0BAAA,MAAA,EAAK,OAAI,MACPH,UAAAA;IAAAA;IAAiC;IAAI;IACrCC;EAAAA,EAGH,CAAA,EACF,CAAA,GAAM5Y,EAAAA,EAAAA,IAAA2Y,KAAA3Y,EAAAA,EAAAA,IAAA4Y,KAAA5Y,EAAAA,EAAAA,IAAA8Y,OAAAA,MAAA9Y,EAAA,EAAA;AAAAmf,MAAAA;AAAAnf,IAAAA,EAAAA,MAAAgF,KAGDma,MAAAna,EAAE,8BAA8B,GAAChF,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAmf,OAAAA,MAAAnf,EAAA,EAAA;AAAAof,MAAAA;AAAApf,IAAA0kB,EAAAA,MAAAA,WAAA1kB,EAAAA,EAAAA,MAAAgF,KACjCoa,MAAOsF,OAAAA,WAAY,WAAW,GAAGA,OAAO,OAAO1f,EAAE,8BAA8B,GAAChF,EAAAA,EAAAA,IAAA0kB,SAAA1kB,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAof,OAAAA,MAAApf,EAAA,EAAA;AAAAqf,MAAAA;AAAArf,IAAAmf,EAAAA,MAAAA,OAAAnf,EAAAA,EAAAA,MAAAof,OAHrFC,UAAC,yBAAA,KAAA,EAAgB,YAAA,GACf,cAAC,0BAAA,MAAA,EAAK,OAAI,MACPF,UAAAA;IAAAA;IAAkC;IAAE;IACpCC;EAAAA,EACH,CAAA,EACF,CAAA,GAAMpf,EAAAA,EAAAA,IAAAmf,KAAAnf,EAAAA,EAAAA,IAAAof,KAAApf,EAAAA,EAAAA,IAAAqf,OAAAA,MAAArf,EAAA,EAAA;AAAAsf,MAAAA;AAAAtf,IAAA8Y,EAAAA,MAAAA,OAAA9Y,EAAAA,EAAAA,MAAAqf,OAfVC,UAAC,yBAAA,aAAA,EAAsB,UAAC,GAAY,UAAC,GAAS,QAAA,UAC5C,cAAC,0BAAA,sBAAA,EAA2B,OAAA,UAC1BxG,UAAAA;IAAAA;IAQAuG;EAAAA,EAMF,CAAA,EACF,CAAA,GAAcrf,EAAAA,EAAAA,IAAA8Y,KAAA9Y,EAAAA,EAAAA,IAAAqf,KAAArf,EAAAA,EAAAA,IAAAsf,OAAAA,MAAAtf,EAAA,EAAA;AAAAuf,MAAAA;AAAAvf,IAAA,EAAA,MAAA6kB,UAAA7kB,EAAA2kB,EAAAA,MAAAA,aAAA3kB,EAAA4kB,EAAAA,MAAAA,WAAA5kB,EAAAA,EAAAA,MAAAgF,KAEbua,MAAAoF,iBACE,yBAAA,eAAA,EAAwB,UAAC,GAAY,UAAA,GAAU,QAAA,UAC9C,cAAA,yBAAC,iBAAgB,EAAA,OAAI,MACnB,cAAA,yBAAC,WAAA,EACa,YAAA;IAAAG,mBAAAA,UAGN,0BAAAC,oBAAAA,UAAA,EAAA,UAAA;UAAC,yBAAA,gBAAA,EAAwBH,SAAAA,QAAO,CAAA;UAChC,yBAAC,eAAuBC,EAAAA,SAAAA,OAAU,CAAA;IAAA,EAAA,CAAA;EAIhC,GAAA,SAAA,gCACL7f,EAAAA,CAAAA,EAEP,CAAA,EACF,CAAA,GACDhF,EAAAA,EAAAA,IAAA6kB,QAAA7kB,EAAAA,EAAAA,IAAA2kB,WAAA3kB,EAAAA,EAAAA,IAAA4kB,SAAA5kB,EAAAA,EAAAA,IAAAgF,GAAAhF,EAAAA,EAAAA,IAAAuf,OAAAA,MAAAvf,EAAA,EAAA;AAAAyf,MAAAA;AAAAzf,IAAAsf,EAAAA,MAAAA,OAAAtf,EAAAA,EAAAA,MAAAuf,OArCHE,UAAC,0BAAA,cAAA,EAAqB,SAAA,iBAA2B,WAAA/G,KAC/C4G,UAAAA;IAAAA;IAmBCC;EAAAA,EAkBH,CAAA,GAAevf,EAAAA,EAAAA,IAAAsf,KAAAtf,EAAAA,EAAAA,IAAAuf,KAAAvf,EAAAA,EAAAA,IAAAyf,OAAAA,MAAAzf,EAAA,EAAA;AAAA0f,MAAAA;AAAA,SAAA1f,EAAA8R,EAAAA,MAAAA,OAAA9R,EAAAA,EAAAA,MAAAyf,OApEjBC,UAAC,0BAAA,sBAAA,EAA+B,WAAA,UAAqB,eAAA,iBACnD5N,UAAAA;IAAAA;IA6BA2N;EAAAA,EAuCF,CAAA,GAAuBzf,EAAAA,EAAAA,IAAA8R,KAAA9R,EAAAA,EAAAA,IAAAyf,KAAAzf,EAAAA,EAAAA,IAAA0f,OAAAA,MAAA1f,EAAA,EAAA,GArEvB0f;AAqEuB;AC7E3B,SAASsF,aAAaC,MAAYC,OAA2C;AAC3E,SAAI,CAACD,QAAQ,CAACC,QACL,QAIFD,SAASC,SAAS,CAAC,EAAED,KAAKE,wBAAwBD,KAAa,IAAI;AAC5E;AAEA,IAAME,YACJ;AADF,IAGMC,cAAenU,CACnBoU,UAAAA,SAAS,cAAcpU,KAAK,KAAKoU,SAAS,aAAapU,KAAK;AAqCvD,SAASqU,UAAUxlB,OAAuB;AACzC,QAAA;IAACse;IAAUmH;IAAQC;IAAWC;EAAAA,IAAkB3lB,OAChDoV,QAAQC,SAAAA,GACR;IAACpQ;EAAAA,IAAKuL,eAAeC,qBAAqB,GAC1C;IAAC7F;EAAAA,IAAoBgU,eAAe,GAEpCgH,oBAAoBxY,iBAAiB,GAAGqY,OAAOG,iBAAiB,EAAE,GAClEC,qBAAiBld,sBAA+B,IAAI,GACpDmd,sBAAkBnd,sBAA+B,IAAI,GACrD8S,oBAAgB9S,sBAA8B,IAAI,GAClD4V,iCAA6B5V,sBAAgC,IAAI,GACjEod,2BAAuBpd,sBAAiCwJ,MAAS,GACjE6T,4BAAwBrd,sBAAiCwJ,MAAS,GAElE,CAACjG,aAAY,QAAI7L,wBAAS,MAAMgM,gBAAgBqZ,aAAa,SAAS,CAAC,GAEvE;IAACO;IAAeC;IAAkBC;IAAaC;IAAcC;EAAiB,QAClFjT,uBAAQ,OACC;IACL6S,eAAe/Z,cAAaM,IAAI,WAAWmZ,cAAc;IACzDO,kBAAkBha,cAAaM,IAAI,cAAcoZ,iBAAiB;IAClEO,aAAaja,cAAaM,IAAI,SAAS,EAAE;IACzC4Z,cAAcla,cAAaM,IAAI,UAAU;;EAAU;IACnD6Z,mBAAmBna,cAAaM,IAC9B,eACA2F,MACF;EAED,IAAA,CAACwT,gBAAgBC,mBAAmB1Z,aAAY,CAAC,GAEhD,CAACiS,SAASmI,UAAU,QAAIjmB,wBAAiB,MACzCie,SAASjU,SAAS4b,aAAa,IAC1BA,gBAEL3H,SAASjU,SAASsb,cAAc,IAC3BA,iBAEFrH,SAAS,CAAC,CAClB,GACK,CAAC/Q,YAAYgZ,aAAa,QAAIlmB,wBAAiB,MACnDZ,aAAa4K,SAAS6b,gBAAgB,IAAIA,mBAAmBrmB,mBAC/D,GACM,CAACue,kBAAkBoI,mBAAmB,QAAInmB,wBAAyB,MACvEZ,aAAa4K,SAAS6b,gBAAgB,IAAI,QAAQA,gBACpD,GACM,CAACvH,aAAa8H,mBAAmB,QAAIpmB,wBACzCgmB,qBAAqB,KACvB,GACM5H,oBAAoBL,mBAAmB9Q,mBAAmB8Q,gBAAgB,IAAI,MAE9E,CAACzX,KAAK+f,MAAM,QAAIrmB,wBAA6B8R,MAAS,GACtD,CAACrH,OAAO6b,QAAQ,QAAItmB,wBAAiB,MACzC,OAAO8lB,eAAgB,WAAWA,cAAc,EAClD,GACM,CAACpb,QAAQ6b,SAAS,QAAIvmB,wBAAiB,MAAMqQ,YAAY0V,cAAcnhB,CAAC,CAAC,GACzE,CAACuf,aAAaqC,cAAc,QAAIxmB,wBAA8B8R,MAAS,GACvE,CAACsS,iBAAiBqC,kBAAkB,QAAIzmB,wBAA0B,CAAE,CAAA,GACpE,CAACuQ,OAAOuC,QAAQ,QAAI9S,wBAA4B8R,MAAS,GACzD,CAACuS,WAAWqC,YAAY,QAAI1mB,wBAA6B8R,MAAS,GAClE,CAACwS,SAASqC,UAAU,QAAI3mB,wBAA6B8R,MAAS,GAC9D,CAACsK,iBAAiBwK,kBAAkB,QAAI5mB,wBAAkB,KAAK,GAC/D,CAACmc,kBAAkB0K,mBAAmB,QAAI7mB,wBAAkB,KAAK,GACjE,CAAC8mB,wBAAwBC,yBAAyB,QAAI/mB,wBAAS,KAAK,GAEpE;IAACwb;IAAiBH;EAAAA,IAAsBF,YAAY;IAACC;EAAAA,CAAc,GAGnE4L,UAAUC,UAAU;IACxB/Z,YAAYkR,qBAAqBL,mBAAmBA,mBAAmB7Q;EACxE,CAAA,GACKga,aAASnU,uBAAQ,MACdiU,QAAQG,WAAW;IACxBja,YAAYkR,qBAAqBL,mBAAmBA,mBAAmB7Q;IACvEoR,aAAajU,qBAAqB;MAACC,mBAAmBgU;MAAa/T;IAAAA,CAAiB;IACpFuT;IACAsJ,kBAAkB;EACnB,CAAA,GACA,CACD7c,kBACA+T,aACAP,kBACA7Q,YACA4Q,SACAkJ,SACA5I,iBAAiB,CAClB,GAEKiJ,8BAA0B3T,2BAAY,MAAM;AAC3CgS,yBAAqB/c,YAG1B+c,qBAAqB/c,QAAQ6K,YAAY,GACzCkS,qBAAqB/c,UAAUmJ;EAAAA,GAC9B,CAAA,CAAE,GAECwV,iCAA6B5T,2BAAY,MAAM;AAC9CiS,0BAAsBhd,YAG3Bgd,sBAAsBhd,QAAQ6K,YAAY,GAC1CmS,sBAAsBhd,UAAUmJ;EAAAA,GAC/B,CAAE,CAAA,GAECyV,2BAAuB7T,2BAC1B/I,CAAoC,YAAA;AACnC,QAAIyR,iBAAiB;AACnBiL,8BAAAA,GACAC,2BAAAA,GACAV,mBAAmB,KAAK;AACxB;IAAA;AAGF,UAAMY,UAGF;MACF/c,QAAOE,mCAASF,UAASA;MACzBqT,UAASnT,mCAASmT,YAAWA;MAC7BpT,QAAQ2F,YAAYnF,KAAKC,WAAUR,mCAASD,WAAUA,OAAOiD,QAAQ,MAAM,CAAC,GAAG/I,CAAC;MAChF0Z,aAAajU,qBAAqB;QAChCC,oBAAmBK,mCAAS2T,gBAAeA;QAC3C/T;MAAAA,CACD;MACD2C,aACEvC,mCAASuC,gBACR6Q,oBAAoBK,oBAAoBL,mBAAmB7Q;IAChE;AAEArB,QAAAA,cAAaf,IAAI,SAAS0c,QAAQ/c,KAAK,GACvCoB,cAAaf,IAAI,UAAU0c,QAAQ9c,OAAO4F,GAAG,GAE7CgX,2BAA2B,GAE3BV,mBAAmB,CAACY,QAAQ9c,OAAO6F,SAASvO,CAAQwlB,CAAAA,QAAQ/c,KAAM,GAClEoc,oBAAoB,KAAK,GACzBJ,mBAAmB,CAAE,CAAA,GACrB3T,SAAS0U,QAAQ9c,OAAO6F,QAAQ,IAAIqB,MAAM4V,QAAQ9c,OAAO6F,KAAK,IAAIuB,MAAS,GAC3E0U,eAAe1U,MAAS,GACxB4U,aAAa5U,MAAS,GACtB6U,WAAW7U,MAAS,GAEhB0V,QAAQ9c,OAAO6F;AACjB;AAGF,UAAMkX,eAAkD;MACtDnJ,aAAakJ,QAAQlJ,eAAe,CAAA;IAAA,GAGhCoJ,YAAYR,OAAOC,WAAW;MAClCja,YAAYsa,QAAQta;MACpB4Q,SAAS0J,QAAQ1J;MACjBQ,aAAakJ,QAAQlJ;IAAAA,CACtB,GAEKnI,SAASuR,UAAUC,OACvBD,UAAUE,WACR,SACApd,kBAAkBgd,QAAQ/c,OAAO+c,QAAQ9c,OAAOiD,QAAQ8Z,YAAY,CACtE,CACF;AACApB,WAAOlQ,MAAM;AAEP0R,UAAAA,aAAaxoB,KAAKyoB,IAAI;AAEPnf,yBAAAA,UAAU+e,UAAUK,WACtCC,MAAMR,QAAQ/c,OAAO+c,QAAQ9c,OAAOiD,QAAQ;MAACsa,gBAAgB;MAAOtjB,KAAK;IAAS,CAAA,EAClF2O,UAAU;MACTC,MAAO2U,CAAQ,QAAA;AACbxB,qBAAawB,IAAIC,EAAE,GACnBxB,WAAWtnB,KAAKyoB,IAAAA,IAAQD,UAAU,GAClCrB,eAAe0B,IAAIE,MAAM,GACzBxB,mBAAmB,KAAK,GACxB9T,SAAShB,MAAS;MACpB;MACAvB,OAAQxC,CAAQ,QAAA;AACLA,iBAAAA,GAAG,GACZ6Y,mBAAmB,KAAK;MAAA;IAC1B,CACD;EAAA,GAEL,CACExK,iBACA3R,OACAqT,SACApT,OAAOiD,QACP/I,GACA0Z,aACA/T,kBACAwT,kBACAK,mBACAlR,YACArB,eACAyb,4BACAJ,QACAG,uBAAuB,CAE3B,GAEMgB,qBAAiB3U,2BACpB4U,CAA6C,mBAAA;AACxCA,uBAAmBxW,UAAa,CAAChI,uBAAuBwe,cAAc,MAI1ElC,oBAAoBkC,cAAsC,GAC1Dzc,cAAaf,IAAI,eAAewd,cAAc,GAE9Cf,qBAAqB;MAACjJ,aAAagK;IAAAA,CAAe;EAAA,GAEpD,CAACzc,eAAc0b,oBAAoB,CACrC,GAEMgB,0BAAsB7U,2BACzBgQ,CAAwC,QAAA;AACjC8E,UAAAA,aAAa9E,IAAI+E,OAAO1d;AAC9Bc,kBAAaf,IAAI,WAAW0d,UAAU,GACtCvC,WAAWuC,UAAU,GACrBjB,qBAAqB;MAACzJ,SAAS0K;IAAAA,CAAW;EAAA,GAE5C,CAAC3c,eAAc0b,oBAAoB,CACrC,GAEMmB,6BAAyBhV,2BAC5BgQ,CAAwC,UAAA;;AACjCiF,UAAAA,gBAAgBjF,MAAI+E,OAAO1d;AAC7B4d,SAAAA,+CAAexQ,mBAAkB,SAAS;AAC5CgO,0BAAoB,GAAG,IACvBjI,gCAA2BvV,YAA3BuV,mBAAoC0K;AACpC;IAAA;AAGYD,kBAAAA,aAAa,GAC3BxC,oBAAoB,KAAK,GACzBta,cAAaf,IAAI,cAAc6d,aAAa,GAC5CpB,qBAAqB;MAACra,YAAYyb;IAAAA,CAAc;EAAA,GAElD,CAAC9c,eAAc0b,oBAAoB,CACrC,GAGMsB,mCAA+BnV,2BAClCgQ,CAAuC,UAAA;AAChCoF,UAAAA,sBAAsBpF,MAAI+E,OAAO1d,SAAS;AAChDob,wBAAoB2C,uBAAuB,GAAG,GAE1C7b,mBAAmB6b,mBAAmB,MACxC5C,cAAc4C,mBAAmB,GACjCjd,cAAaf,IAAI,cAAcge,mBAAmB,GAClDvB,qBAAqB;MAACra,YAAY4b;IAAAA,CAAoB;EAAA,GAG1D,CAACjd,eAAc0b,oBAAoB,CACrC,GAGMwB,8BAA0BrV,2BAC7BgQ,CAAwC,UAAA;AACjC4E,UAAAA,mBAAiB5E,MAAI+E,OAAO1d;AACnBud,mBAAAA,qBAAmB,YAAYxW,SAAYwW,gBAAc;EAAA,GAE1E,CAACD,cAAc,CACjB,GAEMW,0BAAsBtV,2BAAagQ,CAA0B,UAAA;AAC7DA,UAAIzD,SAAS,cAIjBwG,mBAAoBwC,CAAAA,kBAClBA,cAAc5f,WAAW,KAAK,CAACqa,OAAK,GAAGuF,cAAcxpB,MAAM,GAAG,EAAE,CAAC,IAAI,CAACikB,OAAK,GAAGuF,aAAa,CAC7F;EAAA,GACC,CAAA,CAAE,GACCC,4BAAwBxV,2BAAY,MAAM;AAC9C,QAAIyI,kBAAkB;AACO,iCAAA,GAC3B0K,oBAAoB,KAAK;AACzB;IAAA;AAGF,UAAM1Q,WAAS+Q,OAAOU,WAAW,UAAUpd,kBAAkBC,OAAOC,OAAOiD,QAAQ,CAAE,CAAA,CAAC,GAEhFwb,gBAAgB,CAACze,OAAO6F,SAAS9F,MAAM4C,KAAAA,EAAOhE,SAAS;AAE7DwC,kBAAaf,IAAI,SAASL,KAAK,GAC/BoB,cAAaf,IAAI,UAAUJ,OAAO4F,GAAG,GAErC+W,wBAAAA,GAEAhB,OAAOlQ,QAAM,GACbsQ,mBAAmB,CAAA,CAAE,GACrBG,mBAAmB,KAAK,GACxBJ,eAAe1U,MAAS,GACxB+U,oBAAoBsC,aAAa,GACjCrW,SAASpI,OAAO6F,QAAQ,IAAIqB,MAAMlH,OAAO6F,KAAK,IAAIuB,MAAS,GAC3D4U,aAAa5U,MAAS,GACtB6U,WAAW7U,MAAS,GAEfqX,kBAILxD,sBAAsBhd,UAAUue,OAC7BkC,OAAO3e,OAAOC,OAAOiD,QAAQ;MAAC0b,QAAQ,CAAC,YAAY,SAAS;MAAGC,oBAAoB;IAAK,CAAA,EACxFhW,UAAU;MACTC,MAAMyV;MACNzY,OAAQxC,CAAQ,UAAA;AACLA,iBAAAA,KAAG,GACZ8Y,oBAAoB,KAAK;MAAA;IAC3B,CACD;EACF,GAAA,CACD1K,kBACAzR,QACAD,OACAoB,eACAwb,yBACA2B,qBACA1B,4BACAJ,MAAM,CACP,GAEKqC,yBAAqB7V,2BACxB3I,CAAkB,UAAA;AACjBwb,cAAUxb,KAAK,GACfc,cAAaf,IAAI,UAAUC,MAAMuF,GAAG;EAAA,GAEtC,CAACzE,aAAY,CACf,GAGM6I,sBAAkBhB,2BACrBL,CAAwC,SAAA;AACjCsO,UAAAA,QAAQtO,KAAKsO,MAAMqD,SAAS;AAClC,QAAI,CAACrD;AACI,aAAA;AAGT,UAAM,CAAG6H,EAAAA,gBAAgBC,aAAaC,QAAQ,IAAI/H,OAE5C7U,KAAK,IAAIjC,gBAAgB6e,QAAQ,GACjCC,QAA8B9c,oBAAoBC,EAAE;AACtD,QAAA,CAAC6c,MAAc,QAAA;AACnB,QAAIhB,iBACAG;AAEA7b,uBAAmBuc,cAAc,MAC/BpqB,aAAa4K,SAASwf,cAAc,KACtCb,kBAAgBa,gBAChBV,wBAAsB,SAEtBA,wBAAsBU;AAI1B,UAAMlB,mBACJxe,uBAAuB6f,MAAMhf,QAAQ2T,WAAW,KAChD,CAACrU,qBAAqB0f,MAAMhf,QAAQ2T,WAAW,IAC3CqL,MAAMhf,QAAQ2T,cACdxM;AAEN,WACEwW,qBACC,CAACxe,uBAAuB6f,MAAMhf,QAAQ2T,WAAW,KAChDrU,qBAAqB0f,MAAMhf,QAAQ2T,WAAW,MAEhDvJ,MAAM4B,KAAK;MACTC,UAAU;MACVkM,IAAI;MACJjM,QAAQ;MACRC,OAAO;IAAA,CACR,GAGI;MACLrM,OAAOkf,MAAMlf;MACbC,QAAQif,MAAMjf;MACdkf,WAAW1e,KAAKC,UAAUwe,MAAMjf,QAAQ,MAAM,CAAC;MAC/CoT,SAASG,SAASjU,SAASyf,WAAW,IAAIA,cAAc3L;MACxD5Q,YAAYyb,mBAAiBzb;MAC7B6Q,kBAAkB+K;MAClBxK,aAAagK,oBAAkBhK;MAC/BhY,KAAK+M;IACP;EACF,GACA,CAAC4K,UAAUH,SAAS5Q,YAAYoR,aAAavJ,KAAK,CACpD,GAGMJ,4BAAwBjB,2BAC3BmW,CAAiC,iBAAA;;AAEhC5D,eAAW4D,aAAa/L,OAAO,GAC/BwI,SAASuD,aAAapf,KAAK,GAC3B8b,UAAU;MACR5Y,QAAQkc,aAAanf;MACrB4F,KAAKuZ,aAAaD;MAClBpZ,OAAO;MACPD,OAAOuB;IACR,CAAA,GACDoU,cAAc2D,aAAa3c,UAAU,GACjC2c,aAAa9L,oBACfoI,oBAAoB0D,aAAa9L,gBAAgB,GAEnDqI,oBAAoByD,aAAavL,WAAW,GAC5C+H,OAAOwD,aAAavjB,GAAG,IAEvBkf,oBAAe7c,YAAf6c,mBAAwBhc,mBAAmBqgB,aAAapf,SACxDgb,qBAAgB9c,YAAhB8c,mBAAyBjc,mBAAmBqgB,aAAaD,YAGzD/d,cAAaO,MAAM;MACjB3B,OAAOof,aAAapf;MACpBC,QAAQmf,aAAaD;MACrB9L,SAAS+L,aAAa/L;MACtB5Q,YAAY2c,aAAa9L,oBAAoB8L,aAAa3c;MAC1DoR,aAAauL,aAAavL;IAAAA,CAC3B,GAGDiJ,qBAAqBsC,YAAY;EAAA,GAEnC,CAAChe,eAAc0b,oBAAoB,CACrC,GAEMuC,kBAAcpW,2BACjBgQ,CAAwB,UAAA;AACvB,QAAI,CAACA,MAAIqG;AACP;AAGF,UAAM1W,SAAOqQ,MAAIqG,cAAcC,QAAQ,YAAY;AACnDtG,UAAIC,eAAe;AACbsG,UAAAA,WAAWvV,gBAAgBrB,MAAI;AACjC4W,iBACFtV,sBAAsBsV,QAAQ,GAC9BlV,MAAM4B,KAAK;MACTC,UAAU;MACVkM,IAAI;MACJjM,QAAQ;MACRC,OAAO;IAAA,CACR;EAAA,GAGL,CAACpC,iBAAiBC,uBAAuBI,KAAK,CAChD,GAEMmV,oBAAgBxW,2BACnB5C,CAAyB,UAAA;AACxB,UAAMqZ,eACJ/O,cAAczS,WAAWic,aAAaxJ,cAAczS,SAASmI,MAAM2X,MAAM;AACvExD,gBAAYnU,KAAK,KAAKqZ,gBAAgBzf,OAAO8F,UAC/C+W,qBAAqB,GACrBzW,MAAM6S,eAAAA,GACN7S,MAAM8I,gBAAgB;EAG1B,GAAA,CAAClP,OAAO8F,OAAO+W,oBAAoB,CACrC;AAEAhnB,+BAAU,OACR6Z,OAAOI,SAAS4P,iBAAiB,SAASN,WAAW,GACrD1P,OAAOI,SAAS4P,iBAAiB,WAAWF,aAAa,GAElD,MAAM;AACJ1P,WAAAA,SAAS6P,oBAAoB,SAASP,WAAW,GACxD1P,OAAOI,SAAS6P,oBAAoB,WAAWH,aAAa;EAAA,IAE7D,CAACA,eAAeJ,WAAW,CAAC,OAE/BvpB,yBAAU,MACD,MAAM;AACX8mB,4BAAAA,GACAC,2BAA2B;EAAA,GAE5B,CAACD,yBAAyBC,0BAA0B,CAAC;AAElDgD,QAAAA,gCAAgCC,eAAgBC,CAAkCA,WAAA;AAClFA,IAAAA,OAAMnhB,SAAS,KACjBgf,eAAe,eAAe;EAAA,CAEjC;AAED9nB,+BAAU,MAAM;AACd+pB,kCAA8B/f,gBAAgB;EAAA,GAC7C,CAACA,gBAAgB,CAAC;AAErB,QAAMuK,kBAAcpB,2BAClB,CAAC+W,aAAqBC,gBAAyC;AAC7D,UAAMjD,iBAAkD;MACtDnJ,aAAajU,qBAAqB;QAACC,mBAAmBgU;QAAa/T;MAAAA,CAAiB,KAAK,CAAA;IAC3F;AACO2c,WAAAA,OAAOS,OACZT,OAAOU,WAAW,SAASpd,kBAAkBigB,aAAaC,aAAajD,cAAY,CAAC,CACtF;EAEF,GAAA,CAACP,QAAQ5I,aAAa/T,gBAAgB,CACxC;AAEA,aACG,0BAAA,MAAA,EACC,WAAU,UACV,QAAO,QACP,KAAK6Q,eACL,QAAO,UACP,UAAS,UACT,eAAY,eAEZ,UAAA;QAAA,yBAAC,iBAAA,EACC,YACA,kBACA,SACA,UACA,iBAAiBmN,qBACjB,oBAAoBG,wBACpB,4BACA,0BAA0BG,8BAC1B,mBACA,qBAAqBE,yBACrB,KACA,YAAA,CAAyB;QAG3B,yBAAC,oBAAA,EAAmB,MAAK,QACvB,cAAA,0BAAC,WACC,EAAA,SAAS,KACT,aAAa3O,OAAOC,aAAa,KACjC,MAAMyM,yBAAyB1M,OAAOC,aAAaD,OAAOC,aAAa,KACvE,SAAS,MACT,SAAQ,SAER,UAAA;UAAA,yBAAC,KAAI,EAAA,QAAO,WAAU,MAAM,GAC1B,cAAA;QAAC;QAAA;UACC,WAAU;UAEV,OAAOgB,qBAAqB,aAAa;UACzC,SAAS;UAET,UAAA;gBAAA,yBAAC,KAAI,EAAA,QAAO,WAAU,MAAM,GAC1B,cAAC,0BAAA,WAAA,EACC,WAAU,gBACV,OAAM,cACN,aACEA,qBAAqBG,gBAAgBb,cAAca,gBAAgBV,SAErE,MAAMU,gBAAgBZ,MACtB,aAAaY,gBAAgBX,aAC7B,SAASQ,qBAAqBG,gBAAgBV,UAAU,KACxD,SAASU,gBAAgBN,SACzB,SAAQ,SAER,UAAA;kBAAC,yBAAA,gBAAA,EAAe,SAAQ,QAAO,eAAY,uBACzC,cAAC,0BAAA,KAAA,EAAI,MAAM,GACT,UAAA;oBAAC,yBAAA,8BAAA,EACC,cAAC,yBAAA,MAAA,EACC,cAAC,yBAAA,aAAA,EAAY,OAAK,MAAEtW,UAAE,EAAA,aAAa,EAAE,CAAA,EACvC,CAAA,EAAA,CACF;oBAAA,yBACC,kBACC,EAAA,cAAc6F,OACd,UAAU6b,UACV,KAAKd,eAAe,CAAA;cAAA,EAAA,CAExB,EACF,CAAA;kBACA,0BAAC,gBAAe,EAAA,SAAQ,QACtB,UAAA;oBAAA,yBAAC,cACC,EAAA,OAAO9a,OAAO4F,KACd,UAAUiZ,oBACV,aAAa7e,OAAO6F,OACpB,gBAAgB7F,OAAO8F,OACvB,WAAWiV,gBAAAA,CAAgB;oBAG7B,yBAAC,mBACC,EAAA,gBAAgB/a,OAAO8F,OACvB,iBACA,kBACA,kBAAkB+W,sBAClB,mBAAmB2B,sBAAsB,CAAA;cAAA,EAE7C,CAAA;YAAA,EAAA,CACF,EACF,CAAA;gBACA,yBAAC,iBAAA,EACC,OACA,iBACA,aACA,kBACA,iBACA,SACA,WACA,QAAiB,CAAA;UAAA;QAAA;MAAA,EAAA,CAGvB;UACA,0BAAC,KAAA,EAAI,OAAO;QAAChQ,UAAU;QAAYI,QAAQ;MACzC,GAAA,UAAA;YAAA,yBAAC,QACC,EAAA,MAAK,SACL,SAAS,GACT,OAAO;UACLJ,UAAU;UACVyR,MAAM;UACNC,KAAK;UACLC,WAAW;UACXC,QAAQ;UACRC,eAAe;QAAA,GAEjB,SAAS,MAAMhE,0BAA0B,CAACD,sBAAsB,GAEhE,cAAC,yBAAA,OAAA,EAAI,OAAO;UAACkE,SAAS;UAAQC,YAAY;UAAU3R,QAAQ;QAAM,GAC/DwN,UAAAA,6BAA0B,yBAAA,iBAAA,CAAA,CAAkB,QAAI,yBAAA,kBAAA,CAAA,CAAmB,EAAA,CACtE,EACF,CAAA;YACC,yBAAA,aAAA,EACC,KACA,iBACA,uBACA,cAAcrc,OACd,eAAeC,OAAOiD,UAAU,CAAA,GAChC,YAAyB,CAAA;MAAA,EAE7B,CAAA;IAAA,EAAA,CACF,EACF,CAAA;EAAA,EAAA,CACF;AAEJ;AC9sBO,SAAAud,YAAAhE,QAAA;AAAAtnB,QAAAA,QAAAC,iCAAA,CAAA;AAAAI,MAAAA;AAAAL,IAAAA,CAAAA,MAAAsnB,UACajnB,KAAAinB,OAAM9B,OAAAA,GAASxlB,EAAAA,CAAAA,IAAAsnB,QAAAtnB,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAjC,QAAAylB,YAAkBplB,GAAeolB,WACjC,CAAApH,UAAAkN,WAAA,QAAgCnrB,wBAAuC;AAACE,MAAAA;AAAAN,IAAAsnB,CAAAA,MAAAA,OAAAa,WAAA9J,YAE9D/d,KAAAA,MAAA;AACR,UAAAkrB,YAAkBlE,OAAMa,WAAA9J,SAAAoN,KAAAA,EAA2B/X,UAAA;MAAAC,MAAA6U,CAC/B+C,WAAAA,YAAY/C,OAAMhV,IAAAmI,KAAoB,CAAC;MAAChL,OAAAxC,CAC1Cod,QAAAA,YAAYpd,GAAG;IAAA,CAChC;AAAC,WAAA,MAEWqd,UAAS5X,YAAa;EACpC5T,GAAAA,EAAAsnB,CAAAA,IAAAA,OAAAa,WAAA9J,UAAAre,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAAY,MAAAA;AAAA,SAAAZ,EAAAsnB,CAAAA,MAAAA,UAAAtnB,EAAAA,CAAAA,MAAAylB,aAAE7kB,KAAA,CAAC0mB,QAAQ7B,SAAS,GAACzlB,EAAAA,CAAAA,IAAAsnB,QAAAtnB,EAAAA,CAAAA,IAAAylB,WAAAzlB,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA,OAPtBW,yBAAUL,IAOPM,EAAmB,GAEfyd,YAAQnM;AAAa;AAbvB,SAAAyJ,MAAAmE,IAAA;AAAA,SAMgDA,GAAE3a;AAAA;ACFlD,SAAAumB,gBAAA3rB,OAAA;AAAA,QAAAC,QAAAC,iCAAA,EAAA,GACL0rB,iBAAuBL,YAAYvrB,MAAKunB,MAAO;AAAC,MAAA,CAE3CqE,gBAAc;AAAAtrB,QAAAA;AAAAL,WAAAA,EAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAEfvI,UAAA,yBAAC,MAAW,EAAA,OAAA,UAAgB,QAAA,QAAe,SAAA,UACzC,cAAA,yBAAC,gBACH,CAAA,CAAA,EAAA,CAAA,GAAOL,EAAAA,CAAAA,IAAAK,OAAAA,MAAAL,EAAA,CAAA,GAFPK;EAAAA;AAEOA,MAAAA;AAAAL,IAAAA,CAAAA,MAAA2rB,kBAAA3rB,EAAAD,CAAAA,MAAAA,MAAAunB,UAKTjnB,KAAAsrB,0BAAc3Z,QAAiB,CAE1BjS,MAAKunB,OAAA9B,OAAAA,EAAgBtH,WAAY,YAAY,IAE9CyN,gBAAc3rB,EAAAA,CAAAA,IAAA2rB,gBAAA3rB,EAAA,CAAA,IAAAD,MAAAunB,QAAAtnB,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AALpB,QAAAqe,WACEhe;AAIkBC,MAAAA;AAAAN,IAAA,CAAA,MAAAD,MAAAunB,UAEFhnB,KAAAP,MAAKunB,OAAA9B,OAAAA,GAAgBxlB,EAAA,CAAA,IAAAD,MAAAunB,QAAAtnB,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAAvC,QAAAylB,YAAkBnlB,GAAqBmlB;AAAU7kB,MAAAA;AAAAZ,IAAAqe,CAAAA,MAAAA,YAAAre,EAAAA,CAAAA,MAAAD,MAAAunB,UAAAtnB,EAAAD,CAAAA,MAAAA,MAAAylB,OAAAE,kBAC1B9kB,KAAAb,MAAKylB,OAAAE,kBAA0B3lB,MAAKunB,OAAA9B,OAAAA,EAAgBtH,WAAYG,SAAW,CAAA,GAAAre,EAAAA,CAAAA,IAAAqe,UAAAre,EAAA,CAAA,IAAAD,MAAAunB,QAAAtnB,EAAAD,CAAAA,IAAAA,MAAAylB,OAAAE,gBAAA1lB,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA;AAAlG,QAAA0lB,iBAAuB9kB;AAA2E6B,MAAAA;AAAAzC,SAAAA,EAAA,EAAA,MAAAqe,YAAAre,EAAA0lB,EAAAA,MAAAA,kBAAA1lB,EAAAylB,EAAAA,MAAAA,aAAAzlB,EAAAA,EAAAA,MAAAD,SAGhG0C,SAAAA,yBAAC,WACe,EAAA,GACV1C,OACMse,UACCoH,WACKC,eAAAA,GAAAA,SAChB,GAAA1lB,EAAAA,EAAAA,IAAAqe,UAAAre,EAAAA,EAAAA,IAAA0lB,gBAAA1lB,EAAAA,EAAAA,IAAAylB,WAAAzlB,EAAAA,EAAAA,IAAAD,OAAAC,EAAAA,EAAAA,IAAAyC,MAAAA,KAAAzC,EAAA,EAAA,GANFyC;AAME;ACZC,IAAMmpB,sBAAN,cAAkCC,wBAGvC;EACAC,YAAY/rB,OAAiC;AACrCA,UAAAA,KAAK;AAQbgsB,6CAAoBA,MAClB,KAAKC,SAAUzX,CAAU,UAAA;MAAC5D,OAAO;MAAMsb,YAAY1X,KAAK0X,aAAa;IAAA,EAAG;AAE1EC,qDAA4BA,MAAM;AACd,wBAAA,GAClB,KAAKH,kBAAkB;IACzB;AAbE,SAAK7iB,QAAQ;MAACyH,OAAO;MAAMsb,YAAY;IAAC;EAAA;EAG1C,OAAOE,yBAAyBxb,OAAgB;AACvC,WAAA;MAACA,OAAOA,iBAAiBqB,QAAQrB,MAAMvC,UAAU,GAAGuC,KAAK;IAAE;EAAA;EAWpEyb,SAAS;AACH,QAAA,CAAC,KAAKljB,MAAMyH;AACd,aAAO,KAAK5Q,MAAMssB;AAGpB,UAAMje,UAAU,KAAKlF,MAAMyH,OACrB2b,iBAAiB,KAAKpjB,MAAM+iB,aAAa;AAG7C,eAAA,yBAAC,MACC,EAAA,QAAO,QACP,UAAS,QACT,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,GACrB,UAAU,GACV,QAAO,UACP,MAAK,YAEL,cAAA,yBAAC,WAAU,EAAA,OAAO,GAChB,cAAA,0BAAC,OAAM,EAAA,OAAO,GACZ,UAAA;UAAA,yBAAC,OACC,EAAA,cAAA,yBAAC,QACC,EAAA,SAASK,iBAAiB,KAAKJ,4BAA4B,KAAKH,mBAChE,MAAMO,iBAAiB,0BAA0B,SACjD,MAAK,UAAS,CAAA,EAAA,CAElB;UAEA,yBAAC,SAAA,EAAQ,UAAiB,oBAAA,CAAA;UAE1B,yBAAC,MAAA,EAAK,QAAM,MAAC,QAAQ,GAAG,UAAS,QAAO,SAAS,GAAG,MAAK,WACvD,cAAC,yBAAA,OAAA,EAAM,OAAO,GACXle,UAAAA,eAAAA,yBACE,MAAK,EAAA,MAAM,GACV,cAAA,0BAAC,UAAO,EAAA,UAAA;QAAA;QAAQA;MAAAA,EAAAA,CAAQ,EAAA,CAC1B,EAAA,CAEJ,EACF,CAAA;IAAA,EACF,CAAA,EACF,CAAA,EAAA,CACF;EAAA;AAGN;AC5EA,SAAAme,aAAAxsB,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA;AAAAI,MAAAA;AAAAL,IAAA,CAAA,MAAA2I,OAAAC,IAAA,2BAAA,KAC2BvI,KAAA;IAAAiN,YAAa;EAAA,GAAItN,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAA1CsnB,QAAAA,SAAeD,UAAUhnB,EAAiB;AAACC,MAAAA;AAAAN,IAAAD,CAAAA,MAAAA,MAAAysB,KAAAzhB,WACdzK,KAAA;IAAAqlB,mBAAA/lB;IAAA,GAExBG,MAAKysB,KAAAzhB;EACT/K,GAAAA,EAAAD,CAAAA,IAAAA,MAAAysB,KAAAzhB,SAAA/K,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAHD,QAAAwlB,SAA6BllB;AAG5BM,MAAAA;AAAA,SAAAZ,EAAAsnB,CAAAA,MAAAA,UAAAtnB,EAAAA,CAAAA,MAAAwlB,UAGC5kB,SAAC,yBAAA,qBAAA,EACC,cAAC,yBAAA,iBAAA,EAAwB0mB,QAAgB9B,OAC3C,CAAA,EAAA,CAAA,GAAsBxlB,EAAAA,CAAAA,IAAAsnB,QAAAtnB,EAAAA,CAAAA,IAAAwlB,QAAAxlB,EAAAA,CAAAA,IAAAY,MAAAA,KAAAZ,EAAA,CAAA,GAFtBY;AAEsB;", "names": ["key", "exports", "document", "key", "require_utils", "key", "filtered", "line", "document", "json2csv", "__spreadValues", "_SplitPane", "__publicField", "document", "window", "c", "c", "parse", "key", "token", "buffer", "stringify", "stack", "value", "API_VERSIONS", "Date", "toISOString", "split", "DEFAULT_API_VERSION", "slice", "Delayed<PERSON><PERSON>ner", "props", "$", "_c", "show", "setShow", "useState", "t0", "t1", "delay", "timer", "setTimeout", "clearTimeout", "useEffect", "t2", "codemirrorExtensions", "javascriptLanguage", "lineNumbers", "highlightActiveLine", "highlightActiveLineGutter", "highlightSelectionMatches", "highlightSpecialChars", "indentOnInput", "bracketMatching", "closeBrackets", "history", "drawSelection", "syntaxHighlighting", "defaultHighlightStyle", "fallback", "keymap", "of", "key", "run", "defaultKeymap", "historyKeymap", "flat", "filter", "Boolean", "useCodemirrorTheme", "theme", "createTheme", "cmTheme", "t3", "createHighlight", "cmHighlight", "t4", "color", "fonts", "sanity", "card", "enabled", "cursor", "hues", "blue", "dark", "hex", "selection", "gray", "Editor<PERSON><PERSON><PERSON>", "fg", "backgroundColor", "bg", "caretColor", "fontFamily", "code", "family", "fontSize", "rem", "sizes", "lineHeight", "borderLeftColor", "borderBottom", "border", "borderTop", "c", "base", "s", "syntax", "HighlightStyle", "define", "tag", "t", "keyword", "propertyName", "name", "deleted", "character", "macroName", "property", "function", "variableName", "labelName", "constant", "standard", "variable", "definition", "separator", "typeName", "className", "number", "changed", "annotation", "modifier", "self", "namespace", "operator", "operatorKeyword", "url", "escape", "regexp", "link", "special", "string", "meta", "comment", "strong", "fontWeight", "emphasis", "fontStyle", "strikethrough", "textDecoration", "heading", "atom", "bool", "boolean", "processingInstruction", "inserted", "invalid", "EditorRoot", "styled", "div", "space", "VisionCodeMirror", "forwardRef", "ref", "initialValue", "sanityTheme", "useTheme", "codeMirrorRef", "useRef", "Symbol", "for", "newContent", "<PERSON><PERSON><PERSON><PERSON>", "current", "view", "currentDoc", "state", "doc", "toString", "dispatch", "changes", "from", "to", "length", "insert", "EditorSelection", "reset<PERSON><PERSON>or<PERSON><PERSON><PERSON>", "useImperativeHandle", "onChange", "displayName", "SUPPORTED_PERSPECTIVES", "VIRTUAL_PERSPECTIVES", "isSupportedPerspective", "p", "includes", "isVirtualPerspective", "maybeVirtualPerspective", "hasPinnedPerspective", "selectedPerspectiveName", "getActivePerspective", "visionPerspective", "perspectiveStack", "encodeQueryString", "query", "params", "options", "searchParams", "URLSearchParams", "set", "value", "Object", "entries", "JSON", "stringify", "isPlainObject", "obj", "prototype", "call", "hasLocalStorage", "supportsLocalStorage", "keyPrefix", "clearLocalStorage", "i", "localStorage", "startsWith", "removeItem", "getLocalStorage", "storageKey", "loadedState", "get", "merge", "defaultVal", "ensureState", "setItem", "loadState", "stored", "parse", "getItem", "mod", "parseApiQueryString", "qs", "prefixApiVersion", "version", "validateApiVersion", "apiVersion", "parseableApiVersion", "replace", "trim", "toUpperCase", "test", "isNaN", "tryParseParams", "val", "parsed", "JSON5", "Array", "isArray", "err", "message", "Root", "Flex", "Header", "Card", "StyledLabel", "Label", "SplitpaneContainer", "Box", "QueryCopyLink", "a", "InputBackgroundContainer", "InputBackgroundContainerLeft", "InputContainer", "ResultOuterContainer", "ResultInnerContainer", "ResultContainer", "$isInvalid", "css", "Result", "Result<PERSON><PERSON>er", "TimingsCard", "TimingsTextContainer", "text", "ascenderHeight", "descenderHeight", "DownloadsCard", "SaveResultLabel", "Text", "ControlsContainer", "defaultValue", "ParamsEditor", "paramsError", "hasValidParams", "editor<PERSON><PERSON>", "useTranslation", "visionLocaleNamespace", "parseParams", "raw", "error", "valid", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "init", "setInit", "newValue", "event", "handleChangeRaw", "t5", "debounce", "handleChange", "t6", "t7", "t8", "t9", "t10", "t11", "t12", "t13", "parsedParams", "Error", "validationError", "undefined", "STORED_QUERIES_NAMESPACE", "queries", "keyValueStoreKey", "useSavedQueries", "keyValueStore", "useKeyValueStore", "setValue", "saving", "setSaving", "deleting", "setDeleting", "saveQueryError", "setSaveQueryError", "deleteQueryError", "setDelete<PERSON>uery<PERSON>rror", "setError", "useMemo", "<PERSON><PERSON><PERSON>", "sub", "pipe", "startWith", "map", "data", "subscribe", "next", "unsubscribe", "saveQuery", "useCallback", "newQueries", "_key", "uuid", "<PERSON><PERSON><PERSON>", "updateQuery", "updatedQueries", "q", "deleteQuery", "prev", "filteredQueries", "k", "FixedHeader", "<PERSON><PERSON>", "<PERSON>rollC<PERSON>r", "QueryRecall", "getStateFromUrl", "setStateFromParsedUrl", "<PERSON><PERSON><PERSON><PERSON>", "currentParams", "generateUrl", "toast", "useToast", "month", "day", "year", "hour", "minute", "hour12", "formatDate", "useDateTimeFormat", "<PERSON><PERSON><PERSON>", "setEditingKey", "editingTitle", "setEditingTitle", "<PERSON><PERSON><PERSON><PERSON>", "setOptimisticTitles", "searchQuery", "setSearch<PERSON>uery", "selectedUrl", "setSelectedUrl", "newUrl", "some", "savedQueryObj", "isEqual", "duplicate<PERSON><PERSON>y", "find", "q_0", "savedQueryObj_0", "push", "closable", "status", "title", "description", "format", "savedAt", "handleSave", "newTitle", "prev_1", "next_0", "prev_0", "handleTitleSave", "query_0", "newUrl_0", "q_1", "savedQueryObj_1", "duplicateQuery_0", "q_2", "savedQueryObj_2", "handleUpdate", "T0", "T1", "q_3", "toLowerCase", "textTransform", "AddIcon", "t14", "t15", "t16", "currentTarget", "t17", "SearchIcon", "q_4", "queryObj", "isSelected", "areQueriesEqual", "isEdited", "parsedUrl", "position", "event_0", "event_1", "max<PERSON><PERSON><PERSON>", "height", "padding", "width", "borderRadius", "TrashIcon", "event_2", "stopPropagation", "portal", "placement", "tone", "right", "bottom", "e", "narrowBreakpoint", "window", "innerWidth", "calculatePaneSizeOptions", "rootHeight", "document", "body", "getBoundingClientRect", "defaultSize", "size", "allowResize", "minSize", "Math", "min", "max", "maxSize", "usePaneSize", "visionRootRef", "isNarrowBreakpoint", "setIsNarrowBreakpoint", "_temp", "paneSizeOptions", "setPaneSizeOptions", "_temp2", "handleResize", "entry", "contentRect", "resizeObserver", "ResizeObserver", "observe", "disconnect", "VisionGuiControls", "listenInProgress", "queryInProgress", "onQueryExecution", "onListenExecution", "StopIcon", "PlayIcon", "Perspective<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PerspectivePopoverLink", "Dot", "$tone", "PerspectivePopover", "open", "<PERSON><PERSON><PERSON>", "buttonRef", "popoverRef", "handleClick", "useClickOutsideEvent", "HelpCircleIcon", "o", "PinnedReleasePerspectiveOption", "pinnedPerspective", "selectedPerspective", "metadata", "label", "join", "VisionGuiHeader", "onChangeDataset", "dataset", "customApiVersion", "onChangeApiVersion", "datasets", "customApiVersionElementRef", "onCustomApiVersionChange", "isValidApiVersion", "onChangePerspective", "perspective", "usePerspective", "operationUrlElement", "el", "select", "execCommand", "console", "handleCopyUrl", "_temp3", "t18", "t19", "t20", "t21", "t22", "perspectiveName", "t23", "t24", "t25", "CopyIcon", "t26", "ds", "getBlobUrl", "content", "mimeType", "URL", "createObjectURL", "Blob", "type", "getMemoizedBlobUrlResolver", "string<PERSON><PERSON><PERSON>", "prevResult", "prevContent", "input", "revokeObjectURL", "getJsonBlobUrl", "getCsvBlobUrl", "json2csv", "ErrorCode", "Code", "muted", "critical", "QueryErrorDetails", "details", "mapToLegacyDetails", "line", "dashLine", "column", "columnEnd", "lineNumber", "start", "end", "lineStart", "lastIndexOf", "match", "indexOf", "repeat", "hats", "QueryErrorDialog", "ResultViewWrapper", "lru", "LRU", "ResultView", "datasetName", "workspaceDataset", "useDataset", "isRecord", "DocumentEditLabel", "isExpanded", "toggleExpanded", "is<PERSON>ey", "keypath", "endsWith", "id", "keyP<PERSON>", "cached", "segments", "depthLimit", "isArrayKeyOverLimit", "path", "numeric", "segment", "limit", "parseInt", "preventSave", "evt", "preventDefault", "SaveCsvButton", "blobUrl", "isDisabled", "DocumentSheetIcon", "button", "SaveJsonButton", "VisionGuiResult", "query<PERSON><PERSON>ult", "listenMutations", "queryTime", "e2eTime", "hasResult", "jsonUrl", "csvUrl", "SaveResultButtons", "Fragment", "nodeContains", "node", "other", "compareDocumentPosition", "sanityUrl", "isRunHotkey", "isHotkey", "VisionGui", "config", "projectId", "defaultDataset", "defaultApiVersion", "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "editorParamsRef", "querySubscriptionRef", "listenSubscriptionRef", "storedDataset", "storedApiVersion", "storedQuery", "storedParams", "storedPerspective", "setDataset", "setApiVersion", "setCustomApiVersion", "setPerspectiveState", "setUrl", "<PERSON><PERSON><PERSON><PERSON>", "setParams", "set<PERSON><PERSON><PERSON><PERSON><PERSON>ult", "setListenMutations", "setQueryTime", "setE2eTime", "setQueryInProgress", "setListenInProgress", "isQueryRecallCollapsed", "setIsQueryRecallCollapsed", "_client", "useClient", "client", "withConfig", "allowReconfigure", "cancelQuerySubscription", "cancelListenerSubscription", "handleQueryExecution", "context", "urlQueryOpts", "ctxClient", "getUrl", "getDataUrl", "queryStart", "now", "observable", "fetch", "filterResponse", "res", "ms", "result", "setPerspective", "newPerspective", "handleChangeDataset", "newDataset", "target", "handleChangeApiVersion", "newApiVersion", "focus", "handleCustomApiVersionChange", "newCustomApiVersion", "handleChangePerspective", "handleListenerEvent", "prevMutations", "handleListenExecution", "shouldExecute", "listen", "events", "includeAllVersions", "handleParamsChange", "usedApiVersion", "usedDataset", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parts", "rawParams", "parsedUrlObj", "handlePaste", "clipboardData", "getData", "urlState", "handleKeyDown", "isWithinRoot", "addEventListener", "removeEventListener", "handleStudioPerspectiveChange", "useEffectEvent", "stack", "queryString", "queryParams", "left", "top", "transform", "zIndex", "pointerEvents", "display", "alignItems", "useDatasets", "setDatasets", "datasets$", "list", "VisionContainer", "loadedDatasets", "VisionErrorBoundary", "Component", "constructor", "handleRetryRender", "setState", "numRetries", "handleRetryWithCacheClear", "getDerivedStateFromError", "render", "children", "with<PERSON>ache<PERSON><PERSON>r", "SanityVision", "tool"]}
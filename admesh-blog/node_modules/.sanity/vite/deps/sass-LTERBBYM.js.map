{"version": 3, "sources": ["../../../../../node_modules/@codemirror/legacy-modes/mode/sass.js"], "sourcesContent": ["import {keywords as cssKeywords} from \"./css.js\"\nconst propertyKeywords = new Set(cssKeywords.properties)\nconst colorKeywords = new Set(cssKeywords.colors)\nconst valueKeywords = new Set(cssKeywords.values)\nconst fontProperties = new Set(cssKeywords.fonts)\n\nfunction tokenRegexp(words) {\n  return new RegExp(\"^\" + words.join(\"|\"))\n}\n\nlet keywords = [\"true\", \"false\", \"null\", \"auto\"]\nlet keywordsRegexp = new RegExp(\"^\" + keywords.join(\"|\"))\n\nlet operators = [\"\\\\(\", \"\\\\)\", \"=\", \">\", \"<\", \"==\", \">=\", \"<=\", \"\\\\+\", \"-\",\n                 \"\\\\!=\", \"/\", \"\\\\*\", \"%\", \"and\", \"or\", \"not\", \";\",\"\\\\{\",\"\\\\}\",\":\"]\nlet opRegexp = tokenRegexp(operators)\n\nlet pseudoElementsRegexp = /^::?[a-zA-Z_][\\w\\-]*/\n\nlet word\n\nfunction isEndLine(stream) {\n  return !stream.peek() || stream.match(/\\s+$/, false)\n}\n\nfunction urlTokens(stream, state) {\n  let ch = stream.peek()\n\n  if (ch === \")\") {\n    stream.next()\n    state.tokenizer = tokenBase\n    return \"operator\"\n  } else if (ch === \"(\") {\n    stream.next()\n    stream.eatSpace()\n\n    return \"operator\"\n  } else if (ch === \"'\" || ch === '\"') {\n    state.tokenizer = buildStringTokenizer(stream.next())\n    return \"string\"\n  } else {\n    state.tokenizer = buildStringTokenizer(\")\", false)\n    return \"string\"\n  }\n}\nfunction comment(indentation, multiLine) {\n  return function(stream, state) {\n    if (stream.sol() && stream.indentation() <= indentation) {\n      state.tokenizer = tokenBase\n      return tokenBase(stream, state)\n    }\n\n    if (multiLine && stream.skipTo(\"*/\")) {\n      stream.next()\n      stream.next()\n      state.tokenizer = tokenBase\n    } else {\n      stream.skipToEnd()\n    }\n\n    return \"comment\"\n  }\n}\n\nfunction buildStringTokenizer(quote, greedy) {\n  if (greedy == null) { greedy = true }\n\n  function stringTokenizer(stream, state) {\n    let nextChar = stream.next()\n    let peekChar = stream.peek()\n    let previousChar = stream.string.charAt(stream.pos-2)\n\n    let endingString = ((nextChar !== \"\\\\\" && peekChar === quote) || (nextChar === quote && previousChar !== \"\\\\\"))\n\n    if (endingString) {\n      if (nextChar !== quote && greedy) { stream.next() }\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      state.tokenizer = tokenBase\n      return \"string\"\n    } else if (nextChar === \"#\" && peekChar === \"{\") {\n      state.tokenizer = buildInterpolationTokenizer(stringTokenizer)\n      stream.next()\n      return \"operator\"\n    } else {\n      return \"string\"\n    }\n  }\n\n  return stringTokenizer\n}\n\nfunction buildInterpolationTokenizer(currentTokenizer) {\n  return function(stream, state) {\n    if (stream.peek() === \"}\") {\n      stream.next()\n      state.tokenizer = currentTokenizer\n      return \"operator\"\n    } else {\n      return tokenBase(stream, state)\n    }\n  }\n}\n\nfunction indent(state, stream) {\n  if (state.indentCount == 0) {\n    state.indentCount++\n    let lastScopeOffset = state.scopes[0].offset\n    let currentOffset = lastScopeOffset + stream.indentUnit\n    state.scopes.unshift({ offset:currentOffset })\n  }\n}\n\nfunction dedent(state) {\n  if (state.scopes.length == 1) return\n\n  state.scopes.shift()\n}\n\nfunction tokenBase(stream, state) {\n  let ch = stream.peek()\n\n  // Comment\n  if (stream.match(\"/*\")) {\n    state.tokenizer = comment(stream.indentation(), true)\n    return state.tokenizer(stream, state)\n  }\n  if (stream.match(\"//\")) {\n    state.tokenizer = comment(stream.indentation(), false)\n    return state.tokenizer(stream, state)\n  }\n\n  // Interpolation\n  if (stream.match(\"#{\")) {\n    state.tokenizer = buildInterpolationTokenizer(tokenBase)\n    return \"operator\"\n  }\n\n  // Strings\n  if (ch === '\"' || ch === \"'\") {\n    stream.next()\n    state.tokenizer = buildStringTokenizer(ch)\n    return \"string\"\n  }\n\n  if (!state.cursorHalf) {\n    // first half i.e. before : for key-value pairs\n    // including selectors\n    if (ch === \"-\") {\n      if (stream.match(/^-\\w+-/)) {\n        return \"meta\"\n      }\n    }\n\n    if (ch === \".\") {\n      stream.next()\n      if (stream.match(/^[\\w-]+/)) {\n        indent(state, stream)\n        return \"qualifier\"\n      } else if (stream.peek() === \"#\") {\n        indent(state, stream)\n        return \"tag\"\n      }\n    }\n\n    if (ch === \"#\") {\n      stream.next()\n      // ID selectors\n      if (stream.match(/^[\\w-]+/)) {\n        indent(state, stream)\n        return \"builtin\"\n      }\n      if (stream.peek() === \"#\") {\n        indent(state, stream)\n        return \"tag\"\n      }\n    }\n\n    // Variables\n    if (ch === \"$\") {\n      stream.next()\n      stream.eatWhile(/[\\w-]/)\n      return \"variable-2\"\n    }\n\n    // Numbers\n    if (stream.match(/^-?[0-9\\.]+/))\n      return \"number\"\n\n    // Units\n    if (stream.match(/^(px|em|in)\\b/))\n      return \"unit\"\n\n    if (stream.match(keywordsRegexp))\n      return \"keyword\"\n\n    if (stream.match(/^url/) && stream.peek() === \"(\") {\n      state.tokenizer = urlTokens\n      return \"atom\"\n    }\n\n    if (ch === \"=\") {\n      // Match shortcut mixin definition\n      if (stream.match(/^=[\\w-]+/)) {\n        indent(state, stream)\n        return \"meta\"\n      }\n    }\n\n    if (ch === \"+\") {\n      // Match shortcut mixin definition\n      if (stream.match(/^\\+[\\w-]+/)) {\n        return \"meta\"\n      }\n    }\n\n    if (ch === \"@\") {\n      if (stream.match('@extend')) {\n        if (!stream.match(/\\s*[\\w]/))\n          dedent(state)\n      }\n    }\n\n\n    // Indent Directives\n    if (stream.match(/^@(else if|if|media|else|for|each|while|mixin|function)/)) {\n      indent(state, stream)\n      return \"def\"\n    }\n\n    // Other Directives\n    if (ch === \"@\") {\n      stream.next()\n      stream.eatWhile(/[\\w-]/)\n      return \"def\"\n    }\n\n    if (stream.eatWhile(/[\\w-]/)) {\n      if (stream.match(/ *: *[\\w-\\+\\$#!\\(\"']/,false)) {\n        word = stream.current().toLowerCase()\n        let prop = state.prevProp + \"-\" + word\n        if (propertyKeywords.has(prop)) {\n          return \"property\"\n        } else if (propertyKeywords.has(word)) {\n          state.prevProp = word\n          return \"property\"\n        } else if (fontProperties.has(word)) {\n          return \"property\"\n        }\n        return \"tag\"\n      } else if (stream.match(/ *:/,false)) {\n        indent(state, stream)\n        state.cursorHalf = 1\n        state.prevProp = stream.current().toLowerCase()\n        return \"property\"\n      } else if (stream.match(/ *,/,false)) {\n        return \"tag\"\n      } else {\n        indent(state, stream)\n        return \"tag\"\n      }\n    }\n\n    if (ch === \":\") {\n      if (stream.match(pseudoElementsRegexp)) { // could be a pseudo-element\n        return \"type\"\n      }\n      stream.next()\n      state.cursorHalf=1\n      return \"operator\"\n    }\n  } else {\n    if (ch === \"#\") {\n      stream.next()\n      // Hex numbers\n      if (stream.match(/[0-9a-fA-F]{6}|[0-9a-fA-F]{3}/)) {\n        if (isEndLine(stream)) {\n          state.cursorHalf = 0\n        }\n        return \"number\"\n      }\n    }\n\n    // Numbers\n    if (stream.match(/^-?[0-9\\.]+/)) {\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      return \"number\"\n    }\n\n    // Units\n    if (stream.match(/^(px|em|in)\\b/)) {\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      return \"unit\"\n    }\n\n    if (stream.match(keywordsRegexp)) {\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      return \"keyword\"\n    }\n\n    if (stream.match(/^url/) && stream.peek() === \"(\") {\n      state.tokenizer = urlTokens\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      return \"atom\"\n    }\n\n    // Variables\n    if (ch === \"$\") {\n      stream.next()\n      stream.eatWhile(/[\\w-]/)\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      return \"variable-2\"\n    }\n\n    // bang character for !important, !default, etc.\n    if (ch === \"!\") {\n      stream.next()\n      state.cursorHalf = 0\n      return stream.match(/^[\\w]+/) ? \"keyword\": \"operator\"\n    }\n\n    if (stream.match(opRegexp)) {\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      return \"operator\"\n    }\n\n    // attributes\n    if (stream.eatWhile(/[\\w-]/)) {\n      if (isEndLine(stream)) {\n        state.cursorHalf = 0\n      }\n      word = stream.current().toLowerCase()\n      if (valueKeywords.has(word)) {\n        return \"atom\"\n      } else if (colorKeywords.has(word)) {\n        return \"keyword\"\n      } else if (propertyKeywords.has(word)) {\n        state.prevProp = stream.current().toLowerCase()\n        return \"property\"\n      } else {\n        return \"tag\"\n      }\n    }\n\n    if (isEndLine(stream)) {\n      state.cursorHalf = 0\n      return null\n    }\n  }\n\n  if (stream.match(opRegexp))\n    return \"operator\"\n\n  stream.next()\n  return null\n}\n\nfunction tokenLexer(stream, state) {\n  if (stream.sol()) state.indentCount = 0\n  let style = state.tokenizer(stream, state)\n  let current = stream.current()\n\n  if (current === \"@return\" || current === \"}\") {\n    dedent(state)\n  }\n\n  if (style !== null) {\n    let startOfToken = stream.pos - current.length\n\n    let withCurrentIndent = startOfToken + (stream.indentUnit * state.indentCount)\n\n    let newScopes = []\n\n    for (let i = 0; i < state.scopes.length; i++) {\n      let scope = state.scopes[i]\n\n      if (scope.offset <= withCurrentIndent)\n        newScopes.push(scope)\n    }\n\n    state.scopes = newScopes\n  }\n\n  return style\n}\n\nexport const sass = {\n  name: \"sass\",\n  startState: function() {\n    return {\n      tokenizer: tokenBase,\n      scopes: [{offset: 0, type: \"sass\"}],\n      indentCount: 0,\n      cursorHalf: 0,  // cursor half tells us if cursor lies after (1)\n      // or before (0) colon (well... more or less)\n      definedVars: [],\n      definedMixins: []\n    }\n  },\n  token: function(stream, state) {\n    let style = tokenLexer(stream, state)\n    state.lastToken = { style: style, content: stream.current() }\n    return style\n  },\n\n  indent: function(state) {\n    return state.scopes[0].offset\n  },\n\n  languageData: {\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: cssKeywords.all\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAM,mBAAmB,IAAI,IAAI,SAAY,UAAU;AACvD,IAAM,gBAAgB,IAAI,IAAI,SAAY,MAAM;AAChD,IAAM,gBAAgB,IAAI,IAAI,SAAY,MAAM;AAChD,IAAM,iBAAiB,IAAI,IAAI,SAAY,KAAK;AAEhD,SAAS,YAAY,OAAO;AAC1B,SAAO,IAAI,OAAO,MAAM,MAAM,KAAK,GAAG,CAAC;AACzC;AAEA,IAAIA,YAAW,CAAC,QAAQ,SAAS,QAAQ,MAAM;AAC/C,IAAI,iBAAiB,IAAI,OAAO,MAAMA,UAAS,KAAK,GAAG,CAAC;AAExD,IAAI,YAAY;AAAA,EAAC;AAAA,EAAO;AAAA,EAAO;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EACtD;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAO;AAAA,EAAK;AAAA,EAAO;AAAA,EAAM;AAAA,EAAO;AAAA,EAAI;AAAA,EAAM;AAAA,EAAM;AAAG;AACjF,IAAI,WAAW,YAAY,SAAS;AAEpC,IAAI,uBAAuB;AAE3B,IAAI;AAEJ,SAAS,UAAU,QAAQ;AACzB,SAAO,CAAC,OAAO,KAAK,KAAK,OAAO,MAAM,QAAQ,KAAK;AACrD;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,OAAO,KAAK;AACd,WAAO,KAAK;AACZ,UAAM,YAAY;AAClB,WAAO;AAAA,EACT,WAAW,OAAO,KAAK;AACrB,WAAO,KAAK;AACZ,WAAO,SAAS;AAEhB,WAAO;AAAA,EACT,WAAW,OAAO,OAAO,OAAO,KAAK;AACnC,UAAM,YAAY,qBAAqB,OAAO,KAAK,CAAC;AACpD,WAAO;AAAA,EACT,OAAO;AACL,UAAM,YAAY,qBAAqB,KAAK,KAAK;AACjD,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,aAAa,WAAW;AACvC,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,KAAK,OAAO,YAAY,KAAK,aAAa;AACvD,YAAM,YAAY;AAClB,aAAO,UAAU,QAAQ,KAAK;AAAA,IAChC;AAEA,QAAI,aAAa,OAAO,OAAO,IAAI,GAAG;AACpC,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,YAAM,YAAY;AAAA,IACpB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,qBAAqB,OAAO,QAAQ;AAC3C,MAAI,UAAU,MAAM;AAAE,aAAS;AAAA,EAAK;AAEpC,WAAS,gBAAgB,QAAQ,OAAO;AACtC,QAAI,WAAW,OAAO,KAAK;AAC3B,QAAI,WAAW,OAAO,KAAK;AAC3B,QAAI,eAAe,OAAO,OAAO,OAAO,OAAO,MAAI,CAAC;AAEpD,QAAI,eAAiB,aAAa,QAAQ,aAAa,SAAW,aAAa,SAAS,iBAAiB;AAEzG,QAAI,cAAc;AAChB,UAAI,aAAa,SAAS,QAAQ;AAAE,eAAO,KAAK;AAAA,MAAE;AAClD,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,YAAM,YAAY;AAClB,aAAO;AAAA,IACT,WAAW,aAAa,OAAO,aAAa,KAAK;AAC/C,YAAM,YAAY,4BAA4B,eAAe;AAC7D,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,kBAAkB;AACrD,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,KAAK,MAAM,KAAK;AACzB,aAAO,KAAK;AACZ,YAAM,YAAY;AAClB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,UAAU,QAAQ,KAAK;AAAA,IAChC;AAAA,EACF;AACF;AAEA,SAAS,OAAO,OAAO,QAAQ;AAC7B,MAAI,MAAM,eAAe,GAAG;AAC1B,UAAM;AACN,QAAI,kBAAkB,MAAM,OAAO,CAAC,EAAE;AACtC,QAAI,gBAAgB,kBAAkB,OAAO;AAC7C,UAAM,OAAO,QAAQ,EAAE,QAAO,cAAc,CAAC;AAAA,EAC/C;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,MAAM,OAAO,UAAU,EAAG;AAE9B,QAAM,OAAO,MAAM;AACrB;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM,YAAY,QAAQ,OAAO,YAAY,GAAG,IAAI;AACpD,WAAO,MAAM,UAAU,QAAQ,KAAK;AAAA,EACtC;AACA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM,YAAY,QAAQ,OAAO,YAAY,GAAG,KAAK;AACrD,WAAO,MAAM,UAAU,QAAQ,KAAK;AAAA,EACtC;AAGA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM,YAAY,4BAA4B,SAAS;AACvD,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,WAAO,KAAK;AACZ,UAAM,YAAY,qBAAqB,EAAE;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,YAAY;AAGrB,QAAI,OAAO,KAAK;AACd,UAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AACZ,UAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,eAAO,OAAO,MAAM;AACpB,eAAO;AAAA,MACT,WAAW,OAAO,KAAK,MAAM,KAAK;AAChC,eAAO,OAAO,MAAM;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AAEZ,UAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,eAAO,OAAO,MAAM;AACpB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,KAAK,MAAM,KAAK;AACzB,eAAO,OAAO,MAAM;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAGA,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AACZ,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,MAAM,aAAa;AAC5B,aAAO;AAGT,QAAI,OAAO,MAAM,eAAe;AAC9B,aAAO;AAET,QAAI,OAAO,MAAM,cAAc;AAC7B,aAAO;AAET,QAAI,OAAO,MAAM,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK;AACjD,YAAM,YAAY;AAClB,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK;AAEd,UAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,eAAO,OAAO,MAAM;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,OAAO,KAAK;AAEd,UAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,OAAO,KAAK;AACd,UAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,YAAI,CAAC,OAAO,MAAM,SAAS;AACzB,iBAAO,KAAK;AAAA,MAChB;AAAA,IACF;AAIA,QAAI,OAAO,MAAM,yDAAyD,GAAG;AAC3E,aAAO,OAAO,MAAM;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AACZ,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,OAAO,GAAG;AAC5B,UAAI,OAAO,MAAM,wBAAuB,KAAK,GAAG;AAC9C,eAAO,OAAO,QAAQ,EAAE,YAAY;AACpC,YAAI,OAAO,MAAM,WAAW,MAAM;AAClC,YAAI,iBAAiB,IAAI,IAAI,GAAG;AAC9B,iBAAO;AAAA,QACT,WAAW,iBAAiB,IAAI,IAAI,GAAG;AACrC,gBAAM,WAAW;AACjB,iBAAO;AAAA,QACT,WAAW,eAAe,IAAI,IAAI,GAAG;AACnC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,WAAW,OAAO,MAAM,OAAM,KAAK,GAAG;AACpC,eAAO,OAAO,MAAM;AACpB,cAAM,aAAa;AACnB,cAAM,WAAW,OAAO,QAAQ,EAAE,YAAY;AAC9C,eAAO;AAAA,MACT,WAAW,OAAO,MAAM,OAAM,KAAK,GAAG;AACpC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,OAAO,MAAM;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,OAAO,KAAK;AACd,UAAI,OAAO,MAAM,oBAAoB,GAAG;AACtC,eAAO;AAAA,MACT;AACA,aAAO,KAAK;AACZ,YAAM,aAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AAEZ,UAAI,OAAO,MAAM,+BAA+B,GAAG;AACjD,YAAI,UAAU,MAAM,GAAG;AACrB,gBAAM,aAAa;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAGA,QAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,MAAM,eAAe,GAAG;AACjC,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,cAAc,GAAG;AAChC,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK;AACjD,YAAM,YAAY;AAClB,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AACZ,aAAO,SAAS,OAAO;AACvB,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AACZ,YAAM,aAAa;AACnB,aAAO,OAAO,MAAM,QAAQ,IAAI,YAAW;AAAA,IAC7C;AAEA,QAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,SAAS,OAAO,GAAG;AAC5B,UAAI,UAAU,MAAM,GAAG;AACrB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO,OAAO,QAAQ,EAAE,YAAY;AACpC,UAAI,cAAc,IAAI,IAAI,GAAG;AAC3B,eAAO;AAAA,MACT,WAAW,cAAc,IAAI,IAAI,GAAG;AAClC,eAAO;AAAA,MACT,WAAW,iBAAiB,IAAI,IAAI,GAAG;AACrC,cAAM,WAAW,OAAO,QAAQ,EAAE,YAAY;AAC9C,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,UAAU,MAAM,GAAG;AACrB,YAAM,aAAa;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,OAAO,MAAM,QAAQ;AACvB,WAAO;AAET,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,OAAO,IAAI,EAAG,OAAM,cAAc;AACtC,MAAI,QAAQ,MAAM,UAAU,QAAQ,KAAK;AACzC,MAAI,UAAU,OAAO,QAAQ;AAE7B,MAAI,YAAY,aAAa,YAAY,KAAK;AAC5C,WAAO,KAAK;AAAA,EACd;AAEA,MAAI,UAAU,MAAM;AAClB,QAAI,eAAe,OAAO,MAAM,QAAQ;AAExC,QAAI,oBAAoB,eAAgB,OAAO,aAAa,MAAM;AAElE,QAAI,YAAY,CAAC;AAEjB,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC5C,UAAI,QAAQ,MAAM,OAAO,CAAC;AAE1B,UAAI,MAAM,UAAU;AAClB,kBAAU,KAAK,KAAK;AAAA,IACxB;AAEA,UAAM,SAAS;AAAA,EACjB;AAEA,SAAO;AACT;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,WAAW;AAAA,MACX,QAAQ,CAAC,EAAC,QAAQ,GAAG,MAAM,OAAM,CAAC;AAAA,MAClC,aAAa;AAAA,MACb,YAAY;AAAA;AAAA;AAAA,MAEZ,aAAa,CAAC;AAAA,MACd,eAAe,CAAC;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,WAAW,QAAQ,KAAK;AACpC,UAAM,YAAY,EAAE,OAAc,SAAS,OAAO,QAAQ,EAAE;AAC5D,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO;AACtB,WAAO,MAAM,OAAO,CAAC,EAAE;AAAA,EACzB;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,cAAc,SAAY;AAAA,EAC5B;AACF;", "names": ["keywords"]}
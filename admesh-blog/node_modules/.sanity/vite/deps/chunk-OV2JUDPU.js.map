{"version": 3, "sources": ["../../../debounce/index.js", "../../../md5-o-matic/lib/md5omatic.js", "../../../@rexxars/react-json-inspector/src/uid.ts", "../../../@rexxars/react-json-inspector/src/type.ts", "../../../@rexxars/react-json-inspector/src/isPrimitive.ts", "../../../@rexxars/react-json-inspector/src/highlighter.tsx", "../../../@rexxars/react-json-inspector/src/isObject.ts", "../../../@rexxars/react-json-inspector/src/Leaf.tsx", "../../../@rexxars/react-json-inspector/src/noop.ts", "../../../@rexxars/react-json-inspector/src/SearchBar.tsx", "../../../@rexxars/react-json-inspector/src/isEmpty.ts", "../../../@rexxars/react-json-inspector/src/filterer.ts", "../../../@rexxars/react-json-inspector/src/lens.ts", "../../../@rexxars/react-json-inspector/src/JsonInspector.tsx"], "sourcesContent": ["/**\n * Returns a function, that, as long as it continues to be invoked, will not\n * be triggered. The function will be called after it stops being called for\n * N milliseconds. If `immediate` is passed, trigger the function on the\n * leading edge, instead of the trailing. The function also has a property 'clear' \n * that is a function which will clear the timer to prevent previously scheduled executions. \n *\n * @source underscore.js\n * @see http://unscriptable.com/2009/03/20/debouncing-javascript-methods/\n * @param {Function} function to wrap\n * @param {Number} timeout in ms (`100`)\n * @param {Boolean} whether to execute at the beginning (`false`)\n * @api public\n */\nfunction debounce(func, wait, immediate){\n  var timeout, args, context, timestamp, result;\n  if (null == wait) wait = 100;\n\n  function later() {\n    var last = Date.now() - timestamp;\n\n    if (last < wait && last >= 0) {\n      timeout = setTimeout(later, wait - last);\n    } else {\n      timeout = null;\n      if (!immediate) {\n        result = func.apply(context, args);\n        context = args = null;\n      }\n    }\n  };\n\n  var debounced = function(){\n    context = this;\n    args = arguments;\n    timestamp = Date.now();\n    var callNow = immediate && !timeout;\n    if (!timeout) timeout = setTimeout(later, wait);\n    if (callNow) {\n      result = func.apply(context, args);\n      context = args = null;\n    }\n\n    return result;\n  };\n\n  debounced.clear = function() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = null;\n    }\n  };\n  \n  debounced.flush = function() {\n    if (timeout) {\n      result = func.apply(context, args);\n      context = args = null;\n      \n      clearTimeout(timeout);\n      timeout = null;\n    }\n  };\n\n  return debounced;\n};\n\n// Adds compatibility for ES modules\ndebounce.debounce = debounce;\n\nmodule.exports = debounce;\n", "\"use strict\";\n\n/**\n * Expose `md5omatic(str)`.\n */\n \nmodule.exports = md5omatic;\n\n/**\n * Hash any string using message digest.\n *\n * @param {String} str\n * @return {String}\n * @api public\n */\n \nfunction md5omatic(str) {\n    var x = str2blks_MD5(str);\n    var a =  1732584193;\n    var b = -271733879;\n    var c = -1732584194;\n    var d =  271733878;\n\n    for(var i=0; i<x.length; i += 16)\n    {\n        var olda = a;\n        var oldb = b;\n        var oldc = c;\n        var oldd = d;\n\n        a = ff(a, b, c, d, x[i+ 0], 7 , -680876936);\n        d = ff(d, a, b, c, x[i+ 1], 12, -389564586);\n        c = ff(c, d, a, b, x[i+ 2], 17,  606105819);\n        b = ff(b, c, d, a, x[i+ 3], 22, -1044525330);\n        a = ff(a, b, c, d, x[i+ 4], 7 , -176418897);\n        d = ff(d, a, b, c, x[i+ 5], 12,  1200080426);\n        c = ff(c, d, a, b, x[i+ 6], 17, -1473231341);\n        b = ff(b, c, d, a, x[i+ 7], 22, -45705983);\n        a = ff(a, b, c, d, x[i+ 8], 7 ,  1770035416);\n        d = ff(d, a, b, c, x[i+ 9], 12, -1958414417);\n        c = ff(c, d, a, b, x[i+10], 17, -42063);\n        b = ff(b, c, d, a, x[i+11], 22, -1990404162);\n        a = ff(a, b, c, d, x[i+12], 7 ,  1804603682);\n        d = ff(d, a, b, c, x[i+13], 12, -40341101);\n        c = ff(c, d, a, b, x[i+14], 17, -1502002290);\n        b = ff(b, c, d, a, x[i+15], 22,  1236535329);\n        a = gg(a, b, c, d, x[i+ 1], 5 , -165796510);\n        d = gg(d, a, b, c, x[i+ 6], 9 , -1069501632);\n        c = gg(c, d, a, b, x[i+11], 14,  643717713);\n        b = gg(b, c, d, a, x[i+ 0], 20, -373897302);\n        a = gg(a, b, c, d, x[i+ 5], 5 , -701558691);\n        d = gg(d, a, b, c, x[i+10], 9 ,  38016083);\n        c = gg(c, d, a, b, x[i+15], 14, -660478335);\n        b = gg(b, c, d, a, x[i+ 4], 20, -405537848);\n        a = gg(a, b, c, d, x[i+ 9], 5 ,  568446438);\n        d = gg(d, a, b, c, x[i+14], 9 , -1019803690);\n        c = gg(c, d, a, b, x[i+ 3], 14, -187363961);\n        b = gg(b, c, d, a, x[i+ 8], 20,  1163531501);\n        a = gg(a, b, c, d, x[i+13], 5 , -1444681467);\n        d = gg(d, a, b, c, x[i+ 2], 9 , -51403784);\n        c = gg(c, d, a, b, x[i+ 7], 14,  1735328473);\n        b = gg(b, c, d, a, x[i+12], 20, -1926607734);\n        a = hh(a, b, c, d, x[i+ 5], 4 , -378558);\n        d = hh(d, a, b, c, x[i+ 8], 11, -2022574463);\n        c = hh(c, d, a, b, x[i+11], 16,  1839030562);\n        b = hh(b, c, d, a, x[i+14], 23, -35309556);\n        a = hh(a, b, c, d, x[i+ 1], 4 , -1530992060);\n        d = hh(d, a, b, c, x[i+ 4], 11,  1272893353);\n        c = hh(c, d, a, b, x[i+ 7], 16, -155497632);\n        b = hh(b, c, d, a, x[i+10], 23, -1094730640);\n        a = hh(a, b, c, d, x[i+13], 4 ,  681279174);\n        d = hh(d, a, b, c, x[i+ 0], 11, -358537222);\n        c = hh(c, d, a, b, x[i+ 3], 16, -722521979);\n        b = hh(b, c, d, a, x[i+ 6], 23,  76029189);\n        a = hh(a, b, c, d, x[i+ 9], 4 , -640364487);\n        d = hh(d, a, b, c, x[i+12], 11, -421815835);\n        c = hh(c, d, a, b, x[i+15], 16,  530742520);\n        b = hh(b, c, d, a, x[i+ 2], 23, -995338651);\n        a = ii(a, b, c, d, x[i+ 0], 6 , -198630844);\n        d = ii(d, a, b, c, x[i+ 7], 10,  1126891415);\n        c = ii(c, d, a, b, x[i+14], 15, -1416354905);\n        b = ii(b, c, d, a, x[i+ 5], 21, -57434055);\n        a = ii(a, b, c, d, x[i+12], 6 ,  1700485571);\n        d = ii(d, a, b, c, x[i+ 3], 10, -1894986606);\n        c = ii(c, d, a, b, x[i+10], 15, -1051523);\n        b = ii(b, c, d, a, x[i+ 1], 21, -2054922799);\n        a = ii(a, b, c, d, x[i+ 8], 6 ,  1873313359);\n        d = ii(d, a, b, c, x[i+15], 10, -30611744);\n        c = ii(c, d, a, b, x[i+ 6], 15, -1560198380);\n        b = ii(b, c, d, a, x[i+13], 21,  1309151649);\n        a = ii(a, b, c, d, x[i+ 4], 6 , -145523070);\n        d = ii(d, a, b, c, x[i+11], 10, -1120210379);\n        c = ii(c, d, a, b, x[i+ 2], 15,  718787259);\n        b = ii(b, c, d, a, x[i+ 9], 21, -343485551);\n\n        a = addme(a, olda);\n        b = addme(b, oldb);\n        c = addme(c, oldc);\n        d = addme(d, oldd);\n    }\n\n    return rhex(a) + rhex(b) + rhex(c) + rhex(d);\n};\n\nvar hex_chr = \"0123456789abcdef\";\n\nfunction bitOR(a, b)\n{\n    var lsb = (a & 0x1) | (b & 0x1);\n    var msb31 = (a >>> 1) | (b >>> 1);\n\n    return (msb31 << 1) | lsb;\n}\n\nfunction bitXOR(a, b)\n{\n    var lsb = (a & 0x1) ^ (b & 0x1);\n    var msb31 = (a >>> 1) ^ (b >>> 1);\n\n    return (msb31 << 1) | lsb;\n}\n\nfunction bitAND(a, b)\n{\n    var lsb = (a & 0x1) & (b & 0x1);\n    var msb31 = (a >>> 1) & (b >>> 1);\n\n    return (msb31 << 1) | lsb;\n}\n\nfunction addme(x, y)\n{\n    var lsw = (x & 0xFFFF)+(y & 0xFFFF);\n    var msw = (x >> 16)+(y >> 16)+(lsw >> 16);\n\n    return (msw << 16) | (lsw & 0xFFFF);\n}\n\nfunction rhex(num)\n{\n    var str = \"\";\n    var j;\n\n    for(j=0; j<=3; j++)\n        str += hex_chr.charAt((num >> (j * 8 + 4)) & 0x0F) + hex_chr.charAt((num >> (j * 8)) & 0x0F);\n\n    return str;\n}\n\nfunction str2blks_MD5(str)\n{\n    var nblk = ((str.length + 8) >> 6) + 1;\n    var blks = new Array(nblk * 16);\n    var i;\n\n    for(i=0; i<nblk * 16; i++)\n        blks[i] = 0;\n\n    for(i=0; i<str.length; i++)\n        blks[i >> 2] |= str.charCodeAt(i) << (((str.length * 8 + i) % 4) * 8);\n\n    blks[i >> 2] |= 0x80 << (((str.length * 8 + i) % 4) * 8);\n\n    var l = str.length * 8;\n    blks[nblk * 16 - 2] = (l & 0xFF);\n    blks[nblk * 16 - 2] |= ((l >>> 8) & 0xFF) << 8;\n    blks[nblk * 16 - 2] |= ((l >>> 16) & 0xFF) << 16;\n    blks[nblk * 16 - 2] |= ((l >>> 24) & 0xFF) << 24;\n\n    return blks;\n}\n\nfunction rol(num, cnt)\n{\n    return (num << cnt) | (num >>> (32 - cnt));\n}\n\nfunction cmn(q, a, b, x, s, t)\n{\n    return addme(rol((addme(addme(a, q), addme(x, t))), s), b);\n}\n\nfunction ff(a, b, c, d, x, s, t)\n{\n    return cmn(bitOR(bitAND(b, c), bitAND((~b), d)), a, b, x, s, t);\n}\n\nfunction gg(a, b, c, d, x, s, t)\n{\n    return cmn(bitOR(bitAND(b, d), bitAND(c, (~d))), a, b, x, s, t);\n}\n\nfunction hh(a, b, c, d, x, s, t)\n{\n    return cmn(bitXOR(bitXOR(b, c), d), a, b, x, s, t);\n}\n\nfunction ii(a, b, c, d, x, s, t)\n{\n    return cmn(bitXOR(c, bitOR(b, (~d))), a, b, x, s, t);\n}", "let id = Math.ceil(Math.random() * 10)\n\nexport const uid = () => {\n  return ++id\n}\n", "export function type(value: unknown) {\n  return Object.prototype.toString.call(value).slice(8, -1)\n}\n", "import {type} from './type'\n\nexport function isPrimitive(value: unknown): boolean {\n  const t = type(value)\n  return t !== 'Object' && t !== 'Array'\n}\n", "import {Component} from 'react'\n\nexport interface HighlighterProps {\n  string: string\n  highlight?: RegExp | null\n}\n\nexport class Highlighter extends Component<HighlighterProps> {\n  shouldComponentUpdate(p: HighlighterProps) {\n    return p.highlight !== this.props.highlight\n  }\n\n  render() {\n    const str = this.props.string || ''\n    const highlight = this.props.highlight || ''\n    const highlightStart = str.search(highlight)\n\n    if (!highlight || highlightStart === -1) {\n      return <span>{str}</span>\n    }\n\n    const highlightLength = highlight.source.length\n    const highlightString = str.slice(\n      highlightStart,\n      highlightStart + highlightLength,\n    )\n\n    return (\n      <span>\n        {str.split(highlight).map(function (part, index) {\n          return (\n            <span key={index}>\n              {index > 0 ? (\n                <span className=\"json-inspector__hl\">{highlightString}</span>\n              ) : null}\n              {part}\n            </span>\n          )\n        })}\n      </span>\n    )\n  }\n}\n", "export function isObject(value: unknown): value is Record<string, unknown> {\n  return typeof value === 'object' && value !== null && !Array.isArray(value)\n}\n", "import {Component, MouseEvent} from 'react'\n\nimport md5OMatic from 'md5-o-matic'\n\nimport {uid} from './uid'\nimport {type} from './type'\nimport {isPrimitive} from './isPrimitive'\n\nimport {<PERSON><PERSON><PERSON>} from './highlighter'\nimport {isObject} from './isObject'\nimport type {JsonInspectorProps} from './JsonInspector'\n\nconst PATH_PREFIX = '.root.'\n\ninterface LeafProps {\n  data: unknown\n  label: string\n\n  id?: string\n  root?: boolean\n  prefix?: string\n  query?: RegExp | null\n\n  isExpanded?: (keyPath: string, value: unknown) => boolean\n\n  interactiveLabel?: JsonInspectorProps['interactiveLabel']\n  onClick: JsonInspectorProps['onClick']\n\n  // @todo what is this\n  verboseShowOriginal?: boolean\n  getOriginal?: (keypath: string) => unknown\n}\n\ninterface LeafState {\n  expanded: boolean\n\n  // @todo what is this\n  original?: unknown\n}\n\nexport class Leaf extends Component<LeafProps, LeafState> {\n  constructor(props: LeafProps) {\n    super(props)\n\n    this.state = {\n      expanded: this._isInitiallyExpanded(this.props),\n    }\n  }\n\n  render() {\n    const {label, data, root, id: inputId} = this.props\n    const id = 'id_' + uid()\n\n    const d = {\n      path: this.keypath(),\n      key: label.toString(),\n      value: data,\n    }\n\n    const onLabelClick = this._onClick.bind(this, d)\n\n    return (\n      <div\n        data-testid={root ? 'leaf-root' : 'leaf-child'}\n        aria-expanded={this.state.expanded}\n        data-root={root || undefined}\n        className={this.getClassName()}\n        id={'leaf-' + this._rootPath()}\n      >\n        <input\n          className=\"json-inspector__radio\"\n          type=\"radio\"\n          name={id}\n          id={inputId}\n          tabIndex={-1}\n        />\n        <label\n          className=\"json-inspector__line\"\n          htmlFor={id}\n          onClick={onLabelClick}\n        >\n          <div className=\"json-inspector__flatpath\">{d.path}</div>\n          <span className=\"json-inspector__key\">\n            {this.format(d.key)}\n            {':'}\n            {this.renderInteractiveLabel(d.key, true)}\n          </span>\n          {this.renderTitle()}\n          {this.renderShowOriginalButton()}\n        </label>\n        {this.renderChildren()}\n      </div>\n    )\n  }\n\n  renderTitle() {\n    const data = this.data()\n    const t = type(data)\n\n    if (Array.isArray(data)) {\n      const length = data.length\n      return (\n        <span className=\"json-inspector__value json-inspector__value_helper\">\n          {length > 0 ? '[…] ' : '[] '}\n          {items(length)}\n        </span>\n      )\n    }\n\n    if (typeof data === 'object' && data !== null) {\n      const keys = Object.keys(data).length\n      return (\n        <span className=\"json-inspector__value json-inspector__value_helper\">\n          {keys > 0 ? '{…} ' : '{} '}\n          {properties(keys)}\n        </span>\n      )\n    }\n\n    return (\n      <span\n        className={\n          'json-inspector__value json-inspector__value_' + t.toLowerCase()\n        }\n      >\n        {this.format(String(data))}\n        {this.renderInteractiveLabel(data, false)}\n      </span>\n    )\n  }\n\n  renderChildren() {\n    const {\n      verboseShowOriginal,\n      query,\n      id,\n      isExpanded,\n      interactiveLabel,\n      onClick,\n      getOriginal,\n    } = this.props\n    const childPrefix = this._rootPath()\n    const data = this.data()\n\n    if (this.state.expanded && (isObject(data) || Array.isArray(data))) {\n      return Object.keys(data).map((key) => {\n        const value = (data as any)[key]\n\n        const shouldGetOriginal =\n          !this.state.original || (verboseShowOriginal ? query : false)\n\n        return (\n          <Leaf\n            data={value}\n            label={key}\n            prefix={childPrefix}\n            onClick={onClick}\n            id={id}\n            query={query}\n            getOriginal={shouldGetOriginal ? getOriginal : undefined}\n            key={getLeafKey(key, value)}\n            isExpanded={isExpanded}\n            interactiveLabel={interactiveLabel}\n            verboseShowOriginal={verboseShowOriginal}\n          />\n        )\n      })\n    }\n\n    return null\n  }\n\n  renderShowOriginalButton() {\n    const {data, getOriginal, query} = this.props\n    if (\n      isPrimitive(data) ||\n      this.state.original ||\n      !getOriginal ||\n      !query ||\n      query.test(this.keypath())\n    ) {\n      return null\n    }\n\n    return (\n      <span\n        className=\"json-inspector__show-original\"\n        onClick={this._onShowOriginalClick}\n      />\n    )\n  }\n\n  renderInteractiveLabel(originalValue: unknown, isKey: boolean) {\n    const InteractiveLabel = this.props.interactiveLabel\n    if (typeof InteractiveLabel === 'function') {\n      return (\n        <InteractiveLabel\n          value={String(originalValue)}\n          originalValue={originalValue}\n          isKey={isKey}\n          keypath={this.keypath()}\n        />\n      )\n    }\n\n    return null\n  }\n\n  static getDerivedStateFromProps(props: LeafProps, state: LeafState) {\n    if (props.query) {\n      return {\n        expanded: !props.query.test(props.label),\n      }\n    }\n\n    return null\n  }\n\n  componentDidUpdate(prevProps: LeafProps) {\n    // Restore original expansion state when switching from search mode\n    // to full browse mode.\n    if (prevProps.query && !this.props.query) {\n      this.setState({\n        expanded: this._isInitiallyExpanded(this.props),\n      })\n    }\n  }\n\n  _rootPath() {\n    return (this.props.prefix || '') + '.' + this.props.label\n  }\n\n  keypath() {\n    return this._rootPath().slice(PATH_PREFIX.length)\n  }\n\n  data() {\n    return this.state.original || this.props.data\n  }\n\n  format(str: string) {\n    return <Highlighter string={str} highlight={this.props.query} />\n  }\n\n  getClassName() {\n    let cn = 'json-inspector__leaf'\n\n    if (this.props.root) {\n      cn += ' json-inspector__leaf_root'\n    }\n\n    if (this.state.expanded) {\n      cn += ' json-inspector__leaf_expanded'\n    }\n\n    if (!isPrimitive(this.props.data)) {\n      cn += ' json-inspector__leaf_composite'\n    }\n\n    return cn\n  }\n\n  toggle() {\n    this.setState({\n      expanded: !this.state.expanded,\n    })\n  }\n\n  _onClick(\n    data: {\n      path: string\n      key: string\n      value: unknown\n    },\n    e: MouseEvent,\n  ) {\n    this.toggle()\n    if (this.props.onClick) {\n      this.props.onClick(data)\n    }\n\n    e.stopPropagation()\n  }\n\n  _onShowOriginalClick = (e: MouseEvent) => {\n    this.setState({\n      original: this.props.getOriginal?.(this.keypath()),\n    })\n\n    e.stopPropagation()\n  }\n\n  _isInitiallyExpanded(p: LeafProps) {\n    if (p.root) {\n      return true\n    }\n\n    const keypath = this.keypath()\n\n    if (!p.query) {\n      return p.isExpanded ? p.isExpanded(keypath, p.data) : false\n    } else {\n      // When a search query is specified, first check if the keypath\n      // contains the search query: if it does, then the current leaf\n      // is itself a search result and there is no need to expand further.\n      //\n      // Having a `getOriginal` function passed signalizes that current\n      // leaf only displays a subset of data, thus should be rendered\n      // expanded to reveal the children that is being searched for.\n      return !p.query.test(keypath) && typeof p.getOriginal === 'function'\n    }\n  }\n}\n\nfunction items(count: number) {\n  return count + (count === 1 ? ' item' : ' items')\n}\n\nfunction properties(count: number) {\n  return count + (count === 1 ? ' property' : ' properties')\n}\n\nfunction getLeafKey(key: string, value: unknown) {\n  if (isPrimitive(value)) {\n    // TODO: Sanitize `value` better.\n    const hash = md5OMatic(String(value))\n    return key + ':' + hash\n  } else {\n    return key + '[' + type(value) + ']'\n  }\n}\n", "export const noop = (...args: unknown[]) => {}\n", "import {type ChangeEventHand<PERSON>, useCallback} from 'react'\nimport {noop} from './noop'\n\n/**\n * @public\n */\nexport interface SearchBarProps {\n  onChange: (query: string) => void\n  data: unknown\n  query: string\n}\n\nexport const SearchBar = ({onChange = noop}: SearchBarProps) => {\n  const onSearchChange: ChangeEventHandler<HTMLInputElement> = useCallback(\n    (evt) => onChange(evt.target.value),\n    [onChange],\n  )\n\n  return (\n    <input\n      className=\"json-inspector__search\"\n      type=\"search\"\n      placeholder=\"Search\"\n      onChange={onSearchChange}\n    />\n  )\n}\n", "import {isObject} from './isObject'\n\nexport function isEmpty(object: unknown) {\n  if (isObject(object)) {\n    return Object.keys(object).length === 0\n  }\n\n  if (Array.isArray(object)) {\n    return object.length === 0\n  }\n\n  if (\n    object === null ||\n    typeof object !== 'string' ||\n    typeof object !== 'number'\n  ) {\n    return true\n  }\n\n  return Object.keys(object).length === 0\n}\n", "import {isEmpty} from './isEmpty'\nimport {JsonInspectorProps} from './JsonInspector'\nimport {isObject} from './isObject'\n\nexport const getFilterer = memoize(\n  (data: unknown, opts?: JsonInspectorProps['filterOptions']) => {\n    const options = opts || {cacheResults: true}\n\n    const cache: Record<string, Record<string, unknown>> = {}\n\n    return function (query: string) {\n      if (!options.cacheResults) {\n        return find(data, query, options)\n      }\n\n      let subquery\n\n      if (!cache[query]) {\n        for (var i = query.length - 1; i > 0; i -= 1) {\n          subquery = query.slice(0, i)\n\n          if (cache[subquery]) {\n            cache[query] = find(cache[subquery], query, options)\n            break\n          }\n        }\n      }\n\n      if (!cache[query]) {\n        cache[query] = find(data, query, options)\n      }\n\n      return cache[query]\n    }\n  },\n)\n\nfunction find(\n  data: unknown,\n  query: string,\n  options: JsonInspectorProps['filterOptions'],\n) {\n  if (!isObject(data) && !Array.isArray(data)) {\n    return {}\n  }\n\n  return Object.keys(data).reduce(function (\n    acc: Record<string, unknown>,\n    key: string,\n  ) {\n    // This fails because data can be an array, but it technically speaking works.\n    // I'd rather refactor this entire thing, but for now I am just porting it with least-effort.\n    const value = (data as any)[key]\n\n    let matches\n\n    if (!value) {\n      return acc\n    }\n\n    if (typeof value !== 'object') {\n      if (contains(query, key, options) || contains(query, value, options)) {\n        acc[key] = value\n      }\n      return acc\n    }\n\n    // If _key_ matches, include it\n    if (contains(query, key, options)) {\n      acc[key] = value\n      return acc\n    }\n\n    matches = find(value, query, options)\n\n    if (!isEmpty(matches)) {\n      Object.assign(acc, pair(key, matches))\n    }\n\n    return acc\n  }, {})\n}\n\nfunction contains(\n  query: string,\n  value: unknown,\n  options: JsonInspectorProps['filterOptions'],\n) {\n  if (!value) {\n    return false\n  }\n\n  var haystack = String(value)\n  var needle = query\n\n  if (options?.ignoreCase) {\n    haystack = haystack.toLowerCase()\n    needle = needle.toLowerCase()\n  }\n\n  return haystack.indexOf(needle) !== -1\n}\n\nfunction pair(key: string, value: unknown) {\n  return {[key]: value}\n}\n\nfunction memoize<R>(\n  fn: (data: unknown, opts?: JsonInspectorProps['filterOptions']) => R,\n) {\n  let lastData: unknown | undefined\n  let lastOptions: JsonInspectorProps['filterOptions'] | undefined\n  let lastResult: R | undefined\n\n  return (data: unknown, options: JsonInspectorProps['filterOptions']): R => {\n    if (!lastResult || data !== lastData || options !== lastOptions) {\n      lastData = data\n      lastOptions = options\n      lastResult = fn(data, options)\n    }\n    return lastResult\n  }\n}\n", "import {isObject} from './isObject'\n\nconst PATH_DELIMITER = '.'\n\nfunction integer(str: string): number {\n  return parseInt(str, 10)\n}\n\nexport function lens(data: unknown, path: string): unknown {\n  var p = path.split(PATH_DELIMITER)\n  var segment = p.shift()\n\n  if (!segment) {\n    return data\n  }\n\n  if (Array.isArray(data) && data[integer(segment)]) {\n    return lens(data[integer(segment)], p.join(PATH_DELIMITER))\n  }\n\n  if (isObject(data) && segment in data) {\n    return lens(data[segment], p.join(PATH_DELIMITER))\n  }\n\n  return undefined\n}\n", "import {Component} from 'react'\nimport debounce from 'debounce'\n\nimport {Leaf} from './Leaf.js'\nimport {SearchBar, type SearchBarProps} from './SearchBar.js'\n\nimport {getFilterer} from './filterer.js'\nimport {isEmpty} from './isEmpty.js'\nimport {lens} from './lens.js'\nimport {noop} from './noop.js'\n\n/**\n * @public\n */\nexport interface JsonInspectorProps {\n  /**\n   * DOM id for the root node\n   */\n  id?: string\n  /**\n   * JSON object or array to inspect.\n   */\n  data: unknown\n  /**\n   * The class name to be added to the root component element.\n   */\n  className?: string\n  /**\n   * Search bar component that accepts `onChange`, `data` and `query`\n   * properties. Defaults to built-in search bar. Pass `false` to disable\n   * search.\n   */\n  search?: React.ComponentType<SearchBarProps> | false\n  /**\n   * Optional parameters for search (toolbar). Must be an object.\n   */\n  searchOptions?: {\n    /**\n     * wait time (ms) between search field `onChange` events before actually\n     * performing search. This can help provide a better user experience when\n     * searching larger data sets. Defaults to `0`.\n     */\n    debounceTime?: number\n  }\n  /**\n   * Can be used to create custom input fields for JSON property names and\n   * primitive values, see [#3][0] for more information.\n   *\n   * [0]: https://github.com/Lapple/react-json-inspector/issues/3\n   */\n  interactiveLabel?: React.ComponentType<{\n    /**\n     * either stringified property value or key value that is being interacted\n     * with\n     */\n    value: string\n    /**\n     * either the original property value or key value,\n     */\n    originalValue: unknown\n    /**\n     * flag to differentiate between interacting with keys or properties,\n     */\n    isKey: boolean\n    /**\n     * keypath of the node being interacted with, will be the same for keys\n     * and properties\n     */\n    keypath: string\n  }>\n  /**\n   * Callback to be run whenever any key-value pair is clicked. Receives an\n   * object with `key`, `value` and `path` properties.\n   */\n  onClick?: (options: {key: string; value: unknown; path: string}) => void\n  /**\n   * Function to check whether the entered search term is sufficient to query\n   * data. Defaults to `(query) => query.length >= 2`.\n   */\n  validateQuery?: (query: string) => boolean\n  /**\n   * Optional predicate that can determine whether the leaf node should be\n   * expanded on initial render. Receives two arguments: `keypath` and `value`.\n   * Defaults to `(keypath, value) => false`.\n   */\n  isExpanded?: (keyPath: string, value: unknown) => boolean\n  filterOptions?: {\n    /**\n     * Set to `false` to disable the filterer cache. This can sometimes\n     * provide performance enhancements with larger data sets. Defaults to\n     * `true`.\n     */\n    cacheResults?: boolean\n    /**\n     * Set to `true` to enable case insensitivity in search. Defaults to\n     * `false`.\n     */\n    ignoreCase?: boolean\n  }\n  /**\n   * Set to `true` for full showOriginal expansion of children containing\n   * search term. Defaults to `false`.\n   */\n  verboseShowOriginal?: boolean\n}\n\n/**\n * @public\n */\nexport interface JsonInspectorState {\n  query: string\n  filterer(\n    data: unknown,\n    options?: JsonInspectorProps['filterOptions'],\n  ): unknown\n}\n\nconst defaultValidateQuery = (query: string) => query.length >= 2\nconst defaultFilterOptions = {cacheResults: true, ignoreCase: false}\n\n/**\n * @public\n */\nexport class JsonInspector extends Component<\n  JsonInspectorProps,\n  JsonInspectorState\n> {\n  static defaultProps = {\n    data: null,\n    search: SearchBar,\n    searchOptions: {\n      debounceTime: 0,\n    },\n    className: '',\n    id: 'json-' + Date.now(),\n    onClick: noop,\n    filterOptions: {\n      cacheResults: true,\n      ignoreCase: false,\n    },\n    validateQuery: function (query: string) {\n      return query.length >= 2\n    },\n    /**\n     * Decide whether the leaf node at given `keypath` should be expanded initially.\n     *\n     * @param keypath - Path to the node\n     * @param value - Value of the node\n     * @returns True if node should be expanded, false otherwise\n     */\n    isExpanded: function (keypath: string, value: unknown) {\n      return false\n    },\n    verboseShowOriginal: false,\n  }\n\n  constructor(props: JsonInspectorProps) {\n    super(props)\n    this.state = {\n      query: '',\n      filterer: getFilterer(props.data, props.filterOptions),\n    }\n  }\n\n  render() {\n    const {\n      data: rawData,\n      className,\n      onClick,\n      id,\n      isExpanded,\n      interactiveLabel,\n      verboseShowOriginal,\n      filterOptions = defaultFilterOptions,\n      validateQuery = defaultValidateQuery,\n    } = this.props\n\n    const isQueryValid =\n      this.state.query !== '' && validateQuery(this.state.query)\n\n    const data = isQueryValid ? this.state.filterer(this.state.query) : rawData\n\n    const isNotFound = isQueryValid && isEmpty(data)\n\n    return (\n      <div\n        data-testid=\"json-inspector\"\n        className={'json-inspector ' + className}\n      >\n        {this.renderToolbar()}\n        {isNotFound ? (\n          <div className=\"json-inspector__not-found\">Nothing found</div>\n        ) : (\n          <Leaf\n            data={data}\n            onClick={onClick}\n            id={id}\n            getOriginal={this.getOriginal}\n            query={\n              isQueryValid\n                ? new RegExp(\n                    this.state.query,\n                    filterOptions.ignoreCase ? 'i' : '',\n                  )\n                : null\n            }\n            label=\"root\"\n            root={true}\n            isExpanded={isExpanded}\n            interactiveLabel={interactiveLabel}\n            verboseShowOriginal={verboseShowOriginal}\n          />\n        )}\n      </div>\n    )\n  }\n\n  renderToolbar() {\n    const Search = this.props.search\n    if (!Search) {\n      return null\n    }\n\n    return (\n      <div className=\"json-inspector__toolbar\">\n        <Search\n          onChange={debounce(\n            this.search,\n            this.props.searchOptions?.debounceTime,\n          )}\n          data={this.props.data}\n          query={this.state.query}\n        />\n      </div>\n    )\n  }\n\n  search = (query: string) => {\n    this.setState({query})\n  }\n\n  static getDerivedStateFromProps(\n    nextProps: JsonInspectorProps,\n    prevState: JsonInspectorState,\n  ) {\n    const filterer = getFilterer(nextProps.data, nextProps.filterOptions)\n    return filterer === prevState.filterer ? null : {...prevState, filterer}\n  }\n\n  shouldComponentUpdate(\n    nextProps: JsonInspectorProps,\n    prevState: JsonInspectorState,\n  ) {\n    return (\n      prevState.query !== this.state.query ||\n      nextProps.data !== this.props.data ||\n      nextProps.onClick !== this.props.onClick\n    )\n  }\n\n  createFilterer = (\n    data: unknown,\n    options: JsonInspectorProps['filterOptions'],\n  ) => {\n    this.setState({\n      filterer: getFilterer(data, options),\n    })\n  }\n\n  getOriginal = (path: string) => {\n    return lens(this.props.data, path)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAcA,aAASA,UAAS,MAAM,MAAM,WAAU;AACtC,UAAI,SAAS,MAAM,SAAS,WAAW;AACvC,UAAI,QAAQ,KAAM,QAAO;AAEzB,eAAS,QAAQ;AACf,YAAI,OAAO,KAAK,IAAI,IAAI;AAExB,YAAI,OAAO,QAAQ,QAAQ,GAAG;AAC5B,oBAAU,WAAW,OAAO,OAAO,IAAI;AAAA,QACzC,OAAO;AACL,oBAAU;AACV,cAAI,CAAC,WAAW;AACd,qBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,sBAAU,OAAO;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAC;AAED,UAAI,YAAY,WAAU;AACxB,kBAAU;AACV,eAAO;AACP,oBAAY,KAAK,IAAI;AACrB,YAAI,UAAU,aAAa,CAAC;AAC5B,YAAI,CAAC,QAAS,WAAU,WAAW,OAAO,IAAI;AAC9C,YAAI,SAAS;AACX,mBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,oBAAU,OAAO;AAAA,QACnB;AAEA,eAAO;AAAA,MACT;AAEA,gBAAU,QAAQ,WAAW;AAC3B,YAAI,SAAS;AACX,uBAAa,OAAO;AACpB,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,gBAAU,QAAQ,WAAW;AAC3B,YAAI,SAAS;AACX,mBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,oBAAU,OAAO;AAEjB,uBAAa,OAAO;AACpB,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,IAAAA,UAAS,WAAWA;AAEpB,WAAO,UAAUA;AAAA;AAAA;;;ACrEjB;AAAA;AAAA;AAMA,WAAO,UAAU;AAUjB,aAAS,UAAU,KAAK;AACpB,UAAI,IAAI,aAAa,GAAG;AACxB,UAAI,IAAK;AACT,UAAI,IAAI;AACR,UAAI,IAAI;AACR,UAAI,IAAK;AAET,eAAQ,IAAE,GAAG,IAAE,EAAE,QAAQ,KAAK,IAC9B;AACI,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,OAAO;AAEX,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,SAAS;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,MAAM;AACtC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,GAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,SAAS;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,GAAK,QAAQ;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAK,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,GAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,GAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,SAAS;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,OAAO;AACvC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,SAAS;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,GAAK,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,QAAQ;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,SAAS;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,GAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,QAAQ;AACxC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,SAAS;AACzC,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,UAAU;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,GAAI,UAAU;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,SAAS;AAC1C,YAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAE1C,YAAI,MAAM,GAAG,IAAI;AACjB,YAAI,MAAM,GAAG,IAAI;AACjB,YAAI,MAAM,GAAG,IAAI;AACjB,YAAI,MAAM,GAAG,IAAI;AAAA,MACrB;AAEA,aAAO,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,IAC/C;AAEA,QAAI,UAAU;AAEd,aAAS,MAAM,GAAG,GAClB;AACI,UAAI,MAAO,IAAI,IAAQ,IAAI;AAC3B,UAAI,QAAS,MAAM,IAAM,MAAM;AAE/B,aAAQ,SAAS,IAAK;AAAA,IAC1B;AAEA,aAAS,OAAO,GAAG,GACnB;AACI,UAAI,MAAO,IAAI,IAAQ,IAAI;AAC3B,UAAI,QAAS,MAAM,IAAM,MAAM;AAE/B,aAAQ,SAAS,IAAK;AAAA,IAC1B;AAEA,aAAS,OAAO,GAAG,GACnB;AACI,UAAI,MAAO,IAAI,KAAQ,IAAI;AAC3B,UAAI,QAAS,MAAM,IAAM,MAAM;AAE/B,aAAQ,SAAS,IAAK;AAAA,IAC1B;AAEA,aAAS,MAAM,GAAG,GAClB;AACI,UAAI,OAAO,IAAI,UAAS,IAAI;AAC5B,UAAI,OAAO,KAAK,OAAK,KAAK,OAAK,OAAO;AAEtC,aAAQ,OAAO,KAAO,MAAM;AAAA,IAChC;AAEA,aAAS,KAAK,KACd;AACI,UAAI,MAAM;AACV,UAAI;AAEJ,WAAI,IAAE,GAAG,KAAG,GAAG;AACX,eAAO,QAAQ,OAAQ,OAAQ,IAAI,IAAI,IAAM,EAAI,IAAI,QAAQ,OAAQ,OAAQ,IAAI,IAAM,EAAI;AAE/F,aAAO;AAAA,IACX;AAEA,aAAS,aAAa,KACtB;AACI,UAAI,QAAS,IAAI,SAAS,KAAM,KAAK;AACrC,UAAI,OAAO,IAAI,MAAM,OAAO,EAAE;AAC9B,UAAI;AAEJ,WAAI,IAAE,GAAG,IAAE,OAAO,IAAI;AAClB,aAAK,CAAC,IAAI;AAEd,WAAI,IAAE,GAAG,IAAE,IAAI,QAAQ;AACnB,aAAK,KAAK,CAAC,KAAK,IAAI,WAAW,CAAC,MAAQ,IAAI,SAAS,IAAI,KAAK,IAAK;AAEvE,WAAK,KAAK,CAAC,KAAK,QAAW,IAAI,SAAS,IAAI,KAAK,IAAK;AAEtD,UAAI,IAAI,IAAI,SAAS;AACrB,WAAK,OAAO,KAAK,CAAC,IAAK,IAAI;AAC3B,WAAK,OAAO,KAAK,CAAC,MAAO,MAAM,IAAK,QAAS;AAC7C,WAAK,OAAO,KAAK,CAAC,MAAO,MAAM,KAAM,QAAS;AAC9C,WAAK,OAAO,KAAK,CAAC,MAAO,MAAM,KAAM,QAAS;AAE9C,aAAO;AAAA,IACX;AAEA,aAAS,IAAI,KAAK,KAClB;AACI,aAAQ,OAAO,MAAQ,QAAS,KAAK;AAAA,IACzC;AAEA,aAAS,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAC5B;AACI,aAAO,MAAM,IAAK,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,GAAI,CAAC,GAAG,CAAC;AAAA,IAC7D;AAEA,aAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAC9B;AACI,aAAO,IAAI,MAAM,OAAO,GAAG,CAAC,GAAG,OAAQ,CAAC,GAAI,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAClE;AAEA,aAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAC9B;AACI,aAAO,IAAI,MAAM,OAAO,GAAG,CAAC,GAAG,OAAO,GAAI,CAAC,CAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAClE;AAEA,aAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAC9B;AACI,aAAO,IAAI,OAAO,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IACrD;AAEA,aAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAC9B;AACI,aAAO,IAAI,OAAO,GAAG,MAAM,GAAI,CAAC,CAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IACvD;AAAA;AAAA;A;;;;;;ACxMA,IAAI,KAAK,KAAK,KAAK,KAAK,OAAA,IAAW,EAAE;AAExB,IAAA,MAAM,MACV,EAAE;ACHJ,SAAS,KAAK,OAAgB;AAC5B,SAAA,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAC1D;ACAO,SAAS,YAAY,OAAyB;AAC7C,QAAA,IAAI,KAAK,KAAK;AACb,SAAA,MAAM,YAAY,MAAM;AACjC;ACEO,IAAM,cAAN,cAA0B,uBAA4B;EAC3D,sBAAsB,GAAqB;AAClC,WAAA,EAAE,cAAc,KAAK,MAAM;EAAA;EAGpC,SAAS;AACP,UAAM,MAAM,KAAK,MAAM,UAAU,IAC3B,YAAY,KAAK,MAAM,aAAa,IACpC,iBAAiB,IAAI,OAAO,SAAS;AAEvC,QAAA,CAAC,aAAa,mBAAmB;AAC5B,iBAAA,wBAAC,QAAA,EAAM,UAAI,IAAA,CAAA;AAGpB,UAAM,kBAAkB,UAAU,OAAO,QACnC,kBAAkB,IAAI;MAC1B;MACA,iBAAiB;IACnB;AAGE,eAAA,wBAAC,QAAA,EACE,UAAI,IAAA,MAAM,SAAS,EAAE,IAAI,SAAU,MAAM,OAAO;AAC/C,iBAAA,yBACG,QACE,EAAA,UAAA;QAAA,QAAQ,QACN,wBAAA,QAAA,EAAK,WAAU,sBAAsB,UAAA,gBAAgB,CAAA,IACpD;QACH;MAAA,EAAA,GAJQ,KAKX;IAEH,CAAA,EAAA,CACH;EAAA;AAGN;AC1CO,SAAS,SAAS,OAAkD;AAClE,SAAA,OAAO,SAAU,YAAY,UAAU,QAAQ,CAAC,MAAM,QAAQ,KAAK;AAC5E;ACUA,IAAM,cAAc;AA4Bb,IAAM,OAAN,MAAM,cAAa,uBAAgC;EACxD,YAAY,OAAkB;AACtB,UAAA,KAAK;AAkPb,gDAAuB,CAAC,MAAkB;;AACxC,WAAK,SAAS;QACZ,WAAU,gBAAK,OAAM,gBAAX,4BAAyB,KAAK,QAAS;MAAA,CAClD,GAED,EAAE,gBAAgB;IACpB;AAtPE,SAAK,QAAQ;MACX,UAAU,KAAK,qBAAqB,KAAK,KAAK;IAChD;EAAA;EAGF,SAAS;AACP,UAAM,EAAC,OAAO,MAAM,MAAM,IAAI,QAAA,IAAW,KAAK,OACxCC,MAAK,QAAQ,IAAA,GAEb,IAAI;MACR,MAAM,KAAK,QAAQ;MACnB,KAAK,MAAM,SAAS;MACpB,OAAO;IAAA,GAGH,eAAe,KAAK,SAAS,KAAK,MAAM,CAAC;AAG7C,eAAA;MAAC;MAAA;QACC,eAAa,OAAO,cAAc;QAClC,iBAAe,KAAK,MAAM;QAC1B,aAAW,QAAQ;QACnB,WAAW,KAAK,aAAa;QAC7B,IAAI,UAAU,KAAK,UAAU;QAE7B,UAAA;cAAA;YAAC;YAAA;cACC,WAAU;cACV,MAAK;cACL,MAAMA;cACN,IAAI;cACJ,UAAU;YAAA;UACZ;cACA;YAAC;YAAA;cACC,WAAU;cACV,SAASA;cACT,SAAS;cAET,UAAA;oBAAA,wBAAC,OAAI,EAAA,WAAU,4BAA4B,UAAA,EAAE,KAAA,CAAK;oBAClD,yBAAC,QAAK,EAAA,WAAU,uBACb,UAAA;kBAAK,KAAA,OAAO,EAAE,GAAG;kBACjB;kBACA,KAAK,uBAAuB,EAAE,KAAK,IAAI;gBAAA,EAAA,CAC1C;gBACC,KAAK,YAAY;gBACjB,KAAK,yBAAyB;cAAA;YAAA;UACjC;UACC,KAAK,eAAe;QAAA;MAAA;IACvB;EAAA;EAIJ,cAAc;AACZ,UAAM,OAAO,KAAK,KACZ,GAAA,IAAI,KAAK,IAAI;AAEf,QAAA,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAM,SAAS,KAAK;AAElB,iBAAA,yBAAC,QAAK,EAAA,WAAU,sDACb,UAAA;QAAA,SAAS,IAAI,SAAS;QACtB,MAAM,MAAM;MAAA,EAAA,CACf;IAAA;AAIJ,QAAI,OAAO,QAAS,YAAY,SAAS,MAAM;AAC7C,YAAM,OAAO,OAAO,KAAK,IAAI,EAAE;AAE7B,iBAAA,yBAAC,QAAK,EAAA,WAAU,sDACb,UAAA;QAAA,OAAO,IAAI,SAAS;QACpB,WAAW,IAAI;MAAA,EAAA,CAClB;IAAA;AAKF,eAAA;MAAC;MAAA;QACC,WACE,iDAAiD,EAAE,YAAY;QAGhE,UAAA;UAAK,KAAA,OAAO,OAAO,IAAI,CAAC;UACxB,KAAK,uBAAuB,MAAM,KAAK;QAAA;MAAA;IAC1C;EAAA;EAIJ,iBAAiB;AACT,UAAA;MACJ;MACA;MACA,IAAAA;MACA;MACA;MACA;MACA;IAAA,IACE,KAAK,OACH,cAAc,KAAK,UAAA,GACnB,OAAO,KAAK,KAAK;AAEvB,WAAI,KAAK,MAAM,aAAa,SAAS,IAAI,KAAK,MAAM,QAAQ,IAAI,KACvD,OAAO,KAAK,IAAI,EAAE,IAAI,CAAC,QAAQ;AAC9B,YAAA,QAAS,KAAa,GAAG,GAEzB,oBACJ,CAAC,KAAK,MAAM,aAAa,sBAAsB,QAAQ;AAGvD,iBAAA;QAAC;QAAA;UACC,MAAM;UACN,OAAO;UACP,QAAQ;UACR;UACA,IAAAA;UACA;UACA,aAAa,oBAAoB,cAAc;UAE/C;UACA;UACA;QAAA;QAHK,WAAW,KAAK,KAAK;MAI5B;IAEH,CAAA,IAGI;EAAA;EAGT,2BAA2B;AACzB,UAAM,EAAC,MAAM,aAAa,MAAA,IAAS,KAAK;AACxC,WACE,YAAY,IAAI,KAChB,KAAK,MAAM,YACX,CAAC,eACD,CAAC,SACD,MAAM,KAAK,KAAK,QAAS,CAAA,IAElB,WAIP;MAAC;MAAA;QACC,WAAU;QACV,SAAS,KAAK;MAAA;IAChB;EAAA;EAIJ,uBAAuB,eAAwB,OAAgB;AACvD,UAAA,mBAAmB,KAAK,MAAM;AAChC,WAAA,OAAO,oBAAqB,iBAE5B;MAAC;MAAA;QACC,OAAO,OAAO,aAAa;QAC3B;QACA;QACA,SAAS,KAAK,QAAQ;MAAA;IAAA,IAKrB;EAAA;EAGT,OAAO,yBAAyB,OAAkB,OAAkB;AAClE,WAAI,MAAM,QACD;MACL,UAAU,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK;IAAA,IAIpC;EAAA;EAGT,mBAAmB,WAAsB;AAGnC,cAAU,SAAS,CAAC,KAAK,MAAM,SACjC,KAAK,SAAS;MACZ,UAAU,KAAK,qBAAqB,KAAK,KAAK;IAAA,CAC/C;EAAA;EAIL,YAAY;AACV,YAAQ,KAAK,MAAM,UAAU,MAAM,MAAM,KAAK,MAAM;EAAA;EAGtD,UAAU;AACR,WAAO,KAAK,UAAA,EAAY,MAAM,YAAY,MAAM;EAAA;EAGlD,OAAO;AACL,WAAO,KAAK,MAAM,YAAY,KAAK,MAAM;EAAA;EAG3C,OAAO,KAAa;AAClB,eAAA,wBAAQ,aAAY,EAAA,QAAQ,KAAK,WAAW,KAAK,MAAM,MAAA,CAAO;EAAA;EAGhE,eAAe;AACb,QAAI,KAAK;AAET,WAAI,KAAK,MAAM,SACb,MAAM,+BAGJ,KAAK,MAAM,aACb,MAAM,mCAGH,YAAY,KAAK,MAAM,IAAI,MAC9B,MAAM,oCAGD;EAAA;EAGT,SAAS;AACP,SAAK,SAAS;MACZ,UAAU,CAAC,KAAK,MAAM;IAAA,CACvB;EAAA;EAGH,SACE,MAKA,GACA;AACK,SAAA,OAAA,GACD,KAAK,MAAM,WACb,KAAK,MAAM,QAAQ,IAAI,GAGzB,EAAE,gBAAgB;EAAA;EAWpB,qBAAqB,GAAc;AACjC,QAAI,EAAE;AACG,aAAA;AAGH,UAAA,UAAU,KAAK,QAAQ;AAE7B,WAAK,EAAE,QAUE,CAAC,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,EAAE,eAAgB,aATnD,EAAE,aAAa,EAAE,WAAW,SAAS,EAAE,IAAI,IAAI;EAAA;AAY5D;AAEA,SAAS,MAAM,OAAe;AACrB,SAAA,SAAS,UAAU,IAAI,UAAU;AAC1C;AAEA,SAAS,WAAW,OAAe;AAC1B,SAAA,SAAS,UAAU,IAAI,cAAc;AAC9C;AAEA,SAAS,WAAW,KAAa,OAAgB;AAC3C,MAAA,YAAY,KAAK,GAAG;AAEtB,UAAM,WAAO,mBAAAC,SAAU,OAAO,KAAK,CAAC;AACpC,WAAO,MAAM,MAAM;EACrB;AACE,WAAO,MAAM,MAAM,KAAK,KAAK,IAAI;AAErC;AC1Ua,IAAA,OAAO,IAAI,SAAoB;AAAC;AAAhC,ICYA,YAAY,CAAC,EAAC,WAAW,KAAA,MAA0B;AAC9D,QAAM,qBAAuD;IAC3D,CAAC,QAAQ,SAAS,IAAI,OAAO,KAAK;IAClC,CAAC,QAAQ;EACX;AAGE,aAAA;IAAC;IAAA;MACC,WAAU;MACV,MAAK;MACL,aAAY;MACZ,UAAU;IAAA;EACZ;AAEJ;ACxBO,SAAS,QAAQ,QAAiB;AACvC,SAAI,SAAS,MAAM,IACV,OAAO,KAAK,MAAM,EAAE,WAAW,IAGpC,MAAM,QAAQ,MAAM,IACf,OAAO,WAAW,IAIzB,WAAW,QACX,OAAO,UAAW,YAClB,OAAO,UAAW,WAEX,OAGF,OAAO,KAAK,MAAM,EAAE,WAAW;AACxC;AChBO,IAAM,cAAc;EACzB,CAAC,MAAe,SAA+C;AAC7D,UAAM,UAAU,QAAQ,EAAC,cAAc,KAAI,GAErC,QAAiD,CAAC;AAExD,WAAO,SAAU,OAAe;AAC9B,UAAI,CAAC,QAAQ;AACJ,eAAA,KAAK,MAAM,OAAO,OAAO;AAG9B,UAAA;AAEA,UAAA,CAAC,MAAM,KAAK,GAAA;AACd,iBAAS,IAAI,MAAM,SAAS,GAAG,IAAI,GAAG,KAAK;AACzC,cAAA,WAAW,MAAM,MAAM,GAAG,CAAC,GAEvB,MAAM,QAAQ,GAAG;AACnB,kBAAM,KAAK,IAAI,KAAK,MAAM,QAAQ,GAAG,OAAO,OAAO;AACnD;UAAA;MAAA;AAKN,aAAK,MAAM,KAAK,MACd,MAAM,KAAK,IAAI,KAAK,MAAM,OAAO,OAAO,IAGnC,MAAM,KAAK;IACpB;EAAA;AAEJ;AAEA,SAAS,KACP,MACA,OACA,SACA;AACA,SAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,QAAQ,IAAI,IACjC,CAAC,IAGH,OAAO,KAAK,IAAI,EAAE,OAAO,SAC9B,KACA,KACA;AAGM,UAAA,QAAS,KAAa,GAAG;AAE3B,QAAA;AAEC,WAAA,QAID,OAAO,SAAU,aACf,SAAS,OAAO,KAAK,OAAO,KAAK,SAAS,OAAO,OAAO,OAAO,OACjE,IAAI,GAAG,IAAI,QAEN,OAIL,SAAS,OAAO,KAAK,OAAO,KAC9B,IAAI,GAAG,IAAI,OACJ,QAGT,UAAU,KAAK,OAAO,OAAO,OAAO,GAE/B,QAAQ,OAAO,KAClB,OAAO,OAAO,KAAK,KAAK,KAAK,OAAO,CAAC,GAGhC,OAtBE;EAuBX,GAAG,CAAA,CAAE;AACP;AAEA,SAAS,SACP,OACA,OACA,SACA;AACA,MAAI,CAAC;AACI,WAAA;AAGT,MAAI,WAAW,OAAO,KAAK,GACvB,SAAS;AAEb,UAAI,mCAAS,gBACX,WAAW,SAAS,YAAY,GAChC,SAAS,OAAO,YAAY,IAGvB,SAAS,QAAQ,MAAM,MAAM;AACtC;AAEA,SAAS,KAAK,KAAa,OAAgB;AACzC,SAAO,EAAC,CAAC,GAAG,GAAG,MAAK;AACtB;AAEA,SAAS,QACP,IACA;AACA,MAAI,UACA,aACA;AAEJ,SAAO,CAAC,MAAe,cACjB,CAAC,cAAc,SAAS,YAAY,YAAY,iBAClD,WAAW,MACX,cAAc,SACd,aAAa,GAAG,MAAM,OAAO,IAExB;AAEX;ACxHA,IAAM,iBAAiB;AAEvB,SAAS,QAAQ,KAAqB;AAC7B,SAAA,SAAS,KAAK,EAAE;AACzB;AAEgB,SAAA,KAAK,MAAe,MAAuB;AACzD,MAAI,IAAI,KAAK,MAAM,cAAc,GAC7B,UAAU,EAAE,MAAM;AAEtB,MAAI,CAAC;AACI,WAAA;AAGT,MAAI,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ,OAAO,CAAC;AACvC,WAAA,KAAK,KAAK,QAAQ,OAAO,CAAC,GAAG,EAAE,KAAK,cAAc,CAAC;AAGxD,MAAA,SAAS,IAAI,KAAK,WAAW;AAC/B,WAAO,KAAK,KAAK,OAAO,GAAG,EAAE,KAAK,cAAc,CAAC;AAIrD;AC4FA,IAAM,uBAAuB,CAAC,UAAkB,MAAM,UAAU;AAAhE,IACM,uBAAuB,EAAC,cAAc,MAAM,YAAY,MAAK;AAK5D,IAAM,gBAAN,cAA4B,uBAGjC;EA8BA,YAAY,OAA2B;AAC/B,UAAA,KAAK;AAgFb,kCAAS,CAAC,UAAkB;AACrB,WAAA,SAAS,EAAC,MAAA,CAAM;IACvB;AAqBA,0CAAiB,CACf,MACA,YACG;AACH,WAAK,SAAS;QACZ,UAAU,YAAY,MAAM,OAAO;MAAA,CACpC;IACH;AAEA,uCAAc,CAAC,SACN,KAAK,KAAK,MAAM,MAAM,IAAI;AAhHjC,SAAK,QAAQ;MACX,OAAO;MACP,UAAU,YAAY,MAAM,MAAM,MAAM,aAAa;IACvD;EAAA;EAGF,SAAS;AACD,UAAA;MACJ,MAAM;MACN;MACA;MACA,IAAAD;MACA;MACA;MACA;MACA,gBAAgB;MAChB,gBAAgB;IAClB,IAAI,KAAK,OAEH,eACJ,KAAK,MAAM,UAAU,MAAM,cAAc,KAAK,MAAM,KAAK,GAErD,OAAO,eAAe,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,IAAI,SAE9D,aAAa,gBAAgB,QAAQ,IAAI;AAG7C,eAAA;MAAC;MAAA;QACC,eAAY;QACZ,WAAW,oBAAoB;QAE9B,UAAA;UAAA,KAAK,cAAc;UACnB,iBACE,wBAAA,OAAA,EAAI,WAAU,6BAA4B,UAAA,gBAAa,CAAA,QAExD;YAAC;YAAA;cACC;cACA;cACA,IAAAA;cACA,aAAa,KAAK;cAClB,OACE,eACI,IAAI;gBACF,KAAK,MAAM;gBACX,cAAc,aAAa,MAAM;cAAA,IAEnC;cAEN,OAAM;cACN,MAAM;cACN;cACA;cACA;YAAA;UAAA;QACF;MAAA;IAEJ;EAAA;EAIJ,gBAAgB;;AACR,UAAA,SAAS,KAAK,MAAM;AAC1B,WAAK,aAKH,wBAAC,OAAI,EAAA,WAAU,2BACb,cAAA;MAAC;MAAA;QACC,cAAU,gBAAAE;UACR,KAAK;WACL,UAAK,MAAM,kBAAX,mBAA0B;QAC5B;QACA,MAAM,KAAK,MAAM;QACjB,OAAO,KAAK,MAAM;MAAA;IAAA,EAAA,CAEtB,IAbO;EAAA;EAqBX,OAAO,yBACL,WACA,WACA;AACA,UAAM,WAAW,YAAY,UAAU,MAAM,UAAU,aAAa;AACpE,WAAO,aAAa,UAAU,WAAW,OAAO,EAAC,GAAG,WAAW,SAAQ;EAAA;EAGzE,sBACE,WACA,WACA;AACA,WACE,UAAU,UAAU,KAAK,MAAM,SAC/B,UAAU,SAAS,KAAK,MAAM,QAC9B,UAAU,YAAY,KAAK,MAAM;EAAA;AAgBvC;AAjJE,cAJW,eAIJ,gBAAe;EACpB,MAAM;EACN,QAAQ;EACR,eAAe;IACb,cAAc;EAChB;EACA,WAAW;EACX,IAAI,UAAU,KAAK,IAAI;EACvB,SAAS;EACT,eAAe;IACb,cAAc;IACd,YAAY;EACd;EACA,eAAe,SAAU,OAAe;AACtC,WAAO,MAAM,UAAU;EACzB;;;;;;;;EAQA,YAAY,SAAU,SAAiB,OAAgB;AAC9C,WAAA;EACT;EACA,qBAAqB;AACvB;", "names": ["debounce", "id", "md5OMatic", "debounce"]}
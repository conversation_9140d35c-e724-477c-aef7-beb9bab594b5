{"version": 3, "sources": ["../../../@sanity/ui/src/core/primitives/code/refractor.tsx"], "sourcesContent": ["import Refractor from 'react-refractor'\n\nexport default function LazyRefractor(\n  props: Partial<Pick<React.ComponentProps<typeof Refractor>, 'language'>> & {\n    value: React.ReactNode\n  },\n) {\n  const {language: languageProp, value} = props\n  const language = typeof languageProp === 'string' ? languageProp : undefined\n  const registered = language ? Refractor.hasLanguage(language as any) : false\n\n  return (\n    <>\n      {!(language && registered) && <code>{value}</code>}\n      {language && registered && <Refractor inline language={language} value={String(value)} />}\n    </>\n  )\n}\n\nLazyRefractor.displayName = 'LazyRefractor'\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAEA,SAAeA,cAAAC,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GAKb;IAAAC,UAAAC;IAAAC;EAAAA,IAAwCL,OACxCG,WAAiB,OAAOC,gBAAiB,WAAWA,eAAYE;AAAYC,MAAAA;AAAAN,IAAAA,CAAAA,MAAAE,YACzDI,KAAAJ,WAAWK,uBAAAA,QAAAC,YAAsBN,QAAe,IAAS,OAAAF,EAAAA,CAAAA,IAAAE,UAAAF,EAAAA,CAAAA,IAAAM,MAAAA,KAAAN,EAAA,CAAA;AAA5E,QAAAS,aAAmBH;AAAyDI,MAAAA;AAAAV,IAAAE,CAAAA,MAAAA,YAAAF,EAAAA,CAAAA,MAAAS,cAAAT,EAAA,CAAA,MAAAI,SAIvEM,KAAA,EAAER,YAAYO,mBAAAA,wBAAAA,QAAsBL,EAAAA,UAAAA,MAAAA,CAAM,GAAOJ,EAAAA,CAAAA,IAAAE,UAAAF,EAAAA,CAAAA,IAAAS,YAAAT,EAAAA,CAAAA,IAAAI,OAAAJ,EAAAA,CAAAA,IAAAU,MAAAA,KAAAV,EAAA,CAAA;AAAAW,MAAAA;AAAAX,IAAAE,CAAAA,MAAAA,YAAAF,EAAAA,CAAAA,MAAAS,cAAAT,EAAA,CAAA,MAAAI,SACjDO,KAAAT,YAAYO,kBAAAA,wBAAe,uBAAAF,SAAU,EAAA,QAAK,MAAYL,UAAiB,OAAAU,OAAOR,KAAK,EAAA,CAAK,GAAAJ,EAAAA,CAAAA,IAAAE,UAAAF,EAAAA,CAAAA,IAAAS,YAAAT,EAAAA,CAAAA,IAAAI,OAAAJ,EAAAA,CAAAA,IAAAW,MAAAA,KAAAX,EAAA,CAAA;AAAAa,MAAAA;AAAAb,SAAAA,EAAAU,EAAAA,MAAAA,MAAAV,EAAAA,EAAAA,MAAAW,MAF3FE,SACGH,yBAAAA,6BAAAA,EAAAA,UAAAA;IAAAA;IACAC;EAAAA,EAAwF,CAAA,GACxFX,EAAAA,EAAAA,IAAAU,IAAAV,EAAAA,EAAAA,IAAAW,IAAAX,EAAAA,EAAAA,IAAAa,MAAAA,KAAAb,EAAA,EAAA,GAHHa;AAGG;AAIPf,cAAcgB,cAAc;", "names": ["LazyRefractor", "props", "$", "_c", "language", "languageProp", "value", "undefined", "t0", "Refractor", "hasLanguage", "registered", "t1", "t2", "String", "t3", "displayName"]}
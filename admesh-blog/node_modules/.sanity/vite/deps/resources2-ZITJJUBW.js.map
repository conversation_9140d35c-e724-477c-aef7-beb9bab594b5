{"version": 3, "sources": ["../../../sanity/src/core/releases/i18n/resources.ts"], "sourcesContent": ["/**\n * Defined locale strings for the releases tool, in US English.\n *\n * @internal\n */\nconst releasesLocaleStrings = {\n  /** Action text for adding a document to release */\n  'action.add-document': 'Add document',\n  /** Action text for archiving a release */\n  'action.archive': 'Archive release',\n  /** Tooltip for when the archive release action is disabled due to release being scheduled  */\n  'action.archive.tooltip': 'Unschedule this release to archive it',\n  /** Action text for showing the archived releases */\n  'action.archived': 'Archived',\n  /** Action text for reverting a release by creating a new release */\n  'action.create-revert-release': 'Stage in new release',\n  /** Action text for deleting a release */\n  'action.delete-release': 'Delete release',\n  /** Action text for duplicating a release */\n  'action.duplicate-release': 'Duplicate release',\n  /** Action text for editing a release */\n  'action.edit': 'Edit release',\n  /** Action text for opening a release */\n  'action.open': 'Active',\n  /** Action text for scheduling a release */\n  'action.schedule': 'Schedule release...',\n  /** Action text for unpublishing a document in a release in the context menu */\n  'action.unpublish': 'Unpublish',\n  /** Action message for scheduling an unpublished of a document  */\n  'action.unpublish-doc-actions': 'Unpublish when releasing',\n  /** Action text for unscheduling a release */\n  'action.unschedule': 'Unschedule release',\n  /** Action text for publishing all documents in a release (and the release itself) */\n  'action.publish-all-documents': 'Run release',\n  /** Text for the review changes button in release tool */\n  'action.review': 'Review changes',\n  /** Action text for reverting a release */\n  'action.revert': 'Revert release',\n  /** Text for the summary button in release tool */\n  'actions.summary': 'Summary',\n  /** Action text for reverting a release immediately without staging changes */\n  'action.immediate-revert-release': 'Revert now',\n  /** Label for unarchiving a release */\n  'action.unarchive': 'Unarchive release',\n  /* The text for the activity event when a document is added to a release */\n  'activity.event.add-document': 'added a document version',\n  /* The text for the activity event when the release is archived */\n  'activity.event.archive': 'archived the <strong>{{releaseTitle}}</strong> release',\n  /* The text for the activity event when the release is created */\n  'activity.event.create':\n    'created the <strong>{{releaseTitle}}</strong> release <ScheduleTarget>targeting </ScheduleTarget>',\n  /* The text for the activity event when a document is removed from a release */\n  'activity.event.discard-document': 'discarded a document version',\n  'activity.event.edit': 'set release time to <ScheduleTarget></ScheduleTarget>',\n  /**The text to display in the changes when the release type changes to asap */\n  'activity.event.edit-time-asap': 'immediately',\n  /**The text to display in the changes when the release type changes to undecided */\n  'activity.event.edit-time-undecided': 'never',\n  /* The text for the activity event when the release is published */\n  'activity.event.publish': 'published the <strong>{{releaseTitle}}</strong> release',\n  /* The text for the activity event when the release is scheduled */\n  'activity.event.schedule': 'marked as scheduled',\n  /** The text for the activity event when the release is unarchived */\n  'activity.event.unarchive': 'unarchived the <strong>{{releaseTitle}}</strong> release',\n  /** The text for the activity event when the release is unscheduled */\n  'activity.event.unschedule': 'marked as unscheduled',\n  /** The loading text for when releases are loading */\n  'activity.panel.loading': 'Loading release activity',\n  /** The loading text for when releases are loading */\n  'activity.panel.error': 'An error occurred getting the release activity',\n  /** The title for the activity panel shown in the releases detail screen */\n  'activity.panel.title': 'Activity',\n\n  /** Header for the dialog confirming the archive of a release */\n  'archive-dialog.confirm-archive-header': 'Are you sure you want to archive this release?',\n  /** Title for the dialog confirming the archive of a release */\n  'archive-dialog.confirm-archive-title':\n    \"Are you sure you want to archive the <strong>'{{title}}'</strong> release?\",\n  /** Description for the dialog confirming the archive of a release with one document */\n  'archive-dialog.confirm-archive-description_one': 'This will archive 1 document version.',\n  /** Description for the dialog confirming the archive of a release with more than one document */\n  'archive-dialog.confirm-archive-description_other':\n    'This will archive {{count}} document versions.',\n  /** Label for the button to proceed with archiving a release */\n  'archive-dialog.confirm-archive-button': 'Yes, archive release',\n\n  /** Title for information card on a archived release */\n  'archive-info.title': 'This release is archived',\n  /** Description for information card on a published or archived release to description retention effects */\n  'archive-info.description':\n    'Your plan supports a {{retentionDays}}-day retention period. After this period this release will be removed.',\n\n  /** Title for changes to published documents */\n  'changes-published-docs.title': 'Changes to published documents',\n  /** Text for when a release / document was created */\n  'created': 'Created <RelativeTime/>',\n  /** Suffix for when a release is a copy of another release */\n  'copy-suffix': 'Copy',\n\n  /** Text for the releases detail screen when a release was published ASAP */\n  'dashboard.details.published-asap': 'Published',\n  /** Text for the releases detail screen when a release was published from scheduling */\n  'dashboard.details.published-on': 'Published on {{date}}',\n\n  /** Text for the releases detail screen in the pin release button. */\n  'dashboard.details.pin-release': 'Pin release',\n  /** Text for the releases detail screen in the unpin release button. */\n  'dashboard.details.unpin-release': 'Unpin release',\n\n  /** Activity inspector button text */\n  'dashboard.details.activity': 'Activity',\n\n  /** Header for deleting a release dialog */\n  'delete-dialog.confirm-delete.header': 'Are you sure you want to delete this release?',\n  /** Description for the dialog confirming the deleting of a release with one document */\n  'delete-dialog.confirm-delete-description_one': 'This will delete 1 document version.',\n  /** Description for the dialog confirming the deleting of a release with more than one document */\n  'delete-dialog.confirm-delete-description_other': 'This will delete {{count}} document versions.',\n  /** Label for the button to proceed deleting a release */\n  'delete-dialog.confirm-delete-button': 'Yes, delete release',\n\n  /** Text for when there's no changes in a release diff */\n  'diff.no-changes': 'No changes',\n  /** Text for when there's no changes in a release diff */\n  'diff.list-empty': 'Changes list is empty, see document',\n  /** Description for discarding a draft of a document dialog */\n  'discard-version-dialog.description-draft':\n    'This will permanently remove all changes made to this document. This action cannot be undone.',\n  /** Description for discarding a version of a document dialog */\n  'discard-version-dialog.description-release':\n    \"This will permanently remove all changes made to this document within the '<strong>{{releaseTitle}}</strong>' release. This action cannot be undone.\",\n  /** Title for dialog for discarding a draft of a document */\n  'discard-version-dialog.header-draft': 'Discard draft?',\n  /** Header for discarding a version from a release of a document dialog */\n  'discard-version-dialog.header-release':\n    \"Remove document from the '<strong>{{releaseTitle}}</strong>' release?\",\n\n  /** Title for dialog for discarding a draft of a document */\n  'discard-version-dialog.title-draft': 'Discard draft',\n  /** Title for dialog for discarding a version of a document */\n  'discard-version-dialog.title-release': 'Remove from release',\n\n  /** Label for when a document in a release has multiple validation warnings */\n  'document-validation.error_other': '{{count}} validation errors',\n  /** Label for when a document in a release has a single validation warning */\n  'document-validation.error_one': '{{count}} validation error',\n\n  /** Label when a release has been deleted by a different user */\n  'deleted-release': \"The '<strong>{{title}}</strong>' release has been deleted\",\n\n  /** Header for the dialog confirming the duplicate of a release */\n  'duplicate-dialog.confirm-duplicate-header': 'Are you sure you want to duplicate this release?',\n  /** Description for the dialog confirming the duplicate of a release with one document */\n  'duplicate-dialog.confirm-duplicate-description_one':\n    'This will duplicate the release and the 1 document version.',\n  /** Description for the dialog confirming the duplicate of a release with more than one document */\n  'duplicate-dialog.confirm-duplicate-description_other':\n    'This will duplicate the release and the {{count}} document versions.',\n  /** Label for the button to proceed with duplicating a release */\n  'duplicate-dialog.confirm-duplicate-button': 'Yes, duplicate release',\n\n  /** Title text displayed for technical error details */\n  'error-details-title': 'Error details',\n  /** Title text when error during release update */\n  'failed-edit-title': 'Failed to save changes',\n  /** Title text displayed for releases that failed to publish  */\n  'failed-publish-title': 'Failed to publish',\n  /** Title text displayed for releases that failed to schedule  */\n  'failed-schedule-title': 'Failed to schedule',\n\n  /**The text that will be shown in the footer to indicate the time the release was archived */\n  'footer.status.archived': 'Archived',\n  /**The text that will be shown in the footer to indicate the time the release was created */\n  'footer.status.created': 'Created',\n  /**The text that will be shown in the footer to indicate the time the release was created */\n  'footer.status.edited': 'Edited',\n  /**The text that will be shown in the footer to indicate the time the release was published */\n  'footer.status.published': 'Published',\n  /**The text that will be shown in the footer to indicate the time the release was unarchived */\n  'footer.status.unarchived': 'Unarchived',\n  /** Label text for the loading state whilst release is being loaded */\n  'loading-release': 'Loading release',\n\n  /** Text for when documents of a release are loading */\n  'loading-release-documents': 'Loading documents',\n  /** Title text for when loading documents on a release failed */\n  'loading-release-documents.error.title': 'Something went wrong',\n  /** Description text for when loading documents on a release failed */\n  'loading-release-documents.error.description':\n    \"We're unable to load the documents for this release. Please try again later.\",\n\n  /** Label for the release menu */\n  'menu.label': 'Release menu',\n  /** Tooltip for the release menu */\n  'menu.tooltip': 'Actions',\n  /** Label for title of actions for \"when releasing\" */\n  'menu.group.when-releasing': 'When releasing',\n\n  /** Text for when no archived releases are found */\n  'no-archived-release': 'No archived releases',\n  /** Text for when no releases are found */\n  'no-releases': 'No Releases',\n  /** Text for when a release is not found */\n  'not-found': 'Release not found: {{releaseId}}',\n\n  /** Text for the button name for the release tool */\n  'overview.action.documentation': 'Documentation',\n  /** Text for when a release is not found */\n  'overview.calendar.tooltip': 'View calendar',\n  /** Description for the release tool */\n  'overview.description':\n    'Releases are collections of document changes which can be managed, scheduled, and rolled back together.',\n  /** Text for the placeholder in the search release input  */\n  'overview.search-releases-placeholder': 'Search releases',\n  /** Title for the release tool */\n  'overview.title': 'Releases',\n\n  /** Tooltip label when the user doesn't have permission for discarding a version */\n  'permissions.error.discard-version': 'You do not have permission to discard this version',\n  /** Tooltip label when the user doesn't have permission for unpublishing a document */\n  'permissions.error.unpublish': 'You do not have permission to unpublish this document',\n  /** Text for when a user doesn't have publish or schedule releases */\n  'permission-missing-title': 'Limited access',\n  /** Description for when a user doesn't have publish or schedule releases */\n  'permission-missing-description':\n    'Your role currently limits what you can see in this release. You may not publish nor schedule this release.',\n  /** Tooltip label when the user doesn't have permission to archive release */\n  'permissions.error.archive': 'You do not have permission to archive this release',\n  /** Tooltip label when the user doesn't have permission to delete release */\n  'permissions.error.delete': 'You do not have permission to delete this release',\n  /** Tooltip label when the user doesn't have permission to duplicate release */\n  'permissions.error.duplicate': 'You do not have permission to duplicate this release',\n  /** Tooltip label when the user doesn't have permission to unarchive release */\n  'permissions.error.unarchive': 'You do not have permission to unarchive this release',\n\n  /** Tooltip text for when one user is editing a document in a release */\n  'presence.tooltip.one':\n    '{{displayName}} is editing this document in the \"{{releaseTitle}}\" release right now',\n  /** Tooltip text for when multiple users are editing a document in a release */\n  'presence.tooltip.other': '{{count}} people are editing this document right now',\n\n  /** Tooltip text for publish release action when there are no documents */\n  'publish-action.validation.no-documents': 'There are no documents to publish',\n  /** Title for the dialog confirming the publish of a release */\n  'publish-dialog.confirm-publish.title':\n    'Are you sure you want to publish the release and all document versions?',\n  /** Description for the dialog confirming the publish of a release with one document */\n  'publish-dialog.confirm-publish-description_one':\n    \"The '<strong>{{title}}</strong>' release and its document will be published.\",\n  /** Description for the dialog confirming the publish of a release with multiple documents */\n  'publish-dialog.confirm-publish-description_other':\n    \"The '<strong>{{title}}</strong>' release and its {{releaseDocumentsLength}} documents will be published.\",\n  /** Label for the button when the user doesn't have permissions to publish a release */\n  'publish-dialog.validation.no-permission': 'You do not have permission to publish',\n  /** Label for when documents are being validated */\n  'publish-dialog.validation.loading': 'Validating documents...',\n  /** Label for when documents in release have validation errors */\n  'publish-dialog.validation.error': 'Some documents have validation errors',\n\n  /** Title for information card on a published release */\n  'publish-info.title': 'This release is published',\n\n  /** Placeholder title for a release with no title */\n  'release-placeholder.title': 'Untitled',\n\n  /** Description for the review changes button in release tool */\n  'review.description': 'Add documents to this release to review changes',\n  /** Text for when a document is edited */\n  'review.edited': 'Edited <RelativeTime/>',\n  /** Description for the dialog confirming the revert of a release with multiple documents */\n  'revert-dialog.confirm-revert-description_one':\n    'This will revert {{releaseDocumentsLength}} document version.',\n  /** Description for the dialog confirming the revert of a release with multiple documents */\n  'revert-dialog.confirm-revert-description_other':\n    'This will revert {{releaseDocumentsLength}} document versions.',\n  /** Title for the dialog confirming the revert of a release */\n  'revert-dialog.confirm-revert.title': \"Are you sure you want to revert the '{{title}}' release?\",\n  /** Checkbox label to confirm whether to create a staged release for revert or immediately revert */\n  'revert-dialog.confirm-revert.stage-revert-checkbox-label':\n    'Stage revert actions in a new release',\n  /** Warning card text for when immediately revert a release with history */\n  'revert-dialog.confirm-revert.warning-card':\n    'Changes were made to documents in this release after they were published. Reverting will overwrite these changes.',\n  /** Title of a reverted release */\n  'revert-release.title': 'Reverting \"{{title}}\"',\n  /** Description of a reverted release */\n  'revert-release.description': 'Revert changes to document versions in \"{{title}}\".',\n\n  /** Title of unschedule release dialog */\n  'schedule-button.tooltip': 'Are you sure you want to unschedule the release?',\n\n  /** Schedule release button tooltip when there are no documents to schedule */\n  'schedule-action.validation.no-documents': 'There are no documents to schedule',\n  /** Schedule release button tooltip when user has no permissions to schedule */\n  'schedule-button-tooltip.validation.no-permission': 'You do not have permission to schedule',\n  /** Schedule release button tooltip when validation is loading */\n  'schedule-button-tooltip.validation.loading': 'Validating documents...',\n  /** Schedule release button tooltip when there are validation errors */\n  'schedule-button-tooltip.validation.error': 'Some documents have validation errors',\n\n  /** Schedule release button tooltip when the release is already scheduled */\n  'schedule-button-tooltip.already-scheduled': 'This release is already scheduled',\n\n  /** Title for unschedule release dialog */\n  'schedule-dialog.confirm-title': 'Schedule the release',\n  /** Description shown in unschedule relaease dialog */\n  'schedule-dialog.confirm-description_one':\n    \"The '<strong>{{title}}</strong>' release and its document will be published on the selected date.\",\n  /** Description for the dialog confirming the publish of a release with multiple documents */\n  'schedule-dialog.confirm-description_other':\n    'The <strong>{{title}}</strong> release and its {{count}} document versions will be scheduled.',\n\n  /** Description for the confirm button for scheduling a release */\n  'schedule-dialog.confirm-button': 'Yes, schedule',\n\n  /** Label for date picker when scheduling a release */\n  'schedule-dialog.select-publish-date-label': 'Schedule on',\n\n  /** Title for unschedule release dialog */\n  'unschedule-dialog.confirm-title': 'Are you sure you want to unschedule the release?',\n  /** Description shown in unschedule relaease dialog */\n  'unschedule-dialog.confirm-description':\n    'The release will no longer be published on the scheduled date',\n  /** Description for warning that the published schedule time is in the past */\n  'schedule-dialog.publish-date-in-past-warning':\n    'Schedule this release for a future time and date.',\n\n  /** Placeholder for search of documents in a release */\n  'search-documents-placeholder': 'Search documents',\n  /** Text for when the release was created */\n  'summary.created': 'Created <RelativeTime/>',\n  /** Text for when the release was published */\n  'summary.published': 'Published <RelativeTime/>',\n  /** Text for when the release has not published */\n  'summary.not-published': 'Not published',\n  /** Text for when the release has no documents */\n  'summary.no-documents': 'No documents',\n  /** Text for when the release is composed of one document */\n  'summary.document-count_one': '{{count}} document',\n  /** Text for when the release is composed of multiple documents */\n  'summary.document-count_other': '{{count}} documents',\n\n  /** add action type that will be shown in the table*/\n  'table-body.action.add': 'Add',\n  /** Change action type that will be shown in the table*/\n  'table-body.action.change': 'Change',\n  /** Change action type that will be shown in the table*/\n  'table-body.action.unpublish': 'Unpublish',\n\n  /** Header for the document table in the release tool - Archived */\n  'table-header.archivedAt': 'Archived',\n  /** Header for the document table in the release tool - contributors */\n  'table-header.contributors': 'Contributors',\n  /** Header for the document table in the release tool - type */\n  'table-header.type': 'Type',\n  /** Header for the document table in the release tool - release title */\n  'table-header.title': 'Release',\n  /** Header for the document table in the release tool - action */\n  'table-header.action': 'Action',\n  /** Header for the document table in the release tool - title */\n  'table-header.documents': 'Documents',\n  /** Header for the document table in the release tool - edited */\n  'table-header.edited': 'Edited',\n  /** Header for the document table in the release tool - Published */\n  'table-header.publishedAt': 'Published',\n  /** Header for the document table in the release tool - time */\n  'table-header.time': 'Time',\n\n  /** Text for toast when release failed to archive */\n  'toast.archive.error': \"Failed to archive '<strong>{{title}}</strong>': {{error}}\",\n  /** Description for toast when creating new version of document in release failed */\n  'toast.create-version.error': 'Failed to add document to release: {{error}}',\n  /** Description for toast when release deletion failed */\n  'toast.delete.error': \"Failed to delete '<strong>{{title}}</strong>': {{error}}\",\n  /** Description for toast when release is successfully deleted */\n  'toast.delete.success': \"The '<strong>{{title}}</strong>' release was successfully deleted\",\n  /** Description for toast when release duplication failed */\n  'toast.duplicate.error': \"Failed to duplicate '<strong>{{title}}</strong>': {{error}}\",\n  /** Description for toast when release is successfully duplicated */\n  'toast.duplicate.success': \"The '<strong>{{title}}</strong>' release was duplicated. <Link/>\",\n  /** Link text for toast link to the duplicated release */\n  'toast.duplicate.success-link': 'View duplicated release',\n  /** Text for toast when release failed to publish */\n  'toast.publish.error': \"Failed to publish '<strong>{{title}}</strong>': {{error}}\",\n  /** Text for toast when release failed to schedule */\n  'toast.schedule.error': \"Failed to schedule '<strong>{{title}}</strong>': {{error}}\",\n  /** Text for toast when release has been scheduled */\n  'toast.schedule.success': \"The '<strong>{{title}}</strong>' release was scheduled.\",\n  /** Text for toast when release failed to unschedule */\n  'toast.unschedule.error': \"Failed to unscheduled '<strong>{{title}}</strong>': {{error}}\",\n  /** Text for toast when release failed to unarchive */\n  'toast.unarchive.error': \"Failed to unarchive '<strong>{{title}}</strong>': {{error}}\",\n  /** Description for toast when release deletion failed */\n  /** Text for tooltip when a release has been scheduled */\n  'type-picker.tooltip.scheduled': 'The release is scheduled, unschedule it to change type',\n  /** Text for toast when release failed to revert */\n  'toast.revert.error': 'Failed to revert release: {{error}}',\n  /** Text for toast when release has been reverted immediately */\n  'toast.immediate-revert.success': \"The '{{title}}' release was successfully reverted\",\n  /** Text for toast when release has reverted release successfully staged */\n  'toast.revert-stage.success': \"Revert release for '{{title}}' was successfully created. <Link/>\",\n  /** Link text for toast link to the generated revert release */\n  'toast.revert-stage.success-link': 'View revert release',\n\n  /** Text for when a document is unpublished */\n  'unpublish.already-unpublished': 'This document is already unpublished.',\n  /** Tooltip label for when a document is unpublished */\n  'unpublish.no-published-version': 'There is no published version of this document.',\n  /** Title for the dialog confirming the unpublish of a release */\n  'unpublish-dialog.header': 'Are you sure you want to unpublish this document when releasing?',\n  /** Text action in unpublish dialog to cancel */\n  'unpublish-dialog.action.cancel': 'Cancel',\n  /** Text action in unpublish dialog to unpublish */\n  'unpublish-dialog.action.unpublish': 'Yes, unpublish when releasing',\n  /** Description for the unpublish dialog, explaining that it will create a draft if no draft exists at time of release */\n  'unpublish-dialog.description.to-draft':\n    'This will unpublish the document as part of the <Label>{{title}}</Label> release, and create a draft if no draft exists at the time of release.',\n  /** Description for unpublish dialog, explaining that all changes made to this document will be lost */\n  'unpublish-dialog.description.lost-changes':\n    'Any changes made to this document version will be lost.',\n}\n\n/**\n * @alpha\n */\nexport type ReleasesLocaleResourceKeys = keyof typeof releasesLocaleStrings\n\nexport default releasesLocaleStrings\n"], "mappings": ";;;AAKA,IAAMA,wBAAwB;;EAE5B,uBAAuB;;EAEvB,kBAAkB;;EAElB,0BAA0B;;EAE1B,mBAAmB;;EAEnB,gCAAgC;;EAEhC,yBAAyB;;EAEzB,4BAA4B;;EAE5B,eAAe;;EAEf,eAAe;;EAEf,mBAAmB;;EAEnB,oBAAoB;;EAEpB,gCAAgC;;EAEhC,qBAAqB;;EAErB,gCAAgC;;EAEhC,iBAAiB;;EAEjB,iBAAiB;;EAEjB,mBAAmB;;EAEnB,mCAAmC;;EAEnC,oBAAoB;;EAEpB,+BAA+B;;EAE/B,0BAA0B;;EAE1B,yBACE;;EAEF,mCAAmC;EACnC,uBAAuB;;EAEvB,iCAAiC;;EAEjC,sCAAsC;;EAEtC,0BAA0B;;EAE1B,2BAA2B;;EAE3B,4BAA4B;;EAE5B,6BAA6B;;EAE7B,0BAA0B;;EAE1B,wBAAwB;;EAExB,wBAAwB;;EAGxB,yCAAyC;;EAEzC,wCACE;;EAEF,kDAAkD;;EAElD,oDACE;;EAEF,yCAAyC;;EAGzC,sBAAsB;;EAEtB,4BACE;;EAGF,gCAAgC;;EAEhC,SAAW;;EAEX,eAAe;;EAGf,oCAAoC;;EAEpC,kCAAkC;;EAGlC,iCAAiC;;EAEjC,mCAAmC;;EAGnC,8BAA8B;;EAG9B,uCAAuC;;EAEvC,gDAAgD;;EAEhD,kDAAkD;;EAElD,uCAAuC;;EAGvC,mBAAmB;;EAEnB,mBAAmB;;EAEnB,4CACE;;EAEF,8CACE;;EAEF,uCAAuC;;EAEvC,yCACE;;EAGF,sCAAsC;;EAEtC,wCAAwC;;EAGxC,mCAAmC;;EAEnC,iCAAiC;;EAGjC,mBAAmB;;EAGnB,6CAA6C;;EAE7C,sDACE;;EAEF,wDACE;;EAEF,6CAA6C;;EAG7C,uBAAuB;;EAEvB,qBAAqB;;EAErB,wBAAwB;;EAExB,yBAAyB;;EAGzB,0BAA0B;;EAE1B,yBAAyB;;EAEzB,wBAAwB;;EAExB,2BAA2B;;EAE3B,4BAA4B;;EAE5B,mBAAmB;;EAGnB,6BAA6B;;EAE7B,yCAAyC;;EAEzC,+CACE;;EAGF,cAAc;;EAEd,gBAAgB;;EAEhB,6BAA6B;;EAG7B,uBAAuB;;EAEvB,eAAe;;EAEf,aAAa;;EAGb,iCAAiC;;EAEjC,6BAA6B;;EAE7B,wBACE;;EAEF,wCAAwC;;EAExC,kBAAkB;;EAGlB,qCAAqC;;EAErC,+BAA+B;;EAE/B,4BAA4B;;EAE5B,kCACE;;EAEF,6BAA6B;;EAE7B,4BAA4B;;EAE5B,+BAA+B;;EAE/B,+BAA+B;;EAG/B,wBACE;;EAEF,0BAA0B;;EAG1B,0CAA0C;;EAE1C,wCACE;;EAEF,kDACE;;EAEF,oDACE;;EAEF,2CAA2C;;EAE3C,qCAAqC;;EAErC,mCAAmC;;EAGnC,sBAAsB;;EAGtB,6BAA6B;;EAG7B,sBAAsB;;EAEtB,iBAAiB;;EAEjB,gDACE;;EAEF,kDACE;;EAEF,sCAAsC;;EAEtC,4DACE;;EAEF,6CACE;;EAEF,wBAAwB;;EAExB,8BAA8B;;EAG9B,2BAA2B;;EAG3B,2CAA2C;;EAE3C,oDAAoD;;EAEpD,8CAA8C;;EAE9C,4CAA4C;;EAG5C,6CAA6C;;EAG7C,iCAAiC;;EAEjC,2CACE;;EAEF,6CACE;;EAGF,kCAAkC;;EAGlC,6CAA6C;;EAG7C,mCAAmC;;EAEnC,yCACE;;EAEF,gDACE;;EAGF,gCAAgC;;EAEhC,mBAAmB;;EAEnB,qBAAqB;;EAErB,yBAAyB;;EAEzB,wBAAwB;;EAExB,8BAA8B;;EAE9B,gCAAgC;;EAGhC,yBAAyB;;EAEzB,4BAA4B;;EAE5B,+BAA+B;;EAG/B,2BAA2B;;EAE3B,6BAA6B;;EAE7B,qBAAqB;;EAErB,sBAAsB;;EAEtB,uBAAuB;;EAEvB,0BAA0B;;EAE1B,uBAAuB;;EAEvB,4BAA4B;;EAE5B,qBAAqB;;EAGrB,uBAAuB;;EAEvB,8BAA8B;;EAE9B,sBAAsB;;EAEtB,wBAAwB;;EAExB,yBAAyB;;EAEzB,2BAA2B;;EAE3B,gCAAgC;;EAEhC,uBAAuB;;EAEvB,wBAAwB;;EAExB,0BAA0B;;EAE1B,0BAA0B;;EAE1B,yBAAyB;;;EAGzB,iCAAiC;;EAEjC,sBAAsB;;EAEtB,kCAAkC;;EAElC,8BAA8B;;EAE9B,mCAAmC;;EAGnC,iCAAiC;;EAEjC,kCAAkC;;EAElC,2BAA2B;;EAE3B,kCAAkC;;EAElC,qCAAqC;;EAErC,yCACE;;EAEF,6CACE;AACJ;", "names": ["releasesLocaleStrings"]}
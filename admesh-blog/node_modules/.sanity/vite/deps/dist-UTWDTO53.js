import {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
} from "./chunk-24MG4Z6R.js";
import "./chunk-SGSHY4V2.js";
import "./chunk-DT6GHPDN.js";
import "./chunk-EOZ53ZFO.js";
import "./chunk-OCBYBPSH.js";
export {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
};
